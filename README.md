# Legal Case Management System

> **Secure AI-powered legal case management with zero-exposure architecture**

## 🚀 Quick Start

```bash
# Start the system
./lcm start

# Interactive menu
./lcm

# Access web interface
https://localhost/
```

## 📋 Commands

| Command | Description |
|---------|-------------|
| `./lcm start` | Start the LCM system |
| `./lcm stop` | Stop the LCM system |
| `./lcm restart` | Restart the LCM system |
| `./lcm status` | Show system status |
| `./lcm logs` | View system logs |
| `./lcm shell` | Access container shell |
| `./lcm update` | Update and rebuild |
| `./lcm` | Interactive menu |

## 🛡️ Security Features

- **🔒 HTTPS Only**: Port 443 exclusively
- **🗄️ Internal Databases**: No external database access
- **🐳 Containerized**: Complete isolation
- **🔐 Encrypted**: All data encrypted at rest and in transit
- **🛡️ Zero Exposure**: Minimal attack surface

## 🏗️ Architecture

```
lcm/
├── .env                    # Environment configuration
├── .gitignore             # Git ignore rules  
├── lcm                    # Control script
├── README.md              # This file
├── app/                   # Application code
├── core/                  # Core modules
├── deployment/            # Deployment scripts
├── config-files/          # Docker & config files
├── scripts/               # Utility scripts
├── docs/                  # Documentation
└── ssl/                   # SSL certificates
```

## 📊 System Requirements

- **Docker**: Latest version
- **RAM**: 4GB minimum, 8GB recommended
- **Storage**: 10GB available space
- **Network**: Port 443 available

## 🔧 Administration

### Database Access
```bash
# PostgreSQL
./lcm shell
psql -U legal_cms_user -d legal_cms_main

# MongoDB  
./lcm shell
mongosh

# Redis
./lcm shell
redis-cli
```

### System Management
```bash
# View container logs
./lcm logs

# Update system
./lcm update

# System status
./lcm status
```

## 🆘 Troubleshooting

### System Won't Start
1. Check Docker is running: `docker --version`
2. Check port 443 availability: `sudo netstat -tlnp | grep :443`
3. Verify .env file exists and has credentials
4. View logs: `./lcm logs`

### Web Interface Not Accessible
1. Wait 60 seconds for full startup
2. Check status: `./lcm status`
3. Verify HTTPS: `https://localhost/` (not http://)
4. Check firewall settings

### Database Connection Issues
1. Access container: `./lcm shell`
2. Check database status inside container
3. Restart system: `./lcm restart`

## 📚 Documentation

- **[Setup Guide](docs/DEPLOYMENT.md)** - Detailed setup instructions
- **[Security Guide](docs/SECURITY_MIGRATION_PLAN.md)** - Security configuration
- **[API Reference](docs/LCMALL.md)** - Complete feature documentation

## 🏢 Legal Features

- **📑 Case Management**: Complete case organization
- **🤖 AI Analysis**: Multi-agent legal analysis
- **📄 Document Processing**: OCR and text extraction
- **⚖️ Court Integration**: E-filing and docket monitoring
- **📊 Reporting**: Export and analytics
- **🔍 Search**: Intelligent document search

---

**Made with ⚖️ for legal professionals**