#!/usr/bin/env python3
"""
Test script for the E-Service Queue Scraper
Follows the complete workflow: uncheck filter, search case, scrape all pages, download images
"""

import asyncio
import json
from datetime import datetime
from core.eservice_scraper import scrape_eservice_case

async def test_eservice_scraper():
    """Test the E-Service scraper with the specific workflow"""
    
    case_number = "DR 25 403973"
    
    # Get credentials
    username = input("Enter your e-filing username: ")
    password = input("Enter your e-filing password: ")
    
    print(f"🚀 Starting E-Service Queue scraper for case: {case_number}")
    print("📋 Expected workflow:")
    print("   1. Login to e-filing system")
    print("   2. Navigate to E-Service Queue")
    print("   3. Uncheck 'Unread Notices Only'")
    print("   4. Enter case number and search")
    print("   5. Scrape all 4 pages (15 records each = 60 total)")
    print("   6. Save HTML table data")
    print("   7. Download all associated images/documents")
    print("⏳ This may take several minutes...")
    
    try:
        # Run the scraper
        results = await scrape_eservice_case(
            username=username,
            password=password,
            case_number=case_number,
            headless=False,  # Set to True to hide browser
            download_dir="eservice_downloads"
        )
        
        # Display results
        print("\n" + "="*60)
        print("📊 E-SERVICE SCRAPING RESULTS")
        print("="*60)
        
        if results.get('error'):
            print(f"❌ Error: {results['error']}")
            return
        
        print(f"🔍 Case Number: {results.get('case_number', 'Unknown')}")
        print(f"📄 Total pages scraped: {results.get('total_pages', 0)}")
        print(f"📝 Total records found: {results.get('total_records', 0)}")
        
        # Page breakdown
        print(f"\n📋 Page breakdown:")
        for page in results.get('pages', []):
            page_num = page.get('page_number', '?')
            record_count = len(page.get('records', []))
            print(f"   Page {page_num}: {record_count} records")
        
        # Download results
        downloads = results.get('downloads', {})
        if downloads:
            print(f"\n⬇️  Download summary:")
            print(f"   🔗 Total links found: {downloads.get('total_links', 0)}")
            print(f"   🖼️  Total images found: {downloads.get('total_images', 0)}")
            print(f"   ✅ Successful downloads: {downloads.get('successful_downloads', 0)}")
            print(f"   ❌ Failed downloads: {downloads.get('failed_downloads', 0)}")
            print(f"   📁 Download directory: {results.get('download_directory', 'Unknown')}")
            
            if downloads.get('errors'):
                print(f"\n⚠️  Download errors:")
                for error in downloads['errors'][:3]:  # Show first 3 errors
                    print(f"   - {error}")
                if len(downloads['errors']) > 3:
                    print(f"   ... and {len(downloads['errors']) - 3} more errors")
        
        # Sample record data
        if results.get('all_records'):
            print(f"\n📄 Sample record data:")
            sample_record = results['all_records'][0]
            print(f"   Record from Page {sample_record.get('page_number', '?')}, Row {sample_record.get('row_number', '?')}")
            
            # Show column headers
            data = sample_record.get('data', {})
            if data:
                print(f"   Columns found: {list(data.keys())}")
                
                # Show first few data values
                for i, (key, value) in enumerate(data.items()):
                    if i < 3:  # Show first 3 columns
                        print(f"   {key}: {value[:50]}..." if len(str(value)) > 50 else f"   {key}: {value}")
                    elif i == 3:
                        print(f"   ... and {len(data) - 3} more columns")
                        break
            
            # Show links and images count
            links_count = len(sample_record.get('links', []))
            images_count = len(sample_record.get('images', []))
            if links_count > 0 or images_count > 0:
                print(f"   Links in this record: {links_count}")
                print(f"   Images in this record: {images_count}")
        
        # Save detailed results
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        safe_case = case_number.replace(' ', '_').replace('-', '_')
        results_file = f"eservice_results_{safe_case}_{timestamp}.json"
        
        with open(results_file, 'w') as f:
            json.dump(results, f, indent=2)
        
        print(f"\n💾 Detailed results saved to: {results_file}")
        
        # Save HTML table data separately
        html_file = f"eservice_table_{safe_case}_{timestamp}.html"
        with open(html_file, 'w', encoding='utf-8') as f:
            f.write("<html><head><title>E-Service Records</title></head><body>")
            f.write(f"<h1>E-Service Records for Case {case_number}</h1>")
            
            for page in results.get('pages', []):
                f.write(f"<h2>Page {page.get('page_number', '?')}</h2>")
                f.write("<table border='1' style='border-collapse: collapse; width: 100%;'>")
                
                # Write headers if we have records
                records = page.get('records', [])
                if records:
                    first_record = records[0]
                    headers = list(first_record.get('data', {}).keys())
                    f.write("<tr style='background-color: #f0f0f0;'>")
                    for header in headers:
                        f.write(f"<th style='padding: 8px;'>{header}</th>")
                    f.write("</tr>")
                    
                    # Write data rows
                    for record in records:
                        f.write("<tr>")
                        for header in headers:
                            value = record.get('data', {}).get(header, '')
                            f.write(f"<td style='padding: 8px;'>{value}</td>")
                        f.write("</tr>")
                
                f.write("</table><br>")
            
            f.write("</body></html>")
        
        print(f"📋 HTML table saved to: {html_file}")
        
        # Check if we got the expected results
        total_found = results.get('total_records', 0)
        expected_pages = 4
        expected_total = 60  # 4 pages × 15 records
        
        if results.get('total_pages', 0) == expected_pages and total_found == expected_total:
            print("🎉 SUCCESS: Found exactly 4 pages with 60 records as expected!")
        elif total_found > 0:
            print(f"⚠️  Found {results.get('total_pages', 0)} pages with {total_found} records")
            print(f"   (Expected: 4 pages with 60 records)")
        else:
            print("❌ No records found - check credentials, case number, and search workflow")
        
        return results
        
    except Exception as e:
        print(f"💥 E-Service scraper failed: {e}")
        return None

def main():
    """Main function"""
    print("🔍 Enhanced E-Service Queue Scraper")
    print("=" * 50)
    
    # Check if required packages are available
    try:
        import playwright
        import aiohttp
        import aiofiles
        print("✅ All required packages available")
    except ImportError as e:
        print(f"❌ Missing package: {e}")
        print("Install with: pip install playwright aiohttp aiofiles")
        print("Then run: playwright install chromium")
        return
    
    # Run the test
    asyncio.run(test_eservice_scraper())

if __name__ == "__main__":
    main()