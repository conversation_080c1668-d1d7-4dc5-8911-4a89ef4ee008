{"permissions": {"allow": ["Bash(git add:*)", "Bash(git commit:*)", "Bash(git checkout:*)", "<PERSON><PERSON>(chmod:*)", "Bash(./deployment/deploy_secure_architecture.sh deploy:*)", "Bash(./deployment/deploy_user_secure_architecture.sh deploy:*)", "<PERSON><PERSON>(docker:*)", "<PERSON><PERSON>(docker-compose:*)", "Bash(./deployment/deploy_manual.sh deploy:*)", "Bash(./deployment/deploy_manual.sh:*)", "<PERSON><PERSON>(curl:*)", "Bash(pip3 install:*)", "<PERSON><PERSON>(python3:*)", "<PERSON><PERSON>(source .env)", "Bash(--name legal-cms-mongodb )", "Bash(--network legal-cms-net )", "<PERSON>sh(-p 27018:27017 )", "Bash(-e MONGO_INITDB_ROOT_USERNAME=admin )", "Bash(-e MONGO_INITDB_ROOT_PASSWORD=\"$MONGODB_PASSWORD\" )", "Bash(-e MONGO_INITDB_DATABASE=legal_cms_documents )", "Bash(-v legal_cms_mongo_data:/data/db )", "Bash(--restart unless-stopped )", "Bash(mongo:7.0)", "<PERSON><PERSON>(pkill:*)", "Bash(sqlite3:*)", "<PERSON><PERSON>(playwright install:*)", "<PERSON><PERSON>(mv:*)", "Bash(USE_SECURE_ARCHITECTURE=true streamlit run legal_case_manager.py --server.port 8502)", "Bash(rm:*)", "Bash(python test_enhanced_docket_scraper.py)", "Bash(grep:*)", "Bash(streamlit run:*)", "Bash(sudo ls:*)", "<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(openssl req:*)", "Bash(export MONGODB_PASSWORD=AxWuQaxIGX8g)", "<PERSON><PERSON>(true)", "Bash(./mkcert-v*-linux-amd64)", "Bash(-install)", "Bash(python test_efile_fix.py)", "Bash(./deployment/deploy_user_secure_architecture.sh:*)", "Bash(export USE_SECURE_ARCHITECTURE=true)", "<PERSON><PERSON>(cat:*)", "<PERSON><PERSON>(set -a)", "Bash(set +a)", "<PERSON><PERSON>(python:*)", "Bash(sudo apt remove:*)", "Bash(pgrep:*)", "Bash(./deploy_secure_architecture.sh deploy:*)", "Bash(./deploy_user_secure_architecture.sh:*)", "Bash(deployment/deploy_user_secure_architecture.sh deploy:*)", "<PERSON><PERSON>(echo:*)", "Bash(pip install:*)", "Bash(find:*)", "Bash(__NEW_LINE__ echo \"📈 AFTER OPTIMIZATION (Current results from logs):\")", "Bash(__NEW_LINE__ echo \"🔧 KEY TECHNICAL IMPROVEMENTS:\")", "Bash(__NEW_LINE__ echo \"🎯 RESULT: Transformed scraper from 0% to 100% effectiveness!\")", "Bash(for i in {1..15})", "Bash(do)", "Bash(done)", "Bash(kill:*)", "<PERSON><PERSON>(env)", "Bash(nginx:alpine)", "Bash(./deploy-all-in-one-law-firm-saas.sh:*)", "Bash(./scripts/secure-database-ports.sh:*)", "Bash(config-legal-cms-app)", "Bash(./scripts/security-password-audit.sh:*)", "<PERSON><PERSON>(zero-exposure-law-firm-saas:latest)", "Bash(./health-check.sh:*)", "Bash(PGPASSWORD=\"$POSTGRES_PASSWORD\" psql -h localhost -p 5435 -U legal_cms_user -d legal_cms_main -c \"SELECT 1;\")", "Bash(export POSTGRES_PASSWORD)", "Bash(PGPASSWORD=\"$POSTGRES_PASSWORD\" docker exec legal-cms-postgresql psql -U legal_cms_user -d legal_cms_main -c \"SELECT ''PostgreSQL is ready'' as status;\")", "Bash(postgres:15-alpine)", "Bash(PGPASSWORD=\"$POSTGRES_PASSWORD\" docker exec legal-cms-postgresql psql -U legal_cms_user -d legal_cms_main -c \"SELECT ''PostgreSQL connection successful!'' as status;\")", "Bash(export POSTGRES_PASSWORD MONGODB_PASSWORD REDIS_PASSWORD)", "Bash(export MONGODB_PASSWORD)", "<PERSON><PERSON>(vault:1.15)", "Bash(PGPASSWORD=\"$POSTGRES_PASSWORD\" docker exec legal-cms-postgresql psql -U legal_cms_user -d legal_cms_main -c \"SELECT ''Connected'' as status;\")", "Bash(export REDIS_HOST=localhost REDIS_PORT=6382)", "Bash(export POSTGRES_PASSWORD MONGODB_PASSWORD REDIS_PASSWORD REDIS_HOST=localhost REDIS_PORT=6382)", "Bash(PGPASSWORD=\"$POSTGRES_PASSWORD\" docker exec legal-cms-postgresql psql -U legal_cms_user -d legal_cms_main -c \"\\dt\")", "Bash(PGPASSWORD=\"$POSTGRES_PASSWORD\" docker exec legal-cms-postgresql psql -U legal_cms_user -d legal_cms_main -c \"SELECT COUNT(*) FROM cases;\")", "Bash(PGPASSWORD=\"$POSTGRES_PASSWORD\" docker exec legal-cms-postgresql psql -U legal_cms_user -d legal_cms_main -c \"SELECT * FROM cases LIMIT 3;\")", "Bash(export POSTGRES_PASSWORD MONGODB_PASSWORD)", "Bash(export POSTGRES_PASSWORD=AxWuQaxIGX8g)", "<PERSON><PERSON>(touch:*)", "Bash(export REDIS_HOST=localhost)", "Bash(export REDIS_PORT=6382)"], "deny": [], "defaultMode": "acceptEdits"}}