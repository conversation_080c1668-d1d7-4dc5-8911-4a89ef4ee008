#!/bin/bash
# Legal Case Management (LCM) Control Script
# Single script to manage the entire application

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# Application settings
APP_NAME="Legal Case Management System"
CONTAINER_NAME="lcm-secure"
DOCKER_IMAGE="lcm:latest"
WEB_PORT="443"

# Paths
APP_DIR="app"
CONFIG_DIR="config-files"
DEPLOYMENT_DIR="deployment"

clear
echo -e "${BLUE}"
echo "╔══════════════════════════════════════════════════════════════╗"
echo "║              LEGAL CASE MANAGEMENT SYSTEM                   ║"
echo "║                    Control Panel                            ║"
echo "╚══════════════════════════════════════════════════════════════╝"
echo -e "${NC}"

show_status() {
    echo -e "${YELLOW}📊 System Status:${NC}"
    
    # Check if container is running
    if docker ps --format "table {{.Names}}" | grep -q "^${CONTAINER_NAME}$"; then
        echo -e "   Container: ${GREEN}✅ Running${NC}"
        
        # Check web access
        if curl -k -s -o /dev/null -w "%{http_code}" https://localhost:${WEB_PORT}/ | grep -q "200"; then
            echo -e "   Web Access: ${GREEN}✅ Available at https://localhost:${WEB_PORT}/${NC}"
        else
            echo -e "   Web Access: ${YELLOW}⚠️  Starting up...${NC}"
        fi
        
        # Check databases
        if docker exec ${CONTAINER_NAME} pg_isready -U legal_cms_user -d legal_cms_main -q 2>/dev/null; then
            echo -e "   PostgreSQL: ${GREEN}✅ Ready${NC}"
        else
            echo -e "   PostgreSQL: ${YELLOW}⚠️  Initializing...${NC}"
        fi
        
        if docker exec ${CONTAINER_NAME} mongosh --eval "db.runCommand('ping')" --quiet 2>/dev/null | grep -q "ok"; then
            echo -e "   MongoDB: ${GREEN}✅ Ready${NC}"
        else
            echo -e "   MongoDB: ${YELLOW}⚠️  Initializing...${NC}"
        fi
        
        if docker exec ${CONTAINER_NAME} redis-cli ping 2>/dev/null | grep -q "PONG"; then
            echo -e "   Redis: ${GREEN}✅ Ready${NC}"
        else
            echo -e "   Redis: ${YELLOW}⚠️  Initializing...${NC}"
        fi
        
    elif docker ps -a --format "table {{.Names}}" | grep -q "^${CONTAINER_NAME}$"; then
        echo -e "   Container: ${RED}❌ Stopped${NC}"
    else
        echo -e "   Container: ${RED}❌ Not Found${NC}"
    fi
    echo ""
}

start_system() {
    echo -e "${YELLOW}🚀 Starting ${APP_NAME}...${NC}"
    
    # Load environment
    if [ -f .env ]; then
        source .env
    else
        echo -e "${RED}❌ .env file not found${NC}"
        exit 1
    fi
    
    # Check if already running
    if docker ps --format "table {{.Names}}" | grep -q "^${CONTAINER_NAME}$"; then
        echo -e "${YELLOW}⚠️  System is already running${NC}"
        return
    fi
    
    # Start the secure container
    echo -e "${BLUE}🔒 Starting secure HTTPS-only deployment...${NC}"
    ${DEPLOYMENT_DIR}/deploy-secure-443-only.sh
    
    echo -e "${GREEN}✅ System started successfully!${NC}"
    echo -e "${BLUE}🌐 Access: https://localhost:${WEB_PORT}/${NC}"
}

stop_system() {
    echo -e "${YELLOW}🛑 Stopping ${APP_NAME}...${NC}"
    
    if docker ps --format "table {{.Names}}" | grep -q "^${CONTAINER_NAME}$"; then
        docker stop ${CONTAINER_NAME}
        echo -e "${GREEN}✅ System stopped${NC}"
    else
        echo -e "${YELLOW}⚠️  System is not running${NC}"
    fi
}

restart_system() {
    echo -e "${YELLOW}🔄 Restarting ${APP_NAME}...${NC}"
    stop_system
    sleep 3
    start_system
}

view_logs() {
    echo -e "${YELLOW}📋 System Logs:${NC}"
    echo -e "${BLUE}Press Ctrl+C to exit log view${NC}"
    echo ""
    
    if docker ps --format "table {{.Names}}" | grep -q "^${CONTAINER_NAME}$"; then
        docker logs -f ${CONTAINER_NAME}
    else
        echo -e "${RED}❌ Container is not running${NC}"
    fi
}

shell_access() {
    echo -e "${YELLOW}🔧 Opening container shell...${NC}"
    echo -e "${BLUE}Type 'exit' to return to this menu${NC}"
    echo ""
    
    if docker ps --format "table {{.Names}}" | grep -q "^${CONTAINER_NAME}$"; then
        docker exec -it ${CONTAINER_NAME} /bin/bash
    else
        echo -e "${RED}❌ Container is not running${NC}"
    fi
}

database_access() {
    echo -e "${YELLOW}🗄️  Database Access:${NC}"
    echo "1. PostgreSQL"
    echo "2. MongoDB" 
    echo "3. Redis"
    echo "4. Back to main menu"
    echo ""
    read -p "Select database: " db_choice
    
    case $db_choice in
        1)
            echo -e "${BLUE}Connecting to PostgreSQL...${NC}"
            docker exec -it ${CONTAINER_NAME} psql -U legal_cms_user -d legal_cms_main
            ;;
        2)
            echo -e "${BLUE}Connecting to MongoDB...${NC}"
            docker exec -it ${CONTAINER_NAME} mongosh
            ;;
        3)
            echo -e "${BLUE}Connecting to Redis...${NC}"
            docker exec -it ${CONTAINER_NAME} redis-cli
            ;;
        4)
            return
            ;;
        *)
            echo -e "${RED}Invalid option${NC}"
            ;;
    esac
}

update_system() {
    echo -e "${YELLOW}🔄 Updating ${APP_NAME}...${NC}"
    
    # Pull latest changes
    git pull
    
    # Rebuild container
    stop_system
    docker rmi ${DOCKER_IMAGE} 2>/dev/null || true
    start_system
    
    echo -e "${GREEN}✅ System updated${NC}"
}

show_menu() {
    show_status
    
    echo -e "${PURPLE}🎛️  Control Panel:${NC}"
    echo "1. 🚀 Start System"
    echo "2. 🛑 Stop System" 
    echo "3. 🔄 Restart System"
    echo "4. 📋 View Logs"
    echo "5. 🔧 Shell Access"
    echo "6. 🗄️  Database Access"
    echo "7. 🔄 Update System"
    echo "8. 📊 Status Only"
    echo "9. 🚪 Exit"
    echo ""
    read -p "Select option [1-9]: " choice
    
    case $choice in
        1) start_system ;;
        2) stop_system ;;
        3) restart_system ;;
        4) view_logs ;;
        5) shell_access ;;
        6) database_access ;;
        7) update_system ;;
        8) clear && show_menu ;;
        9) echo -e "${GREEN}👋 Goodbye!${NC}" && exit 0 ;;
        *) echo -e "${RED}Invalid option${NC}" ;;
    esac
    
    echo ""
    read -p "Press Enter to continue..."
    clear
    show_menu
}

# Handle command line arguments
case "${1:-menu}" in
    "start") start_system ;;
    "stop") stop_system ;;
    "restart") restart_system ;;
    "status") show_status ;;
    "logs") view_logs ;;
    "shell") shell_access ;;
    "update") update_system ;;
    "menu"|"") show_menu ;;
    *)
        echo "Usage: $0 {start|stop|restart|status|logs|shell|update|menu}"
        echo ""
        echo "Commands:"
        echo "  start    - Start the LCM system"
        echo "  stop     - Stop the LCM system"
        echo "  restart  - Restart the LCM system"
        echo "  status   - Show system status"
        echo "  logs     - View system logs"
        echo "  shell    - Access container shell"
        echo "  update   - Update and rebuild system"
        echo "  menu     - Show interactive menu (default)"
        exit 1
        ;;
esac