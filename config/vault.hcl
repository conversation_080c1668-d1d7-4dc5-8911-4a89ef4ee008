# HashiCorp Vault Configuration for All-in-One Law Firm SaaS
storage "file" {
  path = "/vault/data"
}

listener "tcp" {
  address         = "0.0.0.0:8200"
  cluster_address = "0.0.0.0:8201"
  tls_disable     = false
  tls_cert_file   = "/etc/letsencrypt/live/law-firm-saas/fullchain.pem"
  tls_key_file    = "/etc/letsencrypt/live/law-firm-saas/privkey.pem"
}

seal "gcpckms" {
  # Disable auto-unseal in development mode
  # Configure for production with proper KMS
}

cluster_addr  = "https://127.0.0.1:8201"
api_addr      = "https://0.0.0.0:8200"
disable_mlock = true

ui = true

# Development mode settings (remove in production)
dev_root_token_id = "law-firm-vault-token-2025"
dev_listen_address = "0.0.0.0:8200"

# Logging
log_level = "Info"
log_format = "json"

# Enable audit logging
audit {
  file {
    file_path = "/vault/logs/audit.log"
  }
}

# Plugin directory
plugin_directory = "/vault/plugins"

# Maximum lease TTL
max_lease_ttl = "8760h"
default_lease_ttl = "8760h"

# Raw storage endpoint (disable in production)
raw_storage_endpoint = false

# Telemetry
telemetry {
  statsd_address = "127.0.0.1:8125"
  disable_hostname = true
}