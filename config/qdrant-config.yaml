# Qdrant Configuration for Legal Case Management System
# Production-ready vector database configuration

log_level: INFO

storage:
  # Storage backend configuration
  storage_path: /qdrant/storage
  snapshots_path: /qdrant/snapshots
  temp_path: /qdrant/temp
  
  # Performance settings
  max_concurrent_updates: 10
  update_queue_size: 100
  
  # Optimization settings
  optimizers:
    deleted_threshold: 0.2
    vacuum_min_vector_number: 1000
    default_segment_number: 0
    max_segment_size_kb: 20000
    memmap_threshold_kb: 100000
    indexing_threshold_kb: 20000
    flush_interval_sec: 5
    max_optimization_threads: 1

service:
  # HTTP API configuration
  http_port: 6333
  grpc_port: 6334
  
  # CORS settings for web interface
  enable_cors: true
  cors_origins: ["*"]
  
  # API key authentication (optional)
  # api_key: "your-api-key-here"
  
  # Rate limiting
  max_request_size_mb: 32
  max_workers: 0  # Auto-detect based on CPU cores

cluster:
  # Cluster configuration (for future scaling)
  enabled: false
  # node_id: 1
  # bootstrap: true
  # consensus:
  #   tick_period_ms: 100
  #   bootstrap_timeout_sec: 10

telemetry:
  # Disable telemetry for privacy
  disabled: true

# Collection defaults
collections:
  # Default vector configuration
  default_vector_config:
    size: 384  # Default embedding size
    distance: Cosine
    
  # Default HNSW configuration
  default_hnsw_config:
    m: 16
    ef_construct: 100
    full_scan_threshold: 10000
    max_indexing_threads: 0
    on_disk: false
    
  # Default quantization (optional)
  # default_quantization_config:
  #   scalar:
  #     type: int8
  #     quantile: 0.99
  #     always_ram: true

# Logging configuration
logging:
  # Log to stdout for container environments
  span_events: false
  
# Security settings
security:
  # Enable TLS (configure certificates if needed)
  # tls:
  #   cert: /path/to/cert.pem
  #   key: /path/to/key.pem
  
  # JWT authentication (optional)
  # jwt:
  #   secret: "your-jwt-secret"
  #   ttl_seconds: 3600
