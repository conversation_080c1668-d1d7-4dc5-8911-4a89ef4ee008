{"postgresql": {"host": "localhost", "port": 5435, "database": "legal_cms_main", "user": "legal_cms_user", "password": "${POSTGRES_PASSWORD}", "sslmode": "disable", "pool_size": {"min": 2, "max": 20}, "connection_timeout": 10, "application_name": "legal_cms_secure"}, "mongodb": {"hosts": ["localhost:27018"], "database": "legal_cms_documents", "username": "admin", "password": "${MONGODB_PASSWORD}", "ssl": false, "auth_source": "admin", "read_preference": "primaryPreferred", "write_concern": {"w": "majority", "j": true, "wtimeout": 5000}, "connection_options": {"serverSelectionTimeoutMS": 5000, "connectTimeoutMS": 10000, "socketTimeoutMS": 20000, "maxPoolSize": 50, "minPoolSize": 5, "maxIdleTimeMS": 30000, "waitQueueTimeoutMS": 5000}}, "redis": {"host": "localhost", "port": 6382, "db": 0, "ssl": false, "socket_timeout": 5, "socket_connect_timeout": 5, "retry_on_timeout": true, "health_check_interval": 30, "connection_pool": {"max_connections": 100, "retry_on_timeout": true}, "ssl_cert_reqs": "none"}, "vault": {"url": "https://vault.legal-cms.internal:8200", "auth_method": "userpass", "mount_point": "legal-cms", "ssl_verify": true, "ssl_ca_bundle": "/etc/ssl/certs/vault-ca.crt", "timeout": 30, "secret_paths": {"pgp_private_keys": "pgp-private-keys", "pgp_public_keys": "pgp-public-keys", "api_keys": "api-keys", "database_credentials": "database-creds"}}, "github": {"organization": "legal-cms-secure", "token_vault_path": "api-keys/github", "repository_prefix": "legal-cms-case-", "backup_repository": "legal-cms-backups", "api_base_url": "https://api.github.com", "max_file_size": *********, "rate_limit": {"requests_per_hour": 5000, "burst_limit": 100}}, "encryption": {"default_algorithm": "AES-256-CBC", "pgp_key_size": 2048, "master_key_size": 4096, "key_rotation_days": 90, "backup_encryption": true, "compression": {"enabled": true, "algorithm": "gzip", "level": 6}}, "security": {"session_timeout": 3600, "max_login_attempts": 5, "account_lockout_duration": 1800, "password_policy": {"min_length": 12, "require_uppercase": true, "require_lowercase": true, "require_numbers": true, "require_special_chars": true, "max_age_days": 90}, "mfa": {"enabled": true, "issuer": "Legal CMS", "backup_codes": 10}, "audit_logging": {"enabled": true, "log_level": "INFO", "retention_days": 2555, "encrypt_logs": true}}, "backup": {"enabled": true, "schedule": "0 2 * * *", "retention": {"daily": 30, "weekly": 12, "monthly": 12, "yearly": 7}, "encryption": {"enabled": true, "algorithm": "AES-256-GCM"}, "destinations": [{"type": "github", "repository": "legal-cms-backups", "path": "database-backups"}, {"type": "vault", "path": "backups/database"}]}, "monitoring": {"enabled": true, "metrics": {"database_connections": true, "query_performance": true, "encryption_operations": true, "github_api_usage": true}, "alerts": {"failed_logins": 10, "database_errors": 5, "encryption_failures": 1, "disk_usage_threshold": 85}, "health_checks": {"interval": 30, "timeout": 10, "endpoints": ["postgresql", "mongodb", "redis", "vault", "github"]}}, "performance": {"query_timeout": 30, "connection_pool_timeout": 5, "cache_ttl": {"user_sessions": 3600, "case_data": 1800, "document_metadata": 3600, "pgp_keys": 86400}, "batch_sizes": {"document_processing": 10, "migration_batch": 100, "backup_batch": 50}}, "development": {"debug_mode": false, "log_sql_queries": false, "disable_ssl_verification": true, "mock_external_services": false, "test_data": {"create_sample_cases": false, "sample_documents": false}}}