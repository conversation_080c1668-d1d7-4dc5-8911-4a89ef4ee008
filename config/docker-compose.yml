version: '3.8'

services:
  # PostgreSQL Primary Database
  legal-cms-postgresql:
    image: postgres:15-alpine
    container_name: legal-cms-postgresql
    environment:
      POSTGRES_DB: legal_cms_main
      POSTGRES_USER: legal_cms_user
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      POSTGRES_INITDB_ARGS: "--auth-host=scram-sha-256 --auth-local=scram-sha-256"
    volumes:
      - postgresql_data:/var/lib/postgresql/data
      - ./init-scripts/postgresql:/docker-entrypoint-initdb.d
    ports:
      - "5435:5432"
    networks:
      - legal-cms-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U legal_cms_user -d legal_cms_main"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # MongoDB Document Store
  legal-cms-mongodb:
    image: mongo:7.0
    container_name: legal-cms-mongodb
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: ${MONGODB_PASSWORD}
      MONGO_INITDB_DATABASE: legal_cms_documents
    volumes:
      - mongodb_data:/data/db
      - ./init-scripts/mongodb:/docker-entrypoint-initdb.d
    ports:
      - "27018:27017"
    networks:
      - legal-cms-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "mongosh", "--eval", "db.adminCommand('ping')"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Redis Cache
  legal-cms-redis:
    image: redis:7-alpine
    container_name: legal-cms-redis
    volumes:
      - redis_data:/data
    ports:
      - "6382:6379"
    networks:
      - legal-cms-network
    restart: unless-stopped
    command: redis-server --requirepass ${REDIS_PASSWORD}
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Qdrant Vector Database
  legal-cms-qdrant:
    image: qdrant/qdrant:latest
    container_name: legal-cms-qdrant
    volumes:
      - qdrant_data:/qdrant/storage
    ports:
      - "6334:6333"
      - "6335:6334"  # gRPC port
    networks:
      - legal-cms-network
    restart: unless-stopped

  # Legal Case Management Application
  legal-cms-app:
    build:
      context: ..
      dockerfile: Dockerfile
    container_name: legal-cms-app
    environment:
      - POSTGRES_HOST=legal-cms-postgresql
      - POSTGRES_PORT=5432
      - POSTGRES_DB=legal_cms_main
      - POSTGRES_USER=legal_cms_user
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - MONGODB_HOST=legal-cms-mongodb
      - MONGODB_PORT=27017
      - MONGODB_DB=legal_cms_documents
      - MONGODB_USERNAME=admin
      - MONGODB_PASSWORD=${MONGODB_PASSWORD}
      - REDIS_HOST=legal-cms-redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=${REDIS_PASSWORD}
      - QDRANT_HOST=legal-cms-qdrant
      - QDRANT_PORT=6333
      - STREAMLIT_SERVER_PORT=8501
      - STREAMLIT_SERVER_ADDRESS=0.0.0.0
      - STREAMLIT_SERVER_HEADLESS=true
      - STREAMLIT_BROWSER_GATHER_USAGE_STATS=false
    volumes:
      - ../case_documents:/app/case_documents
      - ../legal_exports:/app/legal_exports
      - ../efiling_data:/app/efiling_data
      - ../conversation_logs:/app/conversation_logs
    ports:
      - "8501:8501"
    networks:
      - legal-cms-network
    restart: unless-stopped
    depends_on:
      - legal-cms-postgresql
      - legal-cms-mongodb
      - legal-cms-redis
      - legal-cms-qdrant
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8501/_stcore/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s



volumes:
  postgresql_data:
    driver: local
  mongodb_data:
    driver: local
  redis_data:
    driver: local
  qdrant_data:
    driver: local


networks:
  legal-cms-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
