[supervisord]
nodaemon=true
logfile=/app/logs/supervisord.log
pidfile=/app/logs/supervisord.pid
user=root

[inet_http_server]
port=127.0.0.1:9001
username=admin
password=LawFirmSupervisor2025

[supervisorctl]
serverurl=http://127.0.0.1:9001
username=admin
password=LawFirmSupervisor2025

[rpcinterface:supervisor]
supervisor.rpcinterface_factory = supervisor.rpcinterface:make_main_rpcinterface

# PostgreSQL Database Service
[program:postgresql]
command=/usr/lib/postgresql/15/bin/postgres -D /var/lib/postgresql/data -c config_file=/etc/postgresql/15/main/postgresql.conf
user=postgres
autostart=true
autorestart=true
stderr_logfile=/app/logs/postgresql.err.log
stdout_logfile=/app/logs/postgresql.out.log
priority=100

# MongoDB Document Database Service  
[program:mongodb]
command=/usr/bin/mongod --config /etc/mongod.conf
user=mongodb
autostart=true
autorestart=true
stderr_logfile=/app/logs/mongodb.err.log
stdout_logfile=/app/logs/mongodb.out.log
priority=101

# Redis Cache Service
[program:redis]
command=/usr/bin/redis-server /etc/redis/redis.conf
user=redis
autostart=true
autorestart=true
stderr_logfile=/app/logs/redis.err.log
stdout_logfile=/app/logs/redis.out.log
priority=102

# Qdrant Vector Database Service
[program:qdrant]
command=/usr/local/bin/qdrant --config-path /qdrant/config.yaml
user=lawfirm
autostart=true
autorestart=true
stderr_logfile=/app/logs/qdrant.err.log
stdout_logfile=/app/logs/qdrant.out.log
priority=103

# HashiCorp Vault Service
[program:vault]
command=/usr/bin/vault server -config=/etc/vault/vault.hcl
user=vault
autostart=true
autorestart=true
stderr_logfile=/app/logs/vault.err.log
stdout_logfile=/app/logs/vault.out.log
priority=104

# Nginx Web Server with SSL
[program:nginx]
command=/usr/sbin/nginx -g "daemon off;"
user=root
autostart=true
autorestart=true
stderr_logfile=/app/logs/nginx.err.log
stdout_logfile=/app/logs/nginx.out.log
priority=105

# Law Firm SaaS Application (Streamlit)
[program:law-firm-app]
command=/usr/bin/python3 -m streamlit run secure_app.py --server.port=8501 --server.address=0.0.0.0 --server.headless=true
directory=/app
user=lawfirm
autostart=true
autorestart=true
stderr_logfile=/app/logs/streamlit.err.log
stdout_logfile=/app/logs/streamlit.out.log
priority=106
environment=PYTHONPATH="/app"

# SSL Certificate Management (Certbot)
[program:certbot-renewal]
command=/usr/bin/certbot renew --quiet --deploy-hook "supervisorctl restart nginx"
user=root
autostart=false
autorestart=false
stderr_logfile=/app/logs/certbot.err.log
stdout_logfile=/app/logs/certbot.out.log

# Database Backup Service
[program:backup-service]
command=/app/scripts/backup-databases.sh
user=lawfirm
autostart=true
autorestart=true
stderr_logfile=/app/logs/backup.err.log
stdout_logfile=/app/logs/backup.out.log
priority=200

# Security Monitoring Service
[program:security-monitor]
command=/usr/bin/python3 /app/scripts/security-monitor.py
directory=/app
user=lawfirm
autostart=true
autorestart=true
stderr_logfile=/app/logs/security.err.log
stdout_logfile=/app/logs/security.out.log
priority=201