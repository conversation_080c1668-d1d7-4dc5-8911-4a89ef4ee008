[supervisord]
nodaemon=true
pidfile=/var/run/supervisord.pid
logfile=/var/log/supervisor/supervisord.log
logfile_maxbytes=50MB
logfile_backups=10
loglevel=info

[program:postgresql]
command=/usr/lib/postgresql/15/bin/postgres -D /var/lib/postgresql/data
user=postgres
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=/app/logs/postgresql.log
stdout_logfile_maxbytes=10MB
stdout_logfile_backups=3

[program:mongodb]
command=/usr/bin/mongod --dbpath /data/db --bind_ip_all
user=mongodb
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=/app/logs/mongodb.log
stdout_logfile_maxbytes=10MB
stdout_logfile_backups=3

[program:redis]
command=/usr/bin/redis-server --bind 0.0.0.0 --port 6379
user=redis
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=/app/logs/redis.log
stdout_logfile_maxbytes=10MB
stdout_logfile_backups=3

[program:streamlit]
command=python3 -m streamlit run secure_app.py --server.port=8501 --server.address=0.0.0.0 --server.headless=true
directory=/app
user=root
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=/app/logs/streamlit.log
stdout_logfile_maxbytes=10MB
stdout_logfile_backups=3
environment=PYTHONPATH="/app"

[program:nginx]
command=/usr/sbin/nginx -g "daemon off;"
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=/app/logs/nginx.log
stdout_logfile_maxbytes=10MB
stdout_logfile_backups=3