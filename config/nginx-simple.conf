# ALL-IN-ONE LAW FIRM SAAS - SIMPLIFIED NGINX CONFIGURATION

# Rate limiting zones
limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
limit_req_zone $binary_remote_addr zone=uploads:10m rate=5r/s;

# Upstream for Streamlit application
upstream law_firm_app {
    server legal-cms-app:8501;
    keepalive 32;
}

# HTTP server - redirects to HTTPS
server {
    listen 80;
    server_name _;
    
    # Certbot challenge location
    location /.well-known/acme-challenge/ {
        root /var/www/certbot;
        try_files $uri =404;
    }
    
    # Redirect all HTTP traffic to HTTPS
    location / {
        return 301 https://$server_name$request_uri;
    }
}

# HTTPS server - main application
server {
    listen 443 ssl;
    http2 on;
    server_name _;
    
    # SSL Configuration with self-signed certificates for development
    ssl_certificate /etc/ssl/law-firm/law-firm.crt;
    ssl_certificate_key /etc/ssl/law-firm/law-firm.key;
    
    # Modern SSL configuration
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    ssl_session_tickets off;
    
    # Security headers
    add_header Strict-Transport-Security "max-age=63072000; includeSubDomains; preload" always;
    add_header X-Content-Type-Options nosniff always;
    add_header X-Frame-Options DENY always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob:; font-src 'self'; connect-src 'self' wss: ws:;" always;
    
    # Upload limits for large legal documents
    client_max_body_size 500M;
    client_body_timeout 300s;
    client_header_timeout 300s;
    client_body_buffer_size 1M;
    
    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;
    
    # Main application proxy
    location / {
        # Rate limiting
        limit_req zone=api burst=20 nodelay;
        
        # Proxy configuration
        proxy_pass http://law_firm_app;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $host;
        proxy_set_header X-Forwarded-Port $server_port;
        
        # Streamlit specific settings
        proxy_buffering off;
        proxy_read_timeout 86400;
        proxy_send_timeout 300s;
        proxy_connect_timeout 60s;
        
        # Upload handling for large files
        proxy_request_buffering off;
        proxy_max_temp_file_size 0;
        
        # Security
        proxy_hide_header X-Powered-By;
        proxy_hide_header Server;
    }
    
    # WebSocket support for Streamlit real-time features
    location /_stcore/stream {
        proxy_pass http://law_firm_app/_stcore/stream;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        proxy_read_timeout 86400;
        proxy_send_timeout 300s;
    }
    
    # Health check endpoint (localhost only for debugging)
    location /health {
        # Restrict to local access only
        allow 127.0.0.1;
        allow ::1;
        deny all;
        
        access_log off;
        return 200 "ALL-IN-ONE-LAW-FIRM-SAAS healthy\n";
        add_header Content-Type text/plain;
    }
    
    # Debug endpoints (localhost only)
    location /debug {
        # Restrict to local access only for debugging
        allow 127.0.0.1;
        allow ::1;
        deny all;
        
        proxy_pass http://law_firm_app/debug;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # Database admin (localhost only)
    location /admin {
        # Restrict to local access only for database admin
        allow 127.0.0.1;
        allow ::1;
        deny all;
        
        proxy_pass http://law_firm_app/admin;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # System monitoring (localhost only)
    location /monitoring {
        # Restrict to local access only for system monitoring
        allow 127.0.0.1;
        allow ::1;
        deny all;
        
        proxy_pass http://law_firm_app/monitoring;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # Static files caching
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header X-Content-Type-Options nosniff;
        proxy_pass http://law_firm_app;
    }
    
    # Deny access to sensitive files
    location ~ /\. {
        deny all;
        access_log off;
        log_not_found off;
    }
    
    # Custom error pages
    error_page 404 /404.html;
    error_page 500 502 503 504 /50x.html;
    
    location = /404.html {
        root /var/www/html;
        internal;
    }
    
    location = /50x.html {
        root /var/www/html;
        internal;
    }
}