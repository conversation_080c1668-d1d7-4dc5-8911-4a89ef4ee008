# ALL-IN-ONE LAW FIRM SaaS - Nginx Configuration with Certbot SSL

# Rate limiting zones
limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
limit_req_zone $binary_remote_addr zone=uploads:10m rate=5r/s;

# Upstream for Streamlit application
upstream law_firm_app {
    server legal-cms-app:8501;
    keepalive 32;
}

# HTTP server - redirects to HTTPS
server {
    listen 80;
    server_name _;
    
    # Certbot challenge location
    location /.well-known/acme-challenge/ {
        root /var/www/certbot;
        try_files $uri =404;
    }
    
    # Redirect all HTTP traffic to HTTPS
    location / {
        return 301 https://$server_name$request_uri;
    }
}

# HTTPS server - main application
server {
    listen 443 ssl http2;
    server_name _;
    
    # SSL Configuration with self-signed certificates for development
    ssl_certificate /etc/ssl/law-firm/law-firm.crt;
    ssl_certificate_key /etc/ssl/law-firm/law-firm.key;
    
    # Modern SSL configuration
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    ssl_session_tickets off;
    
    # OCSP Stapling disabled for self-signed certificates
    # ssl_stapling on;
    # ssl_stapling_verify on;
    # ssl_trusted_certificate /etc/letsencrypt/live/law-firm-saas/chain.pem;
    # resolver ******* ******* valid=300s;
    # resolver_timeout 5s;
    
    # Security headers
    add_header Strict-Transport-Security "max-age=63072000; includeSubDomains; preload" always;
    add_header X-Content-Type-Options nosniff always;
    add_header X-Frame-Options DENY always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob:; font-src 'self'; connect-src 'self' wss: ws:;" always;
    add_header X-Permitted-Cross-Domain-Policies none always;
    add_header X-Download-Options noopen always;
    
    # Upload limits for large legal documents
    client_max_body_size 500M;
    client_body_timeout 300s;
    client_header_timeout 300s;
    client_body_buffer_size 1M;
    
    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;
    
    # Main application proxy
    location / {
        # Rate limiting
        limit_req zone=api burst=20 nodelay;
        
        # Proxy configuration
        proxy_pass http://law_firm_app;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $host;
        proxy_set_header X-Forwarded-Port $server_port;
        
        # Streamlit specific settings
        proxy_buffering off;
        proxy_read_timeout 86400;
        proxy_send_timeout 300s;
        proxy_connect_timeout 60s;
        
        # Upload handling for large files
        proxy_request_buffering off;
        proxy_max_temp_file_size 0;
        
        # Security
        proxy_hide_header X-Powered-By;
        proxy_hide_header Server;
    }
    
    # WebSocket support for Streamlit real-time features
    location /_stcore/stream {
        proxy_pass http://law_firm_app/_stcore/stream;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        proxy_read_timeout 86400;
        proxy_send_timeout 300s;
    }
    
    # API endpoints with enhanced rate limiting
    location /api/ {
        limit_req zone=api burst=10 nodelay;
        
        proxy_pass http://law_firm_app/api/;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # API security headers
        add_header X-API-Version "1.0" always;
        add_header X-Rate-Limit "10 requests per second" always;
    }
    
    # Upload endpoint with special handling
    location /upload {
        limit_req zone=uploads burst=5 nodelay;
        
        proxy_pass http://law_firm_app/upload;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # Extended timeouts for large uploads
        proxy_read_timeout 600s;
        proxy_send_timeout 600s;
        proxy_connect_timeout 60s;
        client_body_timeout 600s;
    }
    
    # Health check endpoint
    location /health {
        access_log off;
        proxy_pass http://law_firm_app/health;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
    }
    
    # Static files caching
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header X-Content-Type-Options nosniff;
        proxy_pass http://law_firm_app;
    }
    
    # Deny access to sensitive files
    location ~ /\. {
        deny all;
        access_log off;
        log_not_found off;
    }
    
    location ~ /(?:uploads|logs|config|\.env) {
        deny all;
        access_log off;
        log_not_found off;
    }
    
    # Custom error pages
    error_page 404 /404.html;
    error_page 500 502 503 504 /50x.html;
    
    location = /404.html {
        root /var/www/html;
        internal;
    }
    
    location = /50x.html {
        root /var/www/html;
        internal;
    }
}

# Vault admin interface (secured)
server {
    listen 8200 ssl http2;
    server_name _;
    
    # Use same SSL certificates
    ssl_certificate /etc/ssl/law-firm/law-firm.crt;
    ssl_certificate_key /etc/ssl/law-firm/law-firm.key;
    
    # Enhanced security for Vault
    add_header Strict-Transport-Security "max-age=63072000; includeSubDomains; preload" always;
    add_header X-Content-Type-Options nosniff always;
    add_header X-Frame-Options DENY always;
    add_header X-XSS-Protection "1; mode=block" always;
    
    # Restrict access to Vault (add IP whitelist in production)
    # allow 10.0.0.0/8;
    # allow **********/12;
    # allow ***********/16;
    # deny all;
    
    location / {
        proxy_pass http://vault:8200;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}