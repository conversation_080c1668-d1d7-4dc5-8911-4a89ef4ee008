server {
    listen 80;
    server_name localhost;
    
    location / {
        proxy_pass http://127.0.0.1:8501;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # Streamlit specific settings
        proxy_buffering off;
        proxy_read_timeout 86400;
        proxy_send_timeout 300s;
        proxy_connect_timeout 60s;
        
        # WebSocket support
        proxy_set_header Sec-WebSocket-Extensions $http_sec_websocket_extensions;
        proxy_set_header Sec-WebSocket-Key $http_sec_websocket_key;
        proxy_set_header Sec-WebSocket-Protocol $http_sec_websocket_protocol;
        proxy_set_header Sec-WebSocket-Version $http_sec_websocket_version;
    }
    
    # WebSocket endpoint for Streamlit
    location /_stcore/stream {
        proxy_pass http://127.0.0.1:8501/_stcore/stream;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
        proxy_read_timeout 86400;
    }
    
    location /health {
        return 200 "ZERO-EXPOSURE-LAW-FIRM-SAAS healthy\n";
        add_header Content-Type text/plain;
    }
    
    # Internal debugging (container access only)
    location /debug {
        return 200 "Container ID: $hostname\nServices: nginx, streamlit, postgresql, mongodb, redis\n";
        add_header Content-Type text/plain;
    }
}