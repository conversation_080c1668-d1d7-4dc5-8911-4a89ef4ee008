"""
Diagnostic Test for Legal Case Management System

This test helps diagnose what's actually happening with the application
by taking screenshots and dumping page content for analysis.
"""

import logging
import time
from pathlib import Path

import pytest
from playwright.sync_api import Page, <PERSON><PERSON><PERSON>, <PERSON>rowserContext, sync_playwright

logger = logging.getLogger(__name__)

class TestDiagnostic:
    """Diagnostic tests to understand what's happening with the application."""
    
    @pytest.fixture(scope="class")
    def browser_context(self) -> tuple[<PERSON><PERSON><PERSON>, <PERSON>rowserContext, <PERSON>]:
        """Create browser context for diagnostic testing."""
        try:
            logger.info("Starting browser for diagnostic tests")
            playwright = sync_playwright().start()
            browser = playwright.chromium.launch(
                headless=False,  # Run in headed mode to see what's happening
                slow_mo=1000     # Slow down for observation
            )
            context = browser.new_context(viewport={'width': 1920, 'height': 1080})
            page = context.new_page()
            
            yield browser, context, page
            
        finally:
            try:
                context.close()
                browser.close()
                playwright.stop()
            except Exception as e:
                logger.warning(f"Browser cleanup warning: {e}")
    
    def test_page_diagnostic(self, browser_context: tuple[<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>]) -> None:
        """
        Comprehensive diagnostic test to understand page state.
        """
        browser, context, page = browser_context
        
        try:
            logger.info("Starting diagnostic test")
            
            # Navigate to application
            logger.info("Navigating to http://localhost:8501")
            page.goto("http://localhost:8501")
            
            # Wait for different states and check each
            logger.info("Waiting for load state...")
            page.wait_for_load_state("load")
            
            # Take screenshot after load
            page.screenshot(path="diagnostic_after_load.png")
            logger.info("Screenshot taken: diagnostic_after_load.png")
            
            # Wait for network idle
            logger.info("Waiting for network idle...")
            page.wait_for_load_state("networkidle", timeout=30000)
            
            # Take screenshot after network idle
            page.screenshot(path="diagnostic_after_networkidle.png")
            logger.info("Screenshot taken: diagnostic_after_networkidle.png")
            
            # Check page title
            title = page.title()
            logger.info(f"Page title: '{title}'")
            
            # Check page URL
            url = page.url
            logger.info(f"Page URL: {url}")
            
            # Get page content
            body_text = page.locator("body").inner_text()
            logger.info(f"Body text length: {len(body_text)} characters")
            
            if len(body_text) > 0:
                logger.info(f"First 500 characters of body text: {body_text[:500]}")
            else:
                logger.warning("Body text is empty!")
            
            # Get HTML content
            html_content = page.content()
            logger.info(f"HTML content length: {len(html_content)} characters")
            
            # Save HTML content to file for inspection
            with open("diagnostic_page_content.html", "w", encoding="utf-8") as f:
                f.write(html_content)
            logger.info("HTML content saved to: diagnostic_page_content.html")
            
            # Check for specific elements
            elements_to_check = [
                "body",
                "div",
                "main",
                "[data-testid='stAppViewContainer']",
                ".stApp",
                "h1",
                "h2",
                "h3",
                "button",
                "[role='tab']"
            ]
            
            for selector in elements_to_check:
                elements = page.locator(selector)
                count = elements.count()
                logger.info(f"Elements matching '{selector}': {count}")
                
                if count > 0:
                    first_element = elements.first
                    if first_element.is_visible():
                        text = first_element.inner_text()[:100] if first_element.inner_text() else "(no text)"
                        logger.info(f"  First element text: {text}")
            
            # Check for errors in console
            logger.info("Checking for console errors...")
            
            # Wait a bit more to see if content loads
            logger.info("Waiting additional 5 seconds for content to load...")
            page.wait_for_timeout(5000)
            
            # Take final screenshot
            page.screenshot(path="diagnostic_final.png")
            logger.info("Final screenshot taken: diagnostic_final.png")
            
            # Final content check
            final_body_text = page.locator("body").inner_text()
            logger.info(f"Final body text length: {len(final_body_text)} characters")
            
            if len(final_body_text) > 0:
                logger.info(f"Final body text preview: {final_body_text[:200]}")
            
            # Check if Streamlit is actually running
            streamlit_elements = page.locator("[data-testid*='st'], .stApp, .streamlit")
            streamlit_count = streamlit_elements.count()
            logger.info(f"Streamlit-specific elements found: {streamlit_count}")
            
            # Look for any error messages
            error_elements = page.locator("text=/error/i, text=/failed/i, text=/exception/i")
            error_count = error_elements.count()
            if error_count > 0:
                logger.warning(f"Found {error_count} potential error messages")
                for i in range(min(3, error_count)):
                    error_text = error_elements.nth(i).inner_text()
                    logger.warning(f"Error {i}: {error_text}")
            
            # This test always passes - it's just for diagnostics
            logger.info("Diagnostic test completed - check the generated files and logs")
            
        except Exception as e:
            logger.error(f"Diagnostic test encountered error: {e}")
            # Take screenshot on error
            try:
                page.screenshot(path="diagnostic_error.png")
                logger.info("Error screenshot taken: diagnostic_error.png")
            except:
                pass
            raise
