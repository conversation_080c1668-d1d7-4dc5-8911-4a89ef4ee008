"""
Working Functionality Tests for Legal Case Management System

This module provides tests that properly wait for Streamlit content to load
and adapt to the actual page structure.

Author: Legal CMS Team
Version: 1.0.0
"""

import logging
import time
from typing import Optional, Dict, Any, List

import pytest
from playwright.sync_api import Page, <PERSON><PERSON><PERSON>, <PERSON><PERSON>erContext, sync_playwright

# Configure module logger
logger = logging.getLogger(__name__)

class TestWorkingLegalCMSFunctionality:
    """
    Working test suite that properly handles Streamlit loading states.
    """
    
    @pytest.fixture(scope="class")
    def browser_context(self) -> tuple[<PERSON><PERSON><PERSON>, <PERSON>rowserContext, Page]:
        """Create browser context for testing."""
        try:
            logger.info("Starting browser for working functionality tests")
            playwright = sync_playwright().start()
            browser = playwright.chromium.launch(headless=True)
            context = browser.new_context(viewport={'width': 1920, 'height': 1080})
            page = context.new_page()
            
            yield browser, context, page
            
        except Exception as e:
            logger.error(f"Failed to start browser: {e}")
            raise RuntimeError(f"Browser initialization failed: {e}")
        finally:
            try:
                context.close()
                browser.close()
                playwright.stop()
                logger.info("Browser cleanup completed")
            except Exception as e:
                logger.warning(f"Browser cleanup warning: {e}")
    
    def wait_for_streamlit_ready(self, page: Page, timeout: int = 30000) -> bool:
        """
        Wait for Streamlit app to be ready and content to load.
        
        Args:
            page: Playwright page instance
            timeout: Timeout in milliseconds
            
        Returns:
            bool: True if app is ready
        """
        try:
            logger.info("Waiting for Streamlit app to be ready...")
            
            # Wait for the main heading to appear
            main_heading = page.locator("h1:has-text('CUYAHOGA COUNTY PRO SE')")
            main_heading.wait_for(state="visible", timeout=timeout)
            
            # Wait a bit more for dynamic content to load
            page.wait_for_timeout(2000)
            
            logger.info("Streamlit app appears to be ready")
            return True
            
        except Exception as e:
            logger.warning(f"Streamlit app may not be fully ready: {e}")
            return False
    
    def test_application_loads_successfully(self, browser_context: tuple[Browser, BrowserContext, Page]) -> None:
        """
        Test that the Legal CMS application loads successfully.
        """
        browser, context, page = browser_context
        
        try:
            logger.info("Testing application load")
            start_time = time.time()
            
            # Navigate to application
            page.goto("http://localhost:8501", wait_until="networkidle")
            
            # Wait for Streamlit to be ready
            is_ready = self.wait_for_streamlit_ready(page)
            
            load_time = time.time() - start_time
            logger.info(f"Application loaded in {load_time:.2f}s")
            
            # Check that we have the main heading
            main_heading = page.locator("h1")
            assert main_heading.count() > 0, "No main heading found"
            
            heading_text = main_heading.first.inner_text()
            logger.info(f"Found main heading: {heading_text}")
            
            # Should contain legal CMS related text
            assert "CUYAHOGA COUNTY PRO SE" in heading_text, f"Unexpected heading: {heading_text}"
            
            # Check page has content
            body_text = page.locator("body").inner_text()
            assert len(body_text) > 50, f"Page has minimal content: {len(body_text)} characters"
            
            logger.info("Application load test passed")
            
        except Exception as e:
            logger.error(f"Application load test failed: {e}")
            page.screenshot(path="test_failure_working_load.png")
            raise
    
    def test_security_indicators_present(self, browser_context: tuple[Browser, BrowserContext, Page]) -> None:
        """
        Test that security indicators are present.
        """
        browser, context, page = browser_context
        
        try:
            logger.info("Testing security indicators")
            page.goto("http://localhost:8501", wait_until="networkidle")
            self.wait_for_streamlit_ready(page)
            
            # Look for security-related content
            page_text = page.locator("body").inner_text()
            
            security_terms = ["SSL", "TLS", "PGP", "Encrypt", "Vault", "Secure"]
            found_terms = [term for term in security_terms if term in page_text]
            
            logger.info(f"Found security terms: {found_terms}")
            
            # Should have some security indicators
            assert len(found_terms) >= 1, f"No security indicators found. Page text length: {len(page_text)}"
            
            logger.info("Security indicators test passed")
            
        except Exception as e:
            logger.error(f"Security indicators test failed: {e}")
            page.screenshot(path="test_failure_working_security.png")
            raise
    
    def test_interactive_elements_present(self, browser_context: tuple[Browser, BrowserContext, Page]) -> None:
        """
        Test that interactive elements are present and functional.
        """
        browser, context, page = browser_context
        
        try:
            logger.info("Testing interactive elements")
            page.goto("http://localhost:8501", wait_until="networkidle")
            self.wait_for_streamlit_ready(page)
            
            # Count different types of interactive elements
            buttons = page.locator("button")
            button_count = buttons.count()
            logger.info(f"Found {button_count} buttons")
            
            inputs = page.locator("input")
            input_count = inputs.count()
            logger.info(f"Found {input_count} input elements")
            
            selects = page.locator("select")
            select_count = selects.count()
            logger.info(f"Found {select_count} select elements")
            
            # Should have some interactive elements
            total_interactive = button_count + input_count + select_count
            assert total_interactive > 0, "No interactive elements found"
            
            # Test clicking a button if available
            if button_count > 0:
                # Find a clickable button (not disabled)
                for i in range(min(5, button_count)):
                    button = buttons.nth(i)
                    if button.is_visible() and button.is_enabled():
                        logger.info(f"Testing click on button {i}")
                        button.click()
                        page.wait_for_timeout(1000)
                        break
            
            logger.info("Interactive elements test passed")
            
        except Exception as e:
            logger.error(f"Interactive elements test failed: {e}")
            page.screenshot(path="test_failure_working_interactive.png")
            raise
    
    def test_content_structure(self, browser_context: tuple[Browser, BrowserContext, Page]) -> None:
        """
        Test that the page has proper content structure.
        """
        browser, context, page = browser_context
        
        try:
            logger.info("Testing content structure")
            page.goto("http://localhost:8501", wait_until="networkidle")
            self.wait_for_streamlit_ready(page)
            
            # Check for headings
            headings = page.locator("h1, h2, h3, h4, h5, h6")
            heading_count = headings.count()
            logger.info(f"Found {heading_count} headings")
            
            # Should have at least one heading
            assert heading_count >= 1, "No headings found for content structure"
            
            # Check for main content container
            main_container = page.locator("[data-testid='stAppViewContainer']")
            assert main_container.count() > 0, "Main Streamlit container not found"
            
            # Check for content blocks
            content_blocks = page.locator("[data-testid='stVerticalBlock']")
            block_count = content_blocks.count()
            logger.info(f"Found {block_count} content blocks")
            
            # Should have some content structure
            assert block_count > 0, "No content blocks found"
            
            logger.info("Content structure test passed")
            
        except Exception as e:
            logger.error(f"Content structure test failed: {e}")
            page.screenshot(path="test_failure_working_structure.png")
            raise
    
    def test_responsive_design_basic(self, browser_context: tuple[Browser, BrowserContext, Page]) -> None:
        """
        Test basic responsive design functionality.
        """
        browser, context, page = browser_context
        
        viewports = [
            {'width': 1920, 'height': 1080, 'name': 'Desktop'},
            {'width': 768, 'height': 1024, 'name': 'Tablet'},
            {'width': 375, 'height': 667, 'name': 'Mobile'}
        ]
        
        try:
            logger.info("Testing responsive design")
            page.goto("http://localhost:8501", wait_until="networkidle")
            self.wait_for_streamlit_ready(page)
            
            for viewport in viewports:
                logger.info(f"Testing {viewport['name']} viewport: {viewport['width']}x{viewport['height']}")
                
                # Set viewport size
                page.set_viewport_size({'width': viewport['width'], 'height': viewport['height']})
                page.wait_for_timeout(1000)
                
                # Check main heading is still visible
                main_heading = page.locator("h1").first
                assert main_heading.is_visible(), f"Main heading not visible on {viewport['name']}"
                
                # Check for excessive horizontal scrolling
                if viewport['width'] <= 768:
                    body_width = page.evaluate("document.body.scrollWidth")
                    viewport_width = viewport['width']
                    
                    if body_width > viewport_width + 50:
                        logger.warning(f"Potential horizontal scrolling on {viewport['name']}: {body_width}px > {viewport_width}px")
                    else:
                        logger.info(f"Good responsive behavior on {viewport['name']}")
            
            logger.info("Responsive design test passed")
            
        except Exception as e:
            logger.error(f"Responsive design test failed: {e}")
            page.screenshot(path="test_failure_working_responsive.png")
            raise
    
    def test_performance_basic(self, browser_context: tuple[Browser, BrowserContext, Page]) -> None:
        """
        Test basic performance metrics.
        """
        browser, context, page = browser_context
        
        try:
            logger.info("Testing basic performance")
            
            # Measure load time
            start_time = time.time()
            page.goto("http://localhost:8501", wait_until="networkidle")
            self.wait_for_streamlit_ready(page)
            load_time = time.time() - start_time
            
            # Log performance results
            if load_time < 5.0:
                logger.info(f"Excellent load performance: {load_time:.2f}s")
            elif load_time < 10.0:
                logger.info(f"Good load performance: {load_time:.2f}s")
            elif load_time < 20.0:
                logger.warning(f"Slow load performance: {load_time:.2f}s")
            else:
                logger.error(f"Very slow load performance: {load_time:.2f}s")
            
            # Don't fail on performance, just log
            assert load_time < 30.0, f"Load time {load_time:.2f}s exceeds maximum acceptable limit"
            
            # Test basic interaction responsiveness
            buttons = page.locator("button")
            if buttons.count() > 0:
                first_button = buttons.first
                if first_button.is_visible() and first_button.is_enabled():
                    interaction_start = time.time()
                    first_button.click()
                    page.wait_for_timeout(100)
                    interaction_time = time.time() - interaction_start
                    
                    logger.info(f"Button interaction time: {interaction_time:.2f}s")
                    assert interaction_time < 3.0, f"Button interaction too slow: {interaction_time:.2f}s"
            
            logger.info("Performance test passed")
            
        except Exception as e:
            logger.error(f"Performance test failed: {e}")
            page.screenshot(path="test_failure_working_performance.png")
            raise
