"""
Usability Tests for Legal Case Management System

This module provides comprehensive usability testing focusing on user experience,
accessibility, and interface design quality. Tests validate that the application
meets professional usability standards.

Author: Legal CMS Team
Version: 1.0.0
"""

import logging
import time
from typing import Optional, Dict, Any, Tuple
from pathlib import Path

import pytest
from playwright.sync_api import Page, <PERSON><PERSON><PERSON>, BrowserContext, sync_playwright

# Configure module logger
logger = logging.getLogger(__name__)

class TestLegalCMSUsability:
    """
    Test suite for Legal Case Management System usability.
    
    This class contains tests that validate the user experience,
    accessibility, and design quality of the application interface.
    """
    
    @pytest.fixture(scope="class")
    def browser_context(self) -> <PERSON><PERSON>[<PERSON><PERSON><PERSON>, BrowserContext, Page]:
        """
        Create browser context for usability testing.
        
        Returns:
            <PERSON><PERSON>[<PERSON><PERSON><PERSON>, BrowserContext, Page]: Browser instances for testing
            
        Raises:
            RuntimeError: If browser fails to start
        """
        try:
            logger.info("Starting browser for usability tests")
            playwright = sync_playwright().start()
            browser = playwright.chromium.launch(
                headless=True,
                slow_mo=100  # Slight delay for more realistic interaction
            )
            context = browser.new_context(
                viewport={'width': 1920, 'height': 1080},
                user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            )
            page = context.new_page()
            
            yield browser, context, page
            
        except Exception as e:
            logger.error(f"Failed to start browser for usability tests: {e}")
            raise RuntimeError(f"Browser initialization failed: {e}")
        finally:
            try:
                context.close()
                browser.close()
                playwright.stop()
                logger.info("Usability test browser cleanup completed")
            except Exception as e:
                logger.warning(f"Browser cleanup warning: {e}")
    
    def test_page_load_performance(self, browser_context: Tuple[Browser, BrowserContext, Page]) -> None:
        """
        Test page load performance meets usability standards.
        
        Validates:
        - Initial page load time is acceptable (< 10 seconds)
        - Time to interactive is reasonable
        - No blocking resources cause delays
        - Performance metrics are within acceptable ranges
        
        Args:
            browser_context: Browser context fixture
            
        Raises:
            AssertionError: If performance metrics exceed acceptable limits
        """
        browser, context, page = browser_context
        
        try:
            logger.info("Testing page load performance")
            
            # Measure initial load time
            start_time = time.time()
            page.goto("http://localhost:8501", wait_until="networkidle")
            load_time = time.time() - start_time
            
            # Performance assertions
            assert load_time < 10.0, f"Page load time {load_time:.2f}s exceeds 10s usability limit"
            
            if load_time < 3.0:
                logger.info(f"Excellent load performance: {load_time:.2f}s")
            elif load_time < 6.0:
                logger.info(f"Good load performance: {load_time:.2f}s")
            else:
                logger.warning(f"Slow load performance: {load_time:.2f}s")
            
            # Test time to interactive (basic check)
            main_heading = page.locator("h1").first
            interactive_time = time.time()
            main_heading.wait_for(state="visible", timeout=5000)
            time_to_interactive = interactive_time - start_time
            
            assert time_to_interactive < 8.0, f"Time to interactive {time_to_interactive:.2f}s too slow"
            logger.info(f"Time to interactive: {time_to_interactive:.2f}s")
            
        except Exception as e:
            logger.error(f"Page load performance test failed: {e}")
            page.screenshot(path="test_failure_performance.png")
            raise
    
    def test_visual_hierarchy_and_layout(self, browser_context: Tuple[Browser, BrowserContext, Page]) -> None:
        """
        Test visual hierarchy and layout quality.
        
        Validates:
        - Main title has appropriate prominence
        - Section headings are properly sized
        - Content has adequate spacing
        - Visual hierarchy is clear and logical
        
        Args:
            browser_context: Browser context fixture
        """
        browser, context, page = browser_context
        
        try:
            logger.info("Testing visual hierarchy and layout")
            page.goto("http://localhost:8501", wait_until="networkidle")
            
            # Test main title prominence
            main_title = page.locator("h1").first
            if main_title.is_visible():
                title_styles = main_title.evaluate("""
                    element => {
                        const styles = getComputedStyle(element);
                        return {
                            fontSize: parseFloat(styles.fontSize),
                            fontWeight: styles.fontWeight,
                            marginBottom: parseFloat(styles.marginBottom)
                        };
                    }
                """)
                
                # Title should be prominently sized
                assert title_styles['fontSize'] >= 20, \
                    f"Main title font size {title_styles['fontSize']}px too small for prominence"
                
                logger.debug(f"Main title font size: {title_styles['fontSize']}px")
            
            # Test section headings
            section_headings = page.locator("h2, h3")
            heading_count = section_headings.count()
            
            if heading_count > 0:
                logger.debug(f"Found {heading_count} section headings")
                
                # Check first few headings for proper sizing
                for i in range(min(3, heading_count)):
                    heading = section_headings.nth(i)
                    if heading.is_visible():
                        font_size = heading.evaluate("element => parseFloat(getComputedStyle(element).fontSize)")
                        assert font_size >= 16, f"Section heading {i} font size {font_size}px too small"
            
            logger.info("Visual hierarchy and layout test passed")
            
        except Exception as e:
            logger.error(f"Visual hierarchy test failed: {e}")
            page.screenshot(path="test_failure_visual_hierarchy.png")
            raise
    
    def test_button_usability_standards(self, browser_context: Tuple[Browser, BrowserContext, Page]) -> None:
        """
        Test button design meets usability standards.
        
        Validates:
        - Buttons are adequately sized for interaction
        - Hover states provide visual feedback
        - Button text is readable and descriptive
        - Touch targets meet accessibility guidelines
        
        Args:
            browser_context: Browser context fixture
        """
        browser, context, page = browser_context
        
        try:
            logger.info("Testing button usability standards")
            page.goto("http://localhost:8501", wait_until="networkidle")
            
            # Find all buttons
            buttons = page.locator("button")
            button_count = buttons.count()
            
            if button_count > 0:
                logger.debug(f"Found {button_count} buttons to test")
                
                # Test first few buttons for usability standards
                for i in range(min(5, button_count)):
                    button = buttons.nth(i)
                    
                    if button.is_visible() and button.is_enabled():
                        # Check button sizing
                        box = button.bounding_box()
                        if box:
                            # Minimum touch target size (WCAG guidelines)
                            assert box['height'] >= 32, \
                                f"Button {i} height {box['height']}px below 32px minimum"
                            assert box['width'] >= 32, \
                                f"Button {i} width {box['width']}px below 32px minimum"
                            
                            logger.debug(f"Button {i} size: {box['width']}x{box['height']}px")
                        
                        # Test hover state
                        button.hover()
                        page.wait_for_timeout(200)
                        
                        # Check cursor changes to pointer
                        cursor = button.evaluate("element => getComputedStyle(element).cursor")
                        assert cursor == "pointer", f"Button {i} should show pointer cursor on hover"
                        
                        # Check button has descriptive text
                        button_text = button.inner_text().strip()
                        if button_text:
                            assert len(button_text) > 0, f"Button {i} should have descriptive text"
                            logger.debug(f"Button {i} text: '{button_text}'")
            
            logger.info("Button usability standards test passed")
            
        except Exception as e:
            logger.error(f"Button usability test failed: {e}")
            page.screenshot(path="test_failure_button_usability.png")
            raise
    
    def test_content_readability(self, browser_context: Tuple[Browser, BrowserContext, Page]) -> None:
        """
        Test content readability and typography.
        
        Validates:
        - Text size meets readability standards
        - Line spacing is adequate
        - Content is properly structured
        - Text contrast is sufficient (basic check)
        
        Args:
            browser_context: Browser context fixture
        """
        browser, context, page = browser_context
        
        try:
            logger.info("Testing content readability")
            page.goto("http://localhost:8501", wait_until="networkidle")
            
            # Test main content areas
            content_areas = page.locator("p, div, span").filter(has_text=True)
            content_count = content_areas.count()
            
            if content_count > 0:
                # Test first few content elements
                for i in range(min(5, content_count)):
                    content = content_areas.nth(i)
                    
                    if content.is_visible():
                        # Check font size for readability
                        font_size = content.evaluate("element => parseFloat(getComputedStyle(element).fontSize)")
                        
                        # Minimum readable font size
                        if font_size > 0:  # Only check if font size is detected
                            assert font_size >= 12, \
                                f"Content {i} font size {font_size}px below 12px minimum for readability"
                            
                            if font_size >= 16:
                                logger.debug(f"Content {i} has excellent readability: {font_size}px")
                            elif font_size >= 14:
                                logger.debug(f"Content {i} has good readability: {font_size}px")
                            else:
                                logger.debug(f"Content {i} has minimal readability: {font_size}px")
            
            # Test heading hierarchy
            headings = page.locator("h1, h2, h3, h4, h5, h6")
            heading_count = headings.count()
            
            if heading_count > 0:
                logger.debug(f"Found {heading_count} headings for structure validation")
                
                # Check for proper heading hierarchy (basic)
                h1_count = page.locator("h1").count()
                assert h1_count >= 1, "Page should have at least one H1 heading"
                assert h1_count <= 3, "Page should not have too many H1 headings"
            
            logger.info("Content readability test passed")
            
        except Exception as e:
            logger.error(f"Content readability test failed: {e}")
            page.screenshot(path="test_failure_readability.png")
            raise
    
    def test_interaction_responsiveness(self, browser_context: Tuple[Browser, BrowserContext, Page]) -> None:
        """
        Test UI responsiveness to user interactions.
        
        Validates:
        - Clicks respond within acceptable time
        - Hover effects are immediate
        - Form interactions provide feedback
        - No blocking operations affect UI
        
        Args:
            browser_context: Browser context fixture
        """
        browser, context, page = browser_context
        
        try:
            logger.info("Testing interaction responsiveness")
            page.goto("http://localhost:8501", wait_until="networkidle")
            
            # Test button click responsiveness
            buttons = page.locator("button")
            if buttons.count() > 0:
                first_button = buttons.first
                
                if first_button.is_visible() and first_button.is_enabled():
                    # Measure click response time
                    start_time = time.time()
                    first_button.click()
                    page.wait_for_timeout(100)  # Small delay for UI update
                    response_time = time.time() - start_time
                    
                    # UI should respond quickly
                    assert response_time < 1.0, \
                        f"Button click response time {response_time:.2f}s exceeds 1s limit"
                    
                    logger.debug(f"Button click response time: {response_time:.2f}s")
            
            # Test tab switching responsiveness
            tabs = page.locator("[role='tab']")
            if tabs.count() > 1:
                second_tab = tabs.nth(1)
                
                if second_tab.is_visible():
                    start_time = time.time()
                    second_tab.click()
                    page.wait_for_timeout(200)  # Allow for tab content loading
                    tab_response_time = time.time() - start_time
                    
                    assert tab_response_time < 2.0, \
                        f"Tab switch response time {tab_response_time:.2f}s too slow"
                    
                    logger.debug(f"Tab switch response time: {tab_response_time:.2f}s")
            
            logger.info("Interaction responsiveness test passed")
            
        except Exception as e:
            logger.error(f"Interaction responsiveness test failed: {e}")
            page.screenshot(path="test_failure_responsiveness.png")
            raise
    
    def test_mobile_usability(self, browser_context: Tuple[Browser, BrowserContext, Page]) -> None:
        """
        Test mobile device usability.
        
        Validates:
        - Touch targets are adequately sized
        - Content fits mobile viewport
        - No horizontal scrolling required
        - Key functionality remains accessible
        
        Args:
            browser_context: Browser context fixture
        """
        browser, context, page = browser_context
        
        try:
            logger.info("Testing mobile usability")
            page.goto("http://localhost:8501", wait_until="networkidle")
            
            # Set mobile viewport
            page.set_viewport_size({'width': 375, 'height': 667})
            page.wait_for_timeout(1000)
            
            # Check main content is accessible
            main_content = page.locator("main, [data-testid='stAppViewContainer']")
            if main_content.count() > 0:
                assert main_content.first.is_visible(), "Main content should be visible on mobile"
            
            # Check touch targets are adequate size
            buttons = page.locator("button")
            if buttons.count() > 0:
                for i in range(min(3, buttons.count())):
                    button = buttons.nth(i)
                    if button.is_visible():
                        box = button.bounding_box()
                        if box:
                            # Touch targets should be at least 44px for mobile (Apple guidelines)
                            assert box['height'] >= 32, \
                                f"Mobile touch target {i} height {box['height']}px below 32px minimum"
                            assert box['width'] >= 32, \
                                f"Mobile touch target {i} width {box['width']}px below 32px minimum"
            
            # Test horizontal scrolling
            body_width = page.evaluate("document.body.scrollWidth")
            viewport_width = 375
            
            # Allow small tolerance for scrollbars
            assert body_width <= viewport_width + 20, \
                f"Mobile horizontal scrolling detected: {body_width}px > {viewport_width}px"
            
            logger.info("Mobile usability test passed")
            
        except Exception as e:
            logger.error(f"Mobile usability test failed: {e}")
            page.screenshot(path="test_failure_mobile_usability.png")
            raise
