# Legal Case Management System - Test Suite

Comprehensive Playwright-based testing for functionality and usability of the Legal Case Management System.

## 🎯 Test Coverage

### Functionality Tests (`test_app_playwright.py`)
- ✅ **Application Loading** - Ensures app loads without errors
- ✅ **Security Status** - Validates security indicators (SSL, PGP, Vault)
- ✅ **Case Selection** - Tests case dropdown and creation functionality
- ✅ **Tab Navigation** - Validates all main tabs are accessible
- ✅ **Document Upload** - Tests file upload interface and processing
- ✅ **AI Model Selection** - Validates model dropdown functionality
- ✅ **End-to-End Workflows** - Complete document processing workflows
- ✅ **Accessibility Compliance** - Basic accessibility checks

### Usability Tests (`test_usability_playwright.py`)
- 🎨 **Visual Design** - Layout, hierarchy, and visual elements
- 🎨 **Color Contrast** - Readability and accessibility standards
- 🎨 **Button Design** - Usability standards for interactive elements
- 🎨 **Form Usability** - Input field design and validation feedback
- 🧭 **Navigation** - Intuitiveness and clarity of navigation
- 📱 **Mobile/Tablet** - Responsive design across devices
- ⚡ **Performance** - Load times and interaction responsiveness
- 🔄 **Feedback Systems** - Status indicators and error handling

## 🚀 Quick Start

### 1. Install Dependencies
```bash
cd tests
python run_tests.py --install-deps
```

### 2. Run All Tests
```bash
python run_tests.py --all
```

### 3. Run Specific Test Types
```bash
# Functionality only
python run_tests.py --functionality

# Usability only  
python run_tests.py --usability

# Accessibility only
python run_tests.py --accessibility

# Quick tests (exclude slow ones)
python run_tests.py --quick
```

## 📊 Test Reports

Test reports are generated in the `reports/` directory:
- `complete_test_report.html` - Full test suite results
- `functionality_report.html` - Functionality tests only
- `usability_report.html` - Usability tests only
- `accessibility_report.html` - Accessibility tests only

## 🔧 Manual Test Execution

### Using pytest directly:
```bash
# Run all tests
pytest test_app_playwright.py test_usability_playwright.py -v

# Run with HTML report
pytest test_app_playwright.py -v --html=reports/test_report.html --self-contained-html

# Run specific markers
pytest -m "functionality" -v
pytest -m "usability" -v
pytest -m "accessibility" -v

# Run excluding slow tests
pytest -m "not slow" -v
```

## 🎭 Test Categories

### Functionality Tests
- **Purpose**: Verify features work as intended
- **Focus**: Core functionality, data processing, integrations
- **Examples**: File upload, case creation, AI model selection

### Usability Tests  
- **Purpose**: Evaluate user experience and interface design
- **Focus**: Visual design, navigation, responsiveness
- **Examples**: Button sizing, color contrast, mobile layout

### Performance Tests
- **Purpose**: Validate system performance and responsiveness
- **Focus**: Load times, interaction speed, resource usage
- **Examples**: Page load speed, button response time

### Accessibility Tests
- **Purpose**: Ensure compliance with accessibility standards
- **Focus**: Screen readers, keyboard navigation, color contrast
- **Examples**: Alt text, focus indicators, ARIA labels

## 📱 Device Testing

Tests are run across multiple viewport sizes:
- **Desktop**: 1920x1080
- **Laptop**: 1366x768  
- **Tablet**: 768x1024
- **Mobile**: 375x667

## 🛠️ Test Configuration

### Browser Settings
- **Browser**: Chromium (via Playwright)
- **Mode**: Headless for CI, headed for development
- **Speed**: Slow motion enabled for usability observation
- **Video**: Recording enabled for usability analysis

### Test Data
- Sample legal documents created automatically
- CSV test data for document processing
- Temporary files cleaned up after tests

## 📋 Test Checklist

### Before Running Tests
- [ ] Streamlit app is running on localhost:8501
- [ ] All dependencies installed
- [ ] Test data directory accessible
- [ ] Reports directory created

### After Running Tests
- [ ] Check test reports in `reports/` directory
- [ ] Review any failed tests
- [ ] Verify usability issues identified
- [ ] Update documentation if needed

## 🐛 Troubleshooting

### Common Issues

**App not starting:**
```bash
# Start manually
streamlit run ../secure_app.py --server.port 8501
```

**Playwright browser not found:**
```bash
playwright install chromium
```

**Permission errors:**
```bash
chmod +x run_tests.py
```

**Port already in use:**
```bash
# Kill existing process
lsof -ti:8501 | xargs kill -9
```

## 📈 Test Metrics

### Success Criteria
- **Load Time**: < 10 seconds
- **Button Response**: < 1 second
- **Mobile Touch Targets**: ≥ 44px
- **Font Size**: ≥ 14px for body text
- **Color Contrast**: ≥ 4.5:1 ratio

### Coverage Goals
- **Functionality**: 100% of core features
- **Usability**: All major user interactions
- **Accessibility**: WCAG 2.1 AA compliance
- **Performance**: Key user journeys

## 🔄 Continuous Integration

### GitHub Actions Integration
```yaml
- name: Run Tests
  run: |
    cd tests
    python run_tests.py --install-deps
    python run_tests.py --all
```

### Test Automation
- Tests run on every commit
- Reports uploaded as artifacts
- Failures block deployment
- Performance regression detection

## 📚 Additional Resources

- [Playwright Documentation](https://playwright.dev/python/)
- [pytest Documentation](https://docs.pytest.org/)
- [WCAG 2.1 Guidelines](https://www.w3.org/WAI/WCAG21/quickref/)
- [Usability Testing Best Practices](https://www.nngroup.com/articles/usability-testing-101/)
