"""
Robust Functionality Tests for Legal Case Management System

This module provides adaptive functionality testing that inspects the actual
page structure and adapts to the real implementation rather than making
assumptions about element selectors.

Author: Legal CMS Team
Version: 1.0.0
"""

import logging
import time
from typing import Optional, Dict, Any, List
from pathlib import Path

import pytest
from playwright.sync_api import Page, <PERSON><PERSON><PERSON>, BrowserContext, sync_playwright

# Configure module logger
logger = logging.getLogger(__name__)

class TestRobustLegalCMSFunctionality:
    """
    Robust test suite that adapts to the actual page structure.
    
    This class contains tests that first inspect what's actually on the page
    and then validate functionality based on the real implementation.
    """
    
    @pytest.fixture(scope="class")
    def browser_context(self) -> tuple[<PERSON><PERSON><PERSON>, <PERSON>rowserContext, Page]:
        """
        Create browser context for testing.
        
        Returns:
            tuple: Browser, context, and page instances
        """
        try:
            logger.info("Starting browser for robust functionality tests")
            playwright = sync_playwright().start()
            browser = playwright.chromium.launch(headless=True)
            context = browser.new_context(viewport={'width': 1920, 'height': 1080})
            page = context.new_page()
            
            yield browser, context, page
            
        except Exception as e:
            logger.error(f"Failed to start browser: {e}")
            raise RuntimeError(f"Browser initialization failed: {e}")
        finally:
            try:
                context.close()
                browser.close()
                playwright.stop()
                logger.info("Browser cleanup completed")
            except Exception as e:
                logger.warning(f"Browser cleanup warning: {e}")
    
    def test_application_loads_and_has_content(self, browser_context: tuple[Browser, BrowserContext, Page]) -> None:
        """
        Test that the application loads and has meaningful content.
        
        This test is adaptive - it checks for content rather than specific elements.
        """
        browser, context, page = browser_context
        
        try:
            logger.info("Testing application load with content validation")
            start_time = time.time()
            
            # Navigate to application
            page.goto("http://localhost:8501", wait_until="networkidle")
            load_time = time.time() - start_time
            
            # Basic load time check
            assert load_time < 20.0, f"Application load time {load_time:.2f}s too slow"
            logger.info(f"Application loaded in {load_time:.2f}s")
            
            # Check page has substantial content
            body_text = page.locator("body").inner_text()
            assert len(body_text) > 200, f"Page has minimal content: {len(body_text)} characters"
            
            # Look for legal/case management related content
            legal_keywords = ["legal", "case", "management", "court", "document", "filing", "shepov"]
            found_keywords = [keyword for keyword in legal_keywords if keyword.lower() in body_text.lower()]
            
            assert len(found_keywords) >= 2, f"Page doesn't appear to be legal CMS. Found keywords: {found_keywords}"
            logger.info(f"Found legal CMS keywords: {found_keywords}")
            
            # Check for interactive elements
            buttons = page.locator("button")
            button_count = buttons.count()
            logger.info(f"Found {button_count} interactive buttons")
            
            # Should have some interactive elements
            assert button_count > 0, "Page should have interactive elements"
            
        except Exception as e:
            logger.error(f"Application load test failed: {e}")
            page.screenshot(path="test_failure_robust_load.png")
            raise
    
    def test_page_structure_and_navigation(self, browser_context: tuple[Browser, BrowserContext, Page]) -> None:
        """
        Test page structure and navigation elements.
        
        Adapts to whatever navigation structure is actually present.
        """
        browser, context, page = browser_context
        
        try:
            logger.info("Testing page structure and navigation")
            page.goto("http://localhost:8501", wait_until="networkidle")
            
            # Look for any kind of navigation elements
            nav_selectors = [
                "[role='tab']",
                "[role='tablist'] button",
                ".stTabs button",
                "button[data-baseweb='tab']",
                "button:has-text('Analysis')",
                "button:has-text('Document')",
                "button:has-text('Filing')",
                "button:has-text('Timeline')",
                "button:has-text('Export')",
                "button:has-text('Monitor')"
            ]
            
            found_nav_elements = []
            for selector in nav_selectors:
                elements = page.locator(selector)
                count = elements.count()
                if count > 0:
                    found_nav_elements.append(f"{selector}: {count} elements")
                    logger.debug(f"Found navigation elements: {selector} ({count})")
            
            # Should find some navigation elements
            assert len(found_nav_elements) > 0, f"No navigation elements found. Tried: {nav_selectors}"
            logger.info(f"Found navigation elements: {found_nav_elements}")
            
            # Test clicking on first available navigation element
            for selector in nav_selectors:
                elements = page.locator(selector)
                if elements.count() > 0:
                    first_element = elements.first
                    if first_element.is_visible() and first_element.is_enabled():
                        logger.info(f"Testing click on navigation element: {selector}")
                        first_element.click()
                        page.wait_for_timeout(1000)
                        break
            
        except Exception as e:
            logger.error(f"Page structure test failed: {e}")
            page.screenshot(path="test_failure_robust_navigation.png")
            raise
    
    def test_document_related_functionality(self, browser_context: tuple[Browser, BrowserContext, Page]) -> None:
        """
        Test document-related functionality by looking for document-related elements.
        """
        browser, context, page = browser_context
        
        try:
            logger.info("Testing document-related functionality")
            page.goto("http://localhost:8501", wait_until="networkidle")
            
            # Look for document-related text and elements
            document_keywords = ["upload", "document", "file", "pdf", "docx", "browse"]
            page_text = page.locator("body").inner_text().lower()
            
            found_doc_keywords = [kw for kw in document_keywords if kw in page_text]
            logger.info(f"Found document-related keywords: {found_doc_keywords}")
            
            # Look for file upload elements
            file_inputs = page.locator("input[type='file']")
            file_input_count = file_inputs.count()
            logger.info(f"Found {file_input_count} file input elements")
            
            # Look for upload-related buttons
            upload_buttons = page.locator("button:has-text('Upload'), button:has-text('Browse'), button:has-text('Choose')")
            upload_button_count = upload_buttons.count()
            logger.info(f"Found {upload_button_count} upload-related buttons")
            
            # Should have some document functionality
            has_doc_functionality = (
                len(found_doc_keywords) >= 2 or 
                file_input_count > 0 or 
                upload_button_count > 0
            )
            
            assert has_doc_functionality, "No document-related functionality found"
            logger.info("Document functionality validation passed")
            
        except Exception as e:
            logger.error(f"Document functionality test failed: {e}")
            page.screenshot(path="test_failure_robust_documents.png")
            raise
    
    def test_security_and_status_indicators(self, browser_context: tuple[Browser, BrowserContext, Page]) -> None:
        """
        Test for security and status indicators.
        """
        browser, context, page = browser_context
        
        try:
            logger.info("Testing security and status indicators")
            page.goto("http://localhost:8501", wait_until="networkidle")
            
            # Look for security-related text
            security_keywords = ["ssl", "tls", "pgp", "encrypt", "vault", "secure", "security"]
            page_text = page.locator("body").inner_text().lower()
            
            found_security_keywords = [kw for kw in security_keywords if kw in page_text]
            logger.info(f"Found security keywords: {found_security_keywords}")
            
            # Look for status indicators (emojis or badges)
            status_indicators = page.locator("text=/🔐|🔑|🏛️|🚫|✅|❌/")
            status_count = status_indicators.count()
            logger.info(f"Found {status_count} status indicator emojis")
            
            # Should have some security indicators
            has_security_indicators = len(found_security_keywords) >= 2 or status_count >= 2
            
            if has_security_indicators:
                logger.info("Security indicators found - system appears to have security features")
            else:
                logger.warning("Limited security indicators found")
                # Don't fail the test, just log the observation
            
        except Exception as e:
            logger.error(f"Security indicators test failed: {e}")
            page.screenshot(path="test_failure_robust_security.png")
            raise
    
    def test_responsive_behavior(self, browser_context: tuple[Browser, BrowserContext, Page]) -> None:
        """
        Test responsive behavior across different viewport sizes.
        """
        browser, context, page = browser_context
        
        viewports = [
            {'width': 1920, 'height': 1080, 'name': 'Desktop'},
            {'width': 768, 'height': 1024, 'name': 'Tablet'},
            {'width': 375, 'height': 667, 'name': 'Mobile'}
        ]
        
        try:
            logger.info("Testing responsive behavior")
            page.goto("http://localhost:8501", wait_until="networkidle")
            
            for viewport in viewports:
                logger.info(f"Testing {viewport['name']} viewport: {viewport['width']}x{viewport['height']}")
                
                # Set viewport size
                page.set_viewport_size({'width': viewport['width'], 'height': viewport['height']})
                page.wait_for_timeout(1000)
                
                # Check content is still accessible
                body_text = page.locator("body").inner_text()
                assert len(body_text) > 100, f"Content missing on {viewport['name']} viewport"
                
                # Check for horizontal scrolling on smaller screens
                if viewport['width'] <= 768:
                    body_width = page.evaluate("document.body.scrollWidth")
                    viewport_width = viewport['width']
                    
                    if body_width > viewport_width + 50:  # Allow some tolerance
                        logger.warning(f"Potential horizontal scrolling on {viewport['name']}: {body_width}px > {viewport_width}px")
                    else:
                        logger.info(f"No horizontal scrolling on {viewport['name']}")
                
                # Check buttons are still clickable
                buttons = page.locator("button")
                if buttons.count() > 0:
                    first_button = buttons.first
                    if first_button.is_visible():
                        box = first_button.bounding_box()
                        if box and box['height'] < 30:
                            logger.warning(f"Button may be too small on {viewport['name']}: {box['height']}px height")
            
            logger.info("Responsive behavior test completed")
            
        except Exception as e:
            logger.error(f"Responsive behavior test failed: {e}")
            page.screenshot(path="test_failure_robust_responsive.png")
            raise
    
    def test_performance_and_usability_basics(self, browser_context: tuple[Browser, BrowserContext, Page]) -> None:
        """
        Test basic performance and usability metrics.
        """
        browser, context, page = browser_context
        
        try:
            logger.info("Testing performance and usability basics")
            
            # Measure load time
            start_time = time.time()
            page.goto("http://localhost:8501", wait_until="networkidle")
            load_time = time.time() - start_time
            
            # Performance assertions
            if load_time < 5.0:
                logger.info(f"Excellent load performance: {load_time:.2f}s")
            elif load_time < 10.0:
                logger.info(f"Good load performance: {load_time:.2f}s")
            elif load_time < 20.0:
                logger.warning(f"Slow load performance: {load_time:.2f}s")
            else:
                logger.error(f"Very slow load performance: {load_time:.2f}s")
                # Don't fail test, but log concern
            
            # Test interaction responsiveness
            buttons = page.locator("button")
            if buttons.count() > 0:
                first_button = buttons.first
                if first_button.is_visible() and first_button.is_enabled():
                    interaction_start = time.time()
                    first_button.click()
                    page.wait_for_timeout(100)
                    interaction_time = time.time() - interaction_start
                    
                    assert interaction_time < 2.0, f"Button interaction too slow: {interaction_time:.2f}s"
                    logger.info(f"Button interaction time: {interaction_time:.2f}s")
            
            # Check for basic accessibility
            headings = page.locator("h1, h2, h3, h4, h5, h6")
            heading_count = headings.count()
            logger.info(f"Found {heading_count} headings for structure")
            
            # Check for images with alt text (basic accessibility)
            images = page.locator("img")
            image_count = images.count()
            if image_count > 0:
                logger.info(f"Found {image_count} images")
                # Check first few images for alt text
                for i in range(min(3, image_count)):
                    img = images.nth(i)
                    alt_text = img.get_attribute("alt")
                    if alt_text:
                        logger.debug(f"Image {i} has alt text: {alt_text}")
                    else:
                        logger.debug(f"Image {i} missing alt text")
            
            logger.info("Performance and usability basics test completed")
            
        except Exception as e:
            logger.error(f"Performance and usability test failed: {e}")
            page.screenshot(path="test_failure_robust_performance.png")
            raise
