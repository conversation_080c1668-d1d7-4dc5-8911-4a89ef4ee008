#!/usr/bin/env python3
"""
Test script for the conversation logging system
"""

from core.conversation_logger import log_legal_conversation, get_legal_team_summary
from datetime import datetime

def test_conversation_logging():
    """Test the conversation logging functionality"""
    print("🧪 Testing Legal Conversation Logging System")
    print("=" * 50)
    
    # Test logging a conversation
    test_user_message = "What is the status of my case DR-25-403973? I need to know if there are any urgent motions I need to file."
    
    test_ai_response = """Based on your case DR-25-403973, here's the current status:

1. **Urgent Action Required**: There are missing docket entries that suggest tampering
2. **Motion Recommendation**: You should file a Motion to Compel Complete Docket Records
3. **Timeline**: This should be filed within 30 days to preserve your rights
4. **Evidence**: We have documented proof of document submission that aren't appearing in the docket

I recommend immediately consulting with your attorney to file the necessary motions."""
    
    test_context = {
        'current_tab': 'Test Interface',
        'active_features': ['conversation_logging', 'legal_team_access'],
        'system_status': {'test_mode': True},
        'tools_used': ['legal_analysis', 'docket_monitoring'],
        'documents_count': 15
    }
    
    try:
        # Log the test conversation
        print("📝 Logging test conversation...")
        exchange_id = log_legal_conversation(
            user_message=test_user_message,
            ai_response=test_ai_response,
            context=test_context,
            case_number="DR-25-403973",
            session_id="test_session_001"
        )
        
        if exchange_id:
            print(f"✅ Successfully logged conversation with ID: {exchange_id}")
        else:
            print("❌ Failed to log conversation")
            return False
        
        # Test getting legal team summary
        print("\n📊 Testing legal team summary...")
        summary = get_legal_team_summary(days=30, case_number="DR-25-403973")
        
        if 'total_exchanges' in summary:
            print(f"✅ Summary generated successfully:")
            print(f"   - Total exchanges: {summary['total_exchanges']}")
            print(f"   - Session count: {summary.get('session_count', 0)}")
            print(f"   - Legal advice count: {summary.get('legal_advice_count', 0)}")
            print(f"   - Urgent matters: {summary.get('urgent_matters', 0)}")
        else:
            print(f"⚠️ Summary result: {summary}")
        
        print("\n🎉 All tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_conversation_logging()
    exit(0 if success else 1)