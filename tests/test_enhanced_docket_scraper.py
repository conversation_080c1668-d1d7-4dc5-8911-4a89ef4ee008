#!/usr/bin/env python3
"""
Test script to verify the enhanced docket scraper can find 150+ entries
for case DR-25-403973 (<PERSON><PERSON><PERSON><PERSON> vs SHEPOV)
"""

import asyncio
import sys
import os
from pathlib import Path

# Add the core directory to the path
sys.path.append(str(Path(__file__).parent / "core"))

try:
    from efiling_scraper import CuyahogaEFiling<PERSON>craper<PERSON>laywright, PLAYWRIGHT_AVAILABLE
    print("✅ Successfully imported enhanced e-filing scraper")
except ImportError as e:
    print(f"❌ Failed to import e-filing scraper: {e}")
    sys.exit(1)

async def test_enhanced_docket_scraper():
    """Test the enhanced docket scraper with case DR-25-403973"""
    
    if not PLAYWRIGHT_AVAILABLE:
        print("❌ Playwright not available. Install with: pip install playwright && playwright install chromium")
        return False
    
    print("🚀 Testing Enhanced Docket Scraper")
    print("=" * 60)
    
    # Use environment variables for credentials if available
    username = os.getenv('EFILING_USERNAME', 'GSHEPOV')  
    password = os.getenv('EFILING_PASSWORD')
    case_number = "DR-25-403973"
    
    # Try to load credentials from persistent settings
    if not password:
        try:
            import json
            settings_file = Path("efiling_settings/efiling_persistent_settings.json")
            if settings_file.exists():
                with open(settings_file, 'r') as f:
                    settings = json.load(f)
                    username = settings.get('username', username)
                    password = settings.get('password', '***********.aBrA~CADABRA4U')
                    if password:
                        print("✅ Using saved credentials from persistent settings")
        except Exception as e:
            print(f"⚠️ Could not load saved credentials: {e}")
    
    if not password:
        print("❌ No credentials available for automated testing")
        print("To run this test, either:")
        print("1. Set EFILING_PASSWORD environment variable")
        print("2. Save credentials using the E-Filing UI first")
        print("3. Run the main app and use the E-Filing tab to save credentials")
        return False
    
    print(f"🔐 Testing with username: {username}")
    print(f"📂 Testing case: {case_number}")
    print(f"🎯 Goal: Find 150+ docket entries")
    print()
    
    scraper = None
    try:
        # Initialize scraper
        print("🔧 Initializing enhanced scraper...")
        scraper = CuyahogaEFilingScraperPlaywright(username=username, password=password, headless=True)
        
        # Start browser first
        print("🌐 Starting browser...")
        await scraper.start_browser()
        
        # Login
        print("🔐 Logging in...")
        login_success = await scraper.login()
        
        if not login_success:
            print("❌ Login failed!")
            return False
            
        print("✅ Login successful!")
        
        # Extract case query parameter
        case_query = case_number  # Simplified for this test
        
        # Test the full scraping workflow
        print("\n📋 Testing Full E-Filing Scraping Workflow...")
        print("-" * 40)
        
        # Use the full scraping method which includes proper navigation
        results = await scraper.scrape_all_case_data(case_number)
        docket_data = results.get('docket_information', {})
        
        # Analyze results
        if 'error' in docket_data:
            print(f"❌ Docket scraping failed: {docket_data['error']}")
            return False
            
        entries = docket_data.get('entries', [])
        headers = docket_data.get('headers', [])
        
        print(f"📊 Results Summary:")
        print(f"   🔍 URL used: {docket_data.get('url', 'N/A')}")
        print(f"   📋 Headers found: {len(headers)}")
        print(f"   📝 Docket entries found: {len(entries)}")
        print(f"   ⏰ Scraped at: {docket_data.get('scraped_at', 'N/A')}")
        
        if headers:
            print(f"   🏷️  Column headers: {', '.join(headers)}")
        
        print()
        
        # Detailed analysis
        if entries:
            print("📊 Detailed Analysis:")
            print("-" * 20)
            
            # Count entries with images
            entries_with_images = sum(1 for entry in entries if entry.get('Has_Image'))
            entries_with_descriptions = sum(1 for entry in entries if entry.get('Description', '').strip())
            entries_with_dates = sum(1 for entry in entries if entry.get('Filing Date', '').strip())
            
            print(f"   📄 Entries with images: {entries_with_images}")
            print(f"   📝 Entries with descriptions: {entries_with_descriptions}")
            print(f"   📅 Entries with dates: {entries_with_dates}")
            
            # Show sample entries
            print(f"\n📋 First 5 Docket Entries:")
            print("-" * 30)
            
            for i, entry in enumerate(entries[:5]):
                print(f"   Entry #{entry.get('Docket Number', i+1)}:")
                for key, value in entry.items():
                    if key not in ['Docket Number'] and value:
                        print(f"      {key}: {str(value)[:80]}{'...' if len(str(value)) > 80 else ''}")
                print()
            
            if len(entries) > 5:
                print(f"   ... and {len(entries) - 5} more entries")
            
            # Check if we hit our goal
            if len(entries) >= 150:
                print(f"🎉 SUCCESS! Found {len(entries)} entries (goal: 150+)")
                success = True
            elif len(entries) >= 100:
                print(f"✅ GOOD! Found {len(entries)} entries (close to goal of 150+)")
                success = True
            elif len(entries) >= 50:
                print(f"⚠️ PARTIAL! Found {len(entries)} entries (expected 150+)")
                success = False
            else:
                print(f"❌ LOW! Found only {len(entries)} entries (expected 150+)")
                success = False
                
        else:
            print("❌ No docket entries found!")
            success = False
            
        return success
        
    except Exception as e:
        print(f"❌ Test failed with exception: {e}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        if scraper:
            try:
                await scraper.close()
                print("\n🔒 Scraper closed successfully")
            except:
                pass

def main():
    """Main test function"""
    print("Enhanced Docket Scraper Test")
    print("===========================")
    print()
    
    try:
        success = asyncio.run(test_enhanced_docket_scraper())
        
        print("\n" + "=" * 60)
        if success:
            print("🎉 TEST COMPLETED SUCCESSFULLY!")
            print("The enhanced docket scraper is working as expected.")
        else:
            print("❌ TEST FAILED!")
            print("The enhanced docket scraper needs further improvements.")
            
        print("=" * 60)
        
        return 0 if success else 1
        
    except KeyboardInterrupt:
        print("\n⚠️ Test interrupted by user")
        return 1
    except Exception as e:
        print(f"\n❌ Test failed with unexpected error: {e}")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)