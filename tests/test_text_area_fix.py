#!/usr/bin/env python3
"""
Test script to verify text_area key fixes
"""

import re
import sys
from pathlib import Path

def test_text_area_keys():
    """Test that all text_area elements have unique keys"""
    
    print("🧪 Testing Text Area Key Fixes")
    print("=" * 40)
    
    # Read the main application file
    app_file = Path("/home/<USER>/Documents/Source/lcm/secure_app.py")
    
    if not app_file.exists():
        print("❌ secure_app.py not found")
        return False
    
    with open(app_file, 'r') as f:
        content = f.read()
    
    # Find all text_area instances
    text_area_pattern = r'st\.text_area\([^)]+\)'
    text_areas = re.findall(text_area_pattern, content)
    
    print(f"📊 Found {len(text_areas)} text_area instances")
    
    # Check for keys
    with_keys = []
    without_keys = []
    
    for i, ta in enumerate(text_areas):
        if 'key=' in ta:
            with_keys.append(ta)
        else:
            without_keys.append(ta)
    
    print(f"✅ With keys: {len(with_keys)}")
    print(f"❌ Without keys: {len(without_keys)}")
    
    # Extract all key values to check for uniqueness
    key_pattern = r'key=["\'`]([^"\'`]+)["\'`]'
    keys = []
    
    for ta in with_keys:
        key_match = re.search(key_pattern, ta)
        if key_match:
            keys.append(key_match.group(1))
        else:
            # Handle f-string keys
            f_key_pattern = r'key=f["\'`]([^"\'`]+)["\'`]'
            f_key_match = re.search(f_key_pattern, ta)
            if f_key_match:
                keys.append(f_key_match.group(1))
    
    print(f"🔑 Extracted {len(keys)} key values")
    
    # Check for duplicate keys (basic check)
    unique_keys = set(keys)
    if len(keys) == len(unique_keys):
        print("✅ All keys appear to be unique")
    else:
        print(f"⚠️ Potential duplicate keys detected")
        duplicates = [k for k in keys if keys.count(k) > 1]
        print(f"   Duplicates: {set(duplicates)}")
    
    # Show examples of the keys
    print("\n📝 Sample text_area keys:")
    for i, key in enumerate(keys[:5]):
        print(f"   {i+1}. {key}")
    
    if without_keys:
        print("\n❌ Text areas without keys:")
        for ta in without_keys:
            print(f"   {ta[:100]}...")
        return False
    
    print("\n🎉 All text_area elements have unique keys!")
    return True

def test_application_import():
    """Test that the application can be imported without errors"""
    
    print("\n🧪 Testing Application Import")
    print("=" * 40)
    
    try:
        # Clear any cached modules
        modules_to_clear = [mod for mod in sys.modules.keys() if 'secure_app' in mod]
        for mod in modules_to_clear:
            del sys.modules[mod]
        
        # Try to import the main functions
        from secure_app import render_conversation_history, render_database_admin_panel
        print("✅ Main application functions imported successfully")
        
        # Test that functions are callable
        if callable(render_conversation_history):
            print("✅ render_conversation_history is callable")
        
        if callable(render_database_admin_panel):
            print("✅ render_database_admin_panel is callable")
        
        print("🎉 Application import test passed!")
        return True
        
    except Exception as e:
        print(f"❌ Application import failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all tests"""
    print("🔧 Text Area Key Fix Verification")
    print("=" * 50)
    
    test1_passed = test_text_area_keys()
    test2_passed = test_application_import()
    
    print("\n" + "=" * 50)
    if test1_passed and test2_passed:
        print("✅ ALL TESTS PASSED!")
        print("🎯 Text area ID conflicts should be resolved")
        print("🚀 Application ready to run without key errors")
    else:
        print("❌ Some tests failed")
        print("🔧 Additional fixes may be needed")

if __name__ == "__main__":
    main()