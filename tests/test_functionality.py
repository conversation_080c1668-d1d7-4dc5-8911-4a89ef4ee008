"""
Functionality Tests for Legal Case Management System

This module provides comprehensive functionality testing using Playwright.
Tests are designed to validate core features while maintaining type safety
and proper error handling.

Author: Legal CMS Team
Version: 1.0.0
"""

import logging
import time
from typing import Optional, Dict, Any
from pathlib import Path

import pytest
from playwright.sync_api import Page, <PERSON><PERSON><PERSON>, <PERSON><PERSON>erContext, sync_playwright

# Configure module logger
logger = logging.getLogger(__name__)

class TestLegalCMSFunctionality:
    """
    Test suite for Legal Case Management System functionality.
    
    This class contains tests that validate the core functionality
    of the application including navigation, document processing,
    and user interface elements.
    """
    
    @pytest.fixture(scope="class")
    def browser_context(self) -> tuple[<PERSON><PERSON><PERSON>, <PERSON><PERSON>erContext, Page]:
        """
        Create browser context for testing.
        
        Returns:
            tuple: Browser, context, and page instances
            
        Raises:
            RuntimeError: If browser fails to start
        """
        try:
            logger.info("Starting browser for functionality tests")
            playwright = sync_playwright().start()
            browser = playwright.chromium.launch(
                headless=True,
                args=['--disable-web-security']
            )
            context = browser.new_context(
                viewport={'width': 1920, 'height': 1080}
            )
            page = context.new_page()
            
            yield browser, context, page
            
        except Exception as e:
            logger.error(f"Failed to start browser: {e}")
            raise RuntimeError(f"Browser initialization failed: {e}")
        finally:
            try:
                context.close()
                browser.close()
                playwright.stop()
                logger.info("Browser cleanup completed")
            except Exception as e:
                logger.warning(f"Browser cleanup warning: {e}")
    
    def test_application_loads_successfully(self, browser_context: tuple[Browser, BrowserContext, Page]) -> None:
        """
        Test that the Legal CMS application loads without errors.
        
        This test validates:
        - Page loads within acceptable time
        - Main title is present and correct
        - No JavaScript errors on load
        - Basic UI elements are visible
        
        Args:
            browser_context: Browser context fixture
            
        Raises:
            AssertionError: If application fails to load properly
        """
        browser, context, page = browser_context
        
        try:
            logger.info("Testing application load")
            start_time = time.time()
            
            # Navigate to application
            page.goto("http://localhost:8501", wait_until="networkidle")
            load_time = time.time() - start_time
            
            # Validate load time
            assert load_time < 15.0, f"Application load time {load_time:.2f}s exceeds 15s limit"
            logger.info(f"Application loaded in {load_time:.2f}s")
            
            # Check page title (Streamlit apps often have generic titles)
            title = page.title()
            logger.debug(f"Page title: {title}")
            # Accept either the full title or generic Streamlit title
            title_valid = "Legal Case Management System" in title or "Streamlit" in title
            assert title_valid, f"Unexpected page title: {title}"
            
            # Check main heading (wait for it to load)
            main_heading = page.locator("h1").first
            try:
                main_heading.wait_for(state="visible", timeout=10000)
                assert main_heading.is_visible(), "Main heading not visible"

                heading_text = main_heading.inner_text()
                assert "CUYAHOGA COUNTY PRO SE" in heading_text, f"Unexpected heading: {heading_text}"
            except Exception as heading_error:
                # If H1 not found, check for any prominent heading or title
                logger.warning(f"H1 heading not found: {heading_error}")

                # Look for any heading or prominent text
                any_heading = page.locator("h1, h2, h3, [role='heading']").first
                if any_heading.is_visible():
                    heading_text = any_heading.inner_text()
                    logger.info(f"Found alternative heading: {heading_text}")
                else:
                    # Just verify the page has loaded with some content
                    page_content = page.locator("body").inner_text()
                    assert len(page_content) > 100, "Page appears to have minimal content"
                    logger.info("Page loaded with content, but no clear heading structure found")
            
            logger.info("Application load test passed")
            
        except Exception as e:
            logger.error(f"Application load test failed: {e}")
            # Take screenshot for debugging
            page.screenshot(path="test_failure_app_load.png")
            raise
    
    def test_security_indicators_present(self, browser_context: tuple[Browser, BrowserContext, Page]) -> None:
        """
        Test that security status indicators are present and correct.
        
        Validates:
        - SSL/TLS indicator is visible
        - PGP Encryption indicator is present
        - Vault Integration indicator is shown
        - No unencrypted data warnings
        
        Args:
            browser_context: Browser context fixture
        """
        browser, context, page = browser_context
        
        try:
            logger.info("Testing security indicators")
            page.goto("http://localhost:8501", wait_until="networkidle")
            
            # Check SSL/TLS indicator
            ssl_indicator = page.locator("text=🔐 SSL/TLS Active")
            assert ssl_indicator.is_visible(), "SSL/TLS indicator not found"
            
            # Check PGP Encryption indicator
            pgp_indicator = page.locator("text=🔑 PGP Encryption")
            assert pgp_indicator.is_visible(), "PGP Encryption indicator not found"
            
            # Check Vault Integration indicator
            vault_indicator = page.locator("text=🏛️ Vault Integration")
            assert vault_indicator.is_visible(), "Vault Integration indicator not found"
            
            # Check no unencrypted data warning
            no_unencrypted = page.locator("text=🚫 No Unencrypted Data")
            assert no_unencrypted.is_visible(), "No Unencrypted Data indicator not found"
            
            logger.info("Security indicators test passed")
            
        except Exception as e:
            logger.error(f"Security indicators test failed: {e}")
            page.screenshot(path="test_failure_security_indicators.png")
            raise
    
    def test_navigation_tabs_functional(self, browser_context: tuple[Browser, BrowserContext, Page]) -> None:
        """
        Test that all main navigation tabs are functional.
        
        Validates:
        - All expected tabs are present
        - Tabs are clickable and responsive
        - Tab content loads properly
        - Active state is properly indicated
        
        Args:
            browser_context: Browser context fixture
        """
        browser, context, page = browser_context
        
        expected_tabs = [
            "💬 Chat Analysis",
            "🖼️ Document Viewer",
            "⚖️ E-Filing Integration",
            "📊 Case Timeline",
            "📥 Export & Reports",
            "🔧 System Monitor"
        ]
        
        try:
            logger.info("Testing navigation tabs")
            page.goto("http://localhost:8501", wait_until="networkidle")
            
            for tab_name in expected_tabs:
                logger.debug(f"Testing tab: {tab_name}")
                
                # Find and click tab
                tab = page.locator(f"[role='tab']:has-text('{tab_name}')")
                assert tab.is_visible(), f"Tab '{tab_name}' not visible"
                
                # Click tab and wait for content
                tab.click()
                page.wait_for_timeout(1000)  # Allow content to load
                
                # Verify tab is active (implementation may vary)
                # This is a basic check - adjust based on actual implementation
                tab_classes = tab.get_attribute("class") or ""
                aria_selected = tab.get_attribute("aria-selected")
                
                is_active = "selected" in tab_classes.lower() or aria_selected == "true"
                assert is_active, f"Tab '{tab_name}' not properly activated"
                
            logger.info("Navigation tabs test passed")
            
        except Exception as e:
            logger.error(f"Navigation tabs test failed: {e}")
            page.screenshot(path="test_failure_navigation_tabs.png")
            raise
    
    def test_document_upload_interface(self, browser_context: tuple[Browser, BrowserContext, Page]) -> None:
        """
        Test document upload interface functionality.
        
        Validates:
        - Upload interface is accessible
        - Multiple upload methods are available
        - File type restrictions are properly displayed
        - Upload controls are functional
        
        Args:
            browser_context: Browser context fixture
        """
        browser, context, page = browser_context
        
        try:
            logger.info("Testing document upload interface")
            page.goto("http://localhost:8501", wait_until="networkidle")
            
            # Navigate to Document Viewer tab
            doc_tab = page.locator("[role='tab']:has-text('🖼️ Document Viewer')")
            doc_tab.click()
            page.wait_for_timeout(1000)
            
            # Navigate to Upload sub-tab
            upload_tab = page.locator("[role='tab']:has-text('⬆️ Upload')")
            if upload_tab.is_visible():
                upload_tab.click()
                page.wait_for_timeout(1000)
                
                # Check upload method options
                upload_methods = [
                    "📄 Single Document",
                    "📁 Multiple Files",
                    "🗜️ ZIP Archive",
                    "🌐 URL/Link"
                ]
                
                for method in upload_methods:
                    method_option = page.locator(f"text={method}")
                    if method_option.is_visible():
                        logger.debug(f"Upload method '{method}' is available")
                
                # Check file type information is displayed
                file_types = page.locator("text*=PDF, DOCX, DOC, TXT, CSV")
                if file_types.is_visible():
                    logger.debug("File type restrictions are displayed")
                
            logger.info("Document upload interface test passed")
            
        except Exception as e:
            logger.error(f"Document upload interface test failed: {e}")
            page.screenshot(path="test_failure_upload_interface.png")
            raise
    
    def test_case_selection_functionality(self, browser_context: tuple[Browser, BrowserContext, Page]) -> None:
        """
        Test case selection dropdown functionality.
        
        Validates:
        - Case dropdown is present and functional
        - Default case is properly selected
        - Create new case option is available
        - Case information is displayed correctly
        
        Args:
            browser_context: Browser context fixture
        """
        browser, context, page = browser_context
        
        try:
            logger.info("Testing case selection functionality")
            page.goto("http://localhost:8501", wait_until="networkidle")
            
            # Look for case selection elements
            case_info = page.locator("text*=SHEPOV vs SHEPOV")
            if case_info.is_visible():
                logger.debug("Default case information is displayed")
                
                case_text = case_info.inner_text()
                assert "DR-25-403973" in case_text, "Case number not found in case info"
            
            # Look for case dropdown or selection interface
            case_dropdown = page.locator("select, [role='combobox']").first
            if case_dropdown.is_visible():
                logger.debug("Case selection dropdown found")
                
                # Try to interact with dropdown
                case_dropdown.click()
                page.wait_for_timeout(500)
                
                # Look for "Create New Case" option
                create_option = page.locator("text=Create New Case")
                if create_option.is_visible():
                    logger.debug("Create New Case option is available")
            
            logger.info("Case selection functionality test passed")
            
        except Exception as e:
            logger.error(f"Case selection functionality test failed: {e}")
            page.screenshot(path="test_failure_case_selection.png")
            raise
    
    def test_responsive_design_basic(self, browser_context: tuple[Browser, BrowserContext, Page]) -> None:
        """
        Test basic responsive design functionality.
        
        Validates:
        - Application works at different viewport sizes
        - Main content remains accessible
        - No horizontal scrolling on mobile
        - Key elements remain visible
        
        Args:
            browser_context: Browser context fixture
        """
        browser, context, page = browser_context
        
        viewports = [
            {'width': 1920, 'height': 1080, 'name': 'Desktop'},
            {'width': 768, 'height': 1024, 'name': 'Tablet'},
            {'width': 375, 'height': 667, 'name': 'Mobile'}
        ]
        
        try:
            logger.info("Testing responsive design")
            page.goto("http://localhost:8501", wait_until="networkidle")
            
            for viewport in viewports:
                logger.debug(f"Testing {viewport['name']} viewport: {viewport['width']}x{viewport['height']}")
                
                # Set viewport size
                page.set_viewport_size({
                    'width': viewport['width'],
                    'height': viewport['height']
                })
                page.wait_for_timeout(1000)
                
                # Check main heading is still visible
                main_heading = page.locator("h1").first
                assert main_heading.is_visible(), f"Main heading not visible on {viewport['name']}"
                
                # Check for horizontal scrolling (basic check)
                if viewport['width'] <= 768:  # Mobile/Tablet
                    body_width = page.evaluate("document.body.scrollWidth")
                    viewport_width = viewport['width']
                    
                    # Allow small tolerance for scrollbars
                    assert body_width <= viewport_width + 20, \
                        f"Horizontal scrolling detected on {viewport['name']}: {body_width}px > {viewport_width}px"
            
            logger.info("Responsive design test passed")
            
        except Exception as e:
            logger.error(f"Responsive design test failed: {e}")
            page.screenshot(path="test_failure_responsive_design.png")
            raise
