#!/usr/bin/env python3
"""
Simple Test Runner for Legal Case Management System

This script provides a straightforward way to run functionality and usability tests
with proper error handling and logging. Designed for production use with clear
output and comprehensive reporting.

Author: Legal CMS Team
Version: 1.0.0
"""

import subprocess
import sys
import argparse
import logging
from pathlib import Path
import time
from typing import List, Optional, Dict, Any

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('test_runner.log')
    ]
)
logger = logging.getLogger(__name__)

class TestRunner:
    """
    Test runner for Legal Case Management System.
    
    Provides methods to run different types of tests with proper error handling,
    logging, and reporting capabilities.
    """
    
    def __init__(self, test_dir: Path = None) -> None:
        """
        Initialize test runner.
        
        Args:
            test_dir: Directory containing test files
        """
        self.test_dir = test_dir or Path(__file__).parent
        self.reports_dir = self.test_dir / "reports"
        self.reports_dir.mkdir(exist_ok=True)
        
        logger.info(f"Test runner initialized with test directory: {self.test_dir}")
    
    def run_command(self, command: List[str], description: str, timeout: int = 300) -> bool:
        """
        Run a command with proper error handling and logging.
        
        Args:
            command: Command to execute as list of strings
            description: Human-readable description of the command
            timeout: Command timeout in seconds
            
        Returns:
            bool: True if command succeeded, False otherwise
        """
        logger.info(f"Starting: {description}")
        logger.debug(f"Command: {' '.join(command)}")
        
        try:
            start_time = time.time()
            result = subprocess.run(
                command,
                check=True,
                capture_output=True,
                text=True,
                timeout=timeout,
                cwd=self.test_dir
            )
            
            execution_time = time.time() - start_time
            logger.info(f"✅ {description} completed successfully in {execution_time:.2f}s")
            
            if result.stdout:
                logger.debug(f"Output: {result.stdout}")
            
            return True
            
        except subprocess.CalledProcessError as e:
            logger.error(f"❌ {description} failed with return code {e.returncode}")
            if e.stderr:
                logger.error(f"Error output: {e.stderr}")
            if e.stdout:
                logger.debug(f"Standard output: {e.stdout}")
            return False
            
        except subprocess.TimeoutExpired:
            logger.error(f"❌ {description} timed out after {timeout}s")
            return False
            
        except Exception as e:
            logger.error(f"❌ {description} failed with unexpected error: {e}")
            return False
    
    def check_dependencies(self) -> bool:
        """
        Check if required dependencies are installed.
        
        Returns:
            bool: True if all dependencies are available
        """
        logger.info("Checking test dependencies")
        
        required_packages = ["pytest", "playwright"]
        missing_packages = []
        
        for package in required_packages:
            try:
                __import__(package)
                logger.debug(f"✅ {package} is available")
            except ImportError:
                missing_packages.append(package)
                logger.warning(f"❌ {package} is not available")
        
        if missing_packages:
            logger.error(f"Missing required packages: {', '.join(missing_packages)}")
            logger.info("Install missing packages with: pip install pytest playwright")
            return False
        
        logger.info("All dependencies are available")
        return True
    
    def run_functionality_tests(self) -> bool:
        """
        Run functionality tests.

        Returns:
            bool: True if tests passed
        """
        command = [
            sys.executable, "-m", "pytest",
            "test_working_functionality.py",
            "-v",
            "--tb=short",
            f"--html={self.reports_dir}/functionality_report.html",
            "--self-contained-html"
        ]

        return self.run_command(command, "Functionality tests")
    
    def run_usability_tests(self) -> bool:
        """
        Run usability tests.
        
        Returns:
            bool: True if tests passed
        """
        command = [
            sys.executable, "-m", "pytest",
            "test_usability.py",
            "-v",
            "--tb=short",
            f"--html={self.reports_dir}/usability_report.html",
            "--self-contained-html"
        ]
        
        return self.run_command(command, "Usability tests")
    
    def run_all_tests(self) -> bool:
        """
        Run all tests.

        Returns:
            bool: True if all tests passed
        """
        command = [
            sys.executable, "-m", "pytest",
            "test_working_functionality.py",
            "test_usability.py",
            "-v",
            "--tb=short",
            f"--html={self.reports_dir}/complete_report.html",
            "--self-contained-html"
        ]

        return self.run_command(command, "All tests", timeout=600)
    
    def run_quick_tests(self) -> bool:
        """
        Run quick subset of tests for rapid feedback.

        Returns:
            bool: True if tests passed
        """
        command = [
            sys.executable, "-m", "pytest",
            "test_working_functionality.py::TestWorkingLegalCMSFunctionality::test_application_loads_successfully",
            "test_working_functionality.py::TestWorkingLegalCMSFunctionality::test_security_indicators_present",
            "test_working_functionality.py::TestWorkingLegalCMSFunctionality::test_performance_basic",
            "-v",
            "--tb=short",
            f"--html={self.reports_dir}/quick_report.html",
            "--self-contained-html"
        ]

        return self.run_command(command, "Quick tests")
    
    def generate_summary_report(self, test_results: Dict[str, bool]) -> None:
        """
        Generate a summary report of test results.
        
        Args:
            test_results: Dictionary of test names and their results
        """
        logger.info("Generating test summary report")
        
        total_tests = len(test_results)
        passed_tests = sum(1 for result in test_results.values() if result)
        failed_tests = total_tests - passed_tests
        
        success_rate = (passed_tests / total_tests * 100) if total_tests > 0 else 0
        
        summary = f"""
Legal Case Management System - Test Summary
==========================================

Total Test Suites: {total_tests}
Passed: {passed_tests}
Failed: {failed_tests}
Success Rate: {success_rate:.1f}%

Test Results:
"""
        
        for test_name, result in test_results.items():
            status = "✅ PASSED" if result else "❌ FAILED"
            summary += f"  {test_name}: {status}\n"
        
        summary += f"""
Reports Location: {self.reports_dir.absolute()}
Log File: test_runner.log

For detailed results, check the HTML reports in the reports directory.
"""
        
        print(summary)
        logger.info(summary)
        
        # Save summary to file
        summary_file = self.reports_dir / "test_summary.txt"
        with open(summary_file, 'w') as f:
            f.write(summary)
        
        logger.info(f"Summary report saved to: {summary_file}")

def main() -> int:
    """
    Main entry point for test runner.
    
    Returns:
        int: Exit code (0 for success, 1 for failure)
    """
    parser = argparse.ArgumentParser(
        description="Legal Case Management System Test Runner",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python run_simple_tests.py --all          # Run all tests
  python run_simple_tests.py --functionality # Run functionality tests only
  python run_simple_tests.py --usability    # Run usability tests only
  python run_simple_tests.py --quick        # Run quick smoke tests
        """
    )
    
    parser.add_argument("--functionality", action="store_true", 
                       help="Run functionality tests only")
    parser.add_argument("--usability", action="store_true", 
                       help="Run usability tests only")
    parser.add_argument("--quick", action="store_true", 
                       help="Run quick smoke tests")
    parser.add_argument("--all", action="store_true", 
                       help="Run all tests (default)")
    parser.add_argument("--check-deps", action="store_true", 
                       help="Check dependencies only")
    
    args = parser.parse_args()
    
    # Default to all tests if no specific option is provided
    if not any([args.functionality, args.usability, args.quick, args.check_deps]):
        args.all = True
    
    print("⚖️ Legal Case Management System - Test Runner")
    print("=" * 60)
    
    runner = TestRunner()
    
    # Check dependencies first
    if not runner.check_dependencies():
        logger.error("Dependency check failed. Please install required packages.")
        return 1
    
    if args.check_deps:
        print("✅ All dependencies are available")
        return 0
    
    # Run requested tests
    test_results = {}
    
    try:
        if args.functionality:
            test_results["Functionality Tests"] = runner.run_functionality_tests()
        elif args.usability:
            test_results["Usability Tests"] = runner.run_usability_tests()
        elif args.quick:
            test_results["Quick Tests"] = runner.run_quick_tests()
        elif args.all:
            test_results["Functionality Tests"] = runner.run_functionality_tests()
            test_results["Usability Tests"] = runner.run_usability_tests()
        
        # Generate summary report
        runner.generate_summary_report(test_results)
        
        # Determine exit code
        all_passed = all(test_results.values())
        return 0 if all_passed else 1
        
    except KeyboardInterrupt:
        logger.warning("Test execution interrupted by user")
        return 1
    except Exception as e:
        logger.error(f"Unexpected error during test execution: {e}")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
