"""
PGP Encryption Manager for Legal Case Management System
Provides military-grade document encryption using PGP with key management integration.
"""

import os
import io
import json
import hashlib
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Union
from pathlib import Path
import tempfile

# PGP imports
try:
    import gnupg
    PGP_AVAILABLE = True
except ImportError:
    PGP_AVAILABLE = False
    logging.warning("python-gnupg not available. Install with: pip install python-gnupg")

# Cryptography imports for hybrid encryption
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes, serialization
from cryptography.hazmat.primitives.asymmetric import rsa, padding
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
from cryptography.hazmat.backends import default_backend
import secrets

# Vault integration
try:
    import hvac
    VAULT_AVAILABLE = True
except ImportError:
    VAULT_AVAILABLE = False
    logging.warning("hvac not available. Install with: pip install hvac")

logger = logging.getLogger(__name__)

class PGPKeyManager:
    """Manages PGP keys with Vault integration"""
    
    def __init__(self, vault_client=None, gnupg_home: str = None):
        self.vault_client = vault_client
        self.gnupg_home = gnupg_home or os.path.expanduser("~/.gnupg")
        
        if PGP_AVAILABLE:
            self.gpg = gnupg.GPG(gnupghome=self.gnupg_home)
        else:
            self.gpg = None
            logger.error("PGP functionality disabled - python-gnupg not available")
    
    def generate_master_key(self, name: str, email: str, passphrase: str) -> Dict:
        """Generate master PGP key pair (4096-bit RSA)"""
        if not self.gpg:
            raise RuntimeError("PGP not available")
        
        input_data = self.gpg.gen_key_input(
            key_type="RSA",
            key_length=4096,
            name_real=name,
            name_email=email,
            passphrase=passphrase,
            expire_date='2y'  # 2 years expiry
        )
        
        logger.info(f"Generating master PGP key for {email}...")
        key = self.gpg.gen_key(input_data)
        
        if not key:
            raise RuntimeError("Failed to generate PGP key")
        
        # Export public and private keys
        public_key = self.gpg.export_keys(key.fingerprint)
        private_key = self.gpg.export_keys(key.fingerprint, secret=True, passphrase=passphrase)
        
        key_info = {
            'fingerprint': key.fingerprint,
            'key_id': str(key),
            'public_key': public_key,
            'private_key': private_key,
            'created_at': datetime.now().isoformat(),
            'expires_at': (datetime.now() + timedelta(days=730)).isoformat(),
            'key_type': 'master',
            'algorithm': 'RSA-4096'
        }
        
        # Store in Vault if available
        if self.vault_client:
            self._store_key_in_vault(key_info, f"master-key-{key.fingerprint}")
        
        logger.info(f"Master key generated: {key.fingerprint}")
        return key_info
    
    def generate_case_key(self, case_id: str, master_key_fingerprint: str, passphrase: str) -> Dict:
        """Generate case-specific PGP key pair (2048-bit RSA)"""
        if not self.gpg:
            raise RuntimeError("PGP not available")
        
        input_data = self.gpg.gen_key_input(
            key_type="RSA",
            key_length=2048,
            name_real=f"Case {case_id}",
            name_email=f"case-{case_id}@legal-cms.internal",
            passphrase=passphrase,
            expire_date='90d'  # 90 days expiry for rotation
        )
        
        logger.info(f"Generating case key for case {case_id}...")
        key = self.gpg.gen_key(input_data)
        
        if not key:
            raise RuntimeError("Failed to generate case PGP key")
        
        # Note: Key signing is handled during encryption/verification process
        # Direct key signing is not available in python-gnupg library
        
        public_key = self.gpg.export_keys(key.fingerprint)
        private_key = self.gpg.export_keys(key.fingerprint, secret=True, passphrase=passphrase)
        
        key_info = {
            'fingerprint': key.fingerprint,
            'key_id': str(key),
            'case_id': case_id,
            'public_key': public_key,
            'private_key': private_key,
            'created_at': datetime.now().isoformat(),
            'expires_at': (datetime.now() + timedelta(days=90)).isoformat(),
            'key_type': 'case',
            'algorithm': 'RSA-2048',
            'signed_by': master_key_fingerprint
        }
        
        # Store in Vault
        if self.vault_client:
            self._store_key_in_vault(key_info, f"case-key-{case_id}")
        
        logger.info(f"Case key generated for {case_id}: {key.fingerprint}")
        return key_info
    
    def _store_key_in_vault(self, key_info: Dict, key_name: str):
        """Store PGP key in Vault"""
        if not self.vault_client:
            return
        
        try:
            # Store private key separately with higher security
            private_key_data = {
                'private_key': key_info['private_key'],
                'fingerprint': key_info['fingerprint'],
                'created_at': key_info['created_at']
            }
            
            # Store public key and metadata
            public_key_data = {
                'public_key': key_info['public_key'],
                'fingerprint': key_info['fingerprint'],
                'key_id': key_info['key_id'],
                'created_at': key_info['created_at'],
                'expires_at': key_info['expires_at'],
                'key_type': key_info['key_type'],
                'algorithm': key_info['algorithm']
            }
            
            if 'case_id' in key_info:
                public_key_data['case_id'] = key_info['case_id']
            
            # Store in different paths for security
            self.vault_client.secrets.kv.v2.create_or_update_secret(
                path=f"pgp-private-keys/{key_name}",
                secret=private_key_data
            )
            
            self.vault_client.secrets.kv.v2.create_or_update_secret(
                path=f"pgp-public-keys/{key_name}",
                secret=public_key_data
            )
            
            logger.info(f"Key {key_name} stored in Vault")
            
        except Exception as e:
            logger.error(f"Failed to store key in Vault: {e}")
    
    def get_key_from_vault(self, key_name: str, include_private: bool = False) -> Optional[Dict]:
        """Retrieve PGP key from Vault"""
        if not self.vault_client:
            return None
        
        try:
            # Get public key data
            public_response = self.vault_client.secrets.kv.v2.read_secret_version(
                path=f"pgp-public-keys/{key_name}"
            )
            key_data = public_response['data']['data']
            
            if include_private:
                # Get private key data
                private_response = self.vault_client.secrets.kv.v2.read_secret_version(
                    path=f"pgp-private-keys/{key_name}"
                )
                key_data['private_key'] = private_response['data']['data']['private_key']
            
            return key_data
            
        except Exception as e:
            logger.error(f"Failed to retrieve key from Vault: {e}")
            return None

class DocumentEncryption:
    """Handles document encryption using hybrid PGP + AES approach"""
    
    def __init__(self, key_manager: PGPKeyManager):
        self.key_manager = key_manager
        self.gpg = key_manager.gpg
    
    def encrypt_document(self, content: bytes, case_id: str, document_id: str) -> Dict:
        """
        Encrypt document using hybrid approach:
        1. Generate random AES-256 key
        2. Encrypt document with AES key
        3. Encrypt AES key with case PGP public key
        4. Return encrypted content + metadata
        """
        if not self.gpg:
            raise RuntimeError("PGP not available")
        
        # Get case public key
        case_key = self.key_manager.get_key_from_vault(f"case-key-{case_id}")
        if not case_key:
            raise RuntimeError(f"Case key not found for case {case_id}")
        
        # Import public key
        import_result = self.gpg.import_keys(case_key['public_key'])
        if not import_result.fingerprints:
            raise RuntimeError("Failed to import case public key")
        
        fingerprint = import_result.fingerprints[0]
        
        # Generate random AES key
        aes_key = secrets.token_bytes(32)  # 256-bit key
        iv = secrets.token_bytes(16)       # 128-bit IV
        
        # Encrypt document with AES
        cipher = Cipher(
            algorithms.AES(aes_key),
            modes.CBC(iv),
            backend=default_backend()
        )
        encryptor = cipher.encryptor()
        
        # Pad content to AES block size
        padded_content = self._pad_content(content)
        encrypted_content = encryptor.update(padded_content) + encryptor.finalize()
        
        # Encrypt AES key with PGP
        aes_key_data = aes_key + iv  # Combine key and IV
        encrypted_aes_key = self.gpg.encrypt(
            aes_key_data,
            fingerprint,
            armor=True,
            always_trust=True
        )
        
        if not encrypted_aes_key.ok:
            raise RuntimeError(f"Failed to encrypt AES key: {encrypted_aes_key.status}")
        
        # Create content hash for integrity
        content_hash = hashlib.sha256(content).hexdigest()
        
        encryption_result = {
            'document_id': document_id,
            'case_id': case_id,
            'encrypted_content': encrypted_content,
            'encrypted_aes_key': str(encrypted_aes_key),
            'pgp_fingerprint': fingerprint,
            'content_hash': content_hash,
            'encryption_algorithm': 'AES-256-CBC + PGP-RSA',
            'encrypted_at': datetime.now().isoformat(),
            'content_size': len(content),
            'encrypted_size': len(encrypted_content)
        }
        
        logger.info(f"Document {document_id} encrypted for case {case_id}")
        return encryption_result
    
    def decrypt_document(self, encrypted_data: Dict, passphrase: str) -> bytes:
        """
        Decrypt document using hybrid approach:
        1. Decrypt AES key with PGP private key
        2. Decrypt document with AES key
        3. Verify content integrity
        """
        if not self.gpg:
            raise RuntimeError("PGP not available")
        
        # Decrypt AES key with PGP
        decrypted_aes_data = self.gpg.decrypt(
            encrypted_data['encrypted_aes_key'],
            passphrase=passphrase
        )
        
        if not decrypted_aes_data.ok:
            raise RuntimeError(f"Failed to decrypt AES key: {decrypted_aes_data.status}")
        
        # Extract AES key and IV
        aes_key_data = decrypted_aes_data.data
        aes_key = aes_key_data[:32]  # First 32 bytes
        iv = aes_key_data[32:48]     # Next 16 bytes
        
        # Decrypt document with AES
        cipher = Cipher(
            algorithms.AES(aes_key),
            modes.CBC(iv),
            backend=default_backend()
        )
        decryptor = cipher.decryptor()
        
        decrypted_padded = decryptor.update(encrypted_data['encrypted_content']) + decryptor.finalize()
        decrypted_content = self._unpad_content(decrypted_padded)
        
        # Verify content integrity
        content_hash = hashlib.sha256(decrypted_content).hexdigest()
        if content_hash != encrypted_data['content_hash']:
            raise RuntimeError("Content integrity check failed")
        
        logger.info(f"Document {encrypted_data['document_id']} decrypted successfully")
        return decrypted_content
    
    def _pad_content(self, content: bytes) -> bytes:
        """Add PKCS7 padding to content"""
        block_size = 16  # AES block size
        padding_length = block_size - (len(content) % block_size)
        padding = bytes([padding_length] * padding_length)
        return content + padding
    
    def _unpad_content(self, padded_content: bytes) -> bytes:
        """Remove PKCS7 padding from content"""
        padding_length = padded_content[-1]
        return padded_content[:-padding_length]

# Factory function for easy initialization
def create_pgp_manager(vault_url: str = None, vault_token: str = None) -> Tuple[PGPKeyManager, DocumentEncryption]:
    """Create PGP manager with optional Vault integration"""
    vault_client = None
    
    if vault_url and vault_token and VAULT_AVAILABLE:
        try:
            vault_client = hvac.Client(url=vault_url, token=vault_token)
            if vault_client.is_authenticated():
                logger.info("Connected to Vault successfully")
            else:
                logger.warning("Vault authentication failed")
                vault_client = None
        except Exception as e:
            logger.error(f"Failed to connect to Vault: {e}")
            vault_client = None
    
    key_manager = PGPKeyManager(vault_client)
    document_encryption = DocumentEncryption(key_manager)
    
    return key_manager, document_encryption
