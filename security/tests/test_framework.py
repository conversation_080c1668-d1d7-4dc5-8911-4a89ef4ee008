"""
Comprehensive Testing Framework for Legal Case Management System
Provides unit tests, integration tests, and end-to-end testing capabilities.
"""

import unittest
import tempfile
import shutil
import os
import sqlite3
import json
from datetime import datetime, timedelta
from unittest.mock import Mock, patch, MagicMock
import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# Import modules to test
from core.database import DatabaseManager
from core.document_processor import DocumentProcessor
from core.queue_manager import QueueManager, Task, TaskStatus, TaskPriority
from security.auth import SecurityManager

class BaseTestCase(unittest.TestCase):
    """Base test case with common setup and teardown"""
    
    def setUp(self):
        """Set up test environment"""
        # Create temporary directory for test files
        self.test_dir = tempfile.mkdtemp()
        self.test_db = os.path.join(self.test_dir, "test.db")
        self.test_upload_dir = os.path.join(self.test_dir, "uploads")
        os.makedirs(self.test_upload_dir, exist_ok=True)
        
        # Initialize test managers
        self.db_manager = DatabaseManager(self.test_db)
        self.doc_processor = DocumentProcessor(self.test_upload_dir)
        self.queue_manager = QueueManager(os.path.join(self.test_dir, "queue.db"))
        self.security_manager = SecurityManager(os.path.join(self.test_dir, "security.db"))
        
    def tearDown(self):
        """Clean up test environment"""
        # Stop queue manager
        if hasattr(self, 'queue_manager'):
            self.queue_manager.stop_worker()
        
        # Remove temporary directory
        shutil.rmtree(self.test_dir, ignore_errors=True)
        
    def create_test_file(self, filename: str, content: str = "Test content") -> str:
        """Create a test file and return its path"""
        file_path = os.path.join(self.test_upload_dir, filename)
        with open(file_path, 'w') as f:
            f.write(content)
        return file_path

class TestDatabaseManager(BaseTestCase):
    """Test database operations"""
    
    def test_database_initialization(self):
        """Test database tables are created correctly"""
        conn = sqlite3.connect(self.test_db)
        cursor = conn.cursor()
        
        # Check if tables exist
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = [row[0] for row in cursor.fetchall()]
        
        expected_tables = ['cases', 'documents', 'url_monitoring', 'user_actions', 
                          'system_monitoring', 'case_notes', 'deadlines']
        
        for table in expected_tables:
            self.assertIn(table, tables, f"Table {table} not created")
        
        conn.close()
        
    def test_create_case(self):
        """Test case creation"""
        case_id = self.db_manager.create_case(
            name="Test Case",
            case_number="TC-001",
            description="Test case description",
            client_name="Test Client"
        )
        
        self.assertIsInstance(case_id, int)
        self.assertGreater(case_id, 0)
        
        # Verify case was created
        case = self.db_manager.get_case(case_id)
        self.assertIsNotNone(case)
        self.assertEqual(case['name'], "Test Case")
        self.assertEqual(case['case_number'], "TC-001")
        
    def test_get_all_cases(self):
        """Test retrieving all cases"""
        # Create test cases
        case1_id = self.db_manager.create_case("Case 1", "C1-001")
        case2_id = self.db_manager.create_case("Case 2", "C2-001")
        
        cases = self.db_manager.get_all_cases()
        self.assertEqual(len(cases), 2)
        
        case_ids = [case['id'] for case in cases]
        self.assertIn(case1_id, case_ids)
        self.assertIn(case2_id, case_ids)
        
    def test_update_case(self):
        """Test case updates"""
        case_id = self.db_manager.create_case("Original Name", "ON-001")
        
        # Update case
        success = self.db_manager.update_case(
            case_id, 
            name="Updated Name",
            status="closed"
        )
        
        self.assertTrue(success)
        
        # Verify update
        case = self.db_manager.get_case(case_id)
        self.assertEqual(case['name'], "Updated Name")
        self.assertEqual(case['status'], "closed")
        
    def test_delete_case(self):
        """Test case deletion"""
        case_id = self.db_manager.create_case("To Delete", "TD-001")
        
        # Verify case exists
        case = self.db_manager.get_case(case_id)
        self.assertIsNotNone(case)
        
        # Delete case
        success = self.db_manager.delete_case(case_id)
        self.assertTrue(success)
        
        # Verify case is deleted
        case = self.db_manager.get_case(case_id)
        self.assertIsNone(case)
        
    def test_add_document(self):
        """Test document addition"""
        case_id = self.db_manager.create_case("Test Case", "TC-001")
        
        doc_id = self.db_manager.add_document(
            case_id=case_id,
            filename="test.pdf",
            file_type="pdf",
            file_size=1024,
            content_preview="Test document content"
        )
        
        self.assertIsInstance(doc_id, int)
        self.assertGreater(doc_id, 0)
        
        # Verify document was added
        doc = self.db_manager.get_document(doc_id)
        self.assertIsNotNone(doc)
        self.assertEqual(doc['filename'], "test.pdf")
        self.assertEqual(doc['case_id'], case_id)
        
    def test_get_case_documents(self):
        """Test retrieving case documents"""
        case_id = self.db_manager.create_case("Test Case", "TC-001")
        
        # Add test documents
        doc1_id = self.db_manager.add_document(case_id, "doc1.pdf", "pdf", 1024)
        doc2_id = self.db_manager.add_document(case_id, "doc2.txt", "document", 512)
        
        documents = self.db_manager.get_case_documents(case_id)
        self.assertEqual(len(documents), 2)
        
        doc_ids = [doc['id'] for doc in documents]
        self.assertIn(doc1_id, doc_ids)
        self.assertIn(doc2_id, doc_ids)
        
    def test_user_action_logging(self):
        """Test user action logging"""
        case_id = self.db_manager.create_case("Test Case", "TC-001")
        
        self.db_manager.log_user_action(
            user_id=1,
            action="CASE_CREATED",
            details="Created test case",
            case_id=case_id
        )
        
        actions = self.db_manager.get_user_actions(user_id=1)
        self.assertEqual(len(actions), 1)
        self.assertEqual(actions[0]['action'], "CASE_CREATED")
        self.assertEqual(actions[0]['case_id'], case_id)

class TestDocumentProcessor(BaseTestCase):
    """Test document processing functionality"""
    
    def test_file_type_detection(self):
        """Test file type category detection"""
        self.assertEqual(self.doc_processor.get_file_type_category("test.pdf"), "pdf")
        self.assertEqual(self.doc_processor.get_file_type_category("image.jpg"), "image")
        self.assertEqual(self.doc_processor.get_file_type_category("doc.docx"), "document")
        self.assertEqual(self.doc_processor.get_file_type_category("data.csv"), "spreadsheet")
        self.assertEqual(self.doc_processor.get_file_type_category("archive.zip"), "archive")
        self.assertEqual(self.doc_processor.get_file_type_category("unknown.xyz"), "other")
        
    def test_file_validation(self):
        """Test file validation"""
        # Valid file
        is_valid, message = self.doc_processor.validate_file("test.pdf", 1024)
        self.assertTrue(is_valid)
        
        # File too large
        is_valid, message = self.doc_processor.validate_file("large.pdf", 100 * 1024 * 1024)
        self.assertFalse(is_valid)
        self.assertIn("too large", message)
        
        # Dangerous file type
        is_valid, message = self.doc_processor.validate_file("virus.exe", 1024)
        self.assertFalse(is_valid)
        self.assertIn("not allowed", message)
        
    def test_safe_filename_generation(self):
        """Test safe filename generation"""
        filename = self.doc_processor.generate_safe_filename("test document.pdf", 1)
        
        # Should contain case ID
        self.assertIn("case_1", filename)
        
        # Should preserve extension
        self.assertTrue(filename.endswith(".pdf"))
        
        # Should be different for different inputs
        filename2 = self.doc_processor.generate_safe_filename("another document.pdf", 1)
        self.assertNotEqual(filename, filename2)
        
    def test_text_extraction_from_txt(self):
        """Test text extraction from plain text files"""
        # Create test text file
        test_content = "This is a test document with some content."
        file_path = self.create_test_file("test.txt", test_content)
        
        success, extracted_text = self.doc_processor.extract_text_from_txt(file_path)
        
        self.assertTrue(success)
        self.assertEqual(extracted_text.strip(), test_content)
        
    def test_document_type_classification(self):
        """Test document type classification"""
        # Test filename-based classification
        self.assertEqual(
            self.doc_processor._classify_document_type("motion_to_dismiss.pdf"),
            "motion"
        )
        self.assertEqual(
            self.doc_processor._classify_document_type("service_agreement.docx"),
            "contract"
        )
        self.assertEqual(
            self.doc_processor._classify_document_type("evidence_photo.jpg"),
            "evidence"
        )
        
        # Test content-based classification
        contract_text = "This agreement is entered into between the parties..."
        self.assertEqual(
            self.doc_processor._classify_document_type("document.pdf", contract_text),
            "contract"
        )
        
    @patch('core.document_processor.PyPDF2')
    def test_pdf_text_extraction_mock(self, mock_pypdf2):
        """Test PDF text extraction with mocked PyPDF2"""
        # Mock PDF reader
        mock_page = Mock()
        mock_page.extract_text.return_value = "Extracted PDF text"
        
        mock_reader = Mock()
        mock_reader.pages = [mock_page]
        
        mock_pypdf2.PdfReader.return_value = mock_reader
        
        # Create test PDF file
        file_path = self.create_test_file("test.pdf", "dummy content")
        
        success, extracted_text = self.doc_processor.extract_text_from_pdf(file_path)
        
        self.assertTrue(success)
        self.assertIn("Extracted PDF text", extracted_text)

class TestQueueManager(BaseTestCase):
    """Test queue management and task processing"""
    
    def test_task_creation(self):
        """Test task creation and storage"""
        task_id = self.queue_manager.add_task(
            name="Test Task",
            function_name="test_function",
            args=(1, 2, 3),
            kwargs={"key": "value"},
            priority=TaskPriority.HIGH
        )
        
        self.assertIsInstance(task_id, str)
        
        # Retrieve task
        task = self.queue_manager.get_task(task_id)
        self.assertIsNotNone(task)
        self.assertEqual(task.name, "Test Task")
        self.assertEqual(task.function_name, "test_function")
        self.assertEqual(task.args, (1, 2, 3))
        self.assertEqual(task.kwargs, {"key": "value"})
        self.assertEqual(task.priority, TaskPriority.HIGH)
        self.assertEqual(task.status, TaskStatus.PENDING)
        
    def test_scheduled_task(self):
        """Test scheduled task creation"""
        future_time = datetime.now() + timedelta(hours=1)
        
        task_id = self.queue_manager.add_task(
            name="Scheduled Task",
            function_name="test_function",
            scheduled_at=future_time
        )
        
        task = self.queue_manager.get_task(task_id)
        self.assertEqual(task.scheduled_at, future_time)
        self.assertEqual(task.status, TaskStatus.PENDING)
        
    def test_task_cancellation(self):
        """Test task cancellation"""
        task_id = self.queue_manager.add_task(
            name="Cancellable Task",
            function_name="test_function"
        )
        
        # Cancel task
        success = self.queue_manager.cancel_task(task_id)
        self.assertTrue(success)
        
        # Verify cancellation
        task = self.queue_manager.get_task(task_id)
        self.assertEqual(task.status, TaskStatus.CANCELLED)
        
    def test_queue_stats(self):
        """Test queue statistics"""
        # Add some tasks
        self.queue_manager.add_task("Task 1", "test_function")
        self.queue_manager.add_task("Task 2", "test_function")
        
        stats = self.queue_manager.get_queue_stats()
        
        self.assertIn('pending_count', stats)
        self.assertIn('queue_size', stats)
        self.assertIn('max_workers', stats)
        self.assertGreaterEqual(stats['pending_count'], 2)

class TestSecurityManager(BaseTestCase):
    """Test security and authentication"""
    
    def test_user_creation(self):
        """Test user creation"""
        success = self.security_manager.create_user(
            username="testuser",
            email="<EMAIL>",
            password="TestPass123!",
            role="user"
        )
        
        self.assertTrue(success)
        
        # Try to create duplicate user
        success = self.security_manager.create_user(
            username="testuser",
            email="<EMAIL>",
            password="TestPass123!",
            role="user"
        )
        
        self.assertFalse(success)
        
    def test_password_hashing(self):
        """Test password hashing and verification"""
        password = "TestPassword123!"
        
        password_hash, salt = self.security_manager.hash_password(password)
        
        # Verify correct password
        self.assertTrue(
            self.security_manager.verify_password(password, password_hash, salt)
        )
        
        # Verify incorrect password
        self.assertFalse(
            self.security_manager.verify_password("WrongPassword", password_hash, salt)
        )
        
    def test_user_authentication(self):
        """Test user authentication"""
        # Create test user
        self.security_manager.create_user(
            username="authtest",
            email="<EMAIL>",
            password="AuthPass123!",
            role="user"
        )
        
        # Test successful authentication
        user = self.security_manager.authenticate_user("authtest", "AuthPass123!")
        self.assertIsNotNone(user)
        self.assertEqual(user['username'], "authtest")
        self.assertIn('session_token', user)
        
        # Test failed authentication
        user = self.security_manager.authenticate_user("authtest", "WrongPassword")
        self.assertIsNone(user)
        
    def test_session_validation(self):
        """Test session validation"""
        # Create and authenticate user
        self.security_manager.create_user(
            username="sessiontest",
            email="<EMAIL>",
            password="SessionPass123!",
            role="user"
        )
        
        user = self.security_manager.authenticate_user("sessiontest", "SessionPass123!")
        session_token = user['session_token']
        
        # Validate session
        session_info = self.security_manager.validate_session(session_token)
        self.assertIsNotNone(session_info)
        self.assertEqual(session_info['username'], "sessiontest")
        
        # Invalidate session
        self.security_manager.invalidate_session(session_token)
        
        # Try to validate invalidated session
        session_info = self.security_manager.validate_session(session_token)
        self.assertIsNone(session_info)
        
    def test_permission_checking(self):
        """Test permission checking"""
        # Create test user
        self.security_manager.create_user(
            username="permtest",
            email="<EMAIL>",
            password="PermPass123!",
            role="user"
        )
        
        # Get user ID
        conn = sqlite3.connect(self.security_manager.db_path)
        cursor = conn.cursor()
        cursor.execute("SELECT id FROM users WHERE username = ?", ("permtest",))
        user_id = cursor.fetchone()[0]
        conn.close()
        
        # Test permission (should fail without explicit permission)
        has_permission = self.security_manager.check_permission(user_id, 1, 'read')
        self.assertFalse(has_permission)
        
        # Grant permission
        conn = sqlite3.connect(self.security_manager.db_path)
        cursor = conn.cursor()
        cursor.execute('''
            INSERT INTO case_permissions (user_id, case_id, permission_level)
            VALUES (?, ?, ?)
        ''', (user_id, 1, 'read'))
        conn.commit()
        conn.close()
        
        # Test permission again
        has_permission = self.security_manager.check_permission(user_id, 1, 'read')
        self.assertTrue(has_permission)

class TestIntegration(BaseTestCase):
    """Integration tests for multiple components"""
    
    def test_document_upload_and_processing_workflow(self):
        """Test complete document upload and processing workflow"""
        # Create case
        case_id = self.db_manager.create_case("Integration Test Case", "ITC-001")
        
        # Create test document
        test_content = "This is a test legal document with important content."
        file_path = self.create_test_file("test_document.txt", test_content)
        
        # Simulate file upload
        file_info = {
            'filename': 'test_document.txt',
            'original_filename': 'test_document.txt',
            'file_type': 'document',
            'file_size': len(test_content),
            'file_path': file_path
        }
        
        # Process document
        result = self.doc_processor.process_document(case_id, file_info, uploaded_by=1)
        
        self.assertTrue(result['success'])
        self.assertIsNotNone(result['document_id'])
        self.assertTrue(result['text_extracted'])
        self.assertIn("test legal document", result['text_content'])
        
        # Verify document in database
        documents = self.db_manager.get_case_documents(case_id)
        self.assertEqual(len(documents), 1)
        self.assertEqual(documents[0]['original_filename'], 'test_document.txt')
        
    def test_queue_document_processing(self):
        """Test document processing through queue system"""
        # Create case
        case_id = self.db_manager.create_case("Queue Test Case", "QTC-001")
        
        # Create test document
        test_content = "Queued document processing test."
        file_path = self.create_test_file("queued_doc.txt", test_content)
        
        file_info = {
            'filename': 'queued_doc.txt',
            'original_filename': 'queued_doc.txt',
            'file_type': 'document',
            'file_size': len(test_content),
            'file_path': file_path
        }
        
        # Add document processing task to queue
        task_id = self.queue_manager.add_task(
            name="Process Document",
            function_name="process_document",
            args=(case_id, file_info, 1),
            priority=TaskPriority.HIGH
        )
        
        # Verify task was created
        task = self.queue_manager.get_task(task_id)
        self.assertIsNotNone(task)
        self.assertEqual(task.status, TaskStatus.PENDING)

def run_all_tests():
    """Run all tests and return results"""
    # Create test suite
    test_suite = unittest.TestSuite()
    
    # Add test cases
    test_classes = [
        TestDatabaseManager,
        TestDocumentProcessor,
        TestQueueManager,
        TestSecurityManager,
        TestIntegration
    ]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    return result

if __name__ == "__main__":
    # Run tests when script is executed directly
    result = run_all_tests()
    
    # Print summary
    print(f"\n{'='*50}")
    print(f"TESTS RUN: {result.testsRun}")
    print(f"FAILURES: {len(result.failures)}")
    print(f"ERRORS: {len(result.errors)}")
    print(f"SUCCESS RATE: {((result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun * 100):.1f}%")
    print(f"{'='*50}")
    
    # Exit with appropriate code
    exit_code = 0 if result.wasSuccessful() else 1
    exit(exit_code)
