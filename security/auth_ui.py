"""
Authentication UI Components for Legal Case Management System
Provides login, registration, and session management interfaces.
"""

import streamlit as st
from security.auth import SecurityMana<PERSON>, security_manager
from typing import Optional
import re

def validate_email(email: str) -> bool:
    """Validate email format"""
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return re.match(pattern, email) is not None

def validate_password_strength(password: str) -> tuple[bool, str]:
    """Validate password strength"""
    if len(password) < 8:
        return False, "Password must be at least 8 characters long"
    
    if not re.search(r'[A-Z]', password):
        return False, "Password must contain at least one uppercase letter"
    
    if not re.search(r'[a-z]', password):
        return False, "Password must contain at least one lowercase letter"
    
    if not re.search(r'\d', password):
        return False, "Password must contain at least one number"
    
    if not re.search(r'[!@#$%^&*(),.?":{}|<>]', password):
        return False, "Password must contain at least one special character"
    
    return True, "Password is strong"

def show_login_form():
    """Display login form"""
    st.markdown("### 🔐 Login to Legal Case Management System")
    
    with st.form("login_form"):
        username = st.text_input("Username or Email", placeholder="Enter your username or email")
        password = st.text_input("Password", type="password", placeholder="Enter your password")
        
        col1, col2 = st.columns([1, 1])
        with col1:
            login_button = st.form_submit_button("🔑 Login", use_container_width=True)
        with col2:
            register_button = st.form_submit_button("📝 Register", use_container_width=True)
        
        if login_button:
            if not username or not password:
                st.error("Please enter both username and password")
                return
            
            # Get client IP (simplified for demo)
            ip_address = st.session_state.get('client_ip', '127.0.0.1')
            
            user = security_manager.authenticate_user(username, password, ip_address)
            
            if user:
                st.session_state.user = user
                st.session_state.authenticated = True
                st.success(f"Welcome back, {user['username']}!")
                st.rerun()
            else:
                st.error("Invalid credentials or account locked. Please try again.")
        
        if register_button:
            st.session_state.show_register = True
            st.rerun()

def show_registration_form():
    """Display registration form"""
    st.markdown("### 📝 Register New Account")
    
    with st.form("register_form"):
        username = st.text_input("Username", placeholder="Choose a unique username")
        email = st.text_input("Email", placeholder="Enter your email address")
        password = st.text_input("Password", type="password", placeholder="Create a strong password")
        confirm_password = st.text_input("Confirm Password", type="password", placeholder="Confirm your password")
        
        # Role selection (admin only for demo)
        role = st.selectbox("Role", ["user", "admin"], index=0)
        
        col1, col2 = st.columns([1, 1])
        with col1:
            register_button = st.form_submit_button("✅ Create Account", use_container_width=True)
        with col2:
            back_button = st.form_submit_button("⬅️ Back to Login", use_container_width=True)
        
        if register_button:
            # Validation
            errors = []
            
            if not username or len(username) < 3:
                errors.append("Username must be at least 3 characters long")
            
            if not email or not validate_email(email):
                errors.append("Please enter a valid email address")
            
            if not password:
                errors.append("Password is required")
            else:
                is_strong, message = validate_password_strength(password)
                if not is_strong:
                    errors.append(message)
            
            if password != confirm_password:
                errors.append("Passwords do not match")
            
            if errors:
                for error in errors:
                    st.error(error)
                return
            
            # Create user
            if security_manager.create_user(username, email, password, role):
                st.success("Account created successfully! Please login.")
                st.session_state.show_register = False
                st.rerun()
            else:
                st.error("Username or email already exists. Please choose different credentials.")
        
        if back_button:
            st.session_state.show_register = False
            st.rerun()

def show_user_profile():
    """Display user profile and session info"""
    if 'user' not in st.session_state:
        return
    
    user = st.session_state.user
    
    with st.sidebar:
        st.markdown("---")
        st.markdown("### 👤 User Profile")
        st.write(f"**Username:** {user['username']}")
        st.write(f"**Email:** {user['email']}")
        st.write(f"**Role:** {user['role'].title()}")
        
        if st.button("🚪 Logout", use_container_width=True):
            logout_user()

def logout_user():
    """Logout current user"""
    if 'user' in st.session_state:
        # Invalidate session
        session_token = st.session_state.user.get('session_token')
        if session_token:
            security_manager.invalidate_session(session_token)
        
        # Log audit event
        security_manager.log_audit_event(
            st.session_state.user['user_id'], 
            "LOGOUT", 
            f"user:{st.session_state.user['username']}", 
            "User logged out"
        )
        
        # Clear session state
        for key in ['user', 'authenticated', 'current_case_id']:
            if key in st.session_state:
                del st.session_state[key]
    
    st.success("Logged out successfully!")
    st.rerun()

def check_authentication():
    """Check if user is authenticated and session is valid"""
    if 'authenticated' not in st.session_state:
        st.session_state.authenticated = False
    
    if 'user' in st.session_state and st.session_state.authenticated:
        # Validate session
        session_token = st.session_state.user.get('session_token')
        if session_token:
            user_info = security_manager.validate_session(session_token)
            if user_info:
                # Update user info in session
                st.session_state.user.update(user_info)
                return True
            else:
                # Session expired or invalid
                logout_user()
                return False
    
    return False

def require_authentication():
    """Require authentication for the current page"""
    if not check_authentication():
        st.markdown("# 🔒 Authentication Required")
        
        # Show registration form if requested
        if st.session_state.get('show_register', False):
            show_registration_form()
        else:
            show_login_form()
        
        st.stop()

def show_access_denied(required_permission: str = None, required_role: str = None):
    """Show access denied message"""
    st.error("🚫 Access Denied")
    
    if required_role:
        st.write(f"This feature requires **{required_role}** role.")
    
    if required_permission:
        st.write(f"This action requires **{required_permission}** permission.")
    
    current_user = st.session_state.get('user', {})
    st.write(f"Your current role: **{current_user.get('role', 'unknown')}**")
    
    if st.button("🏠 Go to Dashboard"):
        st.rerun()

def init_demo_users():
    """Initialize demo users for testing"""
    try:
        # Create admin user
        security_manager.create_user(
            username="admin",
            email="<EMAIL>",
            password="Admin123!",
            role="admin"
        )
        
        # Create regular user
        security_manager.create_user(
            username="lawyer1",
            email="<EMAIL>",
            password="Lawyer123!",
            role="user"
        )
        
        st.success("Demo users created successfully!")
        st.info("**Demo Credentials:**")
        st.code("""
Admin User:
Username: admin
Password: Admin123!

Regular User:
Username: lawyer1
Password: Lawyer123!
        """)
        
    except Exception as e:
        st.info("Demo users may already exist.")

def show_security_dashboard():
    """Show security dashboard for admins"""
    if not check_authentication():
        require_authentication()
        return
    
    user = st.session_state.user
    if user['role'] != 'admin':
        show_access_denied(required_role="admin")
        return
    
    st.markdown("## 🛡️ Security Dashboard")
    
    # Tabs for different security views
    tab1, tab2, tab3 = st.tabs(["👥 Users", "📊 Audit Log", "🔐 Sessions"])
    
    with tab1:
        show_users_management()
    
    with tab2:
        show_audit_log()
    
    with tab3:
        show_active_sessions()

def show_users_management():
    """Show user management interface"""
    st.markdown("### 👥 User Management")
    
    # Get all users
    conn = security_manager.db_path
    import sqlite3
    conn = sqlite3.connect(security_manager.db_path)
    cursor = conn.cursor()
    
    cursor.execute('''
        SELECT id, username, email, role, is_active, created_at, last_login, failed_attempts
        FROM users
        ORDER BY created_at DESC
    ''')
    
    users = cursor.fetchall()
    conn.close()
    
    if users:
        import pandas as pd
        df = pd.DataFrame(users, columns=[
            'ID', 'Username', 'Email', 'Role', 'Active', 'Created', 'Last Login', 'Failed Attempts'
        ])
        st.dataframe(df, use_container_width=True)
    else:
        st.info("No users found.")

def show_audit_log():
    """Show audit log"""
    st.markdown("### 📊 Audit Log")
    
    # Get recent audit events
    import sqlite3
    conn = sqlite3.connect(security_manager.db_path)
    cursor = conn.cursor()
    
    cursor.execute('''
        SELECT a.timestamp, u.username, a.action, a.resource, a.details, a.ip_address
        FROM audit_log a
        LEFT JOIN users u ON a.user_id = u.id
        ORDER BY a.timestamp DESC
        LIMIT 100
    ''')
    
    events = cursor.fetchall()
    conn.close()
    
    if events:
        import pandas as pd
        df = pd.DataFrame(events, columns=[
            'Timestamp', 'User', 'Action', 'Resource', 'Details', 'IP Address'
        ])
        st.dataframe(df, use_container_width=True)
    else:
        st.info("No audit events found.")

def show_active_sessions():
    """Show active sessions"""
    st.markdown("### 🔐 Active Sessions")
    
    # Get active sessions
    import sqlite3
    conn = sqlite3.connect(security_manager.db_path)
    cursor = conn.cursor()
    
    cursor.execute('''
        SELECT s.id, u.username, s.ip_address, s.user_agent, s.created_at, s.expires_at
        FROM sessions s
        JOIN users u ON s.user_id = u.id
        WHERE s.expires_at > datetime('now')
        ORDER BY s.created_at DESC
    ''')
    
    sessions = cursor.fetchall()
    conn.close()
    
    if sessions:
        import pandas as pd
        df = pd.DataFrame(sessions, columns=[
            'Session ID', 'User', 'IP Address', 'User Agent', 'Created', 'Expires'
        ])
        st.dataframe(df, use_container_width=True)
    else:
        st.info("No active sessions found.")
