{"timestamp": "2025-08-14T05:30:23.489833", "memory_overcommit_fixed": true, "config_optimizations": {"maxmemory": "2806mb", "maxmemory-policy": "allkeys-lru", "maxclients": "800", "lazyfree-lazy-eviction": "yes", "lazyfree-lazy-expire": "yes", "lazyfree-lazy-server-del": "yes"}, "performance_report": {"timestamp": "2025-08-14T05:30:23.500263", "redis_memory_usage_mb": 1.10614013671875, "redis_memory_peak_mb": 1.10614013671875, "system_memory_usage_percent": 64.9, "connected_clients": 1, "hit_ratio_percent": 0, "evicted_keys": 0, "expired_keys": 0, "total_commands": 184, "status": "suboptimal", "issues": ["Low cache hit ratio - consider increasing memory"]}, "success": true}