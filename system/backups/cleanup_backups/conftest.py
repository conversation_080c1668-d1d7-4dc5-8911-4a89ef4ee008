"""
Pytest configuration for Legal Case Management System tests
"""

import pytest
import asyncio
import subprocess
import time
import requests
from pathlib import Path
import os

# Test configuration
TEST_URL = "http://localhost:8501"
STREAMLIT_PORT = 8501

@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()

@pytest.fixture(scope="session", autouse=True)
def setup_test_environment():
    """Setup test environment and start Streamlit app if needed"""
    print("\n🚀 Setting up test environment...")
    
    # Check if Streamlit is already running
    if not is_streamlit_running():
        print("📱 Starting Streamlit application...")
        streamlit_process = start_streamlit_app()
        
        # Wait for app to be ready
        wait_for_app_ready()
        
        yield
        
        # Cleanup
        print("🧹 Cleaning up test environment...")
        if streamlit_process:
            streamlit_process.terminate()
            streamlit_process.wait()
    else:
        print("✅ Streamlit already running")
        yield

def is_streamlit_running():
    """Check if Streamlit is already running"""
    try:
        response = requests.get(TEST_URL, timeout=5)
        return response.status_code == 200
    except:
        return False

def start_streamlit_app():
    """Start the Streamlit application"""
    try:
        # Get the project root directory
        project_root = Path(__file__).parent.parent
        
        # Start Streamlit
        process = subprocess.Popen([
            "streamlit", "run", "secure_app.py",
            "--server.port", str(STREAMLIT_PORT),
            "--server.headless", "true",
            "--browser.gatherUsageStats", "false"
        ], cwd=project_root)
        
        return process
    except Exception as e:
        print(f"❌ Failed to start Streamlit: {e}")
        return None

def wait_for_app_ready(max_wait=30):
    """Wait for the Streamlit app to be ready"""
    print("⏳ Waiting for application to be ready...")
    
    for i in range(max_wait):
        try:
            response = requests.get(TEST_URL, timeout=5)
            if response.status_code == 200:
                print("✅ Application is ready!")
                return True
        except:
            pass
        
        time.sleep(1)
        if i % 5 == 0:
            print(f"⏳ Still waiting... ({i}/{max_wait}s)")
    
    print("❌ Application failed to start within timeout")
    return False

@pytest.fixture
def test_data_dir():
    """Provide test data directory"""
    test_dir = Path(__file__).parent / "test_data"
    test_dir.mkdir(exist_ok=True)
    return test_dir

@pytest.fixture
def sample_legal_document(test_data_dir):
    """Create a sample legal document for testing"""
    doc_content = """
SAMPLE LEGAL DOCUMENT

Case: SHEPOV vs SHEPOV
Case Number: DR-25-403973
Court: Cuyahoga County Court of Common Pleas

MOTION FOR TEMPORARY ORDERS

TO THE HONORABLE COURT:

Plaintiff respectfully moves this Court for temporary orders regarding:

1. Temporary spousal support
2. Temporary child custody arrangements
3. Temporary use of marital residence

WHEREFORE, Plaintiff respectfully requests that this Court grant the relief requested herein.

Respectfully submitted,
Giorgiy Shepov, Pro Se
5160 Stevenson St
Cleveland, OH 44143
Phone: ************
Email: <EMAIL>
"""
    
    doc_path = test_data_dir / "sample_motion.txt"
    with open(doc_path, 'w') as f:
        f.write(doc_content)
    
    yield doc_path
    
    # Cleanup
    if doc_path.exists():
        doc_path.unlink()

@pytest.fixture
def sample_csv_data(test_data_dir):
    """Create sample CSV data for testing"""
    csv_content = """Document_Type,Date_Filed,Party,Description,Status
Complaint,2024-04-04,Plaintiff,Complaint for Divorce,Filed
Summons,2024-04-05,Court,Summons Issued,Served
Motion,2024-04-15,Plaintiff,Motion for Temporary Orders,Pending
Response,2024-04-20,Defendant,Answer to Complaint,Filed
Discovery,2024-05-01,Plaintiff,Request for Production,Served
"""
    
    csv_path = test_data_dir / "case_data.csv"
    with open(csv_path, 'w') as f:
        f.write(csv_content)
    
    yield csv_path
    
    # Cleanup
    if csv_path.exists():
        csv_path.unlink()

# Test markers
def pytest_configure(config):
    """Configure custom pytest markers"""
    config.addinivalue_line(
        "markers", "functionality: mark test as functionality test"
    )
    config.addinivalue_line(
        "markers", "usability: mark test as usability test"
    )
    config.addinivalue_line(
        "markers", "performance: mark test as performance test"
    )
    config.addinivalue_line(
        "markers", "accessibility: mark test as accessibility test"
    )
    config.addinivalue_line(
        "markers", "integration: mark test as integration test"
    )
    config.addinivalue_line(
        "markers", "slow: mark test as slow running"
    )

# Test collection hooks
def pytest_collection_modifyitems(config, items):
    """Modify test collection to add markers based on test names"""
    for item in items:
        # Add markers based on test names
        if "usability" in item.name.lower():
            item.add_marker(pytest.mark.usability)
        if "performance" in item.name.lower():
            item.add_marker(pytest.mark.performance)
        if "accessibility" in item.name.lower():
            item.add_marker(pytest.mark.accessibility)
        if "integration" in item.name.lower() or "end_to_end" in item.name.lower():
            item.add_marker(pytest.mark.integration)
        if "slow" in item.name.lower() or "load" in item.name.lower():
            item.add_marker(pytest.mark.slow)

# Reporting hooks
def pytest_html_report_title(report):
    """Customize HTML report title"""
    report.title = "Legal Case Management System - Test Report"

def pytest_html_results_summary(prefix, summary, postfix):
    """Customize HTML report summary"""
    prefix.extend([
        "<h2>Legal Case Management System Test Results</h2>",
        "<p>Comprehensive functionality and usability testing</p>"
    ])

# Custom assertions for usability testing
class UsabilityAssertions:
    """Custom assertions for usability testing"""
    
    @staticmethod
    def assert_load_time(actual_time, max_time=5.0):
        """Assert page load time is acceptable"""
        assert actual_time <= max_time, f"Page load time {actual_time:.2f}s exceeds maximum {max_time}s"
    
    @staticmethod
    def assert_element_size(element_box, min_width=44, min_height=44):
        """Assert element meets minimum size requirements for usability"""
        if element_box:
            assert element_box['width'] >= min_width, f"Element width {element_box['width']}px below minimum {min_width}px"
            assert element_box['height'] >= min_height, f"Element height {element_box['height']}px below minimum {min_height}px"
    
    @staticmethod
    def assert_color_contrast(foreground, background, min_ratio=4.5):
        """Assert color contrast meets accessibility standards"""
        # Simplified contrast check - in real implementation would calculate actual contrast ratio
        assert foreground != background, "Foreground and background colors should be different"
    
    @staticmethod
    def assert_response_time(actual_time, max_time=1.0):
        """Assert interaction response time is acceptable"""
        assert actual_time <= max_time, f"Response time {actual_time:.2f}s exceeds maximum {max_time}s"

@pytest.fixture
def usability_assertions():
    """Provide usability assertion helpers"""
    return UsabilityAssertions()

# Test data cleanup
@pytest.fixture(autouse=True)
def cleanup_test_data():
    """Cleanup test data after each test"""
    yield
    
    # Clean up any temporary files created during tests
    test_files = [
        "test_document.txt",
        "test_legal_data.csv",
        "uploaded_test_file.txt"
    ]
    
    for file_name in test_files:
        file_path = Path(file_name)
        if file_path.exists():
            try:
                file_path.unlink()
            except:
                pass  # Ignore cleanup errors
