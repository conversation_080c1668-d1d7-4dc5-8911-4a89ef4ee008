"""
Queue Management System for Legal Case Management
Handles background tasks, batch processing, and asynchronous operations.
"""

import asyncio
import threading
import queue
import time
import logging
from typing import Dict, List, Optional, Callable, Any
from datetime import datetime, timedelta
from enum import Enum
import json
import sqlite3
from dataclasses import dataclass, asdict
from concurrent.futures import ThreadPoolExecutor, as_completed
import uuid

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class TaskStatus(Enum):
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"
    RETRYING = "retrying"

class TaskPriority(Enum):
    LOW = 1
    NORMAL = 2
    HIGH = 3
    URGENT = 4

@dataclass
class Task:
    """Task definition for queue processing"""
    id: str
    name: str
    function_name: str
    args: tuple
    kwargs: dict
    priority: TaskPriority
    status: TaskStatus
    created_at: datetime
    scheduled_at: Optional[datetime] = None
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    error_message: Optional[str] = None
    retry_count: int = 0
    max_retries: int = 3
    timeout: int = 300  # 5 minutes default
    result: Optional[Any] = None
    progress: float = 0.0
    metadata: Optional[Dict] = None

class QueueManager:
    """Manages task queues and background processing"""
    
    def __init__(self, db_path: str = "queue.db", max_workers: int = 4):
        self.db_path = db_path
        self.max_workers = max_workers
        self.task_queue = queue.PriorityQueue()
        self.running_tasks = {}
        self.task_registry = {}
        self.executor = ThreadPoolExecutor(max_workers=max_workers)
        self.is_running = False
        self.worker_thread = None
        
        self.init_database()
        self.register_default_tasks()
        
    def init_database(self):
        """Initialize queue database"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS tasks (
                id TEXT PRIMARY KEY,
                name TEXT NOT NULL,
                function_name TEXT NOT NULL,
                args TEXT,
                kwargs TEXT,
                priority INTEGER,
                status TEXT,
                created_at TIMESTAMP,
                scheduled_at TIMESTAMP,
                started_at TIMESTAMP,
                completed_at TIMESTAMP,
                error_message TEXT,
                retry_count INTEGER DEFAULT 0,
                max_retries INTEGER DEFAULT 3,
                timeout INTEGER DEFAULT 300,
                result TEXT,
                progress REAL DEFAULT 0.0,
                metadata TEXT
            )
        ''')
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS task_logs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                task_id TEXT,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                level TEXT,
                message TEXT,
                FOREIGN KEY (task_id) REFERENCES tasks (id)
            )
        ''')
        
        conn.commit()
        conn.close()
        
    def register_task(self, name: str, function: Callable):
        """Register a task function"""
        self.task_registry[name] = function
        logger.info(f"Registered task: {name}")
        
    def register_default_tasks(self):
        """Register default system tasks"""
        from core.document_processor import document_processor
        from core.vector_manager import vector_manager
        
        # Document processing tasks
        self.register_task("process_document", self._process_document_task)
        self.register_task("batch_process_documents", self._batch_process_documents_task)
        self.register_task("extract_text", self._extract_text_task)
        
        # Vector database tasks
        self.register_task("create_embeddings", self._create_embeddings_task)
        self.register_task("update_knowledge_base", self._update_knowledge_base_task)
        
        # URL monitoring tasks
        self.register_task("check_url", self._check_url_task)
        self.register_task("batch_check_urls", self._batch_check_urls_task)
        
        # System maintenance tasks
        self.register_task("cleanup_old_files", self._cleanup_old_files_task)
        self.register_task("backup_database", self._backup_database_task)
        self.register_task("generate_report", self._generate_report_task)
        
    def add_task(self, name: str, function_name: str, args: tuple = (), kwargs: dict = None,
                priority: TaskPriority = TaskPriority.NORMAL, scheduled_at: datetime = None,
                max_retries: int = 3, timeout: int = 300, metadata: dict = None) -> str:
        """Add a task to the queue"""
        task_id = str(uuid.uuid4())
        
        task = Task(
            id=task_id,
            name=name,
            function_name=function_name,
            args=args,
            kwargs=kwargs or {},
            priority=priority,
            status=TaskStatus.PENDING,
            created_at=datetime.now(),
            scheduled_at=scheduled_at,
            max_retries=max_retries,
            timeout=timeout,
            metadata=metadata or {}
        )
        
        # Save to database
        self._save_task(task)
        
        # Add to queue if not scheduled for future
        if not scheduled_at or scheduled_at <= datetime.now():
            self.task_queue.put((priority.value, time.time(), task))
            logger.info(f"Added task to queue: {name} (ID: {task_id})")
        else:
            logger.info(f"Scheduled task: {name} for {scheduled_at} (ID: {task_id})")
            
        return task_id
        
    def _save_task(self, task: Task):
        """Save task to database"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT OR REPLACE INTO tasks 
            (id, name, function_name, args, kwargs, priority, status, created_at, 
             scheduled_at, started_at, completed_at, error_message, retry_count, 
             max_retries, timeout, result, progress, metadata)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            task.id, task.name, task.function_name, 
            json.dumps(task.args), json.dumps(task.kwargs),
            task.priority.value, task.status.value, task.created_at,
            task.scheduled_at, task.started_at, task.completed_at,
            task.error_message, task.retry_count, task.max_retries,
            task.timeout, json.dumps(task.result) if task.result else None,
            task.progress, json.dumps(task.metadata)
        ))
        
        conn.commit()
        conn.close()
        
    def get_task(self, task_id: str) -> Optional[Task]:
        """Get task by ID"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('SELECT * FROM tasks WHERE id = ?', (task_id,))
        row = cursor.fetchone()
        conn.close()
        
        if not row:
            return None
            
        return Task(
            id=row[0], name=row[1], function_name=row[2],
            args=json.loads(row[3]), kwargs=json.loads(row[4]),
            priority=TaskPriority(row[5]), status=TaskStatus(row[6]),
            created_at=datetime.fromisoformat(row[7]),
            scheduled_at=datetime.fromisoformat(row[8]) if row[8] else None,
            started_at=datetime.fromisoformat(row[9]) if row[9] else None,
            completed_at=datetime.fromisoformat(row[10]) if row[10] else None,
            error_message=row[11], retry_count=row[12], max_retries=row[13],
            timeout=row[14], result=json.loads(row[15]) if row[15] else None,
            progress=row[16], metadata=json.loads(row[17]) if row[17] else {}
        )
        
    def get_tasks(self, status: TaskStatus = None, limit: int = 100) -> List[Task]:
        """Get tasks with optional status filter"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        if status:
            cursor.execute('SELECT * FROM tasks WHERE status = ? ORDER BY created_at DESC LIMIT ?', 
                         (status.value, limit))
        else:
            cursor.execute('SELECT * FROM tasks ORDER BY created_at DESC LIMIT ?', (limit,))
            
        rows = cursor.fetchall()
        conn.close()
        
        tasks = []
        for row in rows:
            task = Task(
                id=row[0], name=row[1], function_name=row[2],
                args=json.loads(row[3]), kwargs=json.loads(row[4]),
                priority=TaskPriority(row[5]), status=TaskStatus(row[6]),
                created_at=datetime.fromisoformat(row[7]),
                scheduled_at=datetime.fromisoformat(row[8]) if row[8] else None,
                started_at=datetime.fromisoformat(row[9]) if row[9] else None,
                completed_at=datetime.fromisoformat(row[10]) if row[10] else None,
                error_message=row[11], retry_count=row[12], max_retries=row[13],
                timeout=row[14], result=json.loads(row[15]) if row[15] else None,
                progress=row[16], metadata=json.loads(row[17]) if row[17] else {}
            )
            tasks.append(task)
            
        return tasks
        
    def start_worker(self):
        """Start the background worker"""
        if self.is_running:
            return
            
        self.is_running = True
        self.worker_thread = threading.Thread(target=self._worker_loop, daemon=True)
        self.worker_thread.start()
        logger.info("Queue worker started")
        
    def stop_worker(self):
        """Stop the background worker"""
        self.is_running = False
        if self.worker_thread:
            self.worker_thread.join(timeout=5)
        logger.info("Queue worker stopped")
        
    def _worker_loop(self):
        """Main worker loop"""
        while self.is_running:
            try:
                # Check for scheduled tasks
                self._check_scheduled_tasks()
                
                # Process pending tasks
                try:
                    priority, timestamp, task = self.task_queue.get(timeout=1)
                    self._execute_task(task)
                except queue.Empty:
                    continue
                    
            except Exception as e:
                logger.error(f"Worker loop error: {e}")
                time.sleep(1)
                
    def _check_scheduled_tasks(self):
        """Check for scheduled tasks that are ready to run"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT * FROM tasks 
            WHERE status = ? AND scheduled_at <= ?
        ''', (TaskStatus.PENDING.value, datetime.now()))
        
        rows = cursor.fetchall()
        conn.close()
        
        for row in rows:
            task = Task(
                id=row[0], name=row[1], function_name=row[2],
                args=json.loads(row[3]), kwargs=json.loads(row[4]),
                priority=TaskPriority(row[5]), status=TaskStatus(row[6]),
                created_at=datetime.fromisoformat(row[7]),
                scheduled_at=datetime.fromisoformat(row[8]) if row[8] else None,
                started_at=datetime.fromisoformat(row[9]) if row[9] else None,
                completed_at=datetime.fromisoformat(row[10]) if row[10] else None,
                error_message=row[11], retry_count=row[12], max_retries=row[13],
                timeout=row[14], result=json.loads(row[15]) if row[15] else None,
                progress=row[16], metadata=json.loads(row[17]) if row[17] else {}
            )
            
            self.task_queue.put((task.priority.value, time.time(), task))
            
    def _execute_task(self, task: Task):
        """Execute a single task"""
        try:
            # Update task status
            task.status = TaskStatus.RUNNING
            task.started_at = datetime.now()
            self._save_task(task)
            self.running_tasks[task.id] = task
            
            logger.info(f"Executing task: {task.name} (ID: {task.id})")
            
            # Get task function
            if task.function_name not in self.task_registry:
                raise Exception(f"Task function not registered: {task.function_name}")
                
            task_function = self.task_registry[task.function_name]
            
            # Execute with timeout
            future = self.executor.submit(task_function, task, *task.args, **task.kwargs)
            
            try:
                result = future.result(timeout=task.timeout)
                
                # Task completed successfully
                task.status = TaskStatus.COMPLETED
                task.completed_at = datetime.now()
                task.result = result
                task.progress = 100.0
                
                logger.info(f"Task completed: {task.name} (ID: {task.id})")
                
            except Exception as e:
                # Task failed
                task.error_message = str(e)
                
                if task.retry_count < task.max_retries:
                    # Retry task
                    task.retry_count += 1
                    task.status = TaskStatus.RETRYING
                    
                    # Add back to queue with delay
                    retry_delay = min(60 * (2 ** task.retry_count), 3600)  # Exponential backoff
                    task.scheduled_at = datetime.now() + timedelta(seconds=retry_delay)
                    
                    logger.warning(f"Task failed, retrying in {retry_delay}s: {task.name} (ID: {task.id})")
                else:
                    # Max retries reached
                    task.status = TaskStatus.FAILED
                    task.completed_at = datetime.now()
                    
                    logger.error(f"Task failed permanently: {task.name} (ID: {task.id}) - {e}")
                    
        except Exception as e:
            task.status = TaskStatus.FAILED
            task.error_message = str(e)
            task.completed_at = datetime.now()
            logger.error(f"Task execution error: {task.name} (ID: {task.id}) - {e}")
            
        finally:
            # Save final task state
            self._save_task(task)
            
            # Remove from running tasks
            if task.id in self.running_tasks:
                del self.running_tasks[task.id]
                
    def cancel_task(self, task_id: str) -> bool:
        """Cancel a pending or running task"""
        task = self.get_task(task_id)
        if not task:
            return False
            
        if task.status in [TaskStatus.PENDING, TaskStatus.RETRYING]:
            task.status = TaskStatus.CANCELLED
            task.completed_at = datetime.now()
            self._save_task(task)
            logger.info(f"Cancelled task: {task.name} (ID: {task_id})")
            return True
            
        return False
        
    def get_queue_stats(self) -> Dict:
        """Get queue statistics"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        stats = {}
        
        # Count by status
        for status in TaskStatus:
            cursor.execute('SELECT COUNT(*) FROM tasks WHERE status = ?', (status.value,))
            count = cursor.fetchone()[0]
            stats[f"{status.value}_count"] = count
            
        # Queue size
        stats['queue_size'] = self.task_queue.qsize()
        stats['running_tasks'] = len(self.running_tasks)
        stats['max_workers'] = self.max_workers
        
        # Recent activity
        cursor.execute('''
            SELECT COUNT(*) FROM tasks 
            WHERE created_at > datetime('now', '-1 hour')
        ''')
        stats['tasks_last_hour'] = cursor.fetchone()[0]
        
        conn.close()
        return stats
        
    # Task implementations
    def _process_document_task(self, task: Task, case_id: int, file_info: dict, uploaded_by: int = None):
        """Process a single document"""
        from core.document_processor import document_processor
        
        task.progress = 10.0
        self._save_task(task)
        
        result = document_processor.process_document(case_id, file_info, uploaded_by)
        
        task.progress = 100.0
        self._save_task(task)
        
        return result
        
    def _batch_process_documents_task(self, task: Task, case_id: int, file_infos: list, uploaded_by: int = None):
        """Process multiple documents in batch"""
        from core.document_processor import document_processor
        
        results = []
        total_files = len(file_infos)
        
        for i, file_info in enumerate(file_infos):
            try:
                result = document_processor.process_document(case_id, file_info, uploaded_by)
                results.append(result)
                
                # Update progress
                task.progress = ((i + 1) / total_files) * 100
                self._save_task(task)
                
            except Exception as e:
                logger.error(f"Error processing file {file_info.get('filename', 'unknown')}: {e}")
                results.append({'success': False, 'error': str(e)})
                
        return {
            'total_processed': len(results),
            'successful': len([r for r in results if r.get('success', False)]),
            'failed': len([r for r in results if not r.get('success', False)]),
            'results': results
        }
        
    def _extract_text_task(self, task: Task, file_path: str, file_type: str):
        """Extract text from a file"""
        from core.document_processor import document_processor
        
        task.progress = 50.0
        self._save_task(task)
        
        success, text_content = document_processor.extract_text_content(file_path, file_type)
        
        task.progress = 100.0
        self._save_task(task)
        
        return {'success': success, 'text_content': text_content}
        
    def _create_embeddings_task(self, task: Task, case_id: int, documents: list):
        """Create embeddings for documents"""
        # Implementation would go here
        task.progress = 100.0
        self._save_task(task)
        return {'embeddings_created': len(documents)}
        
    def _update_knowledge_base_task(self, task: Task, case_id: int):
        """Update knowledge base for case"""
        # Implementation would go here
        task.progress = 100.0
        self._save_task(task)
        return {'knowledge_base_updated': True}
        
    def _check_url_task(self, task: Task, url: str, case_id: int):
        """Check a URL for changes"""
        # Implementation would go here
        task.progress = 100.0
        self._save_task(task)
        return {'url_checked': url, 'changes_detected': False}
        
    def _batch_check_urls_task(self, task: Task, urls: list):
        """Check multiple URLs for changes"""
        results = []
        total_urls = len(urls)
        
        for i, url_info in enumerate(urls):
            # Simulate URL checking
            results.append({'url': url_info['url'], 'changes_detected': False})
            
            task.progress = ((i + 1) / total_urls) * 100
            self._save_task(task)
            
        return {'urls_checked': len(results), 'results': results}
        
    def _cleanup_old_files_task(self, task: Task, days_old: int = 30):
        """Clean up old files"""
        # Implementation would go here
        task.progress = 100.0
        self._save_task(task)
        return {'files_cleaned': 0}
        
    def _backup_database_task(self, task: Task, backup_path: str):
        """Backup database"""
        # Implementation would go here
        task.progress = 100.0
        self._save_task(task)
        return {'backup_created': backup_path}
        
    def _generate_report_task(self, task: Task, case_id: int, report_type: str):
        """Generate case report"""
        # Implementation would go here
        task.progress = 100.0
        self._save_task(task)
        return {'report_generated': True, 'report_type': report_type}

# Global queue manager instance
queue_manager = QueueManager()
