"""
Specialized Usability Tests for Legal Case Management System
Focus on user experience, accessibility, and interface design
"""

import pytest
import asyncio
import time
from playwright.async_api import async_playwright, <PERSON>, Browser, BrowserContext
from pathlib import Path

class TestUsabilityAndUX:
    """Comprehensive usability testing suite"""
    
    @pytest.fixture(scope="session")
    async def browser_setup(self):
        """Setup browser for usability testing"""
        playwright = await async_playwright().start()
        browser = await playwright.chromium.launch(
            headless=False, 
            slow_mo=300,  # Slower for usability observation
            args=['--disable-web-security', '--disable-features=VizDisplayCompositor']
        )
        context = await browser.new_context(
            viewport={'width': 1920, 'height': 1080},
            user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            record_video_dir="test_videos/"  # Record videos for usability analysis
        )
        page = await context.new_page()
        
        yield page, browser, context, playwright
        
        await context.close()
        await browser.close()
        await playwright.stop()
    
    @pytest.fixture
    async def page(self, browser_setup):
        """Get page instance for usability testing"""
        page, browser, context, playwright = browser_setup
        await page.goto("http://localhost:8501")
        await page.wait_for_load_state("networkidle")
        return page
    
    # ========================================
    # VISUAL DESIGN & LAYOUT TESTS
    # ========================================
    
    @pytest.mark.asyncio
    async def test_visual_hierarchy_and_layout(self, page: Page):
        """Test visual hierarchy and layout effectiveness"""
        # Check main title prominence
        main_title = page.locator("h1")
        await main_title.wait_for(state="visible")
        
        # Get title styling
        title_styles = await main_title.evaluate("""
            element => {
                const styles = getComputedStyle(element);
                return {
                    fontSize: styles.fontSize,
                    fontWeight: styles.fontWeight,
                    color: styles.color,
                    marginBottom: styles.marginBottom
                };
            }
        """)
        
        # Title should be prominent
        font_size = float(title_styles['fontSize'].replace('px', ''))
        assert font_size >= 24, f"Main title font size should be at least 24px, got {font_size}px"
        
        # Check section spacing
        sections = page.locator("h2, h3")
        section_count = await sections.count()
        if section_count > 1:
            # Check spacing between sections
            for i in range(min(3, section_count - 1)):
                section = sections.nth(i)
                margin_bottom = await section.evaluate("element => getComputedStyle(element).marginBottom")
                margin_value = float(margin_bottom.replace('px', ''))
                assert margin_value >= 8, f"Section {i} should have adequate spacing"
    
    @pytest.mark.asyncio
    async def test_color_contrast_and_readability(self, page: Page):
        """Test color contrast for accessibility and readability"""
        # Test main text readability
        text_elements = page.locator("p, span, div").filter(has_text=True)
        
        if await text_elements.count() > 0:
            first_text = text_elements.first
            text_styles = await first_text.evaluate("""
                element => {
                    const styles = getComputedStyle(element);
                    return {
                        color: styles.color,
                        backgroundColor: styles.backgroundColor,
                        fontSize: styles.fontSize
                    };
                }
            """)
            
            # Font size should be readable
            font_size = float(text_styles['fontSize'].replace('px', ''))
            assert font_size >= 14, f"Text should be at least 14px for readability, got {font_size}px"
    
    @pytest.mark.asyncio
    async def test_button_design_and_usability(self, page: Page):
        """Test button design and usability standards"""
        buttons = page.locator("button")
        button_count = await buttons.count()
        
        if button_count > 0:
            # Test first few buttons
            for i in range(min(5, button_count)):
                button = buttons.nth(i)
                
                # Check button is visible and clickable
                assert await button.is_visible(), f"Button {i} should be visible"
                assert await button.is_enabled(), f"Button {i} should be enabled"
                
                # Check button sizing
                box = await button.bounding_box()
                if box:
                    # Buttons should be at least 44px tall for touch accessibility
                    assert box['height'] >= 32, f"Button {i} should be at least 32px tall for usability"
                    assert box['width'] >= 60, f"Button {i} should be at least 60px wide"
                
                # Test hover state
                await button.hover()
                await page.wait_for_timeout(200)
                
                # Check cursor changes to pointer
                cursor = await button.evaluate("element => getComputedStyle(element).cursor")
                assert cursor == "pointer", f"Button {i} should show pointer cursor on hover"
    
    @pytest.mark.asyncio
    async def test_form_usability(self, page: Page):
        """Test form design and usability"""
        # Look for form inputs
        inputs = page.locator("input, textarea, select")
        input_count = await inputs.count()
        
        if input_count > 0:
            for i in range(min(3, input_count)):
                input_elem = inputs.nth(i)
                
                # Check input sizing
                box = await input_elem.bounding_box()
                if box:
                    assert box['height'] >= 32, f"Input {i} should be at least 32px tall"
                
                # Test focus state
                await input_elem.focus()
                await page.wait_for_timeout(200)
                
                # Check focus indicator
                outline = await input_elem.evaluate("element => getComputedStyle(element).outline")
                border = await input_elem.evaluate("element => getComputedStyle(element).border")
                # Should have some form of focus indicator
                assert outline != "none" or "focus" in border, f"Input {i} should have focus indicator"
    
    # ========================================
    # INTERACTION & NAVIGATION TESTS
    # ========================================
    
    @pytest.mark.asyncio
    async def test_navigation_intuitiveness(self, page: Page):
        """Test navigation clarity and intuitiveness"""
        # Test tab navigation
        tabs = page.locator("[role='tab']")
        tab_count = await tabs.count()
        
        if tab_count > 0:
            # Test each tab is clearly labeled
            for i in range(tab_count):
                tab = tabs.nth(i)
                tab_text = await tab.inner_text()
                
                # Tab should have meaningful text
                assert len(tab_text.strip()) > 0, f"Tab {i} should have descriptive text"
                assert len(tab_text.strip()) < 50, f"Tab {i} text should be concise"
                
                # Test tab activation
                await tab.click()
                await page.wait_for_timeout(500)
                
                # Check tab becomes active
                is_selected = await tab.get_attribute("aria-selected")
                class_name = await tab.get_attribute("class")
                assert is_selected == "true" or "selected" in (class_name or ""), f"Tab {i} should show active state"
    
    @pytest.mark.asyncio
    async def test_feedback_and_status_indicators(self, page: Page):
        """Test user feedback and status indicators"""
        # Check for status indicators
        status_indicators = page.locator(".stAlert, .stSuccess, .stError, .stWarning, .stInfo")
        
        # Navigate to upload section to trigger status messages
        doc_tab = page.locator("[role='tab']:has-text('Document Viewer')")
        if await doc_tab.count() > 0:
            await doc_tab.click()
            await page.wait_for_timeout(1000)
            
            upload_tab = page.locator("[role='tab']:has-text('Upload')")
            if await upload_tab.count() > 0:
                await upload_tab.click()
                await page.wait_for_timeout(1000)
                
                # Look for status messages
                status_messages = page.locator(".stAlert, .stSuccess, .stError")
                status_count = await status_messages.count()
                
                # If status messages exist, they should be clearly visible
                if status_count > 0:
                    first_status = status_messages.first
                    assert await first_status.is_visible(), "Status messages should be clearly visible"
    
    @pytest.mark.asyncio
    async def test_loading_and_progress_indicators(self, page: Page):
        """Test loading states and progress feedback"""
        # Test page load indicators
        await page.reload()
        
        # Look for loading indicators during page load
        loading_indicators = page.locator(".stSpinner, [data-testid='stSpinner'], .loading")
        
        # Wait for page to fully load
        await page.wait_for_load_state("networkidle")
        
        # Loading indicators should disappear after loading
        final_loading_count = await loading_indicators.count()
        # Most loading indicators should be gone after page loads
        assert final_loading_count <= 1, "Loading indicators should disappear after page loads"
    
    @pytest.mark.asyncio
    async def test_error_handling_ux(self, page: Page):
        """Test error handling user experience"""
        # Test graceful error handling
        # Navigate to a section that might have errors
        chat_tab = page.locator("[role='tab']:has-text('Chat Analysis')")
        if await chat_tab.count() > 0:
            await chat_tab.click()
            await page.wait_for_timeout(2000)
            
            # Look for any error messages
            error_messages = page.locator(".stError, .stException, [role='alert']")
            error_count = await error_messages.count()
            
            # If errors exist, they should be user-friendly
            if error_count > 0:
                first_error = error_messages.first
                error_text = await first_error.inner_text()
                
                # Error should not contain technical stack traces visible to user
                assert "Traceback" not in error_text, "Errors should be user-friendly, not technical"
                assert len(error_text) > 10, "Error messages should be descriptive"
    
    # ========================================
    # MOBILE & RESPONSIVE TESTS
    # ========================================
    
    @pytest.mark.asyncio
    async def test_mobile_usability(self, page: Page):
        """Test mobile device usability"""
        # Test mobile viewport
        await page.set_viewport_size({'width': 375, 'height': 667})
        await page.wait_for_timeout(1000)
        
        # Check main content is accessible
        main_content = page.locator("main, [data-testid='stAppViewContainer']")
        assert await main_content.is_visible(), "Main content should be visible on mobile"
        
        # Check touch targets are adequate size
        buttons = page.locator("button")
        if await buttons.count() > 0:
            first_button = buttons.first
            box = await first_button.bounding_box()
            if box:
                # Touch targets should be at least 44px for mobile
                assert box['height'] >= 40, "Mobile touch targets should be at least 40px tall"
        
        # Test horizontal scrolling (should be minimal)
        body_width = await page.evaluate("document.body.scrollWidth")
        viewport_width = 375
        assert body_width <= viewport_width + 20, "Should not require horizontal scrolling on mobile"
    
    @pytest.mark.asyncio
    async def test_tablet_usability(self, page: Page):
        """Test tablet device usability"""
        # Test tablet viewport
        await page.set_viewport_size({'width': 768, 'height': 1024})
        await page.wait_for_timeout(1000)
        
        # Check layout adapts appropriately
        tabs = page.locator("[role='tab']")
        if await tabs.count() > 0:
            # Tabs should still be accessible on tablet
            first_tab = tabs.first
            assert await first_tab.is_visible(), "Navigation should be accessible on tablet"
    
    # ========================================
    # PERFORMANCE & RESPONSIVENESS TESTS
    # ========================================
    
    @pytest.mark.asyncio
    async def test_interaction_responsiveness(self, page: Page):
        """Test UI responsiveness to user interactions"""
        # Test button click responsiveness
        buttons = page.locator("button")
        if await buttons.count() > 0:
            first_button = buttons.first
            
            # Measure click response time
            start_time = time.time()
            await first_button.click()
            await page.wait_for_timeout(100)  # Small delay for UI update
            response_time = time.time() - start_time
            
            # UI should respond within reasonable time
            assert response_time < 1.0, f"Button click should respond within 1 second, took {response_time:.2f}s"
    
    @pytest.mark.asyncio
    async def test_scroll_performance(self, page: Page):
        """Test scrolling performance and smoothness"""
        # Test page scrolling
        page_height = await page.evaluate("document.body.scrollHeight")
        viewport_height = await page.evaluate("window.innerHeight")
        
        if page_height > viewport_height:
            # Test smooth scrolling
            await page.evaluate("window.scrollTo({top: 500, behavior: 'smooth'})")
            await page.wait_for_timeout(1000)
            
            # Check scroll position
            scroll_position = await page.evaluate("window.pageYOffset")
            assert scroll_position > 0, "Page should scroll smoothly"
