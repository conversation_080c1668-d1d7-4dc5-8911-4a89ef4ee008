"""
Comprehensive Playwright Tests for Legal Case Management System
Tests both functionality and usability aspects
"""

import pytest
import pytest_asyncio
import asyncio
import time
from playwright.async_api import async_playwright, <PERSON>, <PERSON>rowser, BrowserContext
from pathlib import Path
import tempfile
import os

class TestLegalCaseManagementSystem:
    """Test suite for Legal Case Management System"""
    
    @pytest_asyncio.fixture(scope="session")
    async def browser_setup(self):
        """Setup browser for testing"""
        playwright = await async_playwright().start()
        browser = await playwright.chromium.launch(headless=True, slow_mo=100)
        context = await browser.new_context(
            viewport={'width': 1920, 'height': 1080},
            user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        )
        page = await context.new_page()
        # Set page timeout to 30 seconds
        page.set_default_timeout(30000)
        
        yield page, browser, context, playwright
        
        await context.close()
        await browser.close()
        await playwright.stop()
    
    @pytest.fixture
    async def page(self, browser_setup):
        """Get page instance"""
        page, browser, context, playwright = browser_setup
        await page.goto("http://localhost:8501", timeout=30000)
        await page.wait_for_load_state("networkidle", timeout=30000)
        return page
    
    # ========================================
    # FUNCTIONALITY TESTS
    # ========================================
    
    @pytest.mark.asyncio
    @pytest.mark.asyncio
    async def test_app_loads_successfully(self, page: Page):
        """Test that the application loads without errors"""
        # Check page title
        title = await page.title()
        assert "Legal Case Management System" in title
        
        # Check main heading is visible
        heading = page.locator("h1:has-text('CUYAHOGA COUNTY PRO SE Litigation Management System')")
        await heading.wait_for(state="visible", timeout=10000)
        assert await heading.is_visible()
        
        # Check no error messages on load
        error_elements = page.locator("[data-testid*='error'], .stException, .stAlert[kind='error']")
        error_count = await error_elements.count()
        assert error_count == 0, "Application loaded with errors"
    
    @pytest.mark.asyncio
    async def test_security_status_indicators(self, page: Page):
        """Test security status indicators are present and correct"""
        # Check SSL/TLS indicator
        ssl_indicator = page.locator("text=🔐 SSL/TLS Active")
        await ssl_indicator.wait_for(state="visible")
        assert await ssl_indicator.is_visible()
        
        # Check PGP Encryption indicator
        pgp_indicator = page.locator("text=🔑 PGP Encryption")
        await pgp_indicator.wait_for(state="visible")
        assert await pgp_indicator.is_visible()
        
        # Check Vault Integration indicator
        vault_indicator = page.locator("text=🏛️ Vault Integration")
        await vault_indicator.wait_for(state="visible")
        assert await vault_indicator.is_visible()
    
    @pytest.mark.asyncio
    async def test_case_selection_functionality(self, page: Page):
        """Test case selection dropdown functionality"""
        # Find case selection dropdown
        case_dropdown = page.locator("select, [role='combobox']").first
        await case_dropdown.wait_for(state="visible")
        
        # Click to open dropdown
        await case_dropdown.click()
        await page.wait_for_timeout(500)
        
        # Check if "Create New Case..." option exists
        create_option = page.locator("text=Create New Case...")
        assert await create_option.is_visible()
        
        # Check default case is selected
        shepov_case = page.locator("text=SHEPOV vs SHEPOV")
        assert await shepov_case.is_visible()
    
    @pytest.mark.asyncio
    async def test_tab_navigation(self, page: Page):
        """Test all main tabs are accessible and functional"""
        tabs = [
            "💬 Chat Analysis",
            "🖼️ Document Viewer", 
            "⚖️ E-Filing Integration",
            "📊 Case Timeline",
            "📥 Export & Reports",
            "🔧 System Monitor"
        ]
        
        for tab_name in tabs:
            tab = page.locator(f"[role='tab']:has-text('{tab_name}')")
            await tab.wait_for(state="visible")
            await tab.click()
            await page.wait_for_timeout(1000)
            
            # Check tab is selected
            assert await tab.get_attribute("aria-selected") == "true" or "selected" in await tab.get_attribute("class")
    
    @pytest.mark.asyncio
    async def test_document_upload_interface(self, page: Page):
        """Test document upload interface functionality"""
        # Navigate to Document Viewer tab
        doc_tab = page.locator("[role='tab']:has-text('🖼️ Document Viewer')")
        await doc_tab.click()
        await page.wait_for_timeout(1000)
        
        # Navigate to Upload sub-tab
        upload_tab = page.locator("[role='tab']:has-text('⬆️ Upload')")
        await upload_tab.click()
        await page.wait_for_timeout(1000)
        
        # Test upload method selection
        upload_methods = [
            "📄 Single Document",
            "📁 Multiple Files", 
            "🗜️ ZIP Archive",
            "🌐 URL/Link"
        ]
        
        for method in upload_methods:
            radio_button = page.locator(f"input[type='radio'] + label:has-text('{method}')")
            if await radio_button.count() > 0:
                await radio_button.click()
                await page.wait_for_timeout(500)
    
    @pytest.mark.asyncio
    async def test_ai_model_selection(self, page: Page):
        """Test AI model selection functionality"""
        # Navigate to Chat Analysis tab
        chat_tab = page.locator("[role='tab']:has-text('💬 Chat Analysis')")
        await chat_tab.click()
        await page.wait_for_timeout(1000)
        
        # Find model dropdown
        model_dropdown = page.locator("select").first
        if await model_dropdown.count() > 0:
            await model_dropdown.click()
            await page.wait_for_timeout(500)
            
            # Check for expected models
            expected_models = ["GPT-4o", "Claude 3.5 Sonnet", "GPT-4o Mini"]
            for model in expected_models:
                model_option = page.locator(f"option:has-text('{model}')")
                if await model_option.count() > 0:
                    assert await model_option.is_visible()
    
    # ========================================
    # USABILITY TESTS
    # ========================================
    
    @pytest.mark.asyncio
    async def test_page_load_performance(self, page: Page):
        """Test page load performance and responsiveness"""
        start_time = time.time()
        
        await page.goto("http://localhost:8501")
        await page.wait_for_load_state("networkidle")
        
        # Check main content is visible within reasonable time
        main_heading = page.locator("h1")
        await main_heading.wait_for(state="visible", timeout=5000)
        
        load_time = time.time() - start_time
        assert load_time < 10, f"Page took too long to load: {load_time:.2f}s"
    
    @pytest.mark.asyncio
    async def test_responsive_design(self, page: Page):
        """Test responsive design at different viewport sizes"""
        viewports = [
            {'width': 1920, 'height': 1080},  # Desktop
            {'width': 1366, 'height': 768},   # Laptop
            {'width': 768, 'height': 1024},   # Tablet
            {'width': 375, 'height': 667}     # Mobile
        ]
        
        for viewport in viewports:
            await page.set_viewport_size(viewport)
            await page.wait_for_timeout(1000)
            
            # Check main heading is still visible
            heading = page.locator("h1")
            assert await heading.is_visible(), f"Heading not visible at {viewport['width']}x{viewport['height']}"
            
            # Check navigation elements are accessible
            if viewport['width'] >= 768:  # Desktop/Tablet
                tabs = page.locator("[role='tab']")
                tab_count = await tabs.count()
                assert tab_count > 0, "No tabs visible on larger screens"
    
    @pytest.mark.asyncio
    async def test_keyboard_navigation(self, page: Page):
        """Test keyboard navigation accessibility"""
        # Test Tab key navigation
        await page.keyboard.press("Tab")
        await page.wait_for_timeout(200)
        
        # Check if focus is visible
        focused_element = await page.evaluate("document.activeElement.tagName")
        assert focused_element is not None
        
        # Test Enter key on focused elements
        await page.keyboard.press("Enter")
        await page.wait_for_timeout(500)
    
    @pytest.mark.asyncio
    async def test_visual_feedback_on_interactions(self, page: Page):
        """Test visual feedback for user interactions"""
        # Test button hover effects
        buttons = page.locator("button")
        if await buttons.count() > 0:
            first_button = buttons.first
            
            # Hover over button
            await first_button.hover()
            await page.wait_for_timeout(300)
            
            # Check if button style changes (cursor should be pointer)
            cursor_style = await first_button.evaluate("element => getComputedStyle(element).cursor")
            assert cursor_style == "pointer", "Button should show pointer cursor on hover"
    
    @pytest.mark.asyncio
    async def test_error_handling_and_user_feedback(self, page: Page):
        """Test error handling and user feedback mechanisms"""
        # Navigate to upload section
        doc_tab = page.locator("[role='tab']:has-text('🖼️ Document Viewer')")
        await doc_tab.click()
        await page.wait_for_timeout(1000)
        
        upload_tab = page.locator("[role='tab']:has-text('⬆️ Upload')")
        await upload_tab.click()
        await page.wait_for_timeout(1000)
        
        # Try to process without uploading files (should show appropriate feedback)
        process_button = page.locator("button:has-text('Upload & Process')")
        if await process_button.count() > 0:
            await process_button.click()
            await page.wait_for_timeout(2000)
            
            # Check for user feedback (success or error messages)
            feedback_elements = page.locator(".stAlert, .stSuccess, .stError, .stWarning")
            feedback_count = await feedback_elements.count()
            # Should have some feedback, either success or appropriate error
            assert feedback_count >= 0  # Allow for different feedback scenarios
    
    @pytest.mark.asyncio
    async def test_content_readability(self, page: Page):
        """Test content readability and accessibility"""
        # Check text contrast and readability
        main_content = page.locator("main, .main, [data-testid='stAppViewContainer']")
        await main_content.wait_for(state="visible")
        
        # Check font sizes are reasonable
        headings = page.locator("h1, h2, h3")
        if await headings.count() > 0:
            first_heading = headings.first
            font_size = await first_heading.evaluate("element => getComputedStyle(element).fontSize")
            # Font size should be at least 16px for readability
            font_size_num = float(font_size.replace('px', ''))
            assert font_size_num >= 16, f"Heading font size too small: {font_size}"
    
    @pytest.mark.asyncio
    async def test_loading_states_and_spinners(self, page: Page):
        """Test loading states and progress indicators"""
        # Navigate to a section that might show loading states
        chat_tab = page.locator("[role='tab']:has-text('💬 Chat Analysis')")
        await chat_tab.click()
        await page.wait_for_timeout(1000)
        
        # Look for loading indicators
        loading_indicators = page.locator(".stSpinner, [data-testid='stSpinner'], .loading")
        # Loading indicators should appear and disappear appropriately
        # This test ensures the UI provides feedback during operations
    
    @pytest.mark.asyncio
    async def test_form_validation_feedback(self, page: Page):
        """Test form validation and user input feedback"""
        # Test case creation form if available
        case_dropdown = page.locator("select, [role='combobox']").first
        if await case_dropdown.count() > 0:
            await case_dropdown.click()
            await page.wait_for_timeout(500)
            
            create_option = page.locator("text=Create New Case...")
            if await create_option.count() > 0:
                await create_option.click()
                await page.wait_for_timeout(1000)
                
                # Look for form fields
                form_fields = page.locator("input[type='text'], textarea")
                if await form_fields.count() > 0:
                    # Test empty form submission
                    submit_button = page.locator("button[type='submit'], button:has-text('Create'), button:has-text('Save')")
                    if await submit_button.count() > 0:
                        await submit_button.click()
                        await page.wait_for_timeout(1000)
                        
                        # Check for validation messages
                        validation_messages = page.locator(".stError, [role='alert'], .error")
                        # Should provide appropriate feedback for form validation

    # ========================================
    # INTEGRATION TESTS
    # ========================================

    @pytest.mark.asyncio
    async def test_end_to_end_document_workflow(self, page: Page):
        """Test complete document upload and processing workflow"""
        # Navigate to document upload
        doc_tab = page.locator("[role='tab']:has-text('🖼️ Document Viewer')")
        await doc_tab.click()
        await page.wait_for_timeout(1000)

        upload_tab = page.locator("[role='tab']:has-text('⬆️ Upload')")
        await upload_tab.click()
        await page.wait_for_timeout(1000)

        # Select multiple files option
        multiple_files_radio = page.locator("input[type='radio'] + label:has-text('📁 Multiple Files')")
        if await multiple_files_radio.count() > 0:
            await multiple_files_radio.click()
            await page.wait_for_timeout(500)

            # Create test file for upload
            with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
                f.write("Test legal document content for automated testing")
                test_file_path = f.name

            try:
                # Upload file
                file_input = page.locator("input[type='file']")
                if await file_input.count() > 0:
                    await file_input.set_input_files(test_file_path)
                    await page.wait_for_timeout(2000)

                    # Check file appears in upload list
                    uploaded_file = page.locator("text=.txt")
                    if await uploaded_file.count() > 0:
                        # Process the file
                        process_button = page.locator("button:has-text('Upload & Process')")
                        if await process_button.count() > 0:
                            await process_button.click()
                            await page.wait_for_timeout(5000)

                            # Check for success message
                            success_message = page.locator(".stSuccess, text=Successfully processed")
                            success_count = await success_message.count()
                            assert success_count > 0, "Document processing should show success message"

            finally:
                # Clean up test file
                if os.path.exists(test_file_path):
                    os.unlink(test_file_path)

    @pytest.mark.asyncio
    async def test_accessibility_compliance(self, page: Page):
        """Test basic accessibility compliance"""
        # Check for proper heading hierarchy
        h1_count = await page.locator("h1").count()
        assert h1_count >= 1, "Page should have at least one H1 heading"

        # Check for alt text on images
        images = page.locator("img")
        image_count = await images.count()
        if image_count > 0:
            for i in range(min(5, image_count)):  # Check first 5 images
                img = images.nth(i)
                alt_text = await img.get_attribute("alt")
                src = await img.get_attribute("src")
                # Allow decorative images or icons to have empty alt text
                if src and not src.startswith("data:image"):
                    assert alt_text is not None, f"Image {i} should have alt text"

        # Check for proper form labels
        inputs = page.locator("input[type='text'], input[type='email'], textarea")
        input_count = await inputs.count()
        if input_count > 0:
            for i in range(min(3, input_count)):  # Check first 3 inputs
                input_elem = inputs.nth(i)
                # Check for associated label or aria-label
                input_id = await input_elem.get_attribute("id")
                aria_label = await input_elem.get_attribute("aria-label")

                if input_id:
                    label = page.locator(f"label[for='{input_id}']")
                    label_exists = await label.count() > 0
                    assert label_exists or aria_label, f"Input {i} should have proper labeling"
