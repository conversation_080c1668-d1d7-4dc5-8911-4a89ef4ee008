"""
Error Recovery and Resilience System for Legal Case Management
Provides robust error handling, retry logic, circuit breakers, and graceful degradation.
"""

import logging
import time
import functools
import threading
from typing import Dict, List, Optional, Callable, Any, Type
from datetime import datetime, timedelta
from enum import Enum
import traceback
import json
import sqlite3
from dataclasses import dataclass
import asyncio
from concurrent.futures import Thread<PERSON>oolExecutor, TimeoutError
import requests
from contextlib import contextmanager

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ErrorSeverity(Enum):
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

class CircuitState(Enum):
    CLOSED = "closed"      # Normal operation
    OPEN = "open"          # Failing, reject requests
    HALF_OPEN = "half_open"  # Testing if service recovered

@dataclass
class ErrorRecord:
    """Record of an error occurrence"""
    id: str
    timestamp: datetime
    error_type: str
    error_message: str
    severity: ErrorSeverity
    context: Dict[str, Any]
    stack_trace: str
    resolved: bool = False
    resolution_notes: Optional[str] = None

class CircuitBreaker:
    """Circuit breaker pattern implementation"""
    
    def __init__(self, failure_threshold: int = 5, recovery_timeout: int = 60, 
                 expected_exception: Type[Exception] = Exception):
        self.failure_threshold = failure_threshold
        self.recovery_timeout = recovery_timeout
        self.expected_exception = expected_exception
        
        self.failure_count = 0
        self.last_failure_time = None
        self.state = CircuitState.CLOSED
        self.lock = threading.Lock()
        
    def __call__(self, func):
        """Decorator to apply circuit breaker"""
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            return self._call(func, *args, **kwargs)
        return wrapper
        
    def _call(self, func, *args, **kwargs):
        """Execute function with circuit breaker logic"""
        with self.lock:
            if self.state == CircuitState.OPEN:
                if self._should_attempt_reset():
                    self.state = CircuitState.HALF_OPEN
                else:
                    raise Exception("Circuit breaker is OPEN")
                    
        try:
            result = func(*args, **kwargs)
            self._on_success()
            return result
            
        except self.expected_exception as e:
            self._on_failure()
            raise
            
    def _should_attempt_reset(self) -> bool:
        """Check if enough time has passed to attempt reset"""
        return (self.last_failure_time and 
                time.time() - self.last_failure_time >= self.recovery_timeout)
                
    def _on_success(self):
        """Handle successful execution"""
        with self.lock:
            self.failure_count = 0
            self.state = CircuitState.CLOSED
            
    def _on_failure(self):
        """Handle failed execution"""
        with self.lock:
            self.failure_count += 1
            self.last_failure_time = time.time()
            
            if self.failure_count >= self.failure_threshold:
                self.state = CircuitState.OPEN

class RetryManager:
    """Manages retry logic with exponential backoff"""
    
    def __init__(self, max_retries: int = 3, base_delay: float = 1.0, 
                 max_delay: float = 60.0, backoff_factor: float = 2.0):
        self.max_retries = max_retries
        self.base_delay = base_delay
        self.max_delay = max_delay
        self.backoff_factor = backoff_factor
        
    def __call__(self, func):
        """Decorator to apply retry logic"""
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            return self._retry_call(func, *args, **kwargs)
        return wrapper
        
    def _retry_call(self, func, *args, **kwargs):
        """Execute function with retry logic"""
        last_exception = None
        
        for attempt in range(self.max_retries + 1):
            try:
                return func(*args, **kwargs)
                
            except Exception as e:
                last_exception = e
                
                if attempt == self.max_retries:
                    logger.error(f"Function {func.__name__} failed after {self.max_retries} retries: {e}")
                    raise
                    
                delay = min(self.base_delay * (self.backoff_factor ** attempt), self.max_delay)
                logger.warning(f"Function {func.__name__} failed (attempt {attempt + 1}), retrying in {delay}s: {e}")
                time.sleep(delay)
                
        raise last_exception

class ErrorRecoveryManager:
    """Central error recovery and monitoring system"""
    
    def __init__(self, db_path: str = "error_recovery.db"):
        self.db_path = db_path
        self.error_handlers = {}
        self.circuit_breakers = {}
        self.retry_managers = {}
        self.error_records = []
        
        self.init_database()
        self.register_default_handlers()
        
    def init_database(self):
        """Initialize error tracking database"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS error_records (
                id TEXT PRIMARY KEY,
                timestamp TIMESTAMP,
                error_type TEXT,
                error_message TEXT,
                severity TEXT,
                context TEXT,
                stack_trace TEXT,
                resolved BOOLEAN DEFAULT 0,
                resolution_notes TEXT
            )
        ''')
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS system_health (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                component TEXT,
                status TEXT,
                metrics TEXT,
                alerts TEXT
            )
        ''')
        
        conn.commit()
        conn.close()
        
    def register_error_handler(self, error_type: str, handler: Callable):
        """Register custom error handler"""
        self.error_handlers[error_type] = handler
        logger.info(f"Registered error handler for: {error_type}")
        
    def register_default_handlers(self):
        """Register default error handlers"""
        self.register_error_handler("DatabaseError", self._handle_database_error)
        self.register_error_handler("NetworkError", self._handle_network_error)
        self.register_error_handler("FileSystemError", self._handle_filesystem_error)
        self.register_error_handler("AuthenticationError", self._handle_auth_error)
        self.register_error_handler("ValidationError", self._handle_validation_error)
        
    def handle_error(self, error: Exception, context: Dict[str, Any] = None, 
                    severity: ErrorSeverity = ErrorSeverity.MEDIUM) -> bool:
        """Handle an error with appropriate recovery strategy"""
        try:
            # Create error record
            error_record = ErrorRecord(
                id=f"err_{int(time.time() * 1000)}",
                timestamp=datetime.now(),
                error_type=type(error).__name__,
                error_message=str(error),
                severity=severity,
                context=context or {},
                stack_trace=traceback.format_exc()
            )
            
            # Log error
            self._log_error(error_record)
            
            # Try to recover
            recovery_success = self._attempt_recovery(error_record)
            
            # Update record with resolution
            if recovery_success:
                error_record.resolved = True
                error_record.resolution_notes = "Automatically recovered"
                
            # Save to database
            self._save_error_record(error_record)
            
            return recovery_success
            
        except Exception as recovery_error:
            logger.critical(f"Error in error recovery system: {recovery_error}")
            return False
            
    def _log_error(self, error_record: ErrorRecord):
        """Log error with appropriate level"""
        log_message = f"[{error_record.severity.value.upper()}] {error_record.error_type}: {error_record.error_message}"
        
        if error_record.severity == ErrorSeverity.CRITICAL:
            logger.critical(log_message)
        elif error_record.severity == ErrorSeverity.HIGH:
            logger.error(log_message)
        elif error_record.severity == ErrorSeverity.MEDIUM:
            logger.warning(log_message)
        else:
            logger.info(log_message)
            
    def _attempt_recovery(self, error_record: ErrorRecord) -> bool:
        """Attempt to recover from error"""
        error_type = error_record.error_type
        
        # Try specific handler
        if error_type in self.error_handlers:
            try:
                return self.error_handlers[error_type](error_record)
            except Exception as e:
                logger.error(f"Error handler failed for {error_type}: {e}")
                
        # Try generic recovery strategies
        return self._generic_recovery(error_record)
        
    def _generic_recovery(self, error_record: ErrorRecord) -> bool:
        """Generic recovery strategies"""
        try:
            # For database errors, try reconnection
            if "database" in error_record.error_message.lower():
                return self._recover_database_connection()
                
            # For network errors, try reconnection
            if any(keyword in error_record.error_message.lower() 
                  for keyword in ["network", "connection", "timeout", "unreachable"]):
                return self._recover_network_connection(error_record.context)
                
            # For file system errors, try cleanup
            if any(keyword in error_record.error_message.lower() 
                  for keyword in ["file", "directory", "permission", "disk"]):
                return self._recover_filesystem_issue(error_record.context)
                
            return False
            
        except Exception as e:
            logger.error(f"Generic recovery failed: {e}")
            return False
            
    def _save_error_record(self, error_record: ErrorRecord):
        """Save error record to database"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT OR REPLACE INTO error_records 
            (id, timestamp, error_type, error_message, severity, context, 
             stack_trace, resolved, resolution_notes)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            error_record.id, error_record.timestamp, error_record.error_type,
            error_record.error_message, error_record.severity.value,
            json.dumps(error_record.context), error_record.stack_trace,
            error_record.resolved, error_record.resolution_notes
        ))
        
        conn.commit()
        conn.close()
        
    # Specific error handlers
    def _handle_database_error(self, error_record: ErrorRecord) -> bool:
        """Handle database-related errors"""
        try:
            # Try to reconnect to database
            if self._recover_database_connection():
                logger.info("Database connection recovered")
                return True
                
            # Try to repair database if corrupted
            if "corrupt" in error_record.error_message.lower():
                return self._repair_database()
                
            return False
            
        except Exception as e:
            logger.error(f"Database error recovery failed: {e}")
            return False
            
    def _handle_network_error(self, error_record: ErrorRecord) -> bool:
        """Handle network-related errors"""
        try:
            # Test network connectivity
            if self._test_network_connectivity():
                logger.info("Network connectivity restored")
                return True
                
            # Try alternative endpoints if available
            if "url" in error_record.context:
                return self._try_alternative_endpoints(error_record.context["url"])
                
            return False
            
        except Exception as e:
            logger.error(f"Network error recovery failed: {e}")
            return False
            
    def _handle_filesystem_error(self, error_record: ErrorRecord) -> bool:
        """Handle file system errors"""
        try:
            # Check disk space
            if self._check_disk_space():
                # Try to clean up temporary files
                self._cleanup_temp_files()
                return True
                
            # Try to create missing directories
            if "directory" in error_record.error_message.lower():
                return self._create_missing_directories(error_record.context)
                
            return False
            
        except Exception as e:
            logger.error(f"Filesystem error recovery failed: {e}")
            return False
            
    def _handle_auth_error(self, error_record: ErrorRecord) -> bool:
        """Handle authentication errors"""
        try:
            # Try to refresh authentication tokens
            if "token" in error_record.error_message.lower():
                return self._refresh_auth_tokens()
                
            # Clear invalid sessions
            if "session" in error_record.error_message.lower():
                return self._clear_invalid_sessions()
                
            return False
            
        except Exception as e:
            logger.error(f"Authentication error recovery failed: {e}")
            return False
            
    def _handle_validation_error(self, error_record: ErrorRecord) -> bool:
        """Handle validation errors"""
        try:
            # Try to sanitize input data
            if "input" in error_record.context:
                sanitized_data = self._sanitize_input(error_record.context["input"])
                if sanitized_data:
                    logger.info("Input data sanitized successfully")
                    return True
                    
            return False
            
        except Exception as e:
            logger.error(f"Validation error recovery failed: {e}")
            return False
            
    # Recovery utility methods
    def _recover_database_connection(self) -> bool:
        """Attempt to recover database connection"""
        try:
            # Test connection
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute("SELECT 1")
            conn.close()
            return True
        except Exception:
            return False
            
    def _recover_network_connection(self, context: Dict) -> bool:
        """Attempt to recover network connection"""
        try:
            # Test basic connectivity
            response = requests.get("https://httpbin.org/status/200", timeout=5)
            return response.status_code == 200
        except Exception:
            return False
            
    def _recover_filesystem_issue(self, context: Dict) -> bool:
        """Attempt to recover from filesystem issues"""
        try:
            # Check if we can write to temp directory
            import tempfile
            with tempfile.NamedTemporaryFile() as tmp:
                tmp.write(b"test")
                tmp.flush()
            return True
        except Exception:
            return False
            
    def _repair_database(self) -> bool:
        """Attempt to repair corrupted database"""
        try:
            # This is a simplified repair - in production, you'd want more sophisticated repair
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute("PRAGMA integrity_check")
            result = cursor.fetchone()
            conn.close()
            return result[0] == "ok"
        except Exception:
            return False
            
    def _test_network_connectivity(self) -> bool:
        """Test basic network connectivity"""
        try:
            response = requests.get("https://httpbin.org/status/200", timeout=5)
            return response.status_code == 200
        except Exception:
            return False
            
    def _try_alternative_endpoints(self, original_url: str) -> bool:
        """Try alternative endpoints"""
        # This would contain logic to try backup URLs
        return False
        
    def _check_disk_space(self) -> bool:
        """Check available disk space"""
        try:
            import shutil
            total, used, free = shutil.disk_usage("/")
            # Check if we have at least 100MB free
            return free > 100 * 1024 * 1024
        except Exception:
            return False
            
    def _cleanup_temp_files(self):
        """Clean up temporary files"""
        try:
            import tempfile
            import shutil
            temp_dir = tempfile.gettempdir()
            # Clean up old temp files (simplified)
            logger.info("Cleaned up temporary files")
        except Exception as e:
            logger.error(f"Failed to cleanup temp files: {e}")
            
    def _create_missing_directories(self, context: Dict) -> bool:
        """Create missing directories"""
        try:
            if "path" in context:
                import os
                os.makedirs(context["path"], exist_ok=True)
                return True
        except Exception:
            pass
        return False
        
    def _refresh_auth_tokens(self) -> bool:
        """Refresh authentication tokens"""
        # Implementation would depend on your auth system
        return False
        
    def _clear_invalid_sessions(self) -> bool:
        """Clear invalid sessions"""
        # Implementation would depend on your session management
        return False
        
    def _sanitize_input(self, input_data: Any) -> Any:
        """Sanitize input data"""
        # Basic sanitization - would be more sophisticated in production
        if isinstance(input_data, str):
            return input_data.strip()
        return input_data
        
    def get_error_statistics(self, hours: int = 24) -> Dict:
        """Get error statistics for the specified time period"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        since_time = datetime.now() - timedelta(hours=hours)
        
        cursor.execute('''
            SELECT error_type, severity, COUNT(*) as count,
                   SUM(CASE WHEN resolved THEN 1 ELSE 0 END) as resolved_count
            FROM error_records 
            WHERE timestamp > ?
            GROUP BY error_type, severity
        ''', (since_time,))
        
        results = cursor.fetchall()
        conn.close()
        
        stats = {
            'total_errors': 0,
            'resolved_errors': 0,
            'by_type': {},
            'by_severity': {}
        }
        
        for error_type, severity, count, resolved_count in results:
            stats['total_errors'] += count
            stats['resolved_errors'] += resolved_count
            
            if error_type not in stats['by_type']:
                stats['by_type'][error_type] = {'count': 0, 'resolved': 0}
            stats['by_type'][error_type]['count'] += count
            stats['by_type'][error_type]['resolved'] += resolved_count
            
            if severity not in stats['by_severity']:
                stats['by_severity'][severity] = {'count': 0, 'resolved': 0}
            stats['by_severity'][severity]['count'] += count
            stats['by_severity'][severity]['resolved'] += resolved_count
            
        return stats

# Decorators for easy use
def with_retry(max_retries: int = 3, base_delay: float = 1.0):
    """Decorator to add retry logic to functions"""
    return RetryManager(max_retries=max_retries, base_delay=base_delay)

def with_circuit_breaker(failure_threshold: int = 5, recovery_timeout: int = 60):
    """Decorator to add circuit breaker to functions"""
    return CircuitBreaker(failure_threshold=failure_threshold, recovery_timeout=recovery_timeout)

def with_error_recovery(severity: ErrorSeverity = ErrorSeverity.MEDIUM):
    """Decorator to add error recovery to functions"""
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                recovery_manager.handle_error(e, {'function': func.__name__}, severity)
                raise
        return wrapper
    return decorator

@contextmanager
def safe_operation(operation_name: str, severity: ErrorSeverity = ErrorSeverity.MEDIUM):
    """Context manager for safe operations with error recovery"""
    try:
        yield
    except Exception as e:
        recovery_manager.handle_error(e, {'operation': operation_name}, severity)
        raise

# Global error recovery manager
recovery_manager = ErrorRecoveryManager()
