"""
Database Management Module for Legal Case Management System
Handles all database operations, connections, and schema management.
"""

import sqlite3
import logging
from typing import List, Dict, Optional, Any
from datetime import datetime
import os
from contextlib import contextmanager

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DatabaseManager:
    """Centralized database management"""
    
    def __init__(self, db_path: str = "legal_cases.db"):
        self.db_path = db_path
        self.init_database()
    
    def init_database(self):
        """Initialize database with all required tables"""
        logger.info("[DATABASE] Starting init_database")
        
        with self.get_connection() as conn:
            cursor = conn.cursor()
            
            # Cases table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS cases (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL,
                    case_number TEXT UNIQUE,
                    description TEXT,
                    status TEXT DEFAULT 'active',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    created_by INTEGER,
                    client_name TEXT,
                    opposing_party TEXT,
                    court_name TEXT,
                    case_type TEXT,
                    priority TEXT DEFAULT 'medium'
                )
            ''')
            
            # Documents table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS documents (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    case_id INTEGER NOT NULL,
                    filename TEXT NOT NULL,
                    original_filename TEXT,
                    file_type TEXT,
                    file_size INTEGER,
                    upload_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    uploaded_by INTEGER,
                    content_preview TEXT,
                    document_type TEXT,
                    is_processed BOOLEAN DEFAULT 0,
                    ocr_text TEXT,
                    metadata TEXT,
                    FOREIGN KEY (case_id) REFERENCES cases (id) ON DELETE CASCADE
                )
            ''')
            
            # URL monitoring table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS url_monitoring (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    case_id INTEGER NOT NULL,
                    url TEXT NOT NULL,
                    description TEXT,
                    last_checked TIMESTAMP,
                    last_content_hash TEXT,
                    status TEXT DEFAULT 'active',
                    check_frequency INTEGER DEFAULT 3600,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (case_id) REFERENCES cases (id) ON DELETE CASCADE
                )
            ''')
            
            # User actions log
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS user_actions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER,
                    action TEXT NOT NULL,
                    details TEXT,
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    ip_address TEXT,
                    case_id INTEGER,
                    FOREIGN KEY (case_id) REFERENCES cases (id)
                )
            ''')
            
            # System monitoring
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS system_monitoring (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    metric_name TEXT NOT NULL,
                    metric_value REAL,
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    details TEXT
                )
            ''')
            
            # Case notes
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS case_notes (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    case_id INTEGER NOT NULL,
                    note_text TEXT NOT NULL,
                    created_by INTEGER,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    note_type TEXT DEFAULT 'general',
                    is_private BOOLEAN DEFAULT 0,
                    FOREIGN KEY (case_id) REFERENCES cases (id) ON DELETE CASCADE
                )
            ''')
            
            # Deadlines and reminders
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS deadlines (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    case_id INTEGER NOT NULL,
                    title TEXT NOT NULL,
                    description TEXT,
                    due_date TIMESTAMP NOT NULL,
                    priority TEXT DEFAULT 'medium',
                    status TEXT DEFAULT 'pending',
                    created_by INTEGER,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    reminder_sent BOOLEAN DEFAULT 0,
                    FOREIGN KEY (case_id) REFERENCES cases (id) ON DELETE CASCADE
                )
            ''')
            
            conn.commit()
        
        logger.info("[DATABASE] Completed init_database")
    
    @contextmanager
    def get_connection(self):
        """Get database connection with proper error handling"""
        conn = None
        try:
            conn = sqlite3.connect(self.db_path)
            conn.row_factory = sqlite3.Row  # Enable column access by name
            yield conn
        except Exception as e:
            if conn:
                conn.rollback()
            logger.error(f"Database error: {e}")
            raise
        finally:
            if conn:
                conn.close()
    
    def execute_query(self, query: str, params: tuple = (), fetch: str = None) -> Any:
        """Execute a database query with error handling"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute(query, params)
                
                if fetch == 'one':
                    return cursor.fetchone()
                elif fetch == 'all':
                    return cursor.fetchall()
                else:
                    conn.commit()
                    return cursor.lastrowid
        except Exception as e:
            logger.error(f"Query execution error: {e}")
            raise
    
    # Case operations
    def create_case(self, name: str, case_number: str = None, description: str = None, 
                   created_by: int = None, **kwargs) -> int:
        """Create a new case"""
        query = '''
            INSERT INTO cases (name, case_number, description, created_by, client_name, 
                             opposing_party, court_name, case_type, priority)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        '''
        params = (
            name, case_number, description, created_by,
            kwargs.get('client_name'), kwargs.get('opposing_party'),
            kwargs.get('court_name'), kwargs.get('case_type', 'general'),
            kwargs.get('priority', 'medium')
        )
        
        case_id = self.execute_query(query, params)
        logger.info(f"Created case: {name} (ID: {case_id})")
        return case_id
    
    def get_case(self, case_id: int) -> Optional[Dict]:
        """Get case by ID"""
        query = "SELECT * FROM cases WHERE id = ?"
        result = self.execute_query(query, (case_id,), fetch='one')
        return dict(result) if result else None
    
    def get_all_cases(self, created_by: int = None) -> List[Dict]:
        """Get all cases, optionally filtered by creator"""
        if created_by:
            query = "SELECT * FROM cases WHERE created_by = ? ORDER BY updated_at DESC"
            params = (created_by,)
        else:
            query = "SELECT * FROM cases ORDER BY updated_at DESC"
            params = ()
        
        results = self.execute_query(query, params, fetch='all')
        return [dict(row) for row in results] if results else []
    
    def update_case(self, case_id: int, **kwargs) -> bool:
        """Update case information"""
        # Build dynamic update query
        fields = []
        values = []
        
        for key, value in kwargs.items():
            if key in ['name', 'case_number', 'description', 'status', 'client_name', 
                      'opposing_party', 'court_name', 'case_type', 'priority']:
                fields.append(f"{key} = ?")
                values.append(value)
        
        if not fields:
            return False
        
        fields.append("updated_at = CURRENT_TIMESTAMP")
        values.append(case_id)
        
        query = f"UPDATE cases SET {', '.join(fields)} WHERE id = ?"
        self.execute_query(query, tuple(values))
        
        logger.info(f"Updated case ID: {case_id}")
        return True
    
    def delete_case(self, case_id: int) -> bool:
        """Delete case and all related data"""
        query = "DELETE FROM cases WHERE id = ?"
        self.execute_query(query, (case_id,))
        logger.info(f"Deleted case ID: {case_id}")
        return True
    
    # Document operations
    def add_document(self, case_id: int, filename: str, file_type: str = None, 
                    file_size: int = None, uploaded_by: int = None, **kwargs) -> int:
        """Add document to case"""
        query = '''
            INSERT INTO documents (case_id, filename, original_filename, file_type, 
                                 file_size, uploaded_by, content_preview, document_type, 
                                 ocr_text, metadata)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        '''
        params = (
            case_id, filename, kwargs.get('original_filename', filename),
            file_type, file_size, uploaded_by, kwargs.get('content_preview'),
            kwargs.get('document_type', 'general'), kwargs.get('ocr_text'),
            kwargs.get('metadata')
        )
        
        doc_id = self.execute_query(query, params)
        logger.info(f"Added document: {filename} to case {case_id}")
        return doc_id
    
    def get_case_documents(self, case_id: int) -> List[Dict]:
        """Get all documents for a case"""
        query = "SELECT * FROM documents WHERE case_id = ? ORDER BY upload_date DESC"
        results = self.execute_query(query, (case_id,), fetch='all')
        return [dict(row) for row in results] if results else []
    
    def get_document(self, doc_id: int) -> Optional[Dict]:
        """Get document by ID"""
        query = "SELECT * FROM documents WHERE id = ?"
        result = self.execute_query(query, (doc_id,), fetch='one')
        return dict(result) if result else None
    
    def update_document(self, doc_id: int, **kwargs) -> bool:
        """Update document information"""
        fields = []
        values = []
        
        for key, value in kwargs.items():
            if key in ['filename', 'file_type', 'content_preview', 'document_type', 
                      'is_processed', 'ocr_text', 'metadata']:
                fields.append(f"{key} = ?")
                values.append(value)
        
        if not fields:
            return False
        
        values.append(doc_id)
        query = f"UPDATE documents SET {', '.join(fields)} WHERE id = ?"
        self.execute_query(query, tuple(values))
        
        logger.info(f"Updated document ID: {doc_id}")
        return True
    
    def delete_document(self, doc_id: int) -> bool:
        """Delete document"""
        query = "DELETE FROM documents WHERE id = ?"
        self.execute_query(query, (doc_id,))
        logger.info(f"Deleted document ID: {doc_id}")
        return True
    
    # URL monitoring operations
    def add_url_monitor(self, case_id: int, url: str, description: str = None, 
                       check_frequency: int = 3600) -> int:
        """Add URL to monitor for case"""
        query = '''
            INSERT INTO url_monitoring (case_id, url, description, check_frequency)
            VALUES (?, ?, ?, ?)
        '''
        params = (case_id, url, description, check_frequency)
        
        monitor_id = self.execute_query(query, params)
        logger.info(f"Added URL monitor: {url} for case {case_id}")
        return monitor_id
    
    def get_case_url_monitors(self, case_id: int) -> List[Dict]:
        """Get all URL monitors for a case"""
        query = "SELECT * FROM url_monitoring WHERE case_id = ? AND status = 'active'"
        results = self.execute_query(query, (case_id,), fetch='all')
        return [dict(row) for row in results] if results else []
    
    def update_url_monitor(self, monitor_id: int, **kwargs) -> bool:
        """Update URL monitor"""
        fields = []
        values = []
        
        for key, value in kwargs.items():
            if key in ['url', 'description', 'last_checked', 'last_content_hash', 
                      'status', 'check_frequency']:
                fields.append(f"{key} = ?")
                values.append(value)
        
        if not fields:
            return False
        
        values.append(monitor_id)
        query = f"UPDATE url_monitoring SET {', '.join(fields)} WHERE id = ?"
        self.execute_query(query, tuple(values))
        
        return True
    
    # Logging operations
    def log_user_action(self, user_id: int, action: str, details: str = None, 
                       case_id: int = None, ip_address: str = None):
        """Log user action"""
        query = '''
            INSERT INTO user_actions (user_id, action, details, case_id, ip_address)
            VALUES (?, ?, ?, ?, ?)
        '''
        params = (user_id, action, details, case_id, ip_address)
        self.execute_query(query, params)
    
    def get_user_actions(self, user_id: int = None, case_id: int = None, 
                        limit: int = 100) -> List[Dict]:
        """Get user actions with optional filtering"""
        conditions = []
        params = []
        
        if user_id:
            conditions.append("user_id = ?")
            params.append(user_id)
        
        if case_id:
            conditions.append("case_id = ?")
            params.append(case_id)
        
        where_clause = " WHERE " + " AND ".join(conditions) if conditions else ""
        params.append(limit)
        
        query = f'''
            SELECT * FROM user_actions 
            {where_clause}
            ORDER BY timestamp DESC 
            LIMIT ?
        '''
        
        results = self.execute_query(query, tuple(params), fetch='all')
        return [dict(row) for row in results] if results else []
    
    # System monitoring
    def log_system_metric(self, metric_name: str, metric_value: float, details: str = None):
        """Log system metric"""
        query = '''
            INSERT INTO system_monitoring (metric_name, metric_value, details)
            VALUES (?, ?, ?)
        '''
        params = (metric_name, metric_value, details)
        self.execute_query(query, params)
    
    def get_system_metrics(self, metric_name: str = None, hours: int = 24) -> List[Dict]:
        """Get system metrics"""
        if metric_name:
            query = '''
                SELECT * FROM system_monitoring 
                WHERE metric_name = ? AND timestamp > datetime('now', '-{} hours')
                ORDER BY timestamp DESC
            '''.format(hours)
            params = (metric_name,)
        else:
            query = '''
                SELECT * FROM system_monitoring 
                WHERE timestamp > datetime('now', '-{} hours')
                ORDER BY timestamp DESC
            '''.format(hours)
            params = ()
        
        results = self.execute_query(query, params, fetch='all')
        return [dict(row) for row in results] if results else []
    
    # Case notes operations
    def add_case_note(self, case_id: int, note_text: str, created_by: int = None, 
                     note_type: str = 'general', is_private: bool = False) -> int:
        """Add note to case"""
        query = '''
            INSERT INTO case_notes (case_id, note_text, created_by, note_type, is_private)
            VALUES (?, ?, ?, ?, ?)
        '''
        params = (case_id, note_text, created_by, note_type, is_private)
        
        note_id = self.execute_query(query, params)
        logger.info(f"Added note to case {case_id}")
        return note_id
    
    def get_case_notes(self, case_id: int, include_private: bool = True) -> List[Dict]:
        """Get all notes for a case"""
        if include_private:
            query = "SELECT * FROM case_notes WHERE case_id = ? ORDER BY created_at DESC"
            params = (case_id,)
        else:
            query = "SELECT * FROM case_notes WHERE case_id = ? AND is_private = 0 ORDER BY created_at DESC"
            params = (case_id,)
        
        results = self.execute_query(query, params, fetch='all')
        return [dict(row) for row in results] if results else []
    
    # Deadlines operations
    def add_deadline(self, case_id: int, title: str, due_date: str, description: str = None,
                    priority: str = 'medium', created_by: int = None) -> int:
        """Add deadline to case"""
        query = '''
            INSERT INTO deadlines (case_id, title, description, due_date, priority, created_by)
            VALUES (?, ?, ?, ?, ?, ?)
        '''
        params = (case_id, title, description, due_date, priority, created_by)
        
        deadline_id = self.execute_query(query, params)
        logger.info(f"Added deadline to case {case_id}: {title}")
        return deadline_id
    
    def get_case_deadlines(self, case_id: int, status: str = None) -> List[Dict]:
        """Get deadlines for a case"""
        if status:
            query = "SELECT * FROM deadlines WHERE case_id = ? AND status = ? ORDER BY due_date ASC"
            params = (case_id, status)
        else:
            query = "SELECT * FROM deadlines WHERE case_id = ? ORDER BY due_date ASC"
            params = (case_id,)
        
        results = self.execute_query(query, params, fetch='all')
        return [dict(row) for row in results] if results else []
    
    def get_upcoming_deadlines(self, days_ahead: int = 7) -> List[Dict]:
        """Get upcoming deadlines across all cases"""
        query = '''
            SELECT d.*, c.name as case_name 
            FROM deadlines d
            JOIN cases c ON d.case_id = c.id
            WHERE d.due_date BETWEEN datetime('now') AND datetime('now', '+{} days')
            AND d.status = 'pending'
            ORDER BY d.due_date ASC
        '''.format(days_ahead)
        
        results = self.execute_query(query, (), fetch='all')
        return [dict(row) for row in results] if results else []

# Global database manager instance
db_manager = DatabaseManager()
