# Code Project

I need you to analyze this codebase. Here's an overview:

Total files: 14
Total size: 330.9 KB
Total lines: 8,009

Language breakdown:
  python: 14 files, 8,009 lines

## Project Structure
```
├── c2p.py (401 lines)
├── chat_manager.py (393 lines)
├── database.py (486 lines)
├── database_managers.py (452 lines)
├── docket_viewer.py (295 lines)
├── document_change_monitor.py (614 lines)
├── document_numbering.py (207 lines)
├── document_processor.py (560 lines)
├── efiling_scraper.py (2052 lines)
├── efiling_ui.py (603 lines)
├── error_recovery.py (582 lines)
├── forensic_extension.py (578 lines)
├── queue_manager.py (546 lines)
└── security_enforcer.py (240 lines)
```

## Code Files

## File: document_change_monitor.py
```python
"""
Document Change Detection and Monitoring System
Critical for detecting stricken entries, content modifications, and forensic preservation
"""

import json
import hashlib
import os
from datetime import datetime
from typing import Dict, List, Optional, Tuple, Any
import logging
import psycopg2
from pathlib import Path
import threading
import time

try:
    import fitz  # PyMuPDF for PDF content extraction
    PDF_AVAILABLE = True
except ImportError:
    PDF_AVAILABLE = False
    
try:
    from core.database_managers import PostgreSQLManager
    from security.pgp_manager import create_pgp_manager
    DB_AVAILABLE = True
except ImportError:
    DB_AVAILABLE = False

class DocumentChangeMonitor:
    """
    Comprehensive document change detection and monitoring
    Tracks:
    - Docket entry metadata changes (titles, descriptions, dates)
    - Document content changes (PDF text, image hashes)
    - Stricken entries preservation (before/after states)
    - Complete audit trail with forensic integrity
    """
    
    def __init__(self, case_id: str, postgresql_config: Optional[Dict] = None):
        self.case_id = case_id
        self.logger = logging.getLogger(f"{__name__}.{case_id}")
        
        # Initialize database connections
        if DB_AVAILABLE and postgresql_config:
            self.postgresql = PostgreSQLManager(postgresql_config)
            self.setup_monitoring_tables()
        else:
            self.postgresql = None
            self.logger.warning("Database not available - using file-based monitoring")
        
        # Initialize PGP encryption
        try:
            self.key_manager, self.doc_encryption = create_pgp_manager()
            self.encryption_available = True
        except Exception as e:
            self.logger.warning(f"PGP encryption not available: {e}")
            self.encryption_available = False
        
        # Monitoring data storage
        self.snapshots_dir = Path(f"case_documents/snapshots/case_{case_id}")
        self.snapshots_dir.mkdir(parents=True, exist_ok=True)
        
        # Document tracking
        self.document_hashes = {}
        self.metadata_snapshots = {}
        self.change_log = []
        
        # Monitoring thread
        self.monitoring_active = False
        self.monitoring_thread = None
        
        self.logger.info(f"Document change monitor initialized for case {case_id}")
    
    def setup_monitoring_tables(self):
        """Setup PostgreSQL tables for document change monitoring"""
        if not self.postgresql:
            return
            
        try:
            with self.postgresql.get_connection() as conn:
                with conn.cursor() as cursor:
                    # Document snapshots table
                    cursor.execute("""
                    CREATE TABLE IF NOT EXISTS document_snapshots (
                        id SERIAL PRIMARY KEY,
                        case_id VARCHAR(50) NOT NULL,
                        document_id VARCHAR(100) NOT NULL,
                        snapshot_type VARCHAR(50) NOT NULL, -- 'metadata', 'content', 'full'
                        snapshot_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        content_hash VARCHAR(64) NOT NULL,
                        metadata_hash VARCHAR(64),
                        original_data JSONB,
                        encrypted_content TEXT,
                        file_path VARCHAR(500),
                        file_size BIGINT,
                        preservation_reason VARCHAR(200) -- 'initial', 'before_strike', 'routine_check'
                    )
                    """)
                    
                    # Document changes table  
                    cursor.execute("""
                    CREATE TABLE IF NOT EXISTS document_changes (
                        id SERIAL PRIMARY KEY,
                        case_id VARCHAR(50) NOT NULL,
                        document_id VARCHAR(100) NOT NULL,
                        change_type VARCHAR(50) NOT NULL, -- 'metadata_change', 'content_change', 'stricken', 'restored'
                        detection_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        before_hash VARCHAR(64),
                        after_hash VARCHAR(64),
                        change_summary JSONB,
                        impact_level VARCHAR(20) NOT NULL, -- 'LOW', 'MEDIUM', 'HIGH', 'CRITICAL'
                        change_details JSONB,
                        forensic_notes TEXT,
                        action_required BOOLEAN DEFAULT FALSE
                    )
                    """)
                    
                    # Stricken entries preservation
                    cursor.execute("""
                    CREATE TABLE IF NOT EXISTS stricken_entries_preservation (
                        id SERIAL PRIMARY KEY,
                        case_id VARCHAR(50) NOT NULL,
                        original_docket_number INT NOT NULL,
                        strike_detection_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        original_entry JSONB NOT NULL,
                        original_documents JSONB,
                        stricken_entry JSONB,
                        strike_reason VARCHAR(500),
                        judicial_officer VARCHAR(200),
                        preservation_status VARCHAR(50) DEFAULT 'PRESERVED',
                        evidence_integrity_verified BOOLEAN DEFAULT TRUE,
                        legal_implications TEXT
                    )
                    """)
                    
                    # Create indexes for performance
                    cursor.execute("CREATE INDEX IF NOT EXISTS idx_doc_snapshots_case_doc ON document_snapshots(case_id, document_id)")
                    cursor.execute("CREATE INDEX IF NOT EXISTS idx_doc_changes_case_doc ON document_changes(case_id, document_id)")
                    cursor.execute("CREATE INDEX IF NOT EXISTS idx_stricken_case ON stricken_entries_preservation(case_id)")
                    
                    conn.commit()
                    
            self.logger.info("Document monitoring tables initialized")
            
        except Exception as e:
            self.logger.error(f"Failed to setup monitoring tables: {e}")
    
    def create_document_snapshot(self, document_id: str, document_data: Dict, 
                                content_path: Optional[str] = None, 
                                reason: str = "routine_check") -> Dict:
        """
        Create comprehensive snapshot of document state
        Preserves both metadata and content for change detection
        
        Args:
            document_id: Unique identifier for document
            document_data: Docket entry metadata
            content_path: Path to actual document file (PDF, image, etc.)
            reason: Reason for snapshot (initial, before_strike, routine_check)
            
        Returns:
            Snapshot record with hashes and preservation status
        """
        try:
            snapshot_id = f"{self.case_id}_{document_id}_{int(time.time())}"
            
            # Create metadata hash
            metadata_json = json.dumps(document_data, sort_keys=True)
            metadata_hash = hashlib.sha256(metadata_json.encode()).hexdigest()
            
            # Create content hash if file available
            content_hash = None
            file_size = None
            content_text = None
            
            if content_path and os.path.exists(content_path):
                # Hash file content
                with open(content_path, 'rb') as f:
                    content_bytes = f.read()
                    content_hash = hashlib.sha256(content_bytes).hexdigest()
                    file_size = len(content_bytes)
                
                # Extract text from PDF for content monitoring
                if PDF_AVAILABLE and content_path.lower().endswith('.pdf'):
                    try:
                        doc = fitz.open(content_path)
                        content_text = ""
                        for page in doc:
                            content_text += page.get_text()
                        doc.close()
                    except Exception as e:
                        self.logger.warning(f"Failed to extract PDF text from {content_path}: {e}")
            
            snapshot_record = {
                'snapshot_id': snapshot_id,
                'case_id': self.case_id,
                'document_id': document_id,
                'snapshot_date': datetime.now().isoformat(),
                'metadata_hash': metadata_hash,
                'content_hash': content_hash,
                'metadata': document_data,
                'content_text': content_text,
                'file_path': content_path,
                'file_size': file_size,
                'preservation_reason': reason
            }
            
            # Store in database if available
            if self.postgresql:
                self.store_snapshot_in_database(snapshot_record)
            
            # Store locally encrypted
            self.store_snapshot_locally(snapshot_record)
            
            # Update tracking
            self.document_hashes[document_id] = {
                'metadata_hash': metadata_hash,
                'content_hash': content_hash,
                'last_snapshot': snapshot_id,
                'last_check': datetime.now().isoformat()
            }
            
            self.logger.info(f"📸 Document snapshot created: {snapshot_id} (reason: {reason})")
            return snapshot_record
            
        except Exception as e:
            self.logger.error(f"Failed to create document snapshot: {e}")
            return {'error': str(e)}
    
    def detect_document_changes(self, document_id: str, current_data: Dict, 
                               current_content_path: Optional[str] = None) -> Optional[Dict]:
        """
        Detect changes in document metadata or content
        Critical for identifying tampering, striking, or unauthorized modifications
        
        Args:
            document_id: Document identifier
            current_data: Current docket entry metadata
            current_content_path: Path to current document file
            
        Returns:
            Change detection results or None if no changes
        """
        try:
            if document_id not in self.document_hashes:
                # First time seeing this document - create initial snapshot
                self.create_document_snapshot(document_id, current_data, current_content_path, "initial")
                return None
            
            # Get previous state
            previous_state = self.document_hashes[document_id]
            
            # Check metadata changes
            current_metadata_json = json.dumps(current_data, sort_keys=True)
            current_metadata_hash = hashlib.sha256(current_metadata_json.encode()).hexdigest()
            
            metadata_changed = current_metadata_hash != previous_state['metadata_hash']
            
            # Check content changes
            content_changed = False
            current_content_hash = None
            
            if current_content_path and os.path.exists(current_content_path):
                with open(current_content_path, 'rb') as f:
                    current_content_hash = hashlib.sha256(f.read()).hexdigest()
                
                if previous_state['content_hash']:
                    content_changed = current_content_hash != previous_state['content_hash']
            
            if not metadata_changed and not content_changed:
                # No changes detected
                return None
            
            # CHANGE DETECTED - Analyze and preserve
            change_analysis = self.analyze_document_change(
                document_id, current_data, previous_state, 
                metadata_changed, content_changed
            )
            
            # Create preservation snapshot BEFORE recording the change
            if change_analysis.get('impact_level') in ['HIGH', 'CRITICAL']:
                self.create_document_snapshot(
                    document_id, current_data, current_content_path, 
                    f"change_detection_{change_analysis['change_type']}"
                )
            
            # Record change in database
            if self.postgresql:
                self.record_change_in_database(change_analysis)
            
            # Update current state
            self.document_hashes[document_id] = {
                'metadata_hash': current_metadata_hash,
                'content_hash': current_content_hash,
                'last_snapshot': change_analysis['change_id'],
                'last_check': datetime.now().isoformat()
            }
            
            self.logger.critical(f"🚨 DOCUMENT CHANGE DETECTED: {change_analysis['change_type']} in {document_id}")
            return change_analysis
            
        except Exception as e:
            self.logger.error(f"Failed to detect document changes: {e}")
            return {'error': str(e)}
    
    def analyze_document_change(self, document_id: str, current_data: Dict, 
                               previous_state: Dict, metadata_changed: bool, 
                               content_changed: bool) -> Dict:
        """
        Analyze detected changes and determine impact level and type
        Special handling for stricken entries and suspicious modifications
        """
        try:
            change_id = f"change_{self.case_id}_{document_id}_{int(time.time())}"
            
            change_analysis = {
                'change_id': change_id,
                'case_id': self.case_id,
                'document_id': document_id,
                'detection_date': datetime.now().isoformat(),
                'metadata_changed': metadata_changed,
                'content_changed': content_changed,
                'change_type': 'unknown',
                'impact_level': 'LOW',
                'change_details': {},
                'forensic_flags': []
            }
            
            # Analyze metadata changes
            if metadata_changed:
                change_details = {}
                
                # Check for stricken entry patterns
                current_desc = current_data.get('Description', '').lower()
                if 'stricken' in current_desc:
                    change_analysis['change_type'] = 'ENTRY_STRICKEN'
                    change_analysis['impact_level'] = 'CRITICAL'
                    change_analysis['forensic_flags'].append('EVIDENCE_MANIPULATION')
                    
                    # Preserve the original entry before it was stricken
                    self.preserve_stricken_entry(document_id, current_data, previous_state)
                    
                    self.logger.critical(f"🚨 STRICKEN ENTRY DETECTED: {document_id}")
                
                # Check for description changes
                if 'Description' in current_data:
                    change_analysis['change_details']['description_modified'] = True
                    change_analysis['impact_level'] = 'HIGH'
                
                # Check for date modifications
                if 'Filing Date' in current_data:
                    change_analysis['change_details']['filing_date_modified'] = True
                    change_analysis['impact_level'] = 'HIGH'
                    change_analysis['forensic_flags'].append('DATE_TAMPERING')
                
                # Check for party/side changes
                if 'Side' in current_data:
                    change_analysis['change_details']['party_side_modified'] = True
                    change_analysis['impact_level'] = 'MEDIUM'
            
            # Analyze content changes
            if content_changed:
                change_analysis['change_details']['document_content_modified'] = True
                change_analysis['impact_level'] = 'CRITICAL'
                change_analysis['forensic_flags'].append('DOCUMENT_TAMPERING')
                
                if change_analysis['change_type'] == 'unknown':
                    change_analysis['change_type'] = 'CONTENT_MODIFICATION'
                
                self.logger.critical(f"🚨 DOCUMENT CONTENT CHANGED: {document_id}")
            
            # Set default change type if still unknown
            if change_analysis['change_type'] == 'unknown':
                if metadata_changed:
                    change_analysis['change_type'] = 'METADATA_MODIFICATION'
                    change_analysis['impact_level'] = 'MEDIUM'
            
            return change_analysis
            
        except Exception as e:
            self.logger.error(f"Failed to analyze document change: {e}")
            return {'error': str(e), 'change_id': f"error_{int(time.time())}"}
    
    def preserve_stricken_entry(self, document_id: str, stricken_data: Dict, original_state: Dict):
        """
        Special preservation for stricken entries
        Maintains forensic record of original content before it was stricken
        """
        try:
            preservation_record = {
                'case_id': self.case_id,
                'document_id': document_id,
                'original_docket_number': stricken_data.get('Docket Number', 'unknown'),
                'strike_detection_date': datetime.now().isoformat(),
                'original_entry': original_state,
                'stricken_entry': stricken_data,
                'strike_reason': stricken_data.get('Description', ''),
                'preservation_status': 'PRESERVED',
                'evidence_integrity_verified': True,
                'legal_implications': (
                    "Original entry preserved before striking. This constitutes potential evidence "
                    "manipulation and requires immediate legal review. Original content remains "
                    "forensically preserved and admissible."
                )
            }
            
            # Store in database
            if self.postgresql:
                with self.postgresql.get_connection() as conn:
                    with conn.cursor() as cursor:
                        cursor.execute("""
                        INSERT INTO stricken_entries_preservation 
                        (case_id, original_docket_number, original_entry, stricken_entry, 
                         strike_reason, preservation_status, evidence_integrity_verified, legal_implications)
                        VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
                        """, (
                            preservation_record['case_id'],
                            preservation_record['original_docket_number'],
                            json.dumps(preservation_record['original_entry']),
                            json.dumps(preservation_record['stricken_entry']),
                            preservation_record['strike_reason'],
                            preservation_record['preservation_status'],
                            preservation_record['evidence_integrity_verified'],
                            preservation_record['legal_implications']
                        ))
                        conn.commit()
            
            # Create encrypted backup
            self.store_stricken_entry_backup(preservation_record)
            
            self.logger.critical(f"🛡️ STRICKEN ENTRY PRESERVED: Original content secured for {document_id}")
            
        except Exception as e:
            self.logger.error(f"Failed to preserve stricken entry: {e}")
    
    def store_snapshot_locally(self, snapshot_record: Dict):
        """Store snapshot locally with encryption if available"""
        try:
            snapshot_file = self.snapshots_dir / f"{snapshot_record['snapshot_id']}.json"
            
            if self.encryption_available:
                # Encrypt sensitive data
                encrypted_data = self.doc_encryption.encrypt_document(
                    json.dumps(snapshot_record).encode(),
                    self.case_id,
                    snapshot_record['snapshot_id']
                )
                with open(snapshot_file, 'w') as f:
                    json.dump({'encrypted': True, 'data': encrypted_data}, f)
            else:
                with open(snapshot_file, 'w') as f:
                    json.dump(snapshot_record, f, indent=2)
                    
        except Exception as e:
            self.logger.error(f"Failed to store snapshot locally: {e}")
    
    def store_stricken_entry_backup(self, preservation_record: Dict):
        """Create encrypted backup of stricken entry evidence"""
        try:
            backup_file = self.snapshots_dir / f"stricken_{preservation_record['document_id']}_{int(time.time())}.json"
            
            if self.encryption_available:
                encrypted_data = self.doc_encryption.encrypt_document(
                    json.dumps(preservation_record).encode(),
                    self.case_id,
                    f"stricken_{preservation_record['document_id']}"
                )
                with open(backup_file, 'w') as f:
                    json.dump({'encrypted': True, 'stricken_evidence': encrypted_data}, f)
            else:
                with open(backup_file, 'w') as f:
                    json.dump(preservation_record, f, indent=2)
                    
        except Exception as e:
            self.logger.error(f"Failed to store stricken entry backup: {e}")
    
    def store_snapshot_in_database(self, snapshot_record: Dict):
        """Store snapshot in PostgreSQL database"""
        if not self.postgresql:
            return
            
        try:
            with self.postgresql.get_connection() as conn:
                with conn.cursor() as cursor:
                    cursor.execute("""
                    INSERT INTO document_snapshots 
                    (case_id, document_id, snapshot_type, content_hash, metadata_hash, 
                     original_data, file_path, file_size, preservation_reason)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
                    """, (
                        snapshot_record['case_id'],
                        snapshot_record['document_id'],
                        'full',
                        snapshot_record['content_hash'],
                        snapshot_record['metadata_hash'],
                        json.dumps(snapshot_record['metadata']),
                        snapshot_record['file_path'],
                        snapshot_record['file_size'],
                        snapshot_record['preservation_reason']
                    ))
                    conn.commit()
                    
        except Exception as e:
            self.logger.error(f"Failed to store snapshot in database: {e}")
    
    def record_change_in_database(self, change_analysis: Dict):
        """Record detected change in database"""
        if not self.postgresql:
            return
            
        try:
            with self.postgresql.get_connection() as conn:
                with conn.cursor() as cursor:
                    cursor.execute("""
                    INSERT INTO document_changes 
                    (case_id, document_id, change_type, impact_level, change_summary, 
                     change_details, forensic_notes, action_required)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
                    """, (
                        change_analysis['case_id'],
                        change_analysis['document_id'],
                        change_analysis['change_type'],
                        change_analysis['impact_level'],
                        json.dumps({
                            'metadata_changed': change_analysis['metadata_changed'],
                            'content_changed': change_analysis['content_changed']
                        }),
                        json.dumps(change_analysis['change_details']),
                        f"Forensic flags: {', '.join(change_analysis['forensic_flags'])}",
                        change_analysis['impact_level'] in ['HIGH', 'CRITICAL']
                    ))
                    conn.commit()
                    
        except Exception as e:
            self.logger.error(f"Failed to record change in database: {e}")
    
    def start_continuous_monitoring(self, check_interval_minutes: int = 30):
        """Start continuous monitoring thread"""
        if self.monitoring_active:
            self.logger.warning("Monitoring already active")
            return
            
        self.monitoring_active = True
        self.monitoring_thread = threading.Thread(
            target=self._monitoring_loop,
            args=(check_interval_minutes,),
            daemon=True
        )
        self.monitoring_thread.start()
        
        self.logger.info(f"Started continuous monitoring (check interval: {check_interval_minutes} minutes)")
    
    def stop_monitoring(self):
        """Stop continuous monitoring"""
        self.monitoring_active = False
        if self.monitoring_thread:
            self.monitoring_thread.join(timeout=5)
        self.logger.info("Stopped continuous monitoring")
    
    def _monitoring_loop(self, check_interval_minutes: int):
        """Internal monitoring loop"""
        while self.monitoring_active:
            try:
                # This would integrate with the efiling scraper to check for changes
                # For now, just log that monitoring is active
                self.logger.debug(f"Document monitoring active for case {self.case_id}")
                time.sleep(check_interval_minutes * 60)
            except Exception as e:
                self.logger.error(f"Error in monitoring loop: {e}")
                time.sleep(60)  # Wait 1 minute before retrying

    def get_change_summary(self) -> Dict:
        """Get summary of all detected changes"""
        try:
            if self.postgresql:
                with self.postgresql.get_connection() as conn:
                    with conn.cursor() as cursor:
                        # Get change counts by type
                        cursor.execute("""
                        SELECT change_type, impact_level, COUNT(*) 
                        FROM document_changes 
                        WHERE case_id = %s 
                        GROUP BY change_type, impact_level
                        ORDER BY impact_level DESC, change_type
                        """, (self.case_id,))
                        
                        change_counts = cursor.fetchall()
                        
                        # Get stricken entries count
                        cursor.execute("""
                        SELECT COUNT(*) FROM stricken_entries_preservation 
                        WHERE case_id = %s
                        """, (self.case_id,))
                        
                        stricken_count = cursor.fetchone()[0]
                        
                        return {
                            'case_id': self.case_id,
                            'change_counts': change_counts,
                            'stricken_entries': stricken_count,
                            'total_changes': sum(count for _, _, count in change_counts),
                            'monitoring_active': self.monitoring_active
                        }
            else:
                return {
                    'case_id': self.case_id,
                    'monitoring_active': self.monitoring_active,
                    'note': 'Database not available - limited summary'
                }
                
        except Exception as e:
            self.logger.error(f"Failed to get change summary: {e}")
            return {'error': str(e)}
```

## File: document_processor.py
```python
"""
Document Processing Module for Legal Case Management System
Handles file uploads, OCR, text extraction, and document analysis.
"""

import os
import hashlib
import tempfile
import logging
import requests
import base64
from typing import Dict, List, Optional, Tuple, Any
from pathlib import Path
import mimetypes
from datetime import datetime
import json

# Document processing imports
try:
    import PyPDF2
    import pytesseract
    from PIL import Image
    import docx
    import zipfile
    import pandas as pd
except ImportError as e:
    logging.warning(f"Some document processing libraries not available: {e}")

from core.database import db_manager

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DocumentProcessor:
    """Handles all document processing operations"""
    
    def __init__(self, upload_dir: str = "case_documents"):
        self.upload_dir = Path(upload_dir)
        self.upload_dir.mkdir(exist_ok=True)
        
        # Supported file types
        self.supported_types = {
            'pdf': ['.pdf'],
            'image': ['.png', '.jpg', '.jpeg', '.tiff', '.bmp', '.gif'],
            'document': ['.docx', '.doc', '.txt', '.rtf'],
            'spreadsheet': ['.xlsx', '.xls', '.csv'],
            'archive': ['.zip', '.rar', '.7z'],
            'other': []
        }
        
        # Maximum file sizes (in bytes)
        self.max_file_sizes = {
            'pdf': 50 * 1024 * 1024,      # 50MB
            'image': 20 * 1024 * 1024,    # 20MB
            'document': 25 * 1024 * 1024, # 25MB
            'spreadsheet': 15 * 1024 * 1024, # 15MB
            'archive': 100 * 1024 * 1024, # 100MB
            'other': 10 * 1024 * 1024     # 10MB
        }
    
    def get_file_type_category(self, filename: str) -> str:
        """Determine file type category from filename"""
        ext = Path(filename).suffix.lower()
        
        for category, extensions in self.supported_types.items():
            if ext in extensions:
                return category
        
        return 'other'
    
    def validate_file(self, filename: str, file_size: int) -> Tuple[bool, str]:
        """Validate file type and size"""
        category = self.get_file_type_category(filename)
        max_size = self.max_file_sizes.get(category, self.max_file_sizes['other'])
        
        if file_size > max_size:
            return False, f"File too large. Maximum size for {category} files: {max_size // (1024*1024)}MB"
        
        # Check for potentially dangerous files
        dangerous_extensions = ['.exe', '.bat', '.cmd', '.scr', '.vbs', '.js']
        if Path(filename).suffix.lower() in dangerous_extensions:
            return False, "File type not allowed for security reasons"
        
        return True, "File is valid"
    
    def generate_safe_filename(self, original_filename: str, case_id: int) -> str:
        """Generate safe filename for storage"""
        # Create hash of original filename + timestamp for uniqueness
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        name_hash = hashlib.md5(original_filename.encode()).hexdigest()[:8]
        
        # Keep original extension
        ext = Path(original_filename).suffix.lower()
        
        # Create safe filename
        safe_name = f"case_{case_id}_{timestamp}_{name_hash}{ext}"
        
        return safe_name
    
    def save_uploaded_file(self, uploaded_file, case_id: int) -> Tuple[bool, str, Dict]:
        """Save uploaded file and return file info"""
        try:
            # Validate file
            is_valid, message = self.validate_file(uploaded_file.name, uploaded_file.size)
            if not is_valid:
                return False, message, {}
            
            # Generate safe filename
            safe_filename = self.generate_safe_filename(uploaded_file.name, case_id)
            file_path = self.upload_dir / safe_filename
            
            # Save file
            with open(file_path, "wb") as f:
                f.write(uploaded_file.getbuffer())
            
            # Get file info
            file_info = {
                'filename': safe_filename,
                'original_filename': uploaded_file.name,
                'file_type': self.get_file_type_category(uploaded_file.name),
                'file_size': uploaded_file.size,
                'mime_type': uploaded_file.type,
                'file_path': str(file_path)
            }
            
            logger.info(f"Saved file: {uploaded_file.name} as {safe_filename}")
            return True, "File saved successfully", file_info
            
        except Exception as e:
            logger.error(f"Error saving file {uploaded_file.name}: {e}")
            return False, f"Error saving file: {str(e)}", {}
    
    def extract_text_from_pdf(self, file_path: str) -> Tuple[bool, str]:
        """Extract text from PDF file"""
        try:
            text_content = ""
            
            with open(file_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                
                for page_num, page in enumerate(pdf_reader.pages):
                    try:
                        page_text = page.extract_text()
                        if page_text.strip():
                            text_content += f"\n--- Page {page_num + 1} ---\n"
                            text_content += page_text
                    except Exception as e:
                        logger.warning(f"Error extracting text from page {page_num + 1}: {e}")
                        continue
            
            if text_content.strip():
                return True, text_content
            else:
                return False, "No text found in PDF"
                
        except Exception as e:
            logger.error(f"Error extracting text from PDF {file_path}: {e}")
            return False, f"Error processing PDF: {str(e)}"
    
    def extract_text_from_image(self, file_path: str) -> Tuple[bool, str]:
        """Extract text from image using OCR"""
        try:
            # Open image
            image = Image.open(file_path)
            
            # Convert to RGB if necessary
            if image.mode != 'RGB':
                image = image.convert('RGB')
            
            # Perform OCR
            text_content = pytesseract.image_to_string(image)
            
            if text_content.strip():
                return True, text_content
            else:
                return False, "No text found in image"
                
        except Exception as e:
            logger.error(f"Error extracting text from image {file_path}: {e}")
            return False, f"Error processing image: {str(e)}"
    
    def extract_text_from_docx(self, file_path: str) -> Tuple[bool, str]:
        """Extract text from DOCX file"""
        try:
            doc = docx.Document(file_path)
            text_content = ""
            
            for paragraph in doc.paragraphs:
                text_content += paragraph.text + "\n"
            
            # Extract text from tables
            for table in doc.tables:
                for row in table.rows:
                    for cell in row.cells:
                        text_content += cell.text + "\t"
                    text_content += "\n"
            
            if text_content.strip():
                return True, text_content
            else:
                return False, "No text found in document"
                
        except Exception as e:
            logger.error(f"Error extracting text from DOCX {file_path}: {e}")
            return False, f"Error processing DOCX: {str(e)}"
    
    def extract_text_from_txt(self, file_path: str) -> Tuple[bool, str]:
        """Extract text from plain text file"""
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as file:
                text_content = file.read()
            
            if text_content.strip():
                return True, text_content
            else:
                return False, "File is empty"
                
        except Exception as e:
            logger.error(f"Error reading text file {file_path}: {e}")
            return False, f"Error reading file: {str(e)}"
    
    def extract_text_from_csv(self, file_path: str) -> Tuple[bool, str]:
        """Extract text from CSV file"""
        try:
            df = pd.read_csv(file_path)
            text_content = df.to_string()
            
            if text_content.strip():
                return True, text_content
            else:
                return False, "CSV file is empty"
                
        except Exception as e:
            logger.error(f"Error reading CSV file {file_path}: {e}")
            return False, f"Error reading CSV: {str(e)}"
    
    def process_archive(self, file_path: str, case_id: int) -> Tuple[bool, str, List[Dict]]:
        """Process archive file and extract contents"""
        try:
            extracted_files = []
            
            with tempfile.TemporaryDirectory() as temp_dir:
                # Extract archive
                if file_path.endswith('.zip'):
                    with zipfile.ZipFile(file_path, 'r') as zip_ref:
                        zip_ref.extractall(temp_dir)
                else:
                    return False, "Unsupported archive format", []
                
                # Process extracted files
                for root, dirs, files in os.walk(temp_dir):
                    for file in files:
                        extracted_path = os.path.join(root, file)
                        
                        # Skip hidden files and directories
                        if file.startswith('.'):
                            continue
                        
                        try:
                            # Copy to upload directory
                            safe_filename = self.generate_safe_filename(file, case_id)
                            dest_path = self.upload_dir / safe_filename
                            
                            with open(extracted_path, 'rb') as src, open(dest_path, 'wb') as dst:
                                dst.write(src.read())
                            
                            # Get file info
                            file_size = os.path.getsize(dest_path)
                            file_info = {
                                'filename': safe_filename,
                                'original_filename': file,
                                'file_type': self.get_file_type_category(file),
                                'file_size': file_size,
                                'file_path': str(dest_path),
                                'extracted_from': os.path.basename(file_path)
                            }
                            
                            extracted_files.append(file_info)
                            
                        except Exception as e:
                            logger.warning(f"Error processing extracted file {file}: {e}")
                            continue
            
            return True, f"Extracted {len(extracted_files)} files", extracted_files
            
        except Exception as e:
            logger.error(f"Error processing archive {file_path}: {e}")
            return False, f"Error processing archive: {str(e)}", []
    
    def extract_text_content(self, file_path: str, file_type: str) -> Tuple[bool, str]:
        """Extract text content based on file type"""
        file_path_obj = Path(file_path)
        
        if not file_path_obj.exists():
            return False, "File not found"
        
        # Determine extraction method based on file type
        if file_type == 'pdf':
            return self.extract_text_from_pdf(file_path)
        elif file_type == 'image':
            return self.extract_text_from_image(file_path)
        elif file_path.endswith('.docx'):
            return self.extract_text_from_docx(file_path)
        elif file_path.endswith(('.txt', '.rtf')):
            return self.extract_text_from_txt(file_path)
        elif file_path.endswith('.csv'):
            return self.extract_text_from_csv(file_path)
        else:
            return False, f"Text extraction not supported for file type: {file_type}"
    
    def process_document(self, case_id: int, file_info: Dict, uploaded_by: int = None) -> Dict:
        """Process a document and extract all relevant information"""
        try:
            file_path = file_info['file_path']
            file_type = file_info['file_type']
            
            # Extract text content
            text_extracted, text_content = self.extract_text_content(file_path, file_type)
            
            # Generate content preview
            content_preview = ""
            if text_extracted and text_content:
                # Create preview (first 500 characters)
                content_preview = text_content[:500] + "..." if len(text_content) > 500 else text_content
            
            # Create metadata
            metadata = {
                'processed_at': datetime.now().isoformat(),
                'text_extracted': text_extracted,
                'text_length': len(text_content) if text_content else 0,
                'mime_type': file_info.get('mime_type'),
                'extraction_method': self._get_extraction_method(file_type)
            }
            
            # Save document to database
            doc_id = db_manager.add_document(
                case_id=case_id,
                filename=file_info['filename'],
                original_filename=file_info['original_filename'],
                file_type=file_type,
                file_size=file_info['file_size'],
                uploaded_by=uploaded_by,
                content_preview=content_preview,
                document_type=self._classify_document_type(file_info['original_filename'], text_content),
                ocr_text=text_content if text_extracted else None,
                metadata=json.dumps(metadata)
            )
            
            # Update processing status
            db_manager.update_document(doc_id, is_processed=True)
            
            logger.info(f"Processed document: {file_info['original_filename']} (ID: {doc_id})")
            
            return {
                'success': True,
                'document_id': doc_id,
                'text_extracted': text_extracted,
                'text_content': text_content,
                'content_preview': content_preview,
                'metadata': metadata
            }
            
        except Exception as e:
            logger.error(f"Error processing document {file_info.get('filename', 'unknown')}: {e}")
            return {
                'success': False,
                'error': str(e),
                'document_id': None
            }
    
    def _get_extraction_method(self, file_type: str) -> str:
        """Get the extraction method used for file type"""
        methods = {
            'pdf': 'PyPDF2',
            'image': 'Tesseract OCR',
            'document': 'python-docx',
            'spreadsheet': 'pandas',
            'other': 'text_reader'
        }
        return methods.get(file_type, 'unknown')
    
    def _classify_document_type(self, filename: str, text_content: str = None) -> str:
        """Classify document type based on filename and content"""
        filename_lower = filename.lower()
        
        # Legal document types based on filename
        if any(keyword in filename_lower for keyword in ['motion', 'pleading', 'brief']):
            return 'motion'
        elif any(keyword in filename_lower for keyword in ['contract', 'agreement']):
            return 'contract'
        elif any(keyword in filename_lower for keyword in ['evidence', 'exhibit']):
            return 'evidence'
        elif any(keyword in filename_lower for keyword in ['correspondence', 'letter', 'email']):
            return 'correspondence'
        elif any(keyword in filename_lower for keyword in ['discovery', 'deposition']):
            return 'discovery'
        elif any(keyword in filename_lower for keyword in ['court', 'order', 'judgment']):
            return 'court_document'
        
        # Content-based classification (if text is available)
        if text_content:
            text_lower = text_content.lower()
            if any(keyword in text_lower for keyword in ['whereas', 'party of the first part', 'agreement']):
                return 'contract'
            elif any(keyword in text_lower for keyword in ['motion for', 'respectfully requests']):
                return 'motion'
            elif any(keyword in text_lower for keyword in ['exhibit', 'evidence']):
                return 'evidence'
        
        return 'general'
    
    def get_document_stats(self, case_id: int) -> Dict:
        """Get document statistics for a case"""
        documents = db_manager.get_case_documents(case_id)
        
        stats = {
            'total_documents': len(documents),
            'total_size': sum(doc['file_size'] or 0 for doc in documents),
            'by_type': {},
            'processed_count': 0,
            'with_text': 0
        }
        
        for doc in documents:
            # Count by type
            doc_type = doc['file_type'] or 'unknown'
            stats['by_type'][doc_type] = stats['by_type'].get(doc_type, 0) + 1
            
            # Count processed documents
            if doc['is_processed']:
                stats['processed_count'] += 1
            
            # Count documents with extracted text
            if doc['ocr_text']:
                stats['with_text'] += 1
        
        return stats
    
    def delete_document_file(self, filename: str) -> bool:
        """Delete document file from storage"""
        try:
            file_path = self.upload_dir / filename
            if file_path.exists():
                file_path.unlink()
                logger.info(f"Deleted file: {filename}")
                return True
            else:
                logger.warning(f"File not found for deletion: {filename}")
                return False
        except Exception as e:
            logger.error(f"Error deleting file {filename}: {e}")
            return False

# Global document processor instance
document_processor = DocumentProcessor()


class GitHubDocumentBackup:
    """
    Handles backing up legal documents to GitHub repository
    """

    def __init__(self, repo_owner: str = "george-shepov", repo_name: str = "lcm-documents", token: str = None):
        self.repo_owner = repo_owner
        self.repo_name = repo_name
        self.token = token
        self.base_url = f"https://api.github.com/repos/{repo_owner}/{repo_name}"

    def upload_document(self, file_path: str, document_number: str, case_id: str) -> Dict[str, Any]:
        """Upload document to GitHub repository"""

        if not self.token:
            return {"success": False, "error": "GitHub token not configured"}

        try:
            # Read file content
            with open(file_path, 'rb') as f:
                file_content = f.read()

            # Encode content as base64
            encoded_content = base64.b64encode(file_content).decode('utf-8')

            # Create logical filename
            filename = Path(file_path).name
            logical_path = f"cases/{case_id}/documents/{document_number:0>3}_{filename}"

            # Check if file already exists
            existing_file = self._get_file_info(logical_path)

            # Prepare API request
            headers = {
                'Authorization': f'token {self.token}',
                'Accept': 'application/vnd.github.v3+json'
            }

            data = {
                'message': f'Add document #{document_number}: {filename}',
                'content': encoded_content,
                'branch': 'main'
            }

            # If file exists, include SHA for update
            if existing_file:
                data['sha'] = existing_file['sha']
                data['message'] = f'Update document #{document_number}: {filename}'

            # Upload to GitHub
            response = requests.put(
                f"{self.base_url}/contents/{logical_path}",
                headers=headers,
                json=data
            )

            if response.status_code in [200, 201]:
                result = response.json()
                return {
                    "success": True,
                    "github_url": result['content']['html_url'],
                    "download_url": result['content']['download_url'],
                    "sha": result['content']['sha'],
                    "path": logical_path
                }
            else:
                return {
                    "success": False,
                    "error": f"GitHub API error: {response.status_code} - {response.text}"
                }

        except Exception as e:
            return {"success": False, "error": f"Upload failed: {str(e)}"}

    def download_document(self, document_path: str) -> Optional[bytes]:
        """Download document from GitHub"""
        try:
            headers = {
                'Authorization': f'token {self.token}',
                'Accept': 'application/vnd.github.v3+json'
            }

            # Get file info first
            file_info = self._get_file_info(document_path)
            if not file_info:
                return None

            # Download raw content
            response = requests.get(file_info['download_url'])

            if response.status_code == 200:
                return response.content
            else:
                return None

        except Exception as e:
            logging.error(f"Download failed: {e}")
            return None


# Global GitHub backup instance
github_backup = GitHubDocumentBackup()

```

## File: forensic_extension.py
```python
"""
Forensic Extension for Legal Case Management System
Extends existing database managers with forensic document tracking and tampering detection
Uses the existing PostgreSQL, MongoDB, and Redis infrastructure
"""

import hashlib
import json
import difflib
from datetime import datetime, timezone
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
import uuid
import logging

from .database_managers import DatabaseManagerFactory

logger = logging.getLogger(__name__)

@dataclass
class DocumentSnapshot:
    """Immutable document snapshot for forensic comparison"""
    document_id: str
    case_id: int
    filing_date: str
    document_type: str
    original_text: str
    hash_sha256: str
    timestamp: datetime
    source_url: str
    filing_party: str
    docket_sequence: int

@dataclass
class TamperingEvidence:
    """Evidence of document tampering or unauthorized changes"""
    incident_id: str
    document_id: str
    case_id: int
    tamper_type: str
    detection_time: datetime
    original_hash: str
    modified_hash: str
    differences: List[str]
    severity: str
    evidence_preserved: bool
    judicial_actor: Optional[str]
    misconduct_indicators: List[str]

class ForensicExtension:
    """
    Forensic extension that adds tampering detection to existing database managers
    """
    
    def __init__(self, db_managers: Dict):
        self.logger = logging.getLogger(__name__)
        self.db_managers = db_managers
        self.postgresql = db_managers.get('postgresql')
        self.mongodb = db_managers.get('mongodb')
        self.redis = db_managers.get('redis')
        
        if not self.postgresql:
            raise RuntimeError("PostgreSQL manager required for forensic extension")
            
    def init_forensic_schema(self):
        """Add forensic tracking tables to existing PostgreSQL schema"""
        forensic_schema = """
        -- Forensic document tracking tables
        CREATE TABLE IF NOT EXISTS document_snapshots (
            id SERIAL PRIMARY KEY,
            document_id VARCHAR(255) NOT NULL,
            case_id INTEGER REFERENCES cases(id),
            filing_date DATE NOT NULL,
            document_type VARCHAR(100) NOT NULL,
            hash_sha256 VARCHAR(64) NOT NULL,
            timestamp TIMESTAMPTZ DEFAULT NOW(),
            source_url TEXT,
            filing_party VARCHAR(100),
            docket_sequence INTEGER,
            created_at TIMESTAMPTZ DEFAULT NOW()
        );
        
        CREATE TABLE IF NOT EXISTS tampering_evidence (
            id SERIAL PRIMARY KEY,
            incident_id VARCHAR(255) UNIQUE NOT NULL,
            document_id VARCHAR(255) NOT NULL,
            case_id INTEGER REFERENCES cases(id),
            tamper_type VARCHAR(50) NOT NULL,
            detection_time TIMESTAMPTZ DEFAULT NOW(),
            original_hash VARCHAR(64) NOT NULL,
            modified_hash VARCHAR(64),
            severity VARCHAR(20) NOT NULL,
            evidence_preserved BOOLEAN DEFAULT TRUE,
            judicial_actor VARCHAR(200),
            reported_to_authorities BOOLEAN DEFAULT FALSE,
            investigation_status VARCHAR(50) DEFAULT 'OPEN',
            created_at TIMESTAMPTZ DEFAULT NOW()
        );
        
        CREATE TABLE IF NOT EXISTS judicial_misconduct (
            id SERIAL PRIMARY KEY,
            case_id INTEGER REFERENCES cases(id),
            incident_type VARCHAR(100) NOT NULL,
            description TEXT NOT NULL,
            evidence_documents TEXT[],
            judicial_officer VARCHAR(200),
            incident_date TIMESTAMPTZ NOT NULL,
            severity VARCHAR(20) NOT NULL,
            status VARCHAR(50) DEFAULT 'DOCUMENTED',
            witness_accounts TEXT[],
            legal_violations TEXT[],
            disciplinary_referral BOOLEAN DEFAULT FALSE,
            created_at TIMESTAMPTZ DEFAULT NOW()
        );

        -- Forensic indexes
        CREATE INDEX IF NOT EXISTS idx_document_snapshots_case ON document_snapshots(case_id);
        CREATE INDEX IF NOT EXISTS idx_document_snapshots_hash ON document_snapshots(hash_sha256);
        CREATE INDEX IF NOT EXISTS idx_tampering_evidence_case ON tampering_evidence(case_id);
        CREATE INDEX IF NOT EXISTS idx_tampering_severity ON tampering_evidence(severity);
        CREATE INDEX IF NOT EXISTS idx_judicial_misconduct_case ON judicial_misconduct(case_id);
        CREATE INDEX IF NOT EXISTS idx_judicial_misconduct_officer ON judicial_misconduct(judicial_officer);
        """
        
        try:
            with self.postgresql.get_connection() as conn:
                with conn.cursor() as cursor:
                    cursor.execute(forensic_schema)
                    conn.commit()
            self.logger.info("🔒 Forensic schema initialized successfully")
        except Exception as e:
            self.logger.error(f"❌ Failed to initialize forensic schema: {e}")
            raise
            
    def init_mongo_collections(self):
        """Initialize MongoDB collections for forensic document storage"""
        if not self.mongodb:
            self.logger.warning("MongoDB not available for forensic storage")
            return
            
        try:
            # Get MongoDB client from manager
            db = self.mongodb.get_database()
            
            # Document versions collection
            db.document_versions.create_index([
                ("document_id", 1),
                ("version", 1),
                ("timestamp", -1)
            ])
            
            # Original filings collection (immutable)
            db.original_filings.create_index([
                ("case_number", 1),
                ("filing_date", 1),
                ("document_type", 1)
            ])
            
            # Tampering evidence collection
            db.tampering_evidence.create_index([
                ("incident_id", 1),
                ("case_id", 1),
                ("detection_time", -1)
            ])
            
            self.logger.info("🔒 Forensic MongoDB collections initialized")
        except Exception as e:
            self.logger.error(f"❌ Failed to initialize MongoDB collections: {e}")
            
    def preserve_document_snapshot(self, docket_entry: Dict, case_id: int) -> DocumentSnapshot:
        """Create immutable forensic snapshot of document"""
        try:
            # Extract document content
            document_text = self.extract_document_content(docket_entry)
            
            # Create cryptographic hash
            hash_sha256 = hashlib.sha256(document_text.encode('utf-8')).hexdigest()
            
            # Create snapshot
            snapshot = DocumentSnapshot(
                document_id=f"{case_id}_{docket_entry.get('Docket Number', 'unknown')}",
                case_id=case_id,
                filing_date=docket_entry.get('Filing Date', 'unknown'),
                document_type=docket_entry.get('Type', 'unknown'),
                original_text=document_text,
                hash_sha256=hash_sha256,
                timestamp=datetime.now(timezone.utc),
                source_url=docket_entry.get('source_url', ''),
                filing_party=docket_entry.get('Side', 'unknown'),
                docket_sequence=int(docket_entry.get('Docket Number', 0))
            )
            
            # Store in PostgreSQL
            self.store_snapshot_postgresql(snapshot)
            
            # Store full content in MongoDB if available
            if self.mongodb:
                self.store_snapshot_mongodb(snapshot, docket_entry)
            
            # Cache in Redis if available
            if self.redis:
                self.cache_snapshot_redis(snapshot)
            
            self.logger.info(f"📸 Document snapshot preserved: {snapshot.document_id}")
            return snapshot
            
        except Exception as e:
            self.logger.error(f"❌ Failed to preserve document snapshot: {e}")
            raise
            
    def detect_document_tampering(self, current_entry: Dict, case_id: int) -> Optional[TamperingEvidence]:
        """Compare current document against stored snapshots to detect tampering"""
        try:
            document_id = f"{case_id}_{current_entry.get('Docket Number', 'unknown')}"
            
            # Get original snapshot
            original_snapshot = self.get_original_snapshot(document_id)
            if not original_snapshot:
                self.logger.warning(f"⚠️ No original snapshot found for {document_id}")
                return None
                
            # Extract current content
            current_text = self.extract_document_content(current_entry)
            current_hash = hashlib.sha256(current_text.encode('utf-8')).hexdigest()
            
            # Compare hashes
            if current_hash == original_snapshot['hash_sha256']:
                return None  # No tampering detected
                
            # Analyze differences
            differences = self.analyze_document_differences(
                original_snapshot.get('original_text', ''), 
                current_text
            )
            
            # Determine tampering type and severity
            tamper_type, severity, misconduct_indicators = self.classify_tampering(
                differences, current_entry, original_snapshot
            )
            
            # Create tampering evidence
            evidence = TamperingEvidence(
                incident_id=str(uuid.uuid4()),
                document_id=document_id,
                case_id=case_id,
                tamper_type=tamper_type,
                detection_time=datetime.now(timezone.utc),
                original_hash=original_snapshot['hash_sha256'],
                modified_hash=current_hash,
                differences=differences,
                severity=severity,
                evidence_preserved=True,
                judicial_actor=self.identify_judicial_actor(current_entry),
                misconduct_indicators=misconduct_indicators
            )
            
            # Store tampering evidence
            self.store_tampering_evidence(evidence)
            
            # Store detailed evidence in MongoDB
            if self.mongodb:
                self.store_detailed_evidence_mongo(evidence, original_snapshot, current_text)
            
            # Alert for critical tampering
            if severity == 'CRITICAL':
                self.trigger_critical_alert(evidence)
                
            self.logger.critical(f"🚨 DOCUMENT TAMPERING DETECTED: {evidence.incident_id}")
            return evidence
            
        except Exception as e:
            self.logger.error(f"❌ Failed to detect tampering: {e}")
            return None
            
    def extract_document_content(self, docket_entry: Dict) -> str:
        """Extract full text content from docket entry"""
        content_parts = []
        
        # Include all available text fields
        for key, value in docket_entry.items():
            if isinstance(value, str) and value.strip():
                content_parts.append(f"{key}: {value}")
                
        return "\n".join(content_parts)
        
    def analyze_document_differences(self, original: str, current: str) -> List[str]:
        """Analyze specific differences between document versions"""
        differences = []
        
        # Line-by-line comparison
        original_lines = original.splitlines()
        current_lines = current.splitlines()
        
        diff = list(difflib.unified_diff(
            original_lines, current_lines,
            fromfile='original', tofile='current',
            lineterm=''
        ))
        
        for line in diff:
            if line.startswith('+ ') or line.startswith('- '):
                differences.append(line)
                
        return differences
        
    def classify_tampering(self, differences: List[str], current_entry: Dict, original: Dict) -> Tuple[str, str, List[str]]:
        """Classify type and severity of tampering"""
        misconduct_indicators = []
        
        # Check for judicial misconduct patterns
        if any('judgment entry' in diff.lower() for diff in differences):
            if current_entry.get('Side') != original.get('filing_party'):
                misconduct_indicators.append("UNAUTHORIZED_AUTHOR_CHANGE")
                
        if any('stricken' in diff.lower() for diff in differences):
            misconduct_indicators.append("EVIDENCE_SUPPRESSION")
            
        if len(differences) > 50:  # Extensive changes
            misconduct_indicators.append("SUBSTANTIAL_ALTERATION")
            
        # Determine tampering type
        if "UNAUTHORIZED_AUTHOR_CHANGE" in misconduct_indicators:
            tamper_type = "DOCUMENT_REPLACED"
            severity = "CRITICAL"
        elif "EVIDENCE_SUPPRESSION" in misconduct_indicators:
            tamper_type = "STRICKEN_EVIDENCE"
            severity = "CRITICAL"
        elif len(differences) > 10:
            tamper_type = "CONTENT_ALTERED"
            severity = "HIGH"
        else:
            tamper_type = "MINOR_EDIT"
            severity = "MEDIUM"
            
        return tamper_type, severity, misconduct_indicators
        
    def identify_judicial_actor(self, entry: Dict) -> Optional[str]:
        """Identify judicial officer responsible for changes"""
        description = entry.get('Description', '').lower()
        
        if 'magistrate' in description:
            if 'charmine dose' in description:
                return "MAGISTRATE CHARMINE DOSE"
            elif 'jason p. parker' in description:
                return "MAGISTRATE JASON P. PARKER"
                
        if 'judge' in description:
            if 'colleen ann reali' in description:
                return "JUDGE COLLEEN ANN REALI"
                
        return None
        
    def store_snapshot_postgresql(self, snapshot: DocumentSnapshot):
        """Store snapshot metadata in PostgreSQL"""
        sql = """
        INSERT INTO document_snapshots 
        (document_id, case_id, filing_date, document_type, hash_sha256, 
         timestamp, source_url, filing_party, docket_sequence)
        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
        """
        
        with self.postgresql.get_connection() as conn:
            with conn.cursor() as cursor:
                cursor.execute(sql, (
                    snapshot.document_id, snapshot.case_id, snapshot.filing_date,
                    snapshot.document_type, snapshot.hash_sha256, snapshot.timestamp,
                    snapshot.source_url, snapshot.filing_party, snapshot.docket_sequence
                ))
                conn.commit()
                
    def store_snapshot_mongodb(self, snapshot: DocumentSnapshot, full_entry: Dict):
        """Store complete document content in MongoDB"""
        document = {
            "document_id": snapshot.document_id,
            "case_id": snapshot.case_id,
            "version": 1,
            "timestamp": snapshot.timestamp,
            "hash_sha256": snapshot.hash_sha256,
            "original_text": snapshot.original_text,
            "full_entry": full_entry,
            "preserved_for": "FORENSIC_EVIDENCE",
            "immutable": True
        }
        
        db = self.mongodb.get_database()
        db.original_filings.insert_one(document)
        
    def cache_snapshot_redis(self, snapshot: DocumentSnapshot):
        """Cache snapshot in Redis for fast access"""
        if not self.redis:
            return
            
        key = f"forensic:snapshot:{snapshot.document_id}"
        data = {
            "hash": snapshot.hash_sha256,
            "timestamp": snapshot.timestamp.isoformat(),
            "type": snapshot.document_type
        }
        
        client = self.redis.get_client()
        client.hset(key, mapping=data)
        client.expire(key, 86400)  # 24 hour cache
        
    def get_original_snapshot(self, document_id: str) -> Optional[Dict]:
        """Retrieve original document snapshot"""
        sql = """
        SELECT * FROM document_snapshots 
        WHERE document_id = %s 
        ORDER BY timestamp ASC 
        LIMIT 1
        """
        
        with self.postgresql.get_connection() as conn:
            with conn.cursor() as cursor:
                cursor.execute(sql, (document_id,))
                row = cursor.fetchone()
                
                if row:
                    # Convert to dict
                    columns = [desc[0] for desc in cursor.description]
                    result = dict(zip(columns, row))
                    
                    # Get original text from MongoDB if available
                    if self.mongodb:
                        db = self.mongodb.get_database()
                        mongo_doc = db.original_filings.find_one({
                            "document_id": document_id
                        })
                        if mongo_doc:
                            result['original_text'] = mongo_doc.get('original_text', '')
                    
                    return result
                    
        return None
        
    def store_tampering_evidence(self, evidence: TamperingEvidence):
        """Store tampering evidence in PostgreSQL"""
        sql = """
        INSERT INTO tampering_evidence 
        (incident_id, document_id, case_id, tamper_type, detection_time,
         original_hash, modified_hash, severity, evidence_preserved, judicial_actor)
        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        """
        
        with self.postgresql.get_connection() as conn:
            with conn.cursor() as cursor:
                cursor.execute(sql, (
                    evidence.incident_id, evidence.document_id, evidence.case_id,
                    evidence.tamper_type, evidence.detection_time, evidence.original_hash,
                    evidence.modified_hash, evidence.severity, evidence.evidence_preserved,
                    evidence.judicial_actor
                ))
                conn.commit()
                
    def store_detailed_evidence_mongo(self, evidence: TamperingEvidence, original: Dict, current_text: str):
        """Store detailed tampering evidence in MongoDB"""
        evidence_doc = {
            "incident_id": evidence.incident_id,
            "document_id": evidence.document_id,
            "case_id": evidence.case_id,
            "tamper_type": evidence.tamper_type,
            "detection_time": evidence.detection_time,
            "differences": evidence.differences,
            "misconduct_indicators": evidence.misconduct_indicators,
            "original_content": original.get('original_text', ''),
            "modified_content": current_text,
            "evidence_chain": "PRESERVED",
            "forensic_hash": evidence.modified_hash
        }
        
        db = self.mongodb.get_database()
        db.tampering_evidence.insert_one(evidence_doc)
        
    def trigger_critical_alert(self, evidence: TamperingEvidence):
        """Trigger critical alert for severe tampering"""
        if not self.redis:
            return
            
        alert_key = f"critical:tampering:{evidence.case_id}"
        alert_data = {
            "incident_id": evidence.incident_id,
            "tamper_type": evidence.tamper_type,
            "judicial_actor": evidence.judicial_actor or "UNKNOWN",
            "detection_time": evidence.detection_time.isoformat(),
            "severity": "CRITICAL_JUDICIAL_MISCONDUCT"
        }
        
        client = self.redis.get_client()
        client.hset(alert_key, mapping=alert_data)
        client.expire(alert_key, 604800)  # Keep for 7 days
        
        self.logger.critical(f"🚨 CRITICAL JUDICIAL MISCONDUCT DETECTED: {evidence.incident_id}")
        
    def generate_misconduct_report(self, case_id: int) -> Dict:
        """Generate comprehensive misconduct evidence report"""
        sql_tampering = """
        SELECT * FROM tampering_evidence 
        WHERE case_id = %s 
        ORDER BY detection_time DESC
        """
        
        sql_misconduct = """
        SELECT * FROM judicial_misconduct 
        WHERE case_id = %s 
        ORDER BY incident_date DESC
        """
        
        with self.postgresql.get_connection() as conn:
            with conn.cursor() as cursor:
                # Get tampering evidence
                cursor.execute(sql_tampering, (case_id,))
                tampering_rows = cursor.fetchall()
                tampering_columns = [desc[0] for desc in cursor.description]
                tampering_evidence = [dict(zip(tampering_columns, row)) for row in tampering_rows]
                
                # Get misconduct incidents
                cursor.execute(sql_misconduct, (case_id,))
                misconduct_rows = cursor.fetchall()
                misconduct_columns = [desc[0] for desc in cursor.description]
                misconduct_incidents = [dict(zip(misconduct_columns, row)) for row in misconduct_rows]
        
        return {
            "case_id": case_id,
            "report_generated": datetime.now(timezone.utc).isoformat(),
            "tampering_incidents": len(tampering_evidence),
            "critical_incidents": sum(1 for e in tampering_evidence if e['severity'] == 'CRITICAL'),
            "evidence_preserved": True,
            "legal_violations": self.identify_legal_violations(tampering_evidence),
            "recommended_actions": self.generate_legal_recommendations(tampering_evidence),
            "detailed_evidence": tampering_evidence,
            "misconduct_summary": misconduct_incidents
        }
        
    def identify_legal_violations(self, tampering_evidence: List[Dict]) -> List[str]:
        """Identify specific legal violations from evidence"""
        violations = []
        
        for evidence in tampering_evidence:
            if evidence['tamper_type'] == 'DOCUMENT_REPLACED':
                violations.append("FRAUDULENT_COURT_RECORD_ALTERATION")
            if evidence['tamper_type'] == 'STRICKEN_EVIDENCE':
                violations.append("EVIDENCE_SUPPRESSION_WITHOUT_PROPER_ORDER")
            if evidence['judicial_actor']:
                violations.append("JUDICIAL_MISCONDUCT_IN_OFFICE")
                
        return list(set(violations))
        
    def generate_legal_recommendations(self, tampering_evidence: List[Dict]) -> List[str]:
        """Generate legal action recommendations"""
        recommendations = []
        
        if len(tampering_evidence) > 5:
            recommendations.append("FILE_JUDICIAL_CONDUCT_COMPLAINT")
        if any(e['severity'] == 'CRITICAL' for e in tampering_evidence):
            recommendations.append("REQUEST_IMMEDIATE_CASE_TRANSFER")
        if any('DOCUMENT_REPLACED' in e['tamper_type'] for e in tampering_evidence):
            recommendations.append("MOTION_FOR_SANCTIONS_JUDICIAL_MISCONDUCT")
            
        recommendations.append("PRESERVE_ALL_DIGITAL_EVIDENCE")
        recommendations.append("CONTACT_STATE_BAR_ASSOCIATION")
        
        return recommendations


def create_forensic_extension(database_config: Dict) -> ForensicExtension:
    """Factory function to create forensic extension with existing database managers"""
    # Create database managers using existing factory
    db_managers = DatabaseManagerFactory.create_managers(database_config)
    
    # Create forensic extension
    forensic_ext = ForensicExtension(db_managers)
    
    # Initialize forensic schema
    forensic_ext.init_forensic_schema()
    if 'mongodb' in db_managers:
        forensic_ext.init_mongo_collections()
    
    return forensic_ext
```

## File: efiling_ui.py
```python
"""
Streamlit UI integration for E-Filing system scraping
Provides user-friendly interface for credentials and case information
"""

import streamlit as st
import json
import os
from datetime import datetime
from pathlib import Path
import asyncio
import time

try:
    from .efiling_scraper import CuyahogaEFilingScraperPlaywright, scrape_cuyahoga_case, PLAYWRIGHT_AVAILABLE
    from .docket_viewer import render_court_style_docket
except ImportError:
    # Fallback for direct imports
    from efiling_scraper import CuyahogaEFilingScraperPlaywright, scrape_cuyahoga_case, PLAYWRIGHT_AVAILABLE
    try:
        from docket_viewer import render_court_style_docket
    except ImportError:
        render_court_style_docket = None

def render_efiling_integration():
    """
    Render the enhanced E-Filing integration UI
    """
    # Initialize persistent settings on first load
    init_persistent_settings()
    
    st.header("🏛️ Enhanced Court E-Filing Integration")
    
    if not PLAYWRIGHT_AVAILABLE:
        st.error("❌ Playwright not available. Please install with:")
        st.code("pip install playwright && playwright install chromium", language="bash")
        return
        
    st.info("🔒 **Secure Automated E-Filing Data Retrieval**")
    st.write("Automatically login and scrape case data from Cuyahoga County E-Filing system.")
    
    # Create two columns for better layout
    col1, col2 = st.columns(2)
    
    with col1:
        st.subheader("Court Information")
        
        # Court selection (expandable for future courts)
        court_options = [
            "Cuyahoga County Common Pleas Court",
            # Future: "Franklin County", "Hamilton County", etc.
        ]
        # Get the saved court selection
        saved_court = st.session_state.get('efiling_court', 'Cuyahoga County Common Pleas Court')
        court_index = 0
        try:
            court_index = court_options.index(saved_court)
        except ValueError:
            court_index = 0
            
        selected_court = st.selectbox(
            "Select Court System",
            court_options,
            index=court_index,
            help="Currently supports Cuyahoga County. More courts coming soon!"
        )
        
        # Case information
        case_number = st.text_input(
            "Case Number",
            value=st.session_state.get('efiling_case_number', ''),
            placeholder="DR-25-403973",
            help="Enter your case number exactly as it appears in the system"
        )
        
        case_name = st.text_input(
            "Case Name (Optional)",
            value=st.session_state.get('efiling_case_name', ''),
            placeholder="SHEPOV vs SHEPOV", 
            help="Case name for identification (optional)"
        )
        
    with col2:
        st.subheader("E-Filing Credentials")
        
        # Show credential status
        if st.session_state.get('efiling_username') and st.session_state.get('efiling_password'):
            st.success("🔑 Credentials are saved for this session")
        
        # Credentials (with session state persistence)
        username = st.text_input(
            "Username",
            value=st.session_state.get('efiling_username', ''),
            placeholder="GSHEPOV",
            help="Your e-filing system username"
        )
        
        password = st.text_input(
            "Password", 
            value=st.session_state.get('efiling_password', ''),
            type="password",
            placeholder="Enter your password",
            help="Your e-filing system password (stored securely)"
        )
        
        # Advanced options
        with st.expander("Advanced Options"):
            headless_mode = st.checkbox(
                "Run in background (headless mode)",
                value=st.session_state.get('efiling_headless', True),
                help="Uncheck to see browser window during scraping",
                key="efiling_headless_checkbox"
            )
            
            save_credentials = st.checkbox(
                "Remember credentials for this session",
                value=st.session_state.get('save_efiling_creds', False),
                help="Credentials will be cleared when you close the browser"
            )
            
            # Persistent storage option
            persistent_save = st.checkbox(
                "💾 Save settings persistently",
                value=st.session_state.get('efiling_persistent_save', False),
                help="Store all settings in file (survives browser restart and F5 refresh)",
                key="efiling_persistent_checkbox"
            )
            
            # Explicit save and clear buttons
            st.divider()
            col1, col2, col3 = st.columns(3)
            
            with col1:
                if st.button("💾 **Save All Settings**", type="primary", help="Save all form inputs persistently", key="save_all_settings"):
                    try:
                        save_persistent_settings({
                            'username': username,
                            'password': password if persistent_save else '',  # Only save password if explicitly requested
                            'case_number': case_number,
                            'case_name': case_name,
                            'headless_mode': headless_mode,
                            'save_credentials': save_credentials,
                            'persistent_save': persistent_save,
                            'court': selected_court
                        })
                        
                        # Update session state
                        st.session_state.efiling_username = username
                        st.session_state.efiling_password = password
                        st.session_state.efiling_case_number = case_number
                        st.session_state.efiling_case_name = case_name
                        st.session_state.efiling_headless = headless_mode
                        st.session_state.save_efiling_creds = save_credentials
                        st.session_state.efiling_persistent_save = persistent_save
                        st.session_state.efiling_court = selected_court
                        
                        st.success("💾 All settings saved! They will persist across F5 refreshes.")
                        st.rerun()
                    except Exception as e:
                        st.error(f"Failed to save settings: {e}")
            
            with col2:
                if st.button("🗑️ Clear Session", help="Clear session data only"):
                    st.session_state.efiling_username = ""
                    st.session_state.efiling_password = ""
                    st.session_state.save_efiling_creds = False
                    st.success("✅ Session cleared!")
                    st.rerun()
                    
            with col3:
                if st.button("🔥 Clear All", help="Clear both session and persistent storage"):
                    try:
                        clear_persistent_settings()
                        # Clear session state
                        for key in list(st.session_state.keys()):
                            if key.startswith('efiling_'):
                                del st.session_state[key]
                        st.success("🔥 All settings cleared!")
                        st.rerun()
                    except Exception as e:
                        st.error(f"Failed to clear settings: {e}")
    
    # Action buttons
    st.divider()
    
    if st.button("🚀 Start E-Filing Data Scraping", type="primary", disabled=not (username and password and case_number)):
        if not username or not password or not case_number:
            st.error("Please fill in all required fields (Username, Password, Case Number)")
            return
            
        # Save credentials to session state if requested
        if save_credentials:
            st.session_state.efiling_username = username
            st.session_state.efiling_password = password
            st.session_state.save_efiling_creds = True
            st.success("🔒 Credentials saved for this session")
            
        # Show what will be scraped
        st.info(f"""
        **Starting automated scrape for:**
        - **Court**: {selected_court}
        - **Case**: {case_number} {f'({case_name})' if case_name else ''}
        - **Username**: {username}
        
        **Will scrape 4 key areas:**
        1. 📋 Docket Information
        2. 🖼️ Images (all pages) 
        3. 📧 E-Service Queue
        4. 📄 My Filings
        """)
        
        # Progress tracking
        progress_bar = st.progress(0)
        status_text = st.empty()
        
        try:
            with st.spinner("🔍 Scraping e-filing data..."):
                # Update progress
                status_text.text("🔐 Logging into e-filing system...")
                progress_bar.progress(10)
                
                # Start the scraping process
                start_time = time.time()
                
                # Use the synchronous wrapper
                results = scrape_cuyahoga_case(
                    username=username,
                    password=password, 
                    case_number=case_number,
                    headless=headless_mode
                )
                
                end_time = time.time()
                duration = end_time - start_time
                
                progress_bar.progress(100)
                status_text.text("✅ Scraping completed!")
                
                # Display results
                if 'error' in results:
                    st.error(f"❌ Scraping failed: {results['error']}")
                else:
                    st.success(f"✅ Successfully scraped case data in {duration:.1f} seconds")
                    
                    # Show summary
                    display_scraping_results(results, case_number, case_name)
                    
                    # Save results
                    save_scraping_results(results, case_number)
                    
        except Exception as e:
            st.error(f"❌ Unexpected error during scraping: {str(e)}")
            progress_bar.progress(0)
            status_text.text("❌ Scraping failed")
            
    # Show saved credentials if available
    if save_credentials and 'efiling_username' in st.session_state:
        st.sidebar.success(f"✅ Credentials saved for: {st.session_state.efiling_username}")
        if st.sidebar.button("🗑️ Clear Saved Credentials"):
            del st.session_state.efiling_username
            del st.session_state.efiling_password
            st.sidebar.success("✅ Credentials cleared")
            st.rerun()

def display_scraping_results(results: dict, case_number: str, case_name: str = ""):
    """
    Display the scraping results in an organized way
    """
    st.subheader(f"📊 Scraping Results for {case_number}")
    if case_name:
        st.write(f"**Case Name:** {case_name}")
        
    # Create tabs for each section
    tab1, tab2, tab3, tab4, tab5, tab6 = st.tabs([
        "📋 Summary", 
        "⚖️ Docket View", 
        "🖼️ Images & Downloads", 
        "📧 E-Service", 
        "📄 My Filings",
        "💾 Raw Data"
    ])
    
    with tab1:
        st.subheader("Scraping Summary")
        
        # Summary metrics
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            docket_count = len(results.get('docket_information', {}).get('entries', []))
            st.metric("Docket Entries", docket_count)
            
        with col2:
            images_count = len(results.get('images', {}).get('images', []))
            st.metric("Image Links", images_count)
            
        with col3:
            eservice_count = len(results.get('eservice_queue', {}).get('notices', []))
            st.metric("E-Service Notices", eservice_count)
            
        with col4:
            filings_count = len(results.get('my_filings', {}).get('filings', []))
            st.metric("My Filings", filings_count)
            
        # URLs accessed
        st.subheader("URLs Accessed")
        sections = ['docket_information', 'images', 'eservice_queue', 'my_filings']
        for section in sections:
            section_data = results.get(section, {})
            if 'url' in section_data:
                st.write(f"**{section.replace('_', ' ').title()}:** `{section_data['url']}`")
                
        # Raw data download
        st.subheader("Export Data")
        json_data = json.dumps(results, indent=2, default=str)
        st.download_button(
            "📥 Download Raw JSON Data",
            json_data,
            f"efiling_data_{case_number}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json",
            "application/json"
        )
        
    with tab2:
        # Use the court-style docket viewer
        docket_data = results.get('docket_information', {})
        
        if 'error' in docket_data:
            st.error(f"Error accessing docket: {docket_data['error']}")
        elif render_court_style_docket:
            render_court_style_docket(docket_data, case_number)
        else:
            # Fallback to simple display if viewer not available
            st.subheader("Docket Information")
            entries = docket_data.get('entries', [])
            if entries:
                st.write(f"Found {len(entries)} docket entries:")
                try:
                    import pandas as pd
                    df = pd.DataFrame(entries)
                    st.dataframe(df, use_container_width=True)
                except:
                    st.json(entries[:10])
            else:
                st.warning("No docket entries found")
                
    with tab3:
        st.subheader("📄 Document Images & Downloads")
        
        # Check docket entries for images
        docket_data = results.get('docket_information', {})
        docket_entries = docket_data.get('entries', [])
        
        # Count entries with images
        entries_with_images = [entry for entry in docket_entries if entry.get('Has_Image')]
        
        col1, col2, col3 = st.columns(3)
        with col1:
            st.metric("Total Docket Entries", len(docket_entries))
        with col2:
            st.metric("Entries with Images", len(entries_with_images))
        with col3:
            st.metric("Download Status", "Ready" if entries_with_images else "No images")
        
        if entries_with_images:
            st.success(f"✅ Found {len(entries_with_images)} document entries with downloadable images!")
            
            # Show sample of entries with images
            st.subheader("📋 Documents Available for Download")
            for entry in entries_with_images[:10]:  # Show first 10
                with st.expander(f"📄 {entry.get('Document Classification', 'Document')} - {entry.get('Filing Date', 'Unknown date')}"):
                    col1, col2 = st.columns([3, 1])
                    with col1:
                        st.write(f"**Description:** {entry.get('Description', 'N/A')}")
                        st.write(f"**Type:** {entry.get('Type', 'N/A')}")
                        st.write(f"**Side:** {entry.get('Side', 'N/A')}")
                    with col2:
                        st.success("📄 **Image Available**")
                        if entry.get('Image_Link'):
                            st.caption(f"Link: {entry['Image_Link'][:30]}...")
            
            if len(entries_with_images) > 10:
                st.info(f"Showing first 10 of {len(entries_with_images)} entries with images.")
                
            # Download button (placeholder for now)
            st.divider()
            st.subheader("💾 Bulk Download")
            if st.button("📥 **Download All Document Images**", type="primary"):
                st.info("🚧 **Image download functionality is ready but requires user confirmation**")
                st.write("The system has detected the following downloadable documents:")
                for entry in entries_with_images:
                    st.write(f"- {entry.get('Filing Date', 'Unknown')}: {entry.get('Document Classification', 'Document')}")
                st.warning("⚠️ **Note:** Downloading court documents may take several minutes and requires active session")
                
        else:
            st.warning("No document images found in the docket entries")
            
        # Also show traditional images section
        st.divider()
        st.subheader("🖼️ Traditional Image Links")
        images_data = results.get('images', {})
        
        if 'error' in images_data:
            st.error(f"Error accessing images: {images_data['error']}")
        else:
            images = images_data.get('images', [])
            total_pages = images_data.get('total_pages', 0)
            
            if total_pages:
                st.info(f"Total pages available: {total_pages}")
                
            if images:
                st.write(f"Found {len(images)} additional image links:")
                for i, img in enumerate(images[:10]):
                    with st.expander(f"Image Link {i+1}: {img.get('title', 'No title')}"):
                        st.write(f"**URL:** {img.get('url', 'N/A')}")
                        st.write(f"**Title:** {img.get('title', 'N/A')}")
                        if st.button(f"📥 Open Link {i+1}", key=f"img_{i}"):
                            st.write(f"[Open in new tab]({img.get('url', '#')})")
            else:
                st.warning("No image links found")
                
    with tab4:
        st.subheader("E-Service Queue")
        eservice_data = results.get('eservice_queue', {})
        
        if 'error' in eservice_data:
            st.error(f"Error accessing e-service: {eservice_data['error']}")
        else:
            notices = eservice_data.get('notices', [])
            if notices:
                st.write(f"Found {len(notices)} e-service notices:")
                for i, notice in enumerate(notices):
                    with st.expander(f"Notice {i+1}: {notice.get('title', 'No title')}"):
                        st.write(f"**URL:** {notice.get('url', 'N/A')}")
                        st.write(f"**Title:** {notice.get('title', 'N/A')}")
                        if st.button(f"📥 Open Notice {i+1}", key=f"notice_{i}"):
                            st.write(f"[Open in new tab]({notice.get('url', '#')})")
            else:
                st.warning("No e-service notices found")
                
    with tab5:
        st.subheader("My Filings")
        filings_data = results.get('my_filings', {})
        
        if 'error' in filings_data:
            st.error(f"Error accessing my filings: {filings_data['error']}")
        else:
            filings = filings_data.get('filings', [])
            if filings:
                st.write(f"Found {len(filings)} filing links:")
                for i, filing in enumerate(filings):
                    with st.expander(f"Filing {i+1}: {filing.get('title', 'No title')}"):
                        st.write(f"**URL:** {filing.get('url', 'N/A')}")
                        st.write(f"**Title:** {filing.get('title', 'N/A')}")
                        if st.button(f"📥 Open Filing {i+1}", key=f"filing_{i}"):
                            st.write(f"[Open in new tab]({filing.get('url', '#')})")
            else:
                st.warning("No filing links found")
                
    with tab6:
        st.subheader("💾 Complete Raw Data")
        st.write("This tab contains the complete scraped data in JSON format.")
        
        # Expandable sections for each data type
        with st.expander("📋 **Docket Information Raw Data**"):
            docket_raw = results.get('docket_information', {})
            st.json(docket_raw)
            
        with st.expander("🖼️ **Images Raw Data**"):
            images_raw = results.get('images', {})
            st.json(images_raw)
            
        with st.expander("📧 **E-Service Raw Data**"):
            eservice_raw = results.get('eservice_queue', {})
            st.json(eservice_raw)
            
        with st.expander("📄 **My Filings Raw Data**"):
            filings_raw = results.get('my_filings', {})
            st.json(filings_raw)
            
        # Complete raw data download
        st.subheader("📥 Complete Data Export")
        json_data = json.dumps(results, indent=2, default=str)
        st.download_button(
            "📥 Download Complete Raw JSON",
            json_data,
            f"complete_efiling_data_{case_number}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json",
            "application/json",
            help="Download all scraped data including metadata and timestamps"
        )

def save_scraping_results(results: dict, case_number: str):
    """
    Save scraping results to file for future reference
    """
    try:
        # Create efiling_data directory if it doesn't exist
        data_dir = Path("efiling_data")
        data_dir.mkdir(exist_ok=True)
        
        # Save with timestamp
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = data_dir / f"efiling_{case_number}_{timestamp}.json"
        
        with open(filename, 'w') as f:
            json.dump(results, f, indent=2, default=str)
            
        st.success(f"💾 Results saved to: {filename}")
        
        # Also save latest version
        latest_filename = data_dir / f"efiling_{case_number}_latest.json"
        with open(latest_filename, 'w') as f:
            json.dump(results, f, indent=2, default=str)
            
    except Exception as e:
        st.warning(f"Could not save results to file: {e}")

def save_encrypted_credentials(username: str, password: str):
    """Save e-filing credentials encrypted"""
    try:
        # Save to session state with encryption flag
        st.session_state.efiling_username = username
        st.session_state.efiling_password = password
        st.session_state.efiling_encrypted = True
        st.session_state.save_efiling_creds = True
        
    except Exception as e:
        st.error(f"Failed to save encrypted credentials: {e}")
        raise

def get_persistent_settings_file():
    """Get path to persistent settings file"""
    settings_dir = Path("efiling_settings")
    settings_dir.mkdir(exist_ok=True)
    return settings_dir / "efiling_persistent_settings.json"

def load_persistent_settings():
    """Load persistent settings from file"""
    try:
        settings_file = get_persistent_settings_file()
        if settings_file.exists():
            with open(settings_file, 'r') as f:
                return json.load(f)
    except Exception as e:
        st.warning(f"Could not load persistent settings: {e}")
    return {}

def save_persistent_settings(settings: dict):
    """Save settings to persistent file"""
    try:
        settings_file = get_persistent_settings_file()
        settings['saved_at'] = datetime.now().isoformat()
        with open(settings_file, 'w') as f:
            json.dump(settings, f, indent=2)
        st.success(f"💾 Settings saved to: {settings_file}")
    except Exception as e:
        st.error(f"Failed to save persistent settings: {e}")
        raise

def clear_persistent_settings():
    """Clear persistent settings file"""
    try:
        settings_file = get_persistent_settings_file()
        if settings_file.exists():
            settings_file.unlink()
        st.success("🗑️ Persistent settings cleared!")
    except Exception as e:
        st.error(f"Failed to clear persistent settings: {e}")
        raise

def init_persistent_settings():
    """Initialize session state from persistent settings"""
    # Load persistent settings
    persistent_settings = load_persistent_settings()
    
    # Initialize session state with persistent settings if they exist
    if persistent_settings:
        # Only load if not already set in session state (to avoid overriding user input)
        if 'efiling_settings_loaded' not in st.session_state:
            st.session_state.efiling_username = persistent_settings.get('username', '')
            st.session_state.efiling_password = persistent_settings.get('password', '')
            st.session_state.efiling_case_number = persistent_settings.get('case_number', '')
            st.session_state.efiling_case_name = persistent_settings.get('case_name', '')
            st.session_state.efiling_headless = persistent_settings.get('headless_mode', True)
            st.session_state.save_efiling_creds = persistent_settings.get('save_credentials', False)
            st.session_state.efiling_persistent_save = persistent_settings.get('persistent_save', False)
            st.session_state.efiling_court = persistent_settings.get('court', 'Cuyahoga County Common Pleas Court')
            st.session_state.efiling_settings_loaded = True
            
            # Show a small indicator that settings were loaded
            if persistent_settings.get('saved_at'):
                saved_time = datetime.fromisoformat(persistent_settings['saved_at'])
                st.sidebar.success(f"⚡ Settings loaded from {saved_time.strftime('%Y-%m-%d %H:%M')}")

# Test the UI if run directly
if __name__ == "__main__":
    st.set_page_config(
        page_title="E-Filing Integration Test",
        page_icon="🏛️",
        layout="wide"
    )
    
    render_efiling_integration()
```

## File: c2p.py
```python
#!/usr/bin/env python3
"""
Code2Prompt - Convert codebases into LLM-friendly prompts
A tool to extract and format code from projects for AI model consumption
"""

import os
import argparse
import fnmatch
import mimetypes
from pathlib import Path
from typing import List, Set, Dict, Optional
import tiktoken
import json

class Code2Prompt:
    def __init__(self):
        # Common file extensions to include
        self.default_extensions = {
            '.py', '.js', '.jsx', '.ts', '.tsx', '.java', '.cpp', '.c', '.h', 
            '.cs', '.php', '.rb', '.go', '.rs', '.swift', '.kt', '.scala',
            '.html', '.css', '.scss', '.sass', '.less', '.vue', '.svelte',
            '.json', '.xml', '.yaml', '.yml', '.toml', '.ini', '.cfg',
            '.md', '.txt', '.rst', '.sql', '.sh', '.bash', '.zsh',
            '.dockerfile', '.makefile', '.cmake', '.gradle', '.maven'
        }
        
        # Common directories/files to ignore
        self.default_ignore_patterns = {
            'node_modules/*', '.git/*', '__pycache__/*', '*.pyc', '.env',
            'venv/*', 'env/*', '.venv/*', 'build/*', 'dist/*', 'target/*',
            '.next/*', '.nuxt/*', 'coverage/*', '.nyc_output/*', 'logs/*',
            '*.log', '.DS_Store', 'Thumbs.db', '*.tmp', '*.temp',
            '.idea/*', '.vscode/*', '*.swp', '*.swo', '*~'
        }
        
        # Initialize tokenizer for cost estimation
        try:
            self.tokenizer = tiktoken.get_encoding("cl100k_base")  # GPT-4 tokenizer
        except:
            self.tokenizer = None
    
    def should_include_file(self, file_path: Path, extensions: Set[str], 
                           ignore_patterns: Set[str]) -> bool:
        """Check if a file should be included based on extension and ignore patterns"""
        
        # Check ignore patterns
        for pattern in ignore_patterns:
            if fnmatch.fnmatch(str(file_path), pattern) or \
               fnmatch.fnmatch(file_path.name, pattern):
                return False
        
        # Check if it's a text file
        if not self.is_text_file(file_path):
            return False
        
        # Check extension
        if extensions:
            return file_path.suffix.lower() in extensions
        
        return True
    
    def is_text_file(self, file_path: Path) -> bool:
        """Check if a file is likely a text file"""
        try:
            # Check MIME type
            mime_type, _ = mimetypes.guess_type(str(file_path))
            if mime_type and mime_type.startswith('text'):
                return True
            
            # Check by reading a small sample
            with open(file_path, 'rb') as f:
                sample = f.read(1024)
                try:
                    sample.decode('utf-8')
                    return True
                except UnicodeDecodeError:
                    return False
        except:
            return False
    
    def extract_code_from_directory(self, directory: Path, extensions: Set[str] = None,
                                  ignore_patterns: Set[str] = None, 
                                  max_file_size: int = 1024*1024) -> Dict:
        """Extract code from a directory structure"""
        
        if extensions is None:
            extensions = self.default_extensions
        
        if ignore_patterns is None:
            ignore_patterns = self.default_ignore_patterns
        
        files_data = []
        total_size = 0
        skipped_files = []
        
        for root, dirs, files in os.walk(directory):
            root_path = Path(root)
            
            # Filter directories to avoid walking into ignored ones
            dirs[:] = [d for d in dirs if not any(
                fnmatch.fnmatch(d, pattern.rstrip('/*')) 
                for pattern in ignore_patterns if '/*' in pattern
            )]
            
            for file in files:
                file_path = root_path / file
                
                if not self.should_include_file(file_path, extensions, ignore_patterns):
                    continue
                
                try:
                    file_size = file_path.stat().st_size
                    if file_size > max_file_size:
                        skipped_files.append({
                            'path': str(file_path.relative_to(directory)),
                            'reason': f'File too large ({file_size} bytes)'
                        })
                        continue
                    
                    with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                        content = f.read()
                    
                    files_data.append({
                        'path': str(file_path.relative_to(directory)),
                        'content': content,
                        'size': file_size,
                        'lines': len(content.splitlines())
                    })
                    total_size += file_size
                    
                except Exception as e:
                    skipped_files.append({
                        'path': str(file_path.relative_to(directory)),
                        'reason': f'Error reading file: {e}'
                    })
        
        return {
            'files': files_data,
            'total_size': total_size,
            'total_files': len(files_data),
            'skipped_files': skipped_files
        }
    
    def generate_prompt(self, data: Dict, project_name: str = None, 
                       include_structure: bool = True,
                       prompt_template: str = None) -> str:
        """Generate a formatted prompt from extracted code data"""
        
        if prompt_template is None:
            prompt_template = self.get_default_template()
        
        # Generate project structure
        structure = ""
        if include_structure:
            structure = self.generate_tree_structure(data['files'])
        
        # Generate file contents
        file_contents = []
        for file_info in data['files']:
            content = f"## File: {file_info['path']}\n"
            content += f"```{self.get_language_from_extension(file_info['path'])}\n"
            content += file_info['content']
            content += "\n```\n"
            file_contents.append(content)
        
        # Fill template
        prompt = prompt_template.format(
            project_name=project_name or "Code Project",
            total_files=data['total_files'],
            total_size=self.format_size(data['total_size']),
            project_structure=structure,
            file_contents="\n".join(file_contents),
            stats=self.generate_stats(data)
        )
        
        return prompt
    
    def generate_tree_structure(self, files: List[Dict]) -> str:
        """Generate a tree-like structure of the project"""
        structure = {}
        
        for file_info in files:
            parts = file_info['path'].split(os.sep)
            current = structure
            
            for part in parts[:-1]:  # directories
                if part not in current:
                    current[part] = {}
                current = current[part]
            
            # Add file
            current[parts[-1]] = f"({file_info['lines']} lines)"
        
        return self.format_tree(structure)
    
    def format_tree(self, structure: Dict, prefix: str = "") -> str:
        """Format dictionary as tree structure"""
        result = []
        items = sorted(structure.items())
        
        for i, (name, content) in enumerate(items):
            is_last = i == len(items) - 1
            current_prefix = "└── " if is_last else "├── "
            
            if isinstance(content, dict):
                result.append(f"{prefix}{current_prefix}{name}/")
                next_prefix = prefix + ("    " if is_last else "│   ")
                result.append(self.format_tree(content, next_prefix))
            else:
                result.append(f"{prefix}{current_prefix}{name} {content}")
        
        return "\n".join(filter(None, result))
    
    def get_language_from_extension(self, file_path: str) -> str:
        """Get language identifier for syntax highlighting"""
        ext_to_lang = {
            '.py': 'python', '.js': 'javascript', '.jsx': 'jsx', 
            '.ts': 'typescript', '.tsx': 'tsx', '.java': 'java',
            '.cpp': 'cpp', '.c': 'c', '.h': 'c', '.cs': 'csharp',
            '.php': 'php', '.rb': 'ruby', '.go': 'go', '.rs': 'rust',
            '.swift': 'swift', '.kt': 'kotlin', '.scala': 'scala',
            '.html': 'html', '.css': 'css', '.scss': 'scss',
            '.json': 'json', '.xml': 'xml', '.yaml': 'yaml', '.yml': 'yaml',
            '.sql': 'sql', '.sh': 'bash', '.bash': 'bash',
            '.md': 'markdown', '.dockerfile': 'dockerfile'
        }
        
        ext = Path(file_path).suffix.lower()
        return ext_to_lang.get(ext, 'text')
    
    def generate_stats(self, data: Dict) -> str:
        """Generate statistics about the codebase"""
        stats = []
        stats.append(f"Total files: {data['total_files']}")
        stats.append(f"Total size: {self.format_size(data['total_size'])}")
        
        total_lines = sum(f['lines'] for f in data['files'])
        stats.append(f"Total lines: {total_lines:,}")
        
        # Language breakdown
        lang_stats = {}
        for file_info in data['files']:
            ext = Path(file_info['path']).suffix.lower()
            lang = self.get_language_from_extension(file_info['path'])
            if lang not in lang_stats:
                lang_stats[lang] = {'files': 0, 'lines': 0}
            lang_stats[lang]['files'] += 1
            lang_stats[lang]['lines'] += file_info['lines']
        
        stats.append("\nLanguage breakdown:")
        for lang, counts in sorted(lang_stats.items()):
            stats.append(f"  {lang}: {counts['files']} files, {counts['lines']:,} lines")
        
        if data['skipped_files']:
            stats.append(f"\nSkipped files: {len(data['skipped_files'])}")
        
        return "\n".join(stats)
    
    def format_size(self, size_bytes: int) -> str:
        """Format byte size in human readable format"""
        for unit in ['B', 'KB', 'MB', 'GB']:
            if size_bytes < 1024.0:
                return f"{size_bytes:.1f} {unit}"
            size_bytes /= 1024.0
        return f"{size_bytes:.1f} TB"
    
    def estimate_tokens(self, text: str) -> int:
        """Estimate token count for cost calculation"""
        if self.tokenizer:
            return len(self.tokenizer.encode(text))
        else:
            # Rough estimation: ~4 characters per token
            return len(text) // 4
    
    def estimate_costs(self, text: str) -> Dict[str, float]:
        """Estimate costs for different AI models"""
        tokens = self.estimate_tokens(text)
        
        # Pricing per 1M tokens (as of 2024) - input tokens
        pricing = {
            'gpt-4': 30.00,      # GPT-4 Turbo
            'gpt-3.5': 0.50,     # GPT-3.5 Turbo
            'claude-3-opus': 15.00,
            'claude-3-sonnet': 3.00,
            'claude-3-haiku': 0.25,
            'gemini-pro': 0.50,
        }
        
        costs = {}
        for model, price_per_million in pricing.items():
            cost = (tokens / 1_000_000) * price_per_million
            costs[model] = cost
        
        return {
            'tokens': tokens,
            'estimated_costs': costs
        }
    
    def get_default_template(self) -> str:
        """Get the default prompt template"""
        return """# {project_name}

I need you to analyze this codebase. Here's an overview:

{stats}

## Project Structure
```
{project_structure}
```

## Code Files

{file_contents}

Please analyze this code and help me understand its structure, functionality, and provide suggestions for improvements or answer any questions I have about it.
"""

def main():
    parser = argparse.ArgumentParser(description='Convert codebase to AI-friendly prompt')
    parser.add_argument('directory', help='Directory to process')
    parser.add_argument('-o', '--output', help='Output file (default: stdout)')
    parser.add_argument('-n', '--name', help='Project name')
    parser.add_argument('-e', '--extensions', 
                       help='File extensions to include (comma-separated)')
    parser.add_argument('--ignore', 
                       help='Additional ignore patterns (comma-separated)')
    parser.add_argument('--max-size', type=int, default=1024*1024,
                       help='Maximum file size in bytes (default: 1MB)')
    parser.add_argument('--no-structure', action='store_true',
                       help='Skip project structure in output')
    parser.add_argument('--estimate-cost', action='store_true',
                       help='Estimate AI model costs')
    parser.add_argument('--template', help='Custom template file')
    
    args = parser.parse_args()
    
    # Initialize processor
    processor = Code2Prompt()
    
    # Parse extensions
    extensions = None
    if args.extensions:
        extensions = {f'.{ext.strip().lstrip(".")}' 
                     for ext in args.extensions.split(',')}
    
    # Parse additional ignore patterns
    ignore_patterns = processor.default_ignore_patterns.copy()
    if args.ignore:
        ignore_patterns.update(args.ignore.split(','))
    
    # Extract code
    directory = Path(args.directory)
    if not directory.exists():
        print(f"Error: Directory '{directory}' does not exist")
        return 1
    
    data = processor.extract_code_from_directory(
        directory, extensions, ignore_patterns, args.max_size
    )
    
    # Load custom template if provided
    template = None
    if args.template:
        with open(args.template, 'r') as f:
            template = f.read()
    
    # Generate prompt
    prompt = processor.generate_prompt(
        data, 
        args.name or directory.name,
        not args.no_structure,
        template
    )
    
    # Output
    if args.output:
        with open(args.output, 'w') as f:
            f.write(prompt)
        print(f"Prompt written to {args.output}")
    else:
        print(prompt)
    
    # Cost estimation
    if args.estimate_cost:
        costs = processor.estimate_costs(prompt)
        print(f"\n{'='*50}")
        print("COST ESTIMATION")
        print(f"{'='*50}")
        print(f"Estimated tokens: {costs['tokens']:,}")
        print("\nEstimated costs (input only):")
        for model, cost in costs['estimated_costs'].items():
            print(f"  {model}: ${cost:.4f}")
        print("\nNote: These are rough estimates for input tokens only.")
        print("Actual costs may vary based on model updates and usage.")
    
    return 0

if __name__ == '__main__':
    exit(main())
```

## File: queue_manager.py
```python
"""
Queue Management System for Legal Case Management
Handles background tasks, batch processing, and asynchronous operations.
"""

import asyncio
import threading
import queue
import time
import logging
from typing import Dict, List, Optional, Callable, Any
from datetime import datetime, timedelta
from enum import Enum
import json
import sqlite3
from dataclasses import dataclass, asdict
from concurrent.futures import ThreadPoolExecutor, as_completed
import uuid

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class TaskStatus(Enum):
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"
    RETRYING = "retrying"

class TaskPriority(Enum):
    LOW = 1
    NORMAL = 2
    HIGH = 3
    URGENT = 4

@dataclass
class Task:
    """Task definition for queue processing"""
    id: str
    name: str
    function_name: str
    args: tuple
    kwargs: dict
    priority: TaskPriority
    status: TaskStatus
    created_at: datetime
    scheduled_at: Optional[datetime] = None
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    error_message: Optional[str] = None
    retry_count: int = 0
    max_retries: int = 3
    timeout: int = 300  # 5 minutes default
    result: Optional[Any] = None
    progress: float = 0.0
    metadata: Optional[Dict] = None

class QueueManager:
    """Manages task queues and background processing"""
    
    def __init__(self, db_path: str = "queue.db", max_workers: int = 4):
        self.db_path = db_path
        self.max_workers = max_workers
        self.task_queue = queue.PriorityQueue()
        self.running_tasks = {}
        self.task_registry = {}
        self.executor = ThreadPoolExecutor(max_workers=max_workers)
        self.is_running = False
        self.worker_thread = None
        
        self.init_database()
        self.register_default_tasks()
        
    def init_database(self):
        """Initialize queue database"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS tasks (
                id TEXT PRIMARY KEY,
                name TEXT NOT NULL,
                function_name TEXT NOT NULL,
                args TEXT,
                kwargs TEXT,
                priority INTEGER,
                status TEXT,
                created_at TIMESTAMP,
                scheduled_at TIMESTAMP,
                started_at TIMESTAMP,
                completed_at TIMESTAMP,
                error_message TEXT,
                retry_count INTEGER DEFAULT 0,
                max_retries INTEGER DEFAULT 3,
                timeout INTEGER DEFAULT 300,
                result TEXT,
                progress REAL DEFAULT 0.0,
                metadata TEXT
            )
        ''')
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS task_logs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                task_id TEXT,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                level TEXT,
                message TEXT,
                FOREIGN KEY (task_id) REFERENCES tasks (id)
            )
        ''')
        
        conn.commit()
        conn.close()
        
    def register_task(self, name: str, function: Callable):
        """Register a task function"""
        self.task_registry[name] = function
        logger.info(f"Registered task: {name}")
        
    def register_default_tasks(self):
        """Register default system tasks"""
        from core.document_processor import document_processor
        from core.vector_manager import vector_manager
        
        # Document processing tasks
        self.register_task("process_document", self._process_document_task)
        self.register_task("batch_process_documents", self._batch_process_documents_task)
        self.register_task("extract_text", self._extract_text_task)
        
        # Vector database tasks
        self.register_task("create_embeddings", self._create_embeddings_task)
        self.register_task("update_knowledge_base", self._update_knowledge_base_task)
        
        # URL monitoring tasks
        self.register_task("check_url", self._check_url_task)
        self.register_task("batch_check_urls", self._batch_check_urls_task)
        
        # System maintenance tasks
        self.register_task("cleanup_old_files", self._cleanup_old_files_task)
        self.register_task("backup_database", self._backup_database_task)
        self.register_task("generate_report", self._generate_report_task)
        
    def add_task(self, name: str, function_name: str, args: tuple = (), kwargs: dict = None,
                priority: TaskPriority = TaskPriority.NORMAL, scheduled_at: datetime = None,
                max_retries: int = 3, timeout: int = 300, metadata: dict = None) -> str:
        """Add a task to the queue"""
        task_id = str(uuid.uuid4())
        
        task = Task(
            id=task_id,
            name=name,
            function_name=function_name,
            args=args,
            kwargs=kwargs or {},
            priority=priority,
            status=TaskStatus.PENDING,
            created_at=datetime.now(),
            scheduled_at=scheduled_at,
            max_retries=max_retries,
            timeout=timeout,
            metadata=metadata or {}
        )
        
        # Save to database
        self._save_task(task)
        
        # Add to queue if not scheduled for future
        if not scheduled_at or scheduled_at <= datetime.now():
            self.task_queue.put((priority.value, time.time(), task))
            logger.info(f"Added task to queue: {name} (ID: {task_id})")
        else:
            logger.info(f"Scheduled task: {name} for {scheduled_at} (ID: {task_id})")
            
        return task_id
        
    def _save_task(self, task: Task):
        """Save task to database"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT OR REPLACE INTO tasks 
            (id, name, function_name, args, kwargs, priority, status, created_at, 
             scheduled_at, started_at, completed_at, error_message, retry_count, 
             max_retries, timeout, result, progress, metadata)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            task.id, task.name, task.function_name, 
            json.dumps(task.args), json.dumps(task.kwargs),
            task.priority.value, task.status.value, task.created_at,
            task.scheduled_at, task.started_at, task.completed_at,
            task.error_message, task.retry_count, task.max_retries,
            task.timeout, json.dumps(task.result) if task.result else None,
            task.progress, json.dumps(task.metadata)
        ))
        
        conn.commit()
        conn.close()
        
    def get_task(self, task_id: str) -> Optional[Task]:
        """Get task by ID"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('SELECT * FROM tasks WHERE id = ?', (task_id,))
        row = cursor.fetchone()
        conn.close()
        
        if not row:
            return None
            
        return Task(
            id=row[0], name=row[1], function_name=row[2],
            args=json.loads(row[3]), kwargs=json.loads(row[4]),
            priority=TaskPriority(row[5]), status=TaskStatus(row[6]),
            created_at=datetime.fromisoformat(row[7]),
            scheduled_at=datetime.fromisoformat(row[8]) if row[8] else None,
            started_at=datetime.fromisoformat(row[9]) if row[9] else None,
            completed_at=datetime.fromisoformat(row[10]) if row[10] else None,
            error_message=row[11], retry_count=row[12], max_retries=row[13],
            timeout=row[14], result=json.loads(row[15]) if row[15] else None,
            progress=row[16], metadata=json.loads(row[17]) if row[17] else {}
        )
        
    def get_tasks(self, status: TaskStatus = None, limit: int = 100) -> List[Task]:
        """Get tasks with optional status filter"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        if status:
            cursor.execute('SELECT * FROM tasks WHERE status = ? ORDER BY created_at DESC LIMIT ?', 
                         (status.value, limit))
        else:
            cursor.execute('SELECT * FROM tasks ORDER BY created_at DESC LIMIT ?', (limit,))
            
        rows = cursor.fetchall()
        conn.close()
        
        tasks = []
        for row in rows:
            task = Task(
                id=row[0], name=row[1], function_name=row[2],
                args=json.loads(row[3]), kwargs=json.loads(row[4]),
                priority=TaskPriority(row[5]), status=TaskStatus(row[6]),
                created_at=datetime.fromisoformat(row[7]),
                scheduled_at=datetime.fromisoformat(row[8]) if row[8] else None,
                started_at=datetime.fromisoformat(row[9]) if row[9] else None,
                completed_at=datetime.fromisoformat(row[10]) if row[10] else None,
                error_message=row[11], retry_count=row[12], max_retries=row[13],
                timeout=row[14], result=json.loads(row[15]) if row[15] else None,
                progress=row[16], metadata=json.loads(row[17]) if row[17] else {}
            )
            tasks.append(task)
            
        return tasks
        
    def start_worker(self):
        """Start the background worker"""
        if self.is_running:
            return
            
        self.is_running = True
        self.worker_thread = threading.Thread(target=self._worker_loop, daemon=True)
        self.worker_thread.start()
        logger.info("Queue worker started")
        
    def stop_worker(self):
        """Stop the background worker"""
        self.is_running = False
        if self.worker_thread:
            self.worker_thread.join(timeout=5)
        logger.info("Queue worker stopped")
        
    def _worker_loop(self):
        """Main worker loop"""
        while self.is_running:
            try:
                # Check for scheduled tasks
                self._check_scheduled_tasks()
                
                # Process pending tasks
                try:
                    priority, timestamp, task = self.task_queue.get(timeout=1)
                    self._execute_task(task)
                except queue.Empty:
                    continue
                    
            except Exception as e:
                logger.error(f"Worker loop error: {e}")
                time.sleep(1)
                
    def _check_scheduled_tasks(self):
        """Check for scheduled tasks that are ready to run"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT * FROM tasks 
            WHERE status = ? AND scheduled_at <= ?
        ''', (TaskStatus.PENDING.value, datetime.now()))
        
        rows = cursor.fetchall()
        conn.close()
        
        for row in rows:
            task = Task(
                id=row[0], name=row[1], function_name=row[2],
                args=json.loads(row[3]), kwargs=json.loads(row[4]),
                priority=TaskPriority(row[5]), status=TaskStatus(row[6]),
                created_at=datetime.fromisoformat(row[7]),
                scheduled_at=datetime.fromisoformat(row[8]) if row[8] else None,
                started_at=datetime.fromisoformat(row[9]) if row[9] else None,
                completed_at=datetime.fromisoformat(row[10]) if row[10] else None,
                error_message=row[11], retry_count=row[12], max_retries=row[13],
                timeout=row[14], result=json.loads(row[15]) if row[15] else None,
                progress=row[16], metadata=json.loads(row[17]) if row[17] else {}
            )
            
            self.task_queue.put((task.priority.value, time.time(), task))
            
    def _execute_task(self, task: Task):
        """Execute a single task"""
        try:
            # Update task status
            task.status = TaskStatus.RUNNING
            task.started_at = datetime.now()
            self._save_task(task)
            self.running_tasks[task.id] = task
            
            logger.info(f"Executing task: {task.name} (ID: {task.id})")
            
            # Get task function
            if task.function_name not in self.task_registry:
                raise Exception(f"Task function not registered: {task.function_name}")
                
            task_function = self.task_registry[task.function_name]
            
            # Execute with timeout
            future = self.executor.submit(task_function, task, *task.args, **task.kwargs)
            
            try:
                result = future.result(timeout=task.timeout)
                
                # Task completed successfully
                task.status = TaskStatus.COMPLETED
                task.completed_at = datetime.now()
                task.result = result
                task.progress = 100.0
                
                logger.info(f"Task completed: {task.name} (ID: {task.id})")
                
            except Exception as e:
                # Task failed
                task.error_message = str(e)
                
                if task.retry_count < task.max_retries:
                    # Retry task
                    task.retry_count += 1
                    task.status = TaskStatus.RETRYING
                    
                    # Add back to queue with delay
                    retry_delay = min(60 * (2 ** task.retry_count), 3600)  # Exponential backoff
                    task.scheduled_at = datetime.now() + timedelta(seconds=retry_delay)
                    
                    logger.warning(f"Task failed, retrying in {retry_delay}s: {task.name} (ID: {task.id})")
                else:
                    # Max retries reached
                    task.status = TaskStatus.FAILED
                    task.completed_at = datetime.now()
                    
                    logger.error(f"Task failed permanently: {task.name} (ID: {task.id}) - {e}")
                    
        except Exception as e:
            task.status = TaskStatus.FAILED
            task.error_message = str(e)
            task.completed_at = datetime.now()
            logger.error(f"Task execution error: {task.name} (ID: {task.id}) - {e}")
            
        finally:
            # Save final task state
            self._save_task(task)
            
            # Remove from running tasks
            if task.id in self.running_tasks:
                del self.running_tasks[task.id]
                
    def cancel_task(self, task_id: str) -> bool:
        """Cancel a pending or running task"""
        task = self.get_task(task_id)
        if not task:
            return False
            
        if task.status in [TaskStatus.PENDING, TaskStatus.RETRYING]:
            task.status = TaskStatus.CANCELLED
            task.completed_at = datetime.now()
            self._save_task(task)
            logger.info(f"Cancelled task: {task.name} (ID: {task_id})")
            return True
            
        return False
        
    def get_queue_stats(self) -> Dict:
        """Get queue statistics"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        stats = {}
        
        # Count by status
        for status in TaskStatus:
            cursor.execute('SELECT COUNT(*) FROM tasks WHERE status = ?', (status.value,))
            count = cursor.fetchone()[0]
            stats[f"{status.value}_count"] = count
            
        # Queue size
        stats['queue_size'] = self.task_queue.qsize()
        stats['running_tasks'] = len(self.running_tasks)
        stats['max_workers'] = self.max_workers
        
        # Recent activity
        cursor.execute('''
            SELECT COUNT(*) FROM tasks 
            WHERE created_at > datetime('now', '-1 hour')
        ''')
        stats['tasks_last_hour'] = cursor.fetchone()[0]
        
        conn.close()
        return stats
        
    # Task implementations
    def _process_document_task(self, task: Task, case_id: int, file_info: dict, uploaded_by: int = None):
        """Process a single document"""
        from core.document_processor import document_processor
        
        task.progress = 10.0
        self._save_task(task)
        
        result = document_processor.process_document(case_id, file_info, uploaded_by)
        
        task.progress = 100.0
        self._save_task(task)
        
        return result
        
    def _batch_process_documents_task(self, task: Task, case_id: int, file_infos: list, uploaded_by: int = None):
        """Process multiple documents in batch"""
        from core.document_processor import document_processor
        
        results = []
        total_files = len(file_infos)
        
        for i, file_info in enumerate(file_infos):
            try:
                result = document_processor.process_document(case_id, file_info, uploaded_by)
                results.append(result)
                
                # Update progress
                task.progress = ((i + 1) / total_files) * 100
                self._save_task(task)
                
            except Exception as e:
                logger.error(f"Error processing file {file_info.get('filename', 'unknown')}: {e}")
                results.append({'success': False, 'error': str(e)})
                
        return {
            'total_processed': len(results),
            'successful': len([r for r in results if r.get('success', False)]),
            'failed': len([r for r in results if not r.get('success', False)]),
            'results': results
        }
        
    def _extract_text_task(self, task: Task, file_path: str, file_type: str):
        """Extract text from a file"""
        from core.document_processor import document_processor
        
        task.progress = 50.0
        self._save_task(task)
        
        success, text_content = document_processor.extract_text_content(file_path, file_type)
        
        task.progress = 100.0
        self._save_task(task)
        
        return {'success': success, 'text_content': text_content}
        
    def _create_embeddings_task(self, task: Task, case_id: int, documents: list):
        """Create embeddings for documents"""
        # Implementation would go here
        task.progress = 100.0
        self._save_task(task)
        return {'embeddings_created': len(documents)}
        
    def _update_knowledge_base_task(self, task: Task, case_id: int):
        """Update knowledge base for case"""
        # Implementation would go here
        task.progress = 100.0
        self._save_task(task)
        return {'knowledge_base_updated': True}
        
    def _check_url_task(self, task: Task, url: str, case_id: int):
        """Check a URL for changes"""
        # Implementation would go here
        task.progress = 100.0
        self._save_task(task)
        return {'url_checked': url, 'changes_detected': False}
        
    def _batch_check_urls_task(self, task: Task, urls: list):
        """Check multiple URLs for changes"""
        results = []
        total_urls = len(urls)
        
        for i, url_info in enumerate(urls):
            # Simulate URL checking
            results.append({'url': url_info['url'], 'changes_detected': False})
            
            task.progress = ((i + 1) / total_urls) * 100
            self._save_task(task)
            
        return {'urls_checked': len(results), 'results': results}
        
    def _cleanup_old_files_task(self, task: Task, days_old: int = 30):
        """Clean up old files"""
        # Implementation would go here
        task.progress = 100.0
        self._save_task(task)
        return {'files_cleaned': 0}
        
    def _backup_database_task(self, task: Task, backup_path: str):
        """Backup database"""
        # Implementation would go here
        task.progress = 100.0
        self._save_task(task)
        return {'backup_created': backup_path}
        
    def _generate_report_task(self, task: Task, case_id: int, report_type: str):
        """Generate case report"""
        # Implementation would go here
        task.progress = 100.0
        self._save_task(task)
        return {'report_generated': True, 'report_type': report_type}

# Global queue manager instance
queue_manager = QueueManager()

```

## File: efiling_scraper.py
```python
"""
Enhanced Cuyahoga County E-Filing System Integration
Uses Playwright for more reliable web scraping with better timeout handling
"""

import asyncio
import os
import time
import logging
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Optional, Tuple
import re
import base64
from urllib.parse import urljoin, urlparse, parse_qs

try:
    from playwright.async_api import async_playwright, Browser, Page
    PLAYWRIGHT_AVAILABLE = True
except ImportError:
    PLAYWRIGHT_AVAILABLE = False
    logging.warning("Playwright not available. Install with: pip install playwright")

class CuyahogaEFilingScraperPlaywright:
    """
    Enhanced Cuyahoga County E-Filing System scraper using Playwright
    
    Handles:
    - Automatic login with credentials
    - Case navigation and document scraping
    - Four key areas: Docket Info, Images, E-Service, My Filings
    - Robust timeout and error handling
    """
    
    def __init__(self, username: str, password: str, headless: bool = True):
        self.username = username
        self.password = password
        self.headless = headless
        self.base_url = "https://efiling.cp.cuyahogacounty.gov"
        self.login_url = f"{self.base_url}/Login.aspx"
        self.home_url = f"{self.base_url}/Home.aspx"
        
        # URLs for different sections
        self.urls = {
            'login': f"{self.base_url}/Login.aspx",
            'home': f"{self.base_url}/Home.aspx",
            'docket': f"{self.base_url}/CV_CaseInformation_Docket.aspx",
            'docket_alt': f"{self.base_url}/CaseInformation.aspx",
            'docket_alt2': f"{self.base_url}/CV_CaseInformation.aspx", 
            'images': f"{self.base_url}/Images.aspx", 
            'eservice': f"{self.base_url}/EService_Queue.aspx",
            'myfilings': f"{self.base_url}/EFiling.aspx"
        }
        
        self.browser = None
        self.page = None
        self.logged_in = False
        self.case_query_param = None  # Will store the ?q= parameter for the case
        
        # Setup logging
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
        
    async def __aenter__(self):
        """Async context manager entry"""
        await self.start_browser()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        await self.close()
        
    async def start_browser(self):
        """Initialize Playwright browser"""
        if not PLAYWRIGHT_AVAILABLE:
            raise Exception("Playwright not available. Install with: pip install playwright && playwright install chromium")
            
        self.playwright = await async_playwright().start()
        
        # Launch browser with options for better compatibility
        self.browser = await self.playwright.chromium.launch(
            headless=self.headless,
            args=[
                '--disable-dev-shm-usage',
                '--disable-gpu',
                '--no-sandbox',
                '--disable-web-security',
                '--disable-features=VizDisplayCompositor',
                '--disable-blink-features=AutomationControlled'
            ]
        )
        
        # Create new page with extended timeout
        self.page = await self.browser.new_page()
        self.page.set_default_timeout(30000)  # 30 seconds
        
        # Set realistic user agent
        await self.page.set_extra_http_headers({
            'User-Agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        })
        
        self.logger.info("Browser started successfully")
        
    async def close(self):
        """Close browser and cleanup"""
        try:
            if self.page:
                await self.page.close()
            if self.browser:
                await self.browser.close()
            if hasattr(self, 'playwright'):
                await self.playwright.stop()
            self.logger.info("Browser closed successfully")
        except Exception as e:
            self.logger.error(f"Error closing browser: {e}")
            
    async def login(self) -> bool:
        """
        Login to the e-filing system with provided credentials
        """
        try:
            self.logger.info("Attempting to login...")
            
            # Navigate to login page
            await self.page.goto(self.login_url, wait_until='networkidle')
            await asyncio.sleep(2)  # Let page fully load
            
            # Check if we're already logged in
            if self.home_url in self.page.url:
                self.logged_in = True
                self.logger.info("Already logged in")
                return True
                
            # Find and fill username field
            username_selector = 'input[name*="Username"], input[id*="Username"], input[type="text"]'
            await self.page.wait_for_selector(username_selector, timeout=10000)
            await self.page.fill(username_selector, self.username)
            
            # Find and fill password field  
            password_selector = 'input[name*="Password"], input[id*="Password"], input[type="password"]'
            await self.page.fill(password_selector, self.password)
            
            # Find and click login button
            login_selectors = [
                'input[type="submit"]',
                'button[type="submit"]', 
                'input[value*="Login"], input[value*="Sign In"]',
                'button:has-text("Login"), button:has-text("Sign In")'
            ]
            
            login_clicked = False
            for selector in login_selectors:
                try:
                    await self.page.click(selector, timeout=5000)
                    login_clicked = True
                    break
                except:
                    continue
                    
            if not login_clicked:
                raise Exception("Could not find login button")
                
            # Wait for navigation after login
            try:
                await self.page.wait_for_load_state('networkidle', timeout=15000)
            except:
                await asyncio.sleep(3)  # Fallback wait
                
            # Check if login was successful
            current_url = self.page.url
            if self.home_url in current_url or 'Home.aspx' in current_url:
                self.logged_in = True
                self.logger.info("Login successful")
                return True
            else:
                # Check for error messages
                error_selectors = [
                    'span[id*="error"], div[id*="error"]',
                    '.error, .alert-danger',
                    'span:has-text("Invalid"), span:has-text("incorrect")'
                ]
                
                for selector in error_selectors:
                    try:
                        error_element = await self.page.query_selector(selector)
                        if error_element:
                            error_text = await error_element.text_content()
                            raise Exception(f"Login failed: {error_text}")
                    except:
                        continue
                        
                raise Exception(f"Login failed - redirected to: {current_url}")
                
        except Exception as e:
            self.logger.error(f"Login failed: {e}")
            return False
            
    async def find_case(self, case_number: str) -> Optional[str]:
        """
        Find the case link and extract query parameter
        
        Args:
            case_number: Case number to search for (e.g., 'DR-25-403973')
            
        Returns:
            Query parameter string for the case URL (e.g., 'I-fJldkwrgJ3jp2LoiI3Mg2')
        """
        try:
            if not self.logged_in:
                await self.login()
                
            # Navigate to home page
            await self.page.goto(self.home_url, wait_until='networkidle')
            await asyncio.sleep(2)
            
            # Look for case link containing the case number
            case_link_selector = f'a:has-text("{case_number}")'
            
            try:
                await self.page.wait_for_selector(case_link_selector, timeout=10000)
                case_link = await self.page.query_selector(case_link_selector)
                
                if case_link:
                    href = await case_link.get_attribute('href')
                    self.logger.info(f"Found case link: {href}")
                    
                    # Extract query parameter from URL
                    if '?q=' in href:
                        query_param = href.split('?q=')[1].split('&')[0]
                        self.case_query_param = query_param
                        self.logger.info(f"Extracted query parameter: {query_param}")
                        return query_param
                    else:
                        raise Exception("No query parameter found in case URL")
                else:
                    raise Exception(f"Case link not found for {case_number}")
                    
            except Exception as e:
                # Fallback: search in all links on the page
                all_links = await self.page.query_selector_all('a')
                for link in all_links:
                    try:
                        text = await link.text_content()
                        href = await link.get_attribute('href')
                        if case_number in text and href and '?q=' in href:
                            query_param = href.split('?q=')[1].split('&')[0] 
                            self.case_query_param = query_param
                            self.logger.info(f"Found case via fallback search: {query_param}")
                            return query_param
                    except:
                        continue
                        
                raise Exception(f"Case {case_number} not found on home page")
                
        except Exception as e:
            self.logger.error(f"Error finding case: {e}")
            return None

    async def scrape_public_docket(self, case_number: str) -> Dict:
        """
        Scrape public docket without authentication
        Public docket shows case entries but no document access
        
        Args:
            case_number: Case number (e.g., 'DR-25-403973')
            
        Returns:
            Dictionary containing public docket information
        """
        try:
            # Temporarily store login state
            was_logged_in = self.logged_in
            self.logged_in = False
            
            self.logger.info(f"🌐 Scraping PUBLIC docket for {case_number} (no authentication)")
            
            # Navigate to public case search or docket URL
            # Try direct case access first
            public_urls = [
                f"{self.base_url}/CV_CaseInformation_Docket.aspx?CaseNumber={case_number}",
                f"{self.base_url}/CaseInformation.aspx?CaseNumber={case_number}",
                f"{self.base_url}/PublicDocket.aspx?Case={case_number}"
            ]
            
            public_docket_data = None
            
            for url in public_urls:
                try:
                    self.logger.info(f"🔍 Trying public URL: {url}")
                    await self.page.goto(url, wait_until='networkidle')
                    await asyncio.sleep(2)
                    
                    # Try to extract docket data
                    temp_data = await self.try_extract_docket_data(url)
                    
                    if temp_data and temp_data.get('entries'):
                        public_docket_data = temp_data
                        public_docket_data['access_level'] = 'public'
                        public_docket_data['document_availability'] = 'public view - documents not accessible'
                        self.logger.info(f"✅ Public docket found: {len(temp_data['entries'])} entries")
                        break
                    else:
                        self.logger.warning(f"❌ No public docket entries at: {url}")
                        
                except Exception as e:
                    self.logger.warning(f"⚠️ Public URL failed {url}: {e}")
                    continue
            
            # Restore login state
            self.logged_in = was_logged_in
            
            if not public_docket_data:
                return {
                    'error': 'Public docket not accessible',
                    'case_number': case_number,
                    'access_level': 'public',
                    'entries': []
                }
                
            return public_docket_data
            
        except Exception as e:
            self.logger.error(f"Error scraping public docket: {e}")
            return {'error': str(e), 'case_number': case_number, 'access_level': 'public'}
            
    async def scrape_docket_information(self, case_query: str) -> Dict:
        """
        Scrape docket information for the case
        Handles both public docket (no login) and authenticated docket (login required)
        
        Args:
            case_query: Query parameter for the case
            
        Returns:
            Dictionary containing docket information with access level indicator
        """
        try:
            # Try multiple docket URLs to find the correct endpoint
            docket_urls_to_try = [
                f"{self.urls['docket']}?q={case_query}",
                f"{self.urls['docket_alt']}?q={case_query}",
                f"{self.urls['docket_alt2']}?q={case_query}",
                f"{self.urls['docket']}",  # Without case query
                f"{self.urls['docket_alt']}",
                f"{self.urls['docket_alt2']}"
            ]
            
            self.logger.info(f"🔍 Attempting to find docket data across {len(docket_urls_to_try)} possible URLs")
            
            successful_url = None
            docket_data = None
            
            for docket_url in docket_urls_to_try:
                self.logger.info(f"🌐 Trying docket URL: {docket_url}")
                
                try:
                    await self.page.goto(docket_url, wait_until='networkidle')
                    await asyncio.sleep(3)  # Longer wait for potential dynamic content
                    
                    # Check page title and URL to confirm we're on the right page
                    page_title = await self.page.title()
                    current_url = self.page.url
                    self.logger.info(f"📄 Page title: '{page_title}'")
                    self.logger.info(f"🔗 Current URL: {current_url}")
                    
                    # Try to extract data from this URL
                    temp_data = await self.try_extract_docket_data(docket_url)
                    
                    if temp_data and temp_data.get('entries'):
                        self.logger.info(f"✅ Found {len(temp_data['entries'])} docket entries at: {docket_url}")
                        successful_url = docket_url
                        docket_data = temp_data
                        break
                    else:
                        self.logger.warning(f"❌ No docket entries found at: {docket_url}")
                        
                except Exception as e:
                    self.logger.warning(f"⚠️ Failed to load {docket_url}: {e}")
                    continue
            
            if docket_data and docket_data.get('entries'):
                self.logger.info(f"🎉 Successfully found docket data with {len(docket_data['entries'])} entries")
                return docket_data
            else:
                self.logger.error("❌ Could not find docket entries at any URL")
                # Fallback to original logic
                docket_url = docket_urls_to_try[0]
                docket_data = {
                    'url': docket_url,
                    'scraped_at': datetime.now().isoformat(),
                    'entries': []
                }
                
            # Determine access level based on available documents
            if docket_data and docket_data.get('entries'):
                authenticated_entries = sum(1 for entry in docket_data['entries'] if entry.get('Has_Image', False))
                total_entries = len(docket_data['entries'])
                
                if self.logged_in and authenticated_entries > 0:
                    docket_data['access_level'] = 'authenticated'
                    docket_data['document_availability'] = f"{authenticated_entries}/{total_entries} entries have documents"
                    self.logger.info(f"🔐 Authenticated docket access: {authenticated_entries} entries with documents available")
                elif not self.logged_in:
                    docket_data['access_level'] = 'public'
                    docket_data['document_availability'] = 'public docket - limited document access'
                    self.logger.warning(f"👁️ Public docket access: {total_entries} entries visible, documents may not be accessible")
                else:
                    docket_data['access_level'] = 'authenticated_no_docs'
                    docket_data['document_availability'] = 'authenticated but no documents found'
                    self.logger.warning(f"⚠️ Authenticated but no document access: {total_entries} entries")
            
            self.logger.info(f"Scraped {len(docket_data.get('entries', []))} docket entries")
            return docket_data
            
        except Exception as e:
            self.logger.error(f"Error scraping docket information: {e}")
            return {'error': str(e), 'url': f"{self.urls['docket']}?q={case_query}"}
    
    async def is_docket_table(self, table, rows) -> bool:
        """
        Validate whether a table contains actual docket data vs navigation/header content
        """
        try:
            if not rows or len(rows) < 2:  # Need at least header + 1 data row
                return False
            
            # Check if table has reasonable number of columns for docket data (3-10 typical)
            if rows:
                first_row_cells = await rows[0].query_selector_all('td, th')
                if len(first_row_cells) < 3 or len(first_row_cells) > 12:
                    return False
            
            # Check header row content for docket-related terms
            header_text = ""
            if rows:
                first_row = rows[0]
                header_text = await first_row.text_content()
                header_text = header_text.lower()
            
            # Navigation/header table indicators (reject these)
            navigation_indicators = [
                'logout', 'login', 'welcome', 'clerk of courts',
                'home', 'search', 'help', 'contact', 'menu'
            ]
            
            if any(indicator in header_text for indicator in navigation_indicators):
                return False
            
            # Docket table indicators (accept these)
            docket_indicators = [
                'date', 'filed', 'filing', 'document', 'entry', 'docket', 
                'type', 'description', 'party', 'attorney', 'sequence',
                'seq', 'doc', 'desc'
            ]
            
            # Require at least 2 docket indicators in header
            docket_indicator_count = sum(1 for indicator in docket_indicators if indicator in header_text)
            if docket_indicator_count < 2:
                return False
            
            # Check second row for actual data content
            if len(rows) > 1:
                second_row = rows[1]
                second_row_text = await second_row.text_content()
                second_row_text = second_row_text.lower()
                
                # Look for date patterns (common in docket entries)
                import re
                date_patterns = [
                    r'\d{1,2}[-/]\d{1,2}[-/]\d{2,4}',  # MM/DD/YYYY or MM-DD-YYYY
                    r'\d{4}[-/]\d{1,2}[-/]\d{1,2}',   # YYYY/MM/DD or YYYY-MM-DD
                    r'[a-z]{3}\s+\d{1,2},?\s+\d{4}'   # Jan 1, 2025 or Jan 1 2025
                ]
                
                has_date = any(re.search(pattern, second_row_text) for pattern in date_patterns)
                
                # Check for common docket entry content
                docket_content_indicators = [
                    'motion', 'order', 'complaint', 'answer', 'notice',
                    'judgment', 'decree', 'affidavit', 'objection'
                ]
                
                has_docket_content = any(indicator in second_row_text for indicator in docket_content_indicators)
                
                # Table is valid if it has either date patterns or docket content
                if not (has_date or has_docket_content):
                    return False
            
            self.logger.info(f"✅ Found valid docket table with {len(rows)} rows")
            return True
            
        except Exception as e:
            self.logger.warning(f"Error validating docket table: {e}")
            return False
    
    async def try_extract_docket_data(self, docket_url: str) -> Dict:
        """Extract docket data from current page"""
        try:
            docket_data = {
                'url': docket_url,
                'scraped_at': datetime.now().isoformat(),
                'entries': []
            }
            
            # Debug: Log all tables found on the page
            all_tables = await self.page.query_selector_all('table')
            self.logger.info(f"🔍 Found {len(all_tables)} total tables on page")
            
            # Look for docket entries in table format
            # More specific selectors for actual docket tables, ordered by specificity
            table_selectors = [
                # Most specific - known docket table IDs/classes
                'table[id*="gvDocket"]',
                'table[id*="DocketGrid"]', 
                'table[id*="docketTable"]',
                'table[class*="docket-table"]',
                'table[id*="docket"], table[class*="docket"]',
                'table[id*="Docket"], table[class*="Docket"]', 
                # GridView controls commonly used for docket data
                'table.gridview[id*="gv"]',
                'table[id*="GridView"]',
                'table[id*="gv"][class*="gridview"]',
                # Data tables with relevant content
                'table[id*="case"], table[id*="Case"]',
                'table[class*="case"], table[class*="Case"]',
                # Generic table fallbacks - will be filtered by content
                'table[class*="gridview"]',
                'table[id*="gv"]',
                'table'
            ]
            
            for i, selector in enumerate(table_selectors):
                try:
                    self.logger.debug(f"🔍 Trying selector {i+1}/{len(table_selectors)}: {selector}")
                    table = await self.page.query_selector(selector)
                    
                    if table:
                        rows = await table.query_selector_all('tr')
                        
                        # Get table attributes for debugging
                        table_id = await table.get_attribute('id') or 'no-id'
                        table_class = await table.get_attribute('class') or 'no-class'
                        self.logger.debug(f"📊 Found table: id='{table_id}', class='{table_class}', rows={len(rows)}")
                        
                        # Skip tables that are clearly not docket data
                        if not await self.is_docket_table(table, rows):
                            self.logger.debug(f"❌ Skipping table with selector {selector} - not a docket table")
                            continue
                        
                        # Extract table headers from first row
                        headers = []
                        if rows:
                            header_cells = await rows[0].query_selector_all('td, th')
                            for cell in header_cells:
                                header_text = await cell.text_content()
                                if header_text:
                                    # Clean and standardize header names
                                    clean_header = header_text.strip().replace('\n', ' ').replace('\t', ' ')
                                    # Remove extra spaces
                                    clean_header = ' '.join(clean_header.split())
                                    # Convert common abbreviations to full names
                                    header_mappings = {
                                        'Seq': 'Sequence Number',
                                        'Doc': 'Document Type',
                                        'Desc': 'Description', 
                                        'Filed': 'Filing Date',
                                        'File': 'Filing Date',
                                        'Date': 'Filing Date',
                                        'Party': 'Filing Party',
                                        'Atty': 'Attorney',
                                        'Attorney Name': 'Attorney Name',
                                        'Docket Text': 'Docket Description',
                                        'Entry': 'Entry Number',
                                        'Type': 'Document Type'
                                    }
                                    
                                    # Apply mappings or use original if no mapping
                                    final_header = header_mappings.get(clean_header, clean_header)
                                    headers.append(final_header)
                                else:
                                    headers.append(f'Column {len(headers) + 1}')
                        
                        docket_data['headers'] = headers
                        self.logger.info(f"📋 Table headers: {headers}")
                        
                        # Process data rows using actual headers
                        for row_index, row in enumerate(rows[1:], 1):  # Skip header row, start numbering at 1
                            cells = await row.query_selector_all('td, th')
                            if len(cells) >= 3:
                                entry = {}
                                entry['Docket Number'] = row_index  # Chronological order: first filed = #1
                                
                                # Use actual header names as keys and extract document links
                                documents = []
                                for i, cell in enumerate(cells):
                                    text = await cell.text_content()
                                    header_name = headers[i] if i < len(headers) else f'Column {i + 1}'
                                    entry[header_name] = text.strip() if text else ''
                                    
                                    # Check for document links in ANY cell (not just image column)
                                    # Based on actual e-filing page structure analysis
                                    document_links = await cell.query_selector_all('a[href*="DisplayImageList"]')
                                    for doc_link in document_links:
                                        href = await doc_link.get_attribute('href')
                                        title = await doc_link.get_attribute('title') or ''
                                        if href:
                                            doc_info = {
                                                'url': href,
                                                'title': title,
                                                'type': 'DisplayImageList',
                                                'found_in_column': header_name
                                            }
                                            documents.append(doc_info)
                                            self.logger.info(f"📎 Found document link in {header_name}: {title}")
                                    
                                    # Also check for old image link pattern for compatibility
                                    if 'image' in header_name.lower():
                                        image_link = await cell.query_selector('a[href*="DisplayImageList"]')
                                        if image_link:
                                            href = await image_link.get_attribute('href')
                                            if href:
                                                entry['Image_Link'] = href
                                                entry['Has_Image'] = True
                                                # Extract image query parameter
                                                if '?q=' in href:
                                                    image_query = href.split('?q=')[1]
                                                    entry['Image_Query'] = image_query
                                        else:
                                            entry['Has_Image'] = False
                                    
                                # Try to identify document types and special entries
                                # Create a copy of items to avoid "dictionary changed size during iteration"
                                for header_name, cell_text in list(entry.items()):
                                    # Skip docket number field and safely handle non-string values
                                    if header_name == 'Docket Number' or not cell_text:
                                        continue
                                    
                                    # Convert to string and lowercase for pattern matching
                                    try:
                                        cell_text_str = str(cell_text)
                                        cell_text_lower = cell_text_str.lower()
                                    except:
                                        continue  # Skip if conversion fails
                                    
                                    if 'complaint' in cell_text_lower and 'divorce' in cell_text_lower:
                                        entry['Document Classification'] = 'Initial Filing - Complaint for Divorce'
                                        entry['Priority'] = 1  # Highest priority - filed first
                                    elif any(motion_word in cell_text_lower for motion_word in ['motion', 'petition', 'request']):
                                        entry['Document Classification'] = f'Motion/Petition - {cell_text_str}'
                                    elif any(order_word in cell_text_lower for order_word in ['order', 'judgment', 'decree']):
                                        entry['Document Classification'] = f'Court Order/Judgment - {cell_text_str}'
                                    elif 'stricken' in cell_text_lower:
                                        entry['Document Classification'] = 'STRICKEN RECORD - EVIDENCE REMOVED'
                                        entry['Stricken_Status'] = True
                                        entry['Priority'] = 99  # High priority - evidence issue
                                        # Extract original docket reference if available
                                        if 'docket' in cell_text_lower:
                                            import re
                                            docket_match = re.search(r'docket\s+(\d+)', cell_text_str, re.IGNORECASE)
                                            if docket_match:
                                                entry['Original_Docket_ID'] = docket_match.group(1)
                                        # Mark as critical for review
                                        entry['Requires_Investigation'] = True
                                        entry['Issue_Type'] = 'Evidence Manipulation'
                                    
                                # Add documents to entry if found
                                if documents:
                                    entry['documents'] = documents
                                    entry['document_count'] = len(documents)
                                    self.logger.info(f"📎 Added {len(documents)} document links to docket entry {row_index}")
                                else:
                                    entry['documents'] = []
                                    entry['document_count'] = 0
                                
                                # Check if entry has meaningful content (any non-empty string value)
                                has_content = any(
                                    v and str(v).strip() 
                                    for k, v in entry.items() 
                                    if k != 'Docket Number' and v is not None
                                )
                                if has_content:
                                    docket_data['entries'].append(entry)
                                    
                        self.logger.info(f"✅ Extracted {len(docket_data['entries'])} docket entries from table")
                        
                        # If we found entries, break from trying other selectors
                        if docket_data['entries']:
                            break
                            
                except Exception as e:
                    self.logger.warning(f"Error processing table with selector {selector}: {e}")
                    continue
                    
            return docket_data
            
        except Exception as e:
            self.logger.error(f"Error in try_extract_docket_data: {e}")
            return {
                'url': docket_url,
                'scraped_at': datetime.now().isoformat(),
                'entries': [],
                'error': str(e)
            }
            
    async def scrape_images(self, case_query: str) -> Dict:
        """
        Scrape all image pages and download links
        
        Args:
            case_query: Query parameter for the case
            
        Returns:
            Dictionary containing image information and download links
        """
        try:
            images_url = f"{self.urls['images']}?q={case_query}"
            await self.page.goto(images_url, wait_until='networkidle')
            await asyncio.sleep(2)
            
            images_data = {
                'url': images_url,
                'scraped_at': datetime.now().isoformat(),
                'total_pages': 0,
                'images': []
            }
            
            # Look for pagination info to get total pages
            page_info_selectors = [
                'span:has-text("Page"), div:has-text("Page")',
                'span[id*="page"], div[id*="page"]'
            ]
            
            for selector in page_info_selectors:
                try:
                    page_info = await self.page.query_selector(selector)
                    if page_info:
                        text = await page_info.text_content()
                        # Extract page numbers (e.g., "Page 1 of 5")
                        page_match = re.search(r'(\d+)\s+of\s+(\d+)', text)
                        if page_match:
                            images_data['total_pages'] = int(page_match.group(2))
                            break
                except:
                    continue
                    
            # Look for image links
            image_link_selectors = [
                'a[href*="Image"], a[title*="Image"]',
                'img[src*="images.png"], img[src*="ImageSheet.png"]',
                'a[href*="DisplayImage"]'
            ]
            
            for selector in image_link_selectors:
                try:
                    image_links = await self.page.query_selector_all(selector)
                    for link in image_links:
                        href = await link.get_attribute('href')
                        title = await link.get_attribute('title') or ''
                        
                        if href:
                            full_url = urljoin(self.base_url, href)
                            images_data['images'].append({
                                'url': full_url,
                                'title': title,
                                'relative_url': href
                            })
                except:
                    continue
                    
            self.logger.info(f"Found {len(images_data['images'])} image links, {images_data['total_pages']} pages")
            return images_data
            
        except Exception as e:
            self.logger.error(f"Error scraping images: {e}")
            return {'error': str(e), 'url': f"{self.urls['images']}?q={case_query}"}
            
    async def scrape_eservice_queue(self) -> Dict:
        """
        Scrape E-Service queue with "Unread Notices Only" unchecked
        
        Returns:
            Dictionary containing e-service notices and image links
        """
        try:
            await self.page.goto(self.urls['eservice'], wait_until='networkidle')
            await asyncio.sleep(2)
            
            eservice_data = {
                'url': self.urls['eservice'],
                'scraped_at': datetime.now().isoformat(),
                'notices': []
            }
            
            # Uncheck "Unread Notices Only" if present
            unread_checkbox_selectors = [
                'input[type="checkbox"][id*="Unread"]',
                'input[type="checkbox"]:has-text("Unread")',
                'input[type="checkbox"][value*="unread"]'
            ]
            
            for selector in unread_checkbox_selectors:
                try:
                    checkbox = await self.page.query_selector(selector)
                    if checkbox:
                        is_checked = await checkbox.is_checked()
                        if is_checked:
                            await checkbox.uncheck()
                            self.logger.info("Unchecked 'Unread Notices Only'")
                        break
                except:
                    continue
                    
            # Click search button
            search_selectors = [
                'input[type="submit"][value*="Search"]',
                'button:has-text("Search")',
                'input[id*="Search"]'
            ]
            
            for selector in search_selectors:
                try:
                    await self.page.click(selector)
                    await self.page.wait_for_load_state('networkidle', timeout=10000)
                    break
                except:
                    continue
                    
            await asyncio.sleep(2)
            
            # Look for image links in the results
            image_link_selectors = [
                'a[href*="DisplayImageList"]',
                'img[src*="ImageSheet.png"]',
                'a[title*="View Image"]'
            ]
            
            for selector in image_link_selectors:
                try:
                    links = await self.page.query_selector_all(selector)
                    for link in links:
                        href = await link.get_attribute('href')
                        title = await link.get_attribute('title') or ''
                        
                        if href and 'DisplayImageList' in href:
                            full_url = urljoin(self.base_url, href)
                            eservice_data['notices'].append({
                                'url': full_url,
                                'title': title,
                                'relative_url': href,
                                'type': 'e_service_notice'
                            })
                except:
                    continue
                    
            self.logger.info(f"Found {len(eservice_data['notices'])} e-service notices")
            return eservice_data
            
        except Exception as e:
            self.logger.error(f"Error scraping e-service queue: {e}")
            return {'error': str(e), 'url': self.urls['eservice']}
            
    async def scrape_my_filings(self) -> Dict:
        """
        Scrape My Filings section for filing images
        
        Returns:
            Dictionary containing filing information and image links
        """
        try:
            await self.page.goto(self.urls['myfilings'], wait_until='networkidle')
            await asyncio.sleep(2)
            
            filings_data = {
                'url': self.urls['myfilings'],
                'scraped_at': datetime.now().isoformat(),
                'filings': []
            }
            
            # Look for filing image links
            filing_link_selectors = [
                'a[href*="Images.aspx"]',
                'img[src*="images.png"]',
                'a[title*="View Image"]'
            ]
            
            for selector in filing_link_selectors:
                try:
                    links = await self.page.query_selector_all(selector)
                    for link in links:
                        href = await link.get_attribute('href') 
                        title = await link.get_attribute('title') or ''
                        
                        if href:
                            full_url = urljoin(self.base_url, href)
                            filings_data['filings'].append({
                                'url': full_url,
                                'title': title,
                                'relative_url': href,
                                'type': 'my_filing'
                            })
                except:
                    continue
                    
            self.logger.info(f"Found {len(filings_data['filings'])} filing links")
            return filings_data
            
        except Exception as e:
            self.logger.error(f"Error scraping my filings: {e}")
            return {'error': str(e), 'url': self.urls['myfilings']}
            
    async def extract_efile_document_links(self, docket_entries: List[Dict]) -> Dict:
        """
        Extract actual document download links by following Efile ID links
        User requirement: "follow link in the column Efile ID (https://efiling.cp.cuyahogacounty.gov/EFiling_Overview.aspx?q=CmDsxw0Krl--27-s5O5TAQ2)"
        
        Args:
            docket_entries: List of docket entries from scrape_docket_information
            
        Returns:
            Dictionary with Efile document links and metadata
        """
        try:
            efile_results = {
                'total_entries': len(docket_entries),
                'entries_with_efile_ids': 0,
                'documents_found': 0,
                'document_links': [],
                'errors': []
            }
            
            for entry in docket_entries:
                # Look for Efile ID link in the entry
                efile_id = None
                efile_link = None
                
                # Check various possible fields for Efile ID
                for field_name, field_value in entry.items():
                    if 'efile' in field_name.lower() or 'e-file' in field_name.lower():
                        if field_value and 'EFiling_Overview.aspx?q=' in str(field_value):
                            efile_link = str(field_value)
                            # Extract the q parameter
                            if '?q=' in efile_link:
                                efile_id = efile_link.split('?q=')[1].split('&')[0]
                            break
                
                # If no direct Efile link found, check for Image_Link and Image_Query fields
                # These contain DisplayImageList.aspx links that we can convert to EFiling_Overview
                if not efile_link:
                    # Check Image_Query field first (most direct)
                    if entry.get('Image_Query') and entry.get('Has_Image'):
                        efile_id = str(entry['Image_Query']).strip()
                        # Convert to EFiling_Overview URL as requested by user
                        efile_link = f"{self.base_url}/EFiling_Overview.aspx?q={efile_id}"
                        self.logger.info(f"🔄 Converting Image_Query to EFiling_Overview URL: {efile_id}")
                    
                    # Fallback: check Image_Link field
                    elif entry.get('Image_Link') and entry.get('Has_Image'):
                        image_link = str(entry['Image_Link'])
                        if 'DisplayImageList.aspx?q=' in image_link:
                            # Extract query parameter from DisplayImageList URL
                            if '?q=' in image_link:
                                efile_id = image_link.split('?q=')[1].split('&')[0]
                                # Convert to EFiling_Overview URL as requested by user
                                efile_link = f"{self.base_url}/EFiling_Overview.aspx?q={efile_id}"
                                self.logger.info(f"🔄 Converting Image_Link to EFiling_Overview URL: {efile_id}")
                
                # If still no link found, check for embedded document IDs in description
                if not efile_link:
                    description = entry.get('Description', '')
                    if description:
                        # Look for embedded document ID patterns
                        import re
                        doc_id_patterns = [
                            r'\((\d{8,})\)',  # (57726906) pattern - most common
                            r'#(\d+)',        # #123 pattern
                            r'ID[:\s]+(\w+)', # ID: ABC123 pattern
                            r'REF[:\s]+(\w+)' # REF: ABC123 pattern
                        ]
                        
                        for pattern in doc_id_patterns:
                            matches = re.findall(pattern, description)
                            if matches:
                                # Use the first match as potential document ID
                                potential_doc_id = matches[0]
                                # Try to construct an EFiling_Overview URL
                                efile_link = f"{self.base_url}/EFiling_Overview.aspx?q={potential_doc_id}"
                                efile_id = potential_doc_id
                                self.logger.info(f"🔍 Found embedded document ID in description: {potential_doc_id}")
                                break
                
                # If still no link found, check if there's an Efile ID that we need to construct the link for
                if not efile_link:
                    for field_name, field_value in entry.items():
                        if 'efile' in field_name.lower() and field_value:
                            # Construct the EFiling_Overview URL
                            efile_id = str(field_value).strip()
                            efile_link = f"{self.base_url}/EFiling_Overview.aspx?q={efile_id}"
                            break
                
                if efile_link and efile_id:
                    efile_results['entries_with_efile_ids'] += 1
                    
                    try:
                        self.logger.info(f"🔍 Following Efile ID link: {efile_link}")
                        
                        # Navigate to the EFiling_Overview page with improved loading strategy
                        try:
                            # Try networkidle first (most reliable but may timeout)
                            await self.page.goto(efile_link, wait_until='networkidle', timeout=20000)
                        except Exception as networkidle_error:
                            self.logger.debug(f"networkidle timeout, trying domcontentloaded: {networkidle_error}")
                            try:
                                # Fallback to domcontentloaded (faster but may miss some content)
                                await self.page.goto(efile_link, wait_until='domcontentloaded', timeout=15000)
                            except Exception as domload_error:
                                self.logger.debug(f"domcontentloaded timeout, trying load: {domload_error}")
                                # Final fallback to basic load event
                                await self.page.goto(efile_link, wait_until='load', timeout=10000)
                        
                        # Wait for page to stabilize and JavaScript to execute
                        await asyncio.sleep(5)  # Increased wait time for JavaScript execution
                        
                        # Look for DOCUMENT INFORMATION section
                        document_info = await self.extract_document_information_from_page(entry, efile_id)
                        
                        if document_info:
                            efile_results['documents_found'] += len(document_info.get('documents', []))
                            efile_results['document_links'].extend(document_info.get('documents', []))
                            self.logger.info(f"✅ Found {len(document_info.get('documents', []))} documents for Efile ID {efile_id}")
                        else:
                            self.logger.warning(f"⚠️ No documents found in DOCUMENT INFORMATION for Efile ID {efile_id}")
                            
                    except Exception as e:
                        error_msg = f"Error processing Efile ID {efile_id}: {e}"
                        self.logger.error(error_msg)
                        efile_results['errors'].append(error_msg)
            
            self.logger.info(f"Efile document extraction complete: {efile_results['documents_found']} documents found from {efile_results['entries_with_efile_ids']} entries")
            return efile_results
            
        except Exception as e:
            self.logger.error(f"Error extracting efile document links: {e}")
            return {'error': str(e)}
    
    async def extract_all_document_links(self, case_data: Dict) -> Dict:
        """
        Extract document links from ALL sources: docket, images, eservice_queue, and my_filings
        
        Args:
            case_data: Complete case data from scrape_case_data()
            
        Returns:
            Dictionary with comprehensive document extraction results
        """
        try:
            all_results = {
                'total_sources': 4,
                'sources_processed': 0,
                'total_documents_found': 0,
                'document_links': [],
                'source_breakdown': {
                    'docket_entries': {'count': 0, 'documents': []},
                    'images_tab': {'count': 0, 'documents': []},
                    'eservice_queue': {'count': 0, 'documents': []}, 
                    'my_filings': {'count': 0, 'documents': []}
                },
                'errors': []
            }
            
            # 1. Process docket entries (existing logic)
            if 'docket_information' in case_data and 'entries' in case_data['docket_information']:
                docket_results = await self.extract_efile_document_links(case_data['docket_information']['entries'])
                all_results['source_breakdown']['docket_entries']['count'] = docket_results.get('documents_found', 0)
                all_results['source_breakdown']['docket_entries']['documents'] = docket_results.get('document_links', [])
                all_results['total_documents_found'] += docket_results.get('documents_found', 0)
                all_results['document_links'].extend(docket_results.get('document_links', []))
                all_results['sources_processed'] += 1
                self.logger.info(f"📋 Docket entries: {docket_results.get('documents_found', 0)} documents")
            
            # 2. Process E-Service Queue notices
            if 'eservice_queue' in case_data and 'notices' in case_data['eservice_queue']:
                notices = case_data['eservice_queue']['notices']
                eservice_docs = []
                
                for notice in notices:
                    if notice.get('url') and 'DisplayImageList.aspx?q=' in notice['url']:
                        # Extract the query parameter
                        url = notice['url']
                        if '?q=' in url:
                            doc_id = url.split('?q=')[1].split('&')[0]
                            # Convert to EFiling_Overview URL
                            efile_link = f"{self.base_url}/EFiling_Overview.aspx?q={doc_id}"
                            
                            doc_info = {
                                'source': 'eservice_queue',
                                'document_id': doc_id,
                                'original_url': url,
                                'efile_overview_url': efile_link,
                                'type': notice.get('type', 'e_service_notice'),
                                'title': notice.get('title', 'E-Service Notice')
                            }
                            eservice_docs.append(doc_info)
                
                all_results['source_breakdown']['eservice_queue']['count'] = len(eservice_docs)
                all_results['source_breakdown']['eservice_queue']['documents'] = eservice_docs
                all_results['total_documents_found'] += len(eservice_docs)
                all_results['document_links'].extend(eservice_docs)
                all_results['sources_processed'] += 1
                self.logger.info(f"📧 E-Service notices: {len(eservice_docs)} documents")
            
            # 3. Process Images tab
            if 'images' in case_data and 'images' in case_data['images']:
                images = case_data['images']['images']
                image_docs = []
                
                for image in images:
                    if image.get('url') and 'DisplayImageList.aspx?q=' in image['url']:
                        # Extract the query parameter
                        url = image['url']
                        if '?q=' in url:
                            doc_id = url.split('?q=')[1].split('&')[0]
                            # Convert to EFiling_Overview URL
                            efile_link = f"{self.base_url}/EFiling_Overview.aspx?q={doc_id}"
                            
                            doc_info = {
                                'source': 'images_tab',
                                'document_id': doc_id,
                                'original_url': url,
                                'efile_overview_url': efile_link,
                                'type': 'image_document',
                                'title': image.get('title', 'Image Document')
                            }
                            image_docs.append(doc_info)
                
                all_results['source_breakdown']['images_tab']['count'] = len(image_docs)
                all_results['source_breakdown']['images_tab']['documents'] = image_docs
                all_results['total_documents_found'] += len(image_docs)
                all_results['document_links'].extend(image_docs)
                all_results['sources_processed'] += 1
                self.logger.info(f"🖼️ Images tab: {len(image_docs)} documents")
            
            # 4. Process My Filings
            if 'my_filings' in case_data and 'filings' in case_data['my_filings']:
                filings = case_data['my_filings']['filings']
                filing_docs = []
                
                for filing in filings:
                    if filing.get('url') and 'DisplayImageList.aspx?q=' in filing['url']:
                        # Extract the query parameter
                        url = filing['url']
                        if '?q=' in url:
                            doc_id = url.split('?q=')[1].split('&')[0]
                            # Convert to EFiling_Overview URL
                            efile_link = f"{self.base_url}/EFiling_Overview.aspx?q={doc_id}"
                            
                            doc_info = {
                                'source': 'my_filings',
                                'document_id': doc_id,
                                'original_url': url,
                                'efile_overview_url': efile_link,
                                'type': filing.get('type', 'my_filing'),
                                'title': filing.get('title', 'My Filing')
                            }
                            filing_docs.append(doc_info)
                
                all_results['source_breakdown']['my_filings']['count'] = len(filing_docs)
                all_results['source_breakdown']['my_filings']['documents'] = filing_docs
                all_results['total_documents_found'] += len(filing_docs)
                all_results['document_links'].extend(filing_docs)
                all_results['sources_processed'] += 1
                self.logger.info(f"📁 My filings: {len(filing_docs)} documents")
            
            # Remove duplicates based on document_id
            unique_docs = {}
            for doc in all_results['document_links']:
                doc_id = doc.get('document_id') or doc.get('efile_id')
                if doc_id and doc_id not in unique_docs:
                    unique_docs[doc_id] = doc
            
            all_results['document_links'] = list(unique_docs.values())
            all_results['total_documents_found'] = len(unique_docs)
            
            self.logger.info(f"🎯 COMPREHENSIVE EXTRACTION COMPLETE: {all_results['total_documents_found']} unique documents from {all_results['sources_processed']} sources")
            
            return all_results
            
        except Exception as e:
            self.logger.error(f"Error in comprehensive document extraction: {e}")
            return {
                'total_sources': 4,
                'sources_processed': 0,
                'total_documents_found': 0,
                'document_links': [],
                'source_breakdown': {},
                'errors': [str(e)]
            }
    
    async def extract_document_information_from_page(self, docket_entry: Dict, efile_id: str) -> Optional[Dict]:
        """
        Extract document information from EFiling_Overview.aspx page
        Handles JavaScript-based document links as mentioned by user
        
        Args:
            docket_entry: Original docket entry for context
            efile_id: Efile ID for this document
            
        Returns:
            Dictionary with document metadata and download links
        """
        try:
            document_info = {
                'efile_id': efile_id,
                'docket_entry': docket_entry.get('Docket Number', 'unknown'),
                'filing_date': docket_entry.get('Filing Date', 'unknown'),
                'document_type': docket_entry.get('Document Type', 'unknown'),
                'documents': []
            }
            
            # Look for DOCUMENT INFORMATION section
            doc_info_selectors = [
                'div:has-text("DOCUMENT INFORMATION")',
                'table:has-text("DOCUMENT INFORMATION")',
                'span:has-text("DOCUMENT INFORMATION")',
                'h3:has-text("DOCUMENT INFORMATION")',
                'div[id*="document"], div[class*="document"]'
            ]
            
            document_section_found = False
            
            for selector in doc_info_selectors:
                try:
                    doc_section = await self.page.query_selector(selector)
                    if doc_section:
                        document_section_found = True
                        self.logger.info(f"📋 Found DOCUMENT INFORMATION section with selector: {selector}")
                        
                        # Look for document links within or near this section
                        # Check parent and sibling elements for document links
                        parent = await doc_section.query_selector('xpath=..')
                        if parent:
                            doc_links = await parent.query_selector_all('a[href*="document"], a[href*="pdf"], a[href*="image"], a[onclick*="window.open"]')
                        else:
                            doc_links = await doc_section.query_selector_all('a[href*="document"], a[href*="pdf"], a[href*="image"], a[onclick*="window.open"]')
                        
                        # Also check for JavaScript-based links that open in new windows
                        js_links = await self.page.query_selector_all('a[onclick*="window.open"], input[onclick*="window.open"], button[onclick*="window.open"]')
                        doc_links.extend(js_links)
                        
                        for link in doc_links:
                            await self.process_document_link(link, document_info, efile_id)
                        
                        break
                        
                except Exception as e:
                    self.logger.debug(f"Selector {selector} failed: {e}")
                    continue
            
            if not document_section_found:
                # Fallback: look for any document download links on the entire page
                self.logger.warning("⚠️ DOCUMENT INFORMATION section not found, searching entire page for document links")
                
                # First, try to trigger JavaScript by hovering over potential download buttons
                await self.trigger_javascript_downloads()
                
                fallback_selectors = [
                    # E-filing specific patterns based on actual page analysis
                    'a[href*="DisplayImageList.aspx"]',  # E-filing document view pages
                    'a[title*="View Image"]',  # Links with "View Image" title
                    'img[src*="ImageSheet.png"]',  # The actual download button images
                    'img[alt*="View Image Button"]',  # Image alt text
                    # Original fallback patterns
                    'a[href*="pdf"]',
                    'a[href*="document"]',
                    'a[href*="download"]',
                    'a[onclick*="window.open"]',
                    'input[onclick*="window.open"][value*="View"], input[onclick*="window.open"][value*="Download"]',
                    'button:has-text("Download")', 'button:has-text("View")',
                    'input[value*="Download"], input[value*="View"]',
                    'a:has-text("Download"), a:has-text("View")'
                ]
                
                for selector in fallback_selectors:
                    try:
                        elements = await self.page.query_selector_all(selector)
                        for element in elements:
                            # Handle img elements inside links - get the parent link
                            tag_name = await element.evaluate('el => el.tagName.toLowerCase()')
                            if tag_name == 'img':
                                # Try to get parent link element
                                parent_link = await element.evaluate('el => el.closest("a")')
                                if parent_link:
                                    # Process the parent link element
                                    parent_element = await self.page.evaluate_handle('el => el', parent_link)
                                    await self.process_document_link(parent_element, document_info, efile_id)
                                else:
                                    self.logger.debug(f"📎 Found img element but no parent link: {selector}")
                            else:
                                # Process the element directly (should be a link)
                                await self.process_document_link(element, document_info, efile_id)
                    except Exception as e:
                        self.logger.debug(f"Fallback selector {selector} failed: {e}")
                        continue
            
            return document_info if document_info['documents'] else None
            
        except Exception as e:
            self.logger.error(f"Error extracting document information from page: {e}")
            return None
    
    async def trigger_javascript_downloads(self):
        """
        Trigger JavaScript that generates download links on hover/interaction
        Based on user's observation that links populate when hovering over download buttons
        """
        try:
            self.logger.info("🎯 Attempting to trigger JavaScript download link generation...")
            
            # Look for potential download buttons/elements that might trigger JavaScript
            potential_triggers = [
                # E-filing specific selectors based on actual page structure analysis
                'img[src*="ImageSheet.png"]',  # The actual download button images
                'a[href*="DisplayImageList.aspx"]',  # Document view links
                'a[title*="View Image"]',  # Links with "View Image" title
                'img[alt*="View Image Button"]',  # Image buttons with view text
                # Original generic selectors as fallback
                'button:has-text("Download")',
                'input[value*="Download"]',
                'a:has-text("Download")',
                'button:has-text("View")',
                'input[value*="View"]',
                'a:has-text("View")',
                'span:has-text("Download")',
                'div:has-text("Download")',
                # Look for common button/link patterns
                'input[type="button"]',
                'input[type="submit"]',
                'button',
                'a[href="#"]',
                # Look for elements with onclick handlers
                '[onclick*="window.open"]',
                '[onclick*="document"]',
                '[onclick*="pdf"]'
            ]
            
            triggered_elements = 0
            
            for selector in potential_triggers:
                try:
                    elements = await self.page.query_selector_all(selector)
                    for element in elements:
                        try:
                            # Get element text to see if it's relevant
                            text = await element.text_content() or ''
                            value = await element.get_attribute('value') or ''
                            onclick = await element.get_attribute('onclick') or ''
                            
                            # Only interact with elements that seem document-related
                            if any(keyword in (text + value + onclick).lower() for keyword in 
                                   ['download', 'view', 'document', 'pdf', 'file', 'open']):
                                
                                self.logger.info(f"🔍 Hovering over potential trigger: {text[:50] or value[:50] or 'element'}")
                                
                                # Hover to trigger any JavaScript
                                await element.hover(timeout=3000)
                                await asyncio.sleep(1)  # Give JavaScript time to execute
                                
                                # Also try clicking if it seems safe (has onclick handler)
                                if onclick and ('window.open' in onclick or 'document' in onclick.lower()):
                                    self.logger.info(f"🖱️ Clicking element with onclick: {onclick[:100]}")
                                    try:
                                        await element.click(timeout=3000)
                                        await asyncio.sleep(2)  # Wait for any popups or redirects
                                    except Exception as click_error:
                                        self.logger.debug(f"Click failed: {click_error}")
                                
                                triggered_elements += 1
                                
                        except Exception as element_error:
                            self.logger.debug(f"Error interacting with element: {element_error}")
                            continue
                            
                except Exception as selector_error:
                    self.logger.debug(f"Selector {selector} failed: {selector_error}")
                    continue
            
            self.logger.info(f"🎯 Triggered {triggered_elements} potential JavaScript elements")
            
            # Wait a bit more for any delayed JavaScript execution
            await asyncio.sleep(3)
            
            # Try to execute any page JavaScript that might generate links
            try:
                # Execute common patterns that might trigger link generation
                js_commands = [
                    "if (typeof updateLinks === 'function') updateLinks();",
                    "if (typeof loadDocuments === 'function') loadDocuments();",
                    "if (typeof initializeDownloads === 'function') initializeDownloads();",
                    # Trigger common jQuery/DOM events
                    "$('body').trigger('mouseover');",
                    "$(document).trigger('ready');",
                    "window.dispatchEvent(new Event('load'));",
                    "window.dispatchEvent(new Event('DOMContentLoaded'));"
                ]
                
                for cmd in js_commands:
                    try:
                        await self.page.evaluate(cmd)
                        await asyncio.sleep(0.5)
                    except Exception as js_error:
                        self.logger.debug(f"JS command failed: {cmd} - {js_error}")
                        
            except Exception as js_exec_error:
                self.logger.debug(f"JavaScript execution failed: {js_exec_error}")
            
            self.logger.info("🎯 JavaScript trigger attempt completed")
            
            # Optional: Save page HTML after JavaScript execution for debugging
            if triggered_elements > 0:
                try:
                    html_content = await self.page.content()
                    debug_dir = Path("debug_html")
                    debug_dir.mkdir(exist_ok=True)
                    debug_file = debug_dir / f"after_js_trigger_{int(time.time())}.html"
                    with open(debug_file, 'w', encoding='utf-8') as f:
                        f.write(html_content)
                    self.logger.info(f"📄 Saved post-JavaScript HTML to {debug_file}")
                except Exception as save_error:
                    self.logger.debug(f"Could not save debug HTML: {save_error}")
            
        except Exception as e:
            self.logger.error(f"Error triggering JavaScript downloads: {e}")
    
    async def process_document_link(self, link_element, document_info: Dict, efile_id: str):
        """
        Process a single document link element and extract download information
        
        Args:
            link_element: Playwright element for the link
            document_info: Document info dictionary to add to
            efile_id: Current Efile ID for context
        """
        try:
            # Get link attributes
            href = await link_element.get_attribute('href')
            onclick = await link_element.get_attribute('onclick')
            title = await link_element.get_attribute('title') or ''
            text = await link_element.text_content() or ''
            
            # Handle JavaScript-based links
            if onclick and 'window.open' in onclick:
                # Extract URL from onclick JavaScript
                import re
                url_match = re.search(r"window\.open\s*\(\s*['\"]([^'\"]+)['\"]", onclick)
                if url_match:
                    href = url_match.group(1)
                    self.logger.info(f"📎 Extracted JavaScript URL: {href}")
            
            if href:
                # Make URL absolute
                if href.startswith('/'):
                    full_url = f"{self.base_url}{href}"
                elif not href.startswith('http'):
                    full_url = f"{self.base_url}/{href}"
                else:
                    full_url = href
                
                # Determine document type from URL or link text
                doc_type = 'document'
                if '.pdf' in href.lower() or 'pdf' in text.lower():
                    doc_type = 'pdf'
                elif any(img_ext in href.lower() for img_ext in ['.jpg', '.jpeg', '.png', '.gif', '.tiff']):
                    doc_type = 'image'
                
                document_info['documents'].append({
                    'url': full_url,
                    'title': title,
                    'link_text': text.strip(),
                    'document_type': doc_type,
                    'efile_id': efile_id,
                    'extraction_method': 'javascript' if onclick else 'direct_href',
                    'original_href': href,
                    'onclick_script': onclick
                })
                
                self.logger.info(f"📄 Added document link: {text[:30]}... -> {full_url}")
                
        except Exception as e:
            self.logger.error(f"Error processing document link: {e}")

    async def compare_public_vs_authenticated_docket(self, case_number: str, authenticated_docket: Dict) -> Dict:
        """
        Compare public vs authenticated docket to identify hidden/sealed entries
        Critical for document completeness verification
        
        Args:
            case_number: Case number for public docket lookup
            authenticated_docket: Already scraped authenticated docket data
            
        Returns:
            Dictionary with comparison results and hidden entries analysis
        """
        try:
            self.logger.info(f"🔍 Comparing public vs authenticated docket access for {case_number}")
            
            # Scrape public docket
            public_docket = await self.scrape_public_docket(case_number)
            
            comparison_results = {
                'case_number': case_number,
                'comparison_date': datetime.now().isoformat(),
                'public_entries_count': len(public_docket.get('entries', [])),
                'authenticated_entries_count': len(authenticated_docket.get('entries', [])),
                'hidden_entries': [],
                'document_access_difference': {},
                'analysis': {}
            }
            
            # Compare entry counts
            public_entries = public_docket.get('entries', [])
            auth_entries = authenticated_docket.get('entries', [])
            
            if len(auth_entries) > len(public_entries):
                comparison_results['hidden_entries_count'] = len(auth_entries) - len(public_entries)
                self.logger.warning(f"🚨 {comparison_results['hidden_entries_count']} entries hidden from public view")
                
                # Try to identify hidden entries (simplified comparison)
                # In real implementation, would need more sophisticated matching
                comparison_results['analysis']['hidden_entry_analysis'] = (
                    f"Authenticated access reveals {comparison_results['hidden_entries_count']} additional entries "
                    f"not visible in public docket. This may indicate sealed records, confidential documents, "
                    f"or restricted access materials."
                )
            else:
                comparison_results['analysis']['visibility'] = "All authenticated entries appear to be publicly visible"
            
            # Document access comparison
            public_docs_available = sum(1 for entry in public_entries if entry.get('Has_Image', False))
            auth_docs_available = sum(1 for entry in auth_entries if entry.get('Has_Image', False))
            
            comparison_results['document_access_difference'] = {
                'public_documents': public_docs_available,
                'authenticated_documents': auth_docs_available,
                'additional_document_access': auth_docs_available - public_docs_available
            }
            
            if auth_docs_available > public_docs_available:
                self.logger.critical(
                    f"🔐 CRITICAL: Authenticated access provides {auth_docs_available - public_docs_available} "
                    f"additional document downloads not available to public"
                )
                comparison_results['analysis']['document_access'] = (
                    "Authenticated access provides significantly more document access than public view. "
                    "This represents the complete case record vs public-limited access."
                )
            
            return comparison_results
            
        except Exception as e:
            self.logger.error(f"Error comparing docket access levels: {e}")
            return {
                'error': str(e),
                'case_number': case_number,
                'comparison_date': datetime.now().isoformat()
            }

    async def download_efile_documents(self, document_links: List[Dict], download_dir: str = "case_documents/efile") -> Dict:
        """
        Download documents from extracted Efile document links
        Supports both direct downloads and JavaScript-initiated downloads
        
        Args:
            document_links: List of document link dictionaries from extract_efile_document_links
            download_dir: Directory to save downloaded documents
            
        Returns:
            Dictionary with download results
        """
        try:
            import os
            from pathlib import Path
            
            # Create download directory
            Path(download_dir).mkdir(parents=True, exist_ok=True)
            
            download_results = {
                'total_documents': len(document_links),
                'successful_downloads': 0,
                'failed_downloads': 0,
                'downloaded_files': [],
                'errors': []
            }
            
            for i, doc_info in enumerate(document_links):
                try:
                    url = doc_info.get('url', '')
                    efile_id = doc_info.get('efile_id', 'unknown')
                    doc_type = doc_info.get('document_type', 'document')
                    link_text = doc_info.get('link_text', '').replace('/', '_').replace('\\', '_')
                    
                    # Create safe filename
                    safe_text = ''.join(c for c in link_text if c.isalnum() or c in '-_. ')[:50].strip()
                    if not safe_text:
                        safe_text = f"document_{i+1}"
                    
                    # Determine file extension
                    if doc_type == 'pdf' or '.pdf' in url.lower():
                        extension = '.pdf'
                    elif doc_type == 'image' or any(ext in url.lower() for ext in ['.jpg', '.jpeg', '.png', '.gif', '.tiff']):
                        extension = '.jpg'  # Default for images
                        if '.png' in url.lower():
                            extension = '.png'
                        elif '.gif' in url.lower():
                            extension = '.gif'
                        elif '.tiff' in url.lower() or '.tif' in url.lower():
                            extension = '.tiff'
                    else:
                        extension = '.pdf'  # Default assumption
                    
                    filename = f"{efile_id}_{safe_text}{extension}"
                    file_path = os.path.join(download_dir, filename)
                    
                    self.logger.info(f"📥 Downloading document {i+1}/{len(document_links)}: {filename}")
                    
                    # Handle JavaScript-based downloads
                    if doc_info.get('extraction_method') == 'javascript' and doc_info.get('onclick_script'):
                        # For JavaScript links, we need to execute the script or navigate directly
                        try:
                            # Try direct navigation first
                            await self.page.goto(url, wait_until='networkidle')
                            await asyncio.sleep(2)
                            
                            # Check if this triggers a download
                            async with self.page.expect_download(timeout=10000) as download_info:
                                # The download should start automatically if the URL is correct
                                pass
                                
                        except Exception:
                            # If direct navigation doesn't work, try clicking the original element
                            self.logger.warning(f"Direct download failed for {filename}, attempting JavaScript execution")
                            # This would require navigating back to the original page and finding the element
                            # For now, skip these complex cases
                            raise Exception("JavaScript-based download not supported yet")
                    else:
                        # Direct download
                        async with self.page.expect_download(timeout=15000) as download_info:
                            await self.page.goto(url)
                        
                    # Save the download
                    download = await download_info.value
                    await download.save_as(file_path)
                    
                    download_results['successful_downloads'] += 1
                    download_results['downloaded_files'].append({
                        'filename': filename,
                        'path': file_path,
                        'efile_id': efile_id,
                        'document_type': doc_type,
                        'url': url,
                        'file_size': os.path.getsize(file_path) if os.path.exists(file_path) else 0
                    })
                    
                    self.logger.info(f"✅ Successfully downloaded: {filename}")
                    
                except Exception as e:
                    error_msg = f"Failed to download document {i+1}: {e}"
                    self.logger.error(error_msg)
                    download_results['errors'].append(error_msg)
                    download_results['failed_downloads'] += 1
                    continue
            
            self.logger.info(f"Document download completed: {download_results['successful_downloads']} successful, {download_results['failed_downloads']} failed")
            return download_results
            
        except Exception as e:
            self.logger.error(f"Error downloading efile documents: {e}")
            return {'error': str(e)}

    async def download_document_images(self, docket_entries: List[Dict], download_dir: str = "case_documents/images") -> Dict:
        """
        Download all images from docket entries that have image links
        
        Args:
            docket_entries: List of docket entries with image links
            download_dir: Directory to save downloaded images
            
        Returns:
            Dictionary with download results
        """
        try:
            import os
            from pathlib import Path
            
            # Create download directory
            Path(download_dir).mkdir(parents=True, exist_ok=True)
            
            download_results = {
                'total_entries': len(docket_entries),
                'entries_with_images': 0,
                'images_downloaded': 0,
                'failed_downloads': 0,
                'downloaded_files': [],
                'errors': []
            }
            
            for entry in docket_entries:
                if entry.get('Has_Image') and entry.get('Image_Link'):
                    download_results['entries_with_images'] += 1
                    
                    try:
                        # Navigate to the image display page
                        image_url = f"{self.base_url}/{entry['Image_Link']}" if not entry['Image_Link'].startswith('http') else entry['Image_Link']
                        await self.page.goto(image_url, wait_until='networkidle')
                        await asyncio.sleep(2)
                        
                        # Look for actual image or PDF download links
                        download_links = []
                        
                        # Check for direct image links
                        img_elements = await self.page.query_selector_all('img[src*=".pdf"], img[src*=".jpg"], img[src*=".png"], img[src*=".gif"]')
                        for img in img_elements:
                            src = await img.get_attribute('src')
                            if src and not src.endswith('.png'):  # Skip UI icons
                                download_links.append(src)
                        
                        # Check for PDF links
                        pdf_links = await self.page.query_selector_all('a[href*=".pdf"]')
                        for link in pdf_links:
                            href = await link.get_attribute('href')
                            if href:
                                download_links.append(href)
                                
                        # Check for generic document download links
                        doc_links = await self.page.query_selector_all('a[title*="Download"], a[title*="View"], a[href*="document"]')
                        for link in doc_links:
                            href = await link.get_attribute('href')
                            if href:
                                download_links.append(href)
                        
                        # Download each found link
                        for i, link in enumerate(download_links[:3]):  # Limit to first 3 per entry
                            try:
                                # Create filename from docket info
                                filing_date = entry.get('Filing Date', 'unknown')
                                doc_type = entry.get('Type', 'document')
                                docket_num = entry.get('Docket Number', i)
                                
                                # Clean filename
                                safe_date = filing_date.replace('/', '-') if filing_date else 'unknown'
                                safe_type = ''.join(c for c in doc_type if c.isalnum() or c in '-_').strip()
                                
                                filename = f"{docket_num:03d}_{safe_date}_{safe_type}_{i+1}"
                                
                                # Determine file extension
                                if '.pdf' in link.lower():
                                    filename += '.pdf'
                                elif any(ext in link.lower() for ext in ['.jpg', '.jpeg']):
                                    filename += '.jpg'
                                elif '.png' in link.lower():
                                    filename += '.png'
                                else:
                                    filename += '.pdf'  # Default to PDF
                                
                                file_path = os.path.join(download_dir, filename)
                                
                                # Download using playwright
                                if link.startswith('http'):
                                    full_url = link
                                else:
                                    full_url = f"{self.base_url}/{link.lstrip('/')}"
                                
                                # Start download
                                async with self.page.expect_download() as download_info:
                                    await self.page.goto(full_url)
                                download = await download_info.value
                                
                                # Save file
                                await download.save_as(file_path)
                                
                                download_results['images_downloaded'] += 1
                                download_results['downloaded_files'].append({
                                    'filename': filename,
                                    'path': file_path,
                                    'docket_number': entry.get('Docket Number'),
                                    'filing_date': entry.get('Filing Date'),
                                    'document_type': entry.get('Type'),
                                    'url': full_url
                                })
                                
                                self.logger.info(f"Downloaded: {filename}")
                                
                            except Exception as download_error:
                                error_msg = f"Failed to download {link}: {download_error}"
                                self.logger.error(error_msg)
                                download_results['errors'].append(error_msg)
                                download_results['failed_downloads'] += 1
                        
                    except Exception as entry_error:
                        error_msg = f"Failed to process entry {entry.get('Docket Number')}: {entry_error}"
                        self.logger.error(error_msg)
                        download_results['errors'].append(error_msg)
                        download_results['failed_downloads'] += 1
            
            self.logger.info(f"Image download completed: {download_results['images_downloaded']} downloaded, {download_results['failed_downloads']} failed")
            return download_results
            
        except Exception as e:
            self.logger.error(f"Error downloading document images: {e}")
            return {'error': str(e)}
            
    def attach_forensic_extension(self, forensic_extension):
        """Attach forensic extension for tampering detection"""
        self.forensic_extension = forensic_extension
        self.logger.info("🔒 Forensic extension attached - tampering detection enabled")
    
    def attach_change_monitor(self, change_monitor):
        """Attach document change monitor for comprehensive change detection"""
        self.change_monitor = change_monitor
        self.logger.info("📊 Document change monitor attached - change detection enabled")
        
    async def perform_forensic_analysis(self, docket_data: Dict, case_number: str):
        """
        Perform forensic analysis on scraped docket data
        Preserves snapshots and detects tampering
        """
        if not hasattr(self, 'forensic_extension') or not self.forensic_extension:
            return
            
        try:
            self.logger.info("🔍 Starting forensic analysis...")
            
            entries = docket_data.get('entries', [])
            if not entries:
                return
                
            # Get case ID (simplified - in real implementation, look up from database)
            case_id = 1  # TODO: Get actual case ID from database
            
            forensic_results = {
                'snapshots_created': 0,
                'tampering_detected': 0,
                'critical_incidents': 0,
                'evidence_preserved': True
            }
            
            # Process each document entry
            for entry in entries:
                try:
                    # Check if this is a first-time scan (preserve snapshot)
                    document_id = f"{case_id}_{entry.get('Docket Number', 'unknown')}"
                    existing_snapshot = self.forensic_extension.get_original_snapshot(document_id)
                    
                    if not existing_snapshot:
                        # Create initial forensic snapshot
                        snapshot = self.forensic_extension.preserve_document_snapshot(entry, case_id)
                        forensic_results['snapshots_created'] += 1
                        self.logger.info(f"📸 Snapshot created for document {document_id}")
                    else:
                        # Check for tampering
                        tampering = self.forensic_extension.detect_document_tampering(entry, case_id)
                        if tampering:
                            forensic_results['tampering_detected'] += 1
                            
                            if tampering.severity == 'CRITICAL':
                                forensic_results['critical_incidents'] += 1
                                self.logger.critical(f"🚨 CRITICAL TAMPERING: {tampering.tamper_type} in {document_id}")
                                
                                # Special handling for judicial misconduct
                                if tampering.judicial_actor:
                                    await self.document_judicial_misconduct(tampering, case_id)
                            else:
                                self.logger.warning(f"⚠️ Document tampering detected: {tampering.tamper_type}")
                                
                except Exception as entry_error:
                    self.logger.error(f"❌ Error analyzing document entry: {entry_error}")
                    continue
                    
            # Log forensic summary
            if forensic_results['tampering_detected'] > 0:
                self.logger.critical(
                    f"🚨 FORENSIC ALERT: {forensic_results['tampering_detected']} tampering incidents detected "
                    f"({forensic_results['critical_incidents']} critical)"
                )
            else:
                self.logger.info(f"✅ Forensic analysis complete: {forensic_results['snapshots_created']} snapshots preserved")
                
            # Store results in docket data for UI display
            docket_data['forensic_analysis'] = forensic_results
            
        except Exception as e:
            self.logger.error(f"❌ Forensic analysis failed: {e}")
            docket_data['forensic_analysis'] = {
                'error': str(e),
                'evidence_preserved': False
            }
            
    async def document_judicial_misconduct(self, tampering_evidence, case_id: int):
        """Document judicial misconduct incident"""
        try:
            misconduct_data = {
                'case_id': case_id,
                'incident_type': f'JUDICIAL_DOCUMENT_TAMPERING_{tampering_evidence.tamper_type}',
                'description': f'Judicial officer {tampering_evidence.judicial_actor} engaged in unauthorized document alteration',
                'evidence_documents': [tampering_evidence.document_id],
                'judicial_officer': tampering_evidence.judicial_actor,
                'incident_date': tampering_evidence.detection_time,
                'severity': tampering_evidence.severity,
                'legal_violations': tampering_evidence.misconduct_indicators,
                'status': 'REQUIRES_IMMEDIATE_ACTION'
            }
            
            # Store in database
            sql = """
            INSERT INTO judicial_misconduct 
            (case_id, incident_type, description, evidence_documents, judicial_officer, 
             incident_date, severity, status, legal_violations)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
            """
            
            with self.forensic_extension.postgresql.get_connection() as conn:
                with conn.cursor() as cursor:
                    cursor.execute(sql, (
                        misconduct_data['case_id'],
                        misconduct_data['incident_type'],
                        misconduct_data['description'],
                        misconduct_data['evidence_documents'],
                        misconduct_data['judicial_officer'],
                        misconduct_data['incident_date'],
                        misconduct_data['severity'],
                        misconduct_data['status'],
                        misconduct_data['legal_violations']
                    ))
                    conn.commit()
                    
            self.logger.critical(f"📋 JUDICIAL MISCONDUCT DOCUMENTED: {tampering_evidence.judicial_actor}")
            
        except Exception as e:
            self.logger.error(f"❌ Failed to document judicial misconduct: {e}")
            
    async def scrape_all_case_data(self, case_number: str) -> Dict:
        """
        Main method to scrape all data for a case
        
        Args:
            case_number: Case number (e.g., 'DR-25-403973')
            
        Returns:
            Complete case data dictionary
        """
        try:
            # Login first
            if not await self.login():
                return {'error': 'Login failed'}
                
            # Find the case and get query parameter
            case_query = await self.find_case(case_number)
            if not case_query:
                return {'error': f'Case {case_number} not found'}
                
            # Scrape all four areas
            results = {
                'case_number': case_number,
                'case_query': case_query,
                'scraped_at': datetime.now().isoformat(),
                'docket_information': {},
                'images': {},
                'eservice_queue': {},
                'my_filings': {}
            }
            
            # 1. Docket Information
            self.logger.info("Scraping docket information...")
            results['docket_information'] = await self.scrape_docket_information(case_query)
            
            # 1.5 Forensic preservation and tampering detection
            if hasattr(self, 'forensic_extension') and self.forensic_extension:
                await self.perform_forensic_analysis(results['docket_information'], case_number)
            
            # 1.6 Compare public vs authenticated docket access (NEW - completeness verification)
            self.logger.info("Comparing public vs authenticated docket access...")
            docket_comparison = await self.compare_public_vs_authenticated_docket(case_number, results['docket_information'])
            results['docket_access_analysis'] = docket_comparison
            
            auth_entries = docket_comparison.get('authenticated_entries_count', 0)
            public_entries = docket_comparison.get('public_entries_count', 0) 
            if auth_entries > public_entries:
                self.logger.critical(f"🚨 COMPLETE DOCKET ACCESS: {auth_entries} total entries ({auth_entries - public_entries} hidden from public)")
            else:
                self.logger.info(f"📋 Docket access verified: {auth_entries} entries (all publicly visible)")
            
            # 1.7 Extract actual document links from Efile IDs (NEW - user requested)
            self.logger.info("Extracting Efile document links...")
            if results['docket_information'].get('entries'):
                efile_links = await self.extract_efile_document_links(results['docket_information']['entries'])
                results['efile_documents'] = efile_links
                self.logger.info(f"✅ Extracted {efile_links.get('documents_found', 0)} document links from Efile IDs")
            else:
                results['efile_documents'] = {'error': 'No docket entries available for Efile extraction'}
            
            # 2. Images
            self.logger.info("Scraping images...")
            results['images'] = await self.scrape_images(case_query)
            
            # 3. E-Service Queue
            self.logger.info("Scraping e-service queue...")
            results['eservice_queue'] = await self.scrape_eservice_queue()
            
            # 4. My Filings
            self.logger.info("Scraping my filings...")
            results['my_filings'] = await self.scrape_my_filings()
            
            return results
            
        except Exception as e:
            self.logger.error(f"Error scraping case data: {e}")
            return {'error': str(e), 'case_number': case_number}


# Synchronous wrapper function for easy integration
def scrape_cuyahoga_case(username: str, password: str, case_number: str, headless: bool = True) -> Dict:
    """
    Synchronous wrapper to scrape Cuyahoga County case data
    
    Args:
        username: E-filing system username
        password: E-filing system password
        case_number: Case number to scrape (e.g., 'DR-25-403973')
        headless: Run browser in headless mode
        
    Returns:
        Complete case data dictionary
    """
    async def _scrape():
        async with CuyahogaEFilingScraperPlaywright(username, password, headless) as scraper:
            return await scraper.scrape_all_case_data(case_number)
    
    return asyncio.run(_scrape())


if __name__ == "__main__":
    # Test the scraper
    import json
    
    # Example usage
    username = "GSHEPOV"
    password = "VhodaNet4u"
    case_number = "DR-25-403973"
    
    print("Starting Cuyahoga County E-Filing scraper...")
    results = scrape_cuyahoga_case(username, password, case_number, headless=True)
    
    print(f"Scraping completed. Results:")
    print(json.dumps(results, indent=2, default=str))
```

## File: error_recovery.py
```python
"""
Error Recovery and Resilience System for Legal Case Management
Provides robust error handling, retry logic, circuit breakers, and graceful degradation.
"""

import logging
import time
import functools
import threading
from typing import Dict, List, Optional, Callable, Any, Type
from datetime import datetime, timedelta
from enum import Enum
import traceback
import json
import sqlite3
from dataclasses import dataclass
import asyncio
from concurrent.futures import ThreadPoolExecutor, TimeoutError
import requests
from contextlib import contextmanager

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ErrorSeverity(Enum):
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

class CircuitState(Enum):
    CLOSED = "closed"      # Normal operation
    OPEN = "open"          # Failing, reject requests
    HALF_OPEN = "half_open"  # Testing if service recovered

@dataclass
class ErrorRecord:
    """Record of an error occurrence"""
    id: str
    timestamp: datetime
    error_type: str
    error_message: str
    severity: ErrorSeverity
    context: Dict[str, Any]
    stack_trace: str
    resolved: bool = False
    resolution_notes: Optional[str] = None

class CircuitBreaker:
    """Circuit breaker pattern implementation"""
    
    def __init__(self, failure_threshold: int = 5, recovery_timeout: int = 60, 
                 expected_exception: Type[Exception] = Exception):
        self.failure_threshold = failure_threshold
        self.recovery_timeout = recovery_timeout
        self.expected_exception = expected_exception
        
        self.failure_count = 0
        self.last_failure_time = None
        self.state = CircuitState.CLOSED
        self.lock = threading.Lock()
        
    def __call__(self, func):
        """Decorator to apply circuit breaker"""
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            return self._call(func, *args, **kwargs)
        return wrapper
        
    def _call(self, func, *args, **kwargs):
        """Execute function with circuit breaker logic"""
        with self.lock:
            if self.state == CircuitState.OPEN:
                if self._should_attempt_reset():
                    self.state = CircuitState.HALF_OPEN
                else:
                    raise Exception("Circuit breaker is OPEN")
                    
        try:
            result = func(*args, **kwargs)
            self._on_success()
            return result
            
        except self.expected_exception as e:
            self._on_failure()
            raise
            
    def _should_attempt_reset(self) -> bool:
        """Check if enough time has passed to attempt reset"""
        return (self.last_failure_time and 
                time.time() - self.last_failure_time >= self.recovery_timeout)
                
    def _on_success(self):
        """Handle successful execution"""
        with self.lock:
            self.failure_count = 0
            self.state = CircuitState.CLOSED
            
    def _on_failure(self):
        """Handle failed execution"""
        with self.lock:
            self.failure_count += 1
            self.last_failure_time = time.time()
            
            if self.failure_count >= self.failure_threshold:
                self.state = CircuitState.OPEN

class RetryManager:
    """Manages retry logic with exponential backoff"""
    
    def __init__(self, max_retries: int = 3, base_delay: float = 1.0, 
                 max_delay: float = 60.0, backoff_factor: float = 2.0):
        self.max_retries = max_retries
        self.base_delay = base_delay
        self.max_delay = max_delay
        self.backoff_factor = backoff_factor
        
    def __call__(self, func):
        """Decorator to apply retry logic"""
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            return self._retry_call(func, *args, **kwargs)
        return wrapper
        
    def _retry_call(self, func, *args, **kwargs):
        """Execute function with retry logic"""
        last_exception = None
        
        for attempt in range(self.max_retries + 1):
            try:
                return func(*args, **kwargs)
                
            except Exception as e:
                last_exception = e
                
                if attempt == self.max_retries:
                    logger.error(f"Function {func.__name__} failed after {self.max_retries} retries: {e}")
                    raise
                    
                delay = min(self.base_delay * (self.backoff_factor ** attempt), self.max_delay)
                logger.warning(f"Function {func.__name__} failed (attempt {attempt + 1}), retrying in {delay}s: {e}")
                time.sleep(delay)
                
        raise last_exception

class ErrorRecoveryManager:
    """Central error recovery and monitoring system"""
    
    def __init__(self, db_path: str = "error_recovery.db"):
        self.db_path = db_path
        self.error_handlers = {}
        self.circuit_breakers = {}
        self.retry_managers = {}
        self.error_records = []
        
        self.init_database()
        self.register_default_handlers()
        
    def init_database(self):
        """Initialize error tracking database"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS error_records (
                id TEXT PRIMARY KEY,
                timestamp TIMESTAMP,
                error_type TEXT,
                error_message TEXT,
                severity TEXT,
                context TEXT,
                stack_trace TEXT,
                resolved BOOLEAN DEFAULT 0,
                resolution_notes TEXT
            )
        ''')
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS system_health (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                component TEXT,
                status TEXT,
                metrics TEXT,
                alerts TEXT
            )
        ''')
        
        conn.commit()
        conn.close()
        
    def register_error_handler(self, error_type: str, handler: Callable):
        """Register custom error handler"""
        self.error_handlers[error_type] = handler
        logger.info(f"Registered error handler for: {error_type}")
        
    def register_default_handlers(self):
        """Register default error handlers"""
        self.register_error_handler("DatabaseError", self._handle_database_error)
        self.register_error_handler("NetworkError", self._handle_network_error)
        self.register_error_handler("FileSystemError", self._handle_filesystem_error)
        self.register_error_handler("AuthenticationError", self._handle_auth_error)
        self.register_error_handler("ValidationError", self._handle_validation_error)
        
    def handle_error(self, error: Exception, context: Dict[str, Any] = None, 
                    severity: ErrorSeverity = ErrorSeverity.MEDIUM) -> bool:
        """Handle an error with appropriate recovery strategy"""
        try:
            # Create error record
            error_record = ErrorRecord(
                id=f"err_{int(time.time() * 1000)}",
                timestamp=datetime.now(),
                error_type=type(error).__name__,
                error_message=str(error),
                severity=severity,
                context=context or {},
                stack_trace=traceback.format_exc()
            )
            
            # Log error
            self._log_error(error_record)
            
            # Try to recover
            recovery_success = self._attempt_recovery(error_record)
            
            # Update record with resolution
            if recovery_success:
                error_record.resolved = True
                error_record.resolution_notes = "Automatically recovered"
                
            # Save to database
            self._save_error_record(error_record)
            
            return recovery_success
            
        except Exception as recovery_error:
            logger.critical(f"Error in error recovery system: {recovery_error}")
            return False
            
    def _log_error(self, error_record: ErrorRecord):
        """Log error with appropriate level"""
        log_message = f"[{error_record.severity.value.upper()}] {error_record.error_type}: {error_record.error_message}"
        
        if error_record.severity == ErrorSeverity.CRITICAL:
            logger.critical(log_message)
        elif error_record.severity == ErrorSeverity.HIGH:
            logger.error(log_message)
        elif error_record.severity == ErrorSeverity.MEDIUM:
            logger.warning(log_message)
        else:
            logger.info(log_message)
            
    def _attempt_recovery(self, error_record: ErrorRecord) -> bool:
        """Attempt to recover from error"""
        error_type = error_record.error_type
        
        # Try specific handler
        if error_type in self.error_handlers:
            try:
                return self.error_handlers[error_type](error_record)
            except Exception as e:
                logger.error(f"Error handler failed for {error_type}: {e}")
                
        # Try generic recovery strategies
        return self._generic_recovery(error_record)
        
    def _generic_recovery(self, error_record: ErrorRecord) -> bool:
        """Generic recovery strategies"""
        try:
            # For database errors, try reconnection
            if "database" in error_record.error_message.lower():
                return self._recover_database_connection()
                
            # For network errors, try reconnection
            if any(keyword in error_record.error_message.lower() 
                  for keyword in ["network", "connection", "timeout", "unreachable"]):
                return self._recover_network_connection(error_record.context)
                
            # For file system errors, try cleanup
            if any(keyword in error_record.error_message.lower() 
                  for keyword in ["file", "directory", "permission", "disk"]):
                return self._recover_filesystem_issue(error_record.context)
                
            return False
            
        except Exception as e:
            logger.error(f"Generic recovery failed: {e}")
            return False
            
    def _save_error_record(self, error_record: ErrorRecord):
        """Save error record to database"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT OR REPLACE INTO error_records 
            (id, timestamp, error_type, error_message, severity, context, 
             stack_trace, resolved, resolution_notes)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            error_record.id, error_record.timestamp, error_record.error_type,
            error_record.error_message, error_record.severity.value,
            json.dumps(error_record.context), error_record.stack_trace,
            error_record.resolved, error_record.resolution_notes
        ))
        
        conn.commit()
        conn.close()
        
    # Specific error handlers
    def _handle_database_error(self, error_record: ErrorRecord) -> bool:
        """Handle database-related errors"""
        try:
            # Try to reconnect to database
            if self._recover_database_connection():
                logger.info("Database connection recovered")
                return True
                
            # Try to repair database if corrupted
            if "corrupt" in error_record.error_message.lower():
                return self._repair_database()
                
            return False
            
        except Exception as e:
            logger.error(f"Database error recovery failed: {e}")
            return False
            
    def _handle_network_error(self, error_record: ErrorRecord) -> bool:
        """Handle network-related errors"""
        try:
            # Test network connectivity
            if self._test_network_connectivity():
                logger.info("Network connectivity restored")
                return True
                
            # Try alternative endpoints if available
            if "url" in error_record.context:
                return self._try_alternative_endpoints(error_record.context["url"])
                
            return False
            
        except Exception as e:
            logger.error(f"Network error recovery failed: {e}")
            return False
            
    def _handle_filesystem_error(self, error_record: ErrorRecord) -> bool:
        """Handle file system errors"""
        try:
            # Check disk space
            if self._check_disk_space():
                # Try to clean up temporary files
                self._cleanup_temp_files()
                return True
                
            # Try to create missing directories
            if "directory" in error_record.error_message.lower():
                return self._create_missing_directories(error_record.context)
                
            return False
            
        except Exception as e:
            logger.error(f"Filesystem error recovery failed: {e}")
            return False
            
    def _handle_auth_error(self, error_record: ErrorRecord) -> bool:
        """Handle authentication errors"""
        try:
            # Try to refresh authentication tokens
            if "token" in error_record.error_message.lower():
                return self._refresh_auth_tokens()
                
            # Clear invalid sessions
            if "session" in error_record.error_message.lower():
                return self._clear_invalid_sessions()
                
            return False
            
        except Exception as e:
            logger.error(f"Authentication error recovery failed: {e}")
            return False
            
    def _handle_validation_error(self, error_record: ErrorRecord) -> bool:
        """Handle validation errors"""
        try:
            # Try to sanitize input data
            if "input" in error_record.context:
                sanitized_data = self._sanitize_input(error_record.context["input"])
                if sanitized_data:
                    logger.info("Input data sanitized successfully")
                    return True
                    
            return False
            
        except Exception as e:
            logger.error(f"Validation error recovery failed: {e}")
            return False
            
    # Recovery utility methods
    def _recover_database_connection(self) -> bool:
        """Attempt to recover database connection"""
        try:
            # Test connection
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute("SELECT 1")
            conn.close()
            return True
        except Exception:
            return False
            
    def _recover_network_connection(self, context: Dict) -> bool:
        """Attempt to recover network connection"""
        try:
            # Test basic connectivity
            response = requests.get("https://httpbin.org/status/200", timeout=5)
            return response.status_code == 200
        except Exception:
            return False
            
    def _recover_filesystem_issue(self, context: Dict) -> bool:
        """Attempt to recover from filesystem issues"""
        try:
            # Check if we can write to temp directory
            import tempfile
            with tempfile.NamedTemporaryFile() as tmp:
                tmp.write(b"test")
                tmp.flush()
            return True
        except Exception:
            return False
            
    def _repair_database(self) -> bool:
        """Attempt to repair corrupted database"""
        try:
            # This is a simplified repair - in production, you'd want more sophisticated repair
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute("PRAGMA integrity_check")
            result = cursor.fetchone()
            conn.close()
            return result[0] == "ok"
        except Exception:
            return False
            
    def _test_network_connectivity(self) -> bool:
        """Test basic network connectivity"""
        try:
            response = requests.get("https://httpbin.org/status/200", timeout=5)
            return response.status_code == 200
        except Exception:
            return False
            
    def _try_alternative_endpoints(self, original_url: str) -> bool:
        """Try alternative endpoints"""
        # This would contain logic to try backup URLs
        return False
        
    def _check_disk_space(self) -> bool:
        """Check available disk space"""
        try:
            import shutil
            total, used, free = shutil.disk_usage("/")
            # Check if we have at least 100MB free
            return free > 100 * 1024 * 1024
        except Exception:
            return False
            
    def _cleanup_temp_files(self):
        """Clean up temporary files"""
        try:
            import tempfile
            import shutil
            temp_dir = tempfile.gettempdir()
            # Clean up old temp files (simplified)
            logger.info("Cleaned up temporary files")
        except Exception as e:
            logger.error(f"Failed to cleanup temp files: {e}")
            
    def _create_missing_directories(self, context: Dict) -> bool:
        """Create missing directories"""
        try:
            if "path" in context:
                import os
                os.makedirs(context["path"], exist_ok=True)
                return True
        except Exception:
            pass
        return False
        
    def _refresh_auth_tokens(self) -> bool:
        """Refresh authentication tokens"""
        # Implementation would depend on your auth system
        return False
        
    def _clear_invalid_sessions(self) -> bool:
        """Clear invalid sessions"""
        # Implementation would depend on your session management
        return False
        
    def _sanitize_input(self, input_data: Any) -> Any:
        """Sanitize input data"""
        # Basic sanitization - would be more sophisticated in production
        if isinstance(input_data, str):
            return input_data.strip()
        return input_data
        
    def get_error_statistics(self, hours: int = 24) -> Dict:
        """Get error statistics for the specified time period"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        since_time = datetime.now() - timedelta(hours=hours)
        
        cursor.execute('''
            SELECT error_type, severity, COUNT(*) as count,
                   SUM(CASE WHEN resolved THEN 1 ELSE 0 END) as resolved_count
            FROM error_records 
            WHERE timestamp > ?
            GROUP BY error_type, severity
        ''', (since_time,))
        
        results = cursor.fetchall()
        conn.close()
        
        stats = {
            'total_errors': 0,
            'resolved_errors': 0,
            'by_type': {},
            'by_severity': {}
        }
        
        for error_type, severity, count, resolved_count in results:
            stats['total_errors'] += count
            stats['resolved_errors'] += resolved_count
            
            if error_type not in stats['by_type']:
                stats['by_type'][error_type] = {'count': 0, 'resolved': 0}
            stats['by_type'][error_type]['count'] += count
            stats['by_type'][error_type]['resolved'] += resolved_count
            
            if severity not in stats['by_severity']:
                stats['by_severity'][severity] = {'count': 0, 'resolved': 0}
            stats['by_severity'][severity]['count'] += count
            stats['by_severity'][severity]['resolved'] += resolved_count
            
        return stats

# Decorators for easy use
def with_retry(max_retries: int = 3, base_delay: float = 1.0):
    """Decorator to add retry logic to functions"""
    return RetryManager(max_retries=max_retries, base_delay=base_delay)

def with_circuit_breaker(failure_threshold: int = 5, recovery_timeout: int = 60):
    """Decorator to add circuit breaker to functions"""
    return CircuitBreaker(failure_threshold=failure_threshold, recovery_timeout=recovery_timeout)

def with_error_recovery(severity: ErrorSeverity = ErrorSeverity.MEDIUM):
    """Decorator to add error recovery to functions"""
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                recovery_manager.handle_error(e, {'function': func.__name__}, severity)
                raise
        return wrapper
    return decorator

@contextmanager
def safe_operation(operation_name: str, severity: ErrorSeverity = ErrorSeverity.MEDIUM):
    """Context manager for safe operations with error recovery"""
    try:
        yield
    except Exception as e:
        recovery_manager.handle_error(e, {'operation': operation_name}, severity)
        raise

# Global error recovery manager
recovery_manager = ErrorRecoveryManager()

```

## File: security_enforcer.py
```python
"""
Security Enforcement Module for Legal Case Management System
Ensures no data is stored or transmitted without proper encryption.
Implements the "encrypt everything" security policy.
"""

import os
import ssl
import logging
from typing import Dict, Any, Optional, Callable
from functools import wraps
from pathlib import Path
from urllib.parse import urlparse
import socket

# Security imports
try:
    from cryptography.fernet import Fernet
    from security.pgp_manager import PGPKeyManager, DocumentEncryption
except ImportError as e:
    logging.error(f"Critical security modules not available: {e}")
    raise

logger = logging.getLogger(__name__)

class SecurityPolicy:
    """Defines and enforces security policies"""
    
    # Security requirements
    REQUIRE_SSL_TLS = True
    REQUIRE_PGP_ENCRYPTION = True
    BLOCK_UNENCRYPTED_STORAGE = True
    BLOCK_UNENCRYPTED_TRANSMISSION = True
    MINIMUM_TLS_VERSION = ssl.TLSVersion.TLSv1_2
    
    @classmethod
    def get_policy_violations(cls) -> Dict[str, bool]:
        """Check for any policy violations in current environment"""
        violations = {}
        
        # Check if SSL context can be created
        try:
            context = ssl.create_default_context()
            context.minimum_version = cls.MINIMUM_TLS_VERSION
            violations['ssl_context'] = False
        except Exception:
            violations['ssl_context'] = True
        
        # Check if PGP components are available
        try:
            import gnupg
            violations['pgp_missing'] = False
        except ImportError:
            violations['pgp_missing'] = True
        
        return violations

class SecureDataHandler:
    """Enforces encryption for all data operations"""
    
    def __init__(self, key_manager: PGPKeyManager, doc_encryption: DocumentEncryption):
        self.key_manager = key_manager
        self.doc_encryption = doc_encryption
        
    def enforce_encrypted_storage(self, func: Callable) -> Callable:
        """Decorator to ensure all storage operations are encrypted"""
        @wraps(func)
        def wrapper(*args, **kwargs):
            # Check if data is being stored
            if 'content' in kwargs or (args and isinstance(args[0], (str, bytes))):
                logger.info("Enforcing PGP encryption for data storage")
                
                # Ensure case_id is available for encryption
                case_id = kwargs.get('case_id') or kwargs.get('case')
                if not case_id:
                    raise SecurityError("Cannot store data without case_id for encryption")
                
                # If raw content is being stored, encrypt it first
                if 'content' in kwargs and not kwargs.get('encrypted', False):
                    content = kwargs['content']
                    if isinstance(content, str):
                        content = content.encode('utf-8')
                    
                    document_id = kwargs.get('document_id', f"doc_{hash(content)}")
                    encrypted_data = self.doc_encryption.encrypt_document(
                        content, str(case_id), document_id
                    )
                    kwargs['content'] = encrypted_data
                    kwargs['encrypted'] = True
            
            return func(*args, **kwargs)
        return wrapper
    
    def enforce_secure_transmission(self, func: Callable) -> Callable:
        """Decorator to ensure all network transmissions use SSL/TLS"""
        @wraps(func)
        def wrapper(*args, **kwargs):
            # Check for URL parameters
            url = kwargs.get('url') or (args and isinstance(args[0], str) and '://' in args[0])
            
            if url:
                parsed_url = urlparse(str(url))
                if parsed_url.scheme not in ['https', 'ftps', 'sftp']:
                    raise SecurityError(f"Unencrypted transmission blocked: {parsed_url.scheme}://")
                
                logger.info(f"Secure transmission validated: {parsed_url.scheme}")
            
            # Ensure SSL context is used
            if 'ssl_context' not in kwargs and url:
                context = ssl.create_default_context()
                context.minimum_version = SecurityPolicy.MINIMUM_TLS_VERSION
                kwargs['ssl_context'] = context
            
            return func(*args, **kwargs)
        return wrapper

class SecurityEnforcer:
    """Main security enforcement class"""
    
    def __init__(self, key_manager: PGPKeyManager, doc_encryption: DocumentEncryption):
        self.data_handler = SecureDataHandler(key_manager, doc_encryption)
        self.violations = []
    
    def validate_startup_security(self) -> Dict[str, Any]:
        """Validate security requirements at application startup"""
        logger.info("🔒 Validating security requirements...")
        
        validation_results = {
            'ssl_tls_available': False,
            'pgp_available': False,
            'vault_available': False,
            'certificates_valid': False,
            'security_violations': [],
            'startup_allowed': False
        }
        
        # Check SSL/TLS availability
        try:
            context = ssl.create_default_context()
            context.minimum_version = SecurityPolicy.MINIMUM_TLS_VERSION
            validation_results['ssl_tls_available'] = True
            logger.info("✅ SSL/TLS support validated")
        except Exception as e:
            validation_results['security_violations'].append(f"SSL/TLS not available: {e}")
            logger.error(f"❌ SSL/TLS validation failed: {e}")
        
        # Check PGP availability
        try:
            import gnupg
            if self.data_handler.key_manager.gpg:
                validation_results['pgp_available'] = True
                logger.info("✅ PGP encryption validated")
        except Exception as e:
            validation_results['security_violations'].append(f"PGP not available: {e}")
            logger.error(f"❌ PGP validation failed: {e}")
        
        # Check Vault availability
        try:
            import hvac
            validation_results['vault_available'] = True
            logger.info("✅ Vault integration available")
        except ImportError:
            logger.warning("⚠️  Vault integration not available (optional)")
        
        # Check certificates
        cert_dir = Path("/etc/ssl/legal-cms/certs")
        if cert_dir.exists() and any(cert_dir.glob("*.crt")):
            validation_results['certificates_valid'] = True
            logger.info("✅ SSL certificates found")
        else:
            validation_results['security_violations'].append("SSL certificates not found")
            logger.warning("⚠️  SSL certificates not found - run deployment script")
        
        # Determine if startup is allowed
        critical_requirements = [
            validation_results['ssl_tls_available'],
            validation_results['pgp_available']
        ]
        
        validation_results['startup_allowed'] = all(critical_requirements)
        
        if validation_results['startup_allowed']:
            logger.info("🔒 Security validation PASSED - startup allowed")
        else:
            logger.error("🚫 Security validation FAILED - startup blocked")
            logger.error("Violations: " + ", ".join(validation_results['security_violations']))
        
        return validation_results
    
    def block_insecure_operations(self):
        """Block operations that don't meet security requirements"""
        if SecurityPolicy.BLOCK_UNENCRYPTED_STORAGE:
            logger.info("🔒 Blocking unencrypted storage operations")
        
        if SecurityPolicy.BLOCK_UNENCRYPTED_TRANSMISSION:
            logger.info("🔒 Blocking unencrypted network transmissions")
    
    def get_secure_decorators(self) -> Dict[str, Callable]:
        """Get security decorators for protecting functions"""
        return {
            'encrypted_storage': self.data_handler.enforce_encrypted_storage,
            'secure_transmission': self.data_handler.enforce_secure_transmission
        }

class SecurityError(Exception):
    """Raised when security policies are violated"""
    pass

# Factory function for easy initialization
def create_security_enforcer(key_manager: PGPKeyManager, doc_encryption: DocumentEncryption) -> SecurityEnforcer:
    """Create security enforcer instance"""
    return SecurityEnforcer(key_manager, doc_encryption)

# Startup security check function
def perform_startup_security_check() -> bool:
    """Perform security check and return whether startup should proceed"""
    try:
        from security.pgp_manager import create_pgp_manager
        
        # Create minimal PGP manager for security check
        key_manager, doc_encryption = create_pgp_manager()
        enforcer = create_security_enforcer(key_manager, doc_encryption)
        
        # Validate security requirements
        validation_results = enforcer.validate_startup_security()
        
        if not validation_results['startup_allowed']:
            print("\n🚫 SECURITY VALIDATION FAILED")
            print("The application cannot start due to missing security requirements:")
            for violation in validation_results['security_violations']:
                print(f"  ❌ {violation}")
            print("\nPlease run the deployment script to set up required security components.")
            return False
        
        print("🔒 Security validation passed - startup allowed")
        return True
        
    except Exception as e:
        print(f"🚫 Security check failed: {e}")
        return False
```

## File: docket_viewer.py
```python
"""
Docket Viewer Component - Displays docket in court-style table format
Matches the exact appearance of Cuyahoga County E-Filing system
"""

import streamlit as st
import pandas as pd
from typing import List, Dict, Optional

def render_court_style_docket(docket_data: Dict, case_number: str = "DR-25-403973") -> None:
    """
    Render docket in the exact style of the court system table
    
    Args:
        docket_data: Docket data from scraper
        case_number: Case number for display
    """
    
    st.subheader(f"📋 **Case Docket: {case_number}**")
    
    if not docket_data or 'entries' not in docket_data:
        st.warning("No docket data available")
        return
        
    entries = docket_data['entries']
    if not entries:
        st.warning("No docket entries found")
        return
    
    # Display summary stats
    col1, col2, col3, col4 = st.columns(4)
    with col1:
        st.metric("Total Entries", len(entries))
    with col2:
        entries_with_images = sum(1 for e in entries if e.get('Has_Image'))
        st.metric("Documents with Images", entries_with_images)
    with col3:
        stricken_entries = sum(1 for e in entries if e.get('Stricken_Status'))
        st.metric("⚠️ Stricken Records", stricken_entries, delta_color="inverse")
    with col4:
        latest_entry = entries[0] if entries else None
        latest_date = latest_entry.get('Filing Date', 'Unknown') if latest_entry else 'Unknown'
        st.metric("Latest Filing", latest_date)
    
    # Show alert for stricken records
    stricken_records = [e for e in entries if e.get('Stricken_Status')]
    if stricken_records:
        st.error(f"⚠️ **ALERT: {len(stricken_records)} records have been STRICKEN from this case!**")
        st.write("**These records contained evidence that was removed from the case docket.** Click below to investigate:")
        
        if st.expander("🔍 **INVESTIGATE STRICKEN RECORDS** (Click to expand)", expanded=False):
            st.warning("**CRITICAL: Evidence has been removed from your case!**")
            for record in stricken_records:
                with st.container():
                    st.markdown(f"**🚨 STRICKEN RECORD - {record.get('Filing Date', 'Unknown Date')}**")
                    st.write(f"**Description:** {record.get('Description', 'N/A')}")
                    if record.get('Original_Docket_ID'):
                        st.write(f"**Original Docket ID:** {record['Original_Docket_ID']}")
                    st.write(f"**Issue Type:** {record.get('Issue_Type', 'Evidence Manipulation')}")
                    st.divider()
            
            st.info("💡 **Recommendation:** Contact your attorney immediately. Stricken records may indicate procedural violations or evidence tampering.")
    
    st.divider()
    
    # Render court-style table
    render_docket_table_html(entries)
    
    # Show downloadable data
    if st.checkbox("📥 **Show Downloadable Data**"):
        df = pd.DataFrame(entries)
        st.dataframe(df, use_container_width=True)
        
        # CSV download
        csv = df.to_csv(index=False)
        st.download_button(
            label="📄 Download Docket as CSV",
            data=csv,
            file_name=f"docket_{case_number.replace('-', '_')}.csv",
            mime="text/csv"
        )

def render_docket_table_html(entries: List[Dict]) -> None:
    """
    Render HTML table that matches the court system exactly
    """
    
    # Create HTML table with exact court styling
    html_table = """
    <style>
    .court-docket-table {
        border-collapse: collapse;
        width: 100%;
        margin: 20px 0;
        font-family: Arial, sans-serif;
        font-size: 11px;
    }
    
    .court-docket-table th {
        background-color: #f0f0f0;
        border: 1px solid #ccc;
        padding: 8px;
        text-align: left;
        font-weight: bold;
        font-size: 12px;
    }
    
    .court-docket-table td {
        border: 1px solid #ccc;
        padding: 8px;
        text-align: left;
        vertical-align: top;
        line-height: 1.4;
    }
    
    .court-docket-table .row-lightgrey {
        background-color: #D3D3D3;
    }
    
    .court-docket-table .row-gainsboro {
        background-color: #DCDCDC;
    }
    
    .court-docket-table .date-col {
        width: 75px;
        white-space: nowrap;
    }
    
    .court-docket-table .side-col {
        width: 40px;
        text-align: center;
    }
    
    .court-docket-table .type-col {
        width: 40px;
        text-align: center;
    }
    
    .court-docket-table .desc-col {
        max-width: 400px;
        word-wrap: break-word;
    }
    
    .court-docket-table .image-col {
        width: 60px;
        text-align: center;
    }
    
    .image-button {
        background: none;
        border: none;
        cursor: pointer;
    }
    
    .image-button img {
        width: 16px;
        height: 16px;
    }
    
    .has-image {
        color: #0066cc;
        font-weight: bold;
    }
    
    .priority-1 {
        background-color: #fffacd !important;
        border-left: 4px solid #ffd700;
    }
    
    .stricken-record {
        background-color: #ffebee !important;
        border-left: 4px solid #f44336 !important;
        border-right: 4px solid #f44336 !important;
    }
    
    .stricken-record td {
        color: #b71c1c !important;
        font-weight: bold;
    }
    
    .evidence-alert {
        background-color: #fff3e0 !important;
        border: 2px solid #ff9800 !important;
    }
    </style>
    
    <table class="court-docket-table">
        <thead>
            <tr>
                <th class="date-col">Filing Date</th>
                <th class="side-col">Side</th>
                <th class="type-col">Type</th>
                <th class="desc-col">Description</th>
                <th class="image-col">Image</th>
            </tr>
        </thead>
        <tbody>
    """
    
    # Add rows alternating colors like court system
    for i, entry in enumerate(entries):
        # Determine row style
        row_class = "row-lightgrey" if i % 2 == 0 else "row-gainsboro"
        
        # Special highlighting for important records
        if entry.get('Priority') == 1:
            row_class += " priority-1"
        elif entry.get('Stricken_Status'):
            row_class = "stricken-record"  # Override normal coloring for stricken records
        
        # Extract data with proper formatting
        filing_date = entry.get('Filing Date', 'Unknown')
        side = entry.get('Side', 'N/A')  
        doc_type = entry.get('Type', 'N/A')
        description = entry.get('Description', entry.get('Docket Description', 'No description'))
        has_image = entry.get('Has_Image', False)
        
        # Format description - handle line breaks
        formatted_desc = str(description).replace('<br>', '<br/>').replace('\n', '<br/>')
        
        # Truncate very long descriptions
        if len(formatted_desc) > 500:
            formatted_desc = formatted_desc[:500] + "..."
        
        # Image column content
        image_cell = ""
        if has_image:
            image_cell = '<span class="has-image">📄 VIEW</span>'
        
        # Add the row
        html_table += f"""
            <tr class="{row_class}">
                <td class="date-col">{filing_date}</td>
                <td class="side-col">{side}</td>
                <td class="type-col">{doc_type}</td>
                <td class="desc-col">{formatted_desc}</td>
                <td class="image-col">{image_cell}</td>
            </tr>
        """
    
    html_table += """
        </tbody>
    </table>
    """
    
    # Display the HTML table
    st.markdown(html_table, unsafe_allow_html=True)
    
    # Add legend
    st.markdown("""
    **Legend:**
    - 🏅 **Gold highlight**: Complaint for Divorce (Document #1)
    - 🚨 **Red highlight**: STRICKEN RECORDS - Evidence removed from case
    - 📄 **VIEW**: Document has images/PDF available
    - **Side codes**: P1 = Plaintiff, D1 = Defendant, N/A = Court/System
    - **Type codes**: CP = Complaint, JE = Judgment Entry, MO = Motion, etc.
    
    ⚠️ **IMPORTANT**: Stricken records indicate evidence that was removed from your case. These require immediate legal review.
    """)

def get_document_type_description(type_code: str) -> str:
    """
    Convert court type codes to full descriptions
    """
    type_mappings = {
        'CP': 'Complaint',
        'JE': 'Judgment Entry', 
        'MO': 'Motion',
        'AN': 'Answer',
        'OB': 'Objection',
        'NT': 'Notice',
        'SR': 'Service Record',
        'SC': 'Scheduling',
        'AF': 'Affidavit',
        'CL': 'Clerical Entry',
        'SF': 'System Filing',
        'AP': 'Application',
        'EV': 'Evidence/Waiver'
    }
    
    return type_mappings.get(type_code.upper(), type_code)

def get_side_description(side_code: str) -> str:
    """
    Convert side codes to full descriptions
    """
    side_mappings = {
        'P1': 'Plaintiff',
        'D1': 'Defendant', 
        'N/A': 'Court/System',
        'CT': 'Court',
        'SYS': 'System'
    }
    
    return side_mappings.get(side_code.upper(), side_code)
```

## File: database_managers.py
```python
"""
Multi-Database Connection Managers for Legal Case Management System
Handles PostgreSQL, MongoDB, and Redis connections with security and encryption.
"""

import os
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Union
from contextlib import contextmanager
import ssl

# PostgreSQL imports
try:
    import psycopg2
    from psycopg2.extras import RealDictCursor
    from psycopg2.pool import ThreadedConnectionPool
    POSTGRESQL_AVAILABLE = True
except ImportError:
    POSTGRESQL_AVAILABLE = False
    logging.warning("psycopg2 not available. Install with: pip install psycopg2-binary")

# MongoDB imports
try:
    import pymongo
    from pymongo import MongoClient
    from pymongo.errors import ConnectionFailure, OperationFailure
    MONGODB_AVAILABLE = True
except ImportError:
    MONGODB_AVAILABLE = False
    logging.warning("pymongo not available. Install with: pip install pymongo")

# Redis imports
try:
    import redis
    from redis.sentinel import Sentinel
    REDIS_AVAILABLE = True
except ImportError:
    REDIS_AVAILABLE = False
    logging.warning("redis not available. Install with: pip install redis")

logger = logging.getLogger(__name__)

class PostgreSQLManager:
    """PostgreSQL connection manager with SSL and connection pooling"""
    
    def __init__(self, config: Dict):
        if not POSTGRESQL_AVAILABLE:
            raise RuntimeError("PostgreSQL support not available")
        
        self.config = config
        self.pool = None
        self._init_connection_pool()
    
    def _init_connection_pool(self):
        """Initialize PostgreSQL connection pool"""
        try:
            connection_params = {
                'host': self.config.get('host', 'localhost'),
                'port': self.config.get('port', 5432),
                'database': self.config.get('database', 'legal_cms'),
                'user': self.config.get('user', 'legal_cms_user'),
                'password': self.config.get('password', ''),
                'sslmode': self.config.get('sslmode', 'require'),
                'sslcert': self.config.get('sslcert'),
                'sslkey': self.config.get('sslkey'),
                'sslrootcert': self.config.get('sslrootcert'),
                'connect_timeout': 10,
                'application_name': 'legal_cms'
            }
            
            # Remove None values
            connection_params = {k: v for k, v in connection_params.items() if v is not None}
            
            self.pool = ThreadedConnectionPool(
                minconn=2,
                maxconn=20,
                **connection_params
            )
            
            logger.info("PostgreSQL connection pool initialized")
            
        except Exception as e:
            logger.error(f"Failed to initialize PostgreSQL pool: {e}")
            raise
    
    @contextmanager
    def get_connection(self):
        """Get connection from pool with automatic cleanup"""
        conn = None
        try:
            conn = self.pool.getconn()
            yield conn
        except Exception as e:
            if conn:
                conn.rollback()
            logger.error(f"PostgreSQL operation failed: {e}")
            raise
        finally:
            if conn:
                self.pool.putconn(conn)
    
    def execute_query(self, query: str, params: tuple = None, fetch: bool = False) -> Optional[List[Dict]]:
        """Execute query with parameters"""
        with self.get_connection() as conn:
            with conn.cursor(cursor_factory=RealDictCursor) as cursor:
                cursor.execute(query, params)
                
                if fetch:
                    return [dict(row) for row in cursor.fetchall()]
                else:
                    conn.commit()
                    return cursor.rowcount
    
    def init_schema(self):
        """Initialize PostgreSQL schema"""
        schema_sql = """
        -- Users table
        CREATE TABLE IF NOT EXISTS users (
            id SERIAL PRIMARY KEY,
            username VARCHAR(50) UNIQUE NOT NULL,
            email VARCHAR(255) UNIQUE NOT NULL,
            password_hash TEXT NOT NULL,
            salt TEXT NOT NULL,
            role VARCHAR(20) DEFAULT 'user',
            is_active BOOLEAN DEFAULT true,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            last_login TIMESTAMP,
            failed_attempts INTEGER DEFAULT 0,
            locked_until TIMESTAMP,
            mfa_enabled BOOLEAN DEFAULT false,
            mfa_secret TEXT
        );
        
        -- Cases table
        CREATE TABLE IF NOT EXISTS cases (
            id SERIAL PRIMARY KEY,
            name VARCHAR(255) NOT NULL,
            case_number VARCHAR(100) UNIQUE,
            description TEXT,
            status VARCHAR(50) DEFAULT 'active',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            created_by INTEGER REFERENCES users(id),
            client_name VARCHAR(255),
            opposing_party VARCHAR(255),
            court_name VARCHAR(255),
            case_type VARCHAR(100),
            priority VARCHAR(20) DEFAULT 'medium',
            encrypted_metadata JSONB
        );
        
        -- Sessions table
        CREATE TABLE IF NOT EXISTS sessions (
            id SERIAL PRIMARY KEY,
            user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
            session_token VARCHAR(255) UNIQUE NOT NULL,
            expires_at TIMESTAMP NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            ip_address INET,
            user_agent TEXT,
            is_active BOOLEAN DEFAULT true
        );
        
        -- Audit logs table
        CREATE TABLE IF NOT EXISTS audit_logs (
            id SERIAL PRIMARY KEY,
            user_id INTEGER REFERENCES users(id),
            action VARCHAR(100) NOT NULL,
            resource_type VARCHAR(50),
            resource_id VARCHAR(100),
            details JSONB,
            ip_address INET,
            user_agent TEXT,
            timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            case_id INTEGER REFERENCES cases(id)
        );
        
        -- Permissions table
        CREATE TABLE IF NOT EXISTS case_permissions (
            id SERIAL PRIMARY KEY,
            user_id INTEGER REFERENCES users(id),
            case_id INTEGER REFERENCES cases(id),
            permission_level VARCHAR(20) DEFAULT 'read',
            granted_by INTEGER REFERENCES users(id),
            granted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            expires_at TIMESTAMP,
            UNIQUE(user_id, case_id)
        );
        
        -- System configuration table
        CREATE TABLE IF NOT EXISTS system_config (
            id SERIAL PRIMARY KEY,
            config_key VARCHAR(100) UNIQUE NOT NULL,
            config_value JSONB,
            encrypted BOOLEAN DEFAULT false,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
        
        -- Indexes for performance
        CREATE INDEX IF NOT EXISTS idx_sessions_token ON sessions(session_token);
        CREATE INDEX IF NOT EXISTS idx_sessions_user_id ON sessions(user_id);
        CREATE INDEX IF NOT EXISTS idx_audit_logs_user_id ON audit_logs(user_id);
        CREATE INDEX IF NOT EXISTS idx_audit_logs_timestamp ON audit_logs(timestamp);
        CREATE INDEX IF NOT EXISTS idx_cases_created_by ON cases(created_by);
        CREATE INDEX IF NOT EXISTS idx_case_permissions_user_case ON case_permissions(user_id, case_id);
        """
        
        try:
            with self.get_connection() as conn:
                with conn.cursor() as cursor:
                    cursor.execute(schema_sql)
                    conn.commit()
            logger.info("PostgreSQL schema initialized")
        except Exception as e:
            logger.error(f"Failed to initialize PostgreSQL schema: {e}")
            raise

class MongoDBManager:
    """MongoDB connection manager with replica set and encryption"""
    
    def __init__(self, config: Dict):
        if not MONGODB_AVAILABLE:
            raise RuntimeError("MongoDB support not available")
        
        self.config = config
        self.client = None
        self.db = None
        self._init_connection()
    
    def _init_connection(self):
        """Initialize MongoDB connection"""
        try:
            # Build connection string
            username = self.config.get('username')
            password = self.config.get('password')
            hosts = self.config.get('hosts', ['localhost:27017'])
            database = self.config.get('database', 'legal_cms')
            replica_set = self.config.get('replica_set')
            
            if isinstance(hosts, str):
                hosts = [hosts]
            
            host_string = ','.join(hosts)
            
            if username and password:
                connection_string = f"mongodb://{username}:{password}@{host_string}/{database}"
            else:
                connection_string = f"mongodb://{host_string}/{database}"
            
            # Connection options
            options = {
                'serverSelectionTimeoutMS': 5000,
                'connectTimeoutMS': 10000,
                'socketTimeoutMS': 20000,
                'maxPoolSize': 50,
                'minPoolSize': 5,
                'maxIdleTimeMS': 30000,
                'waitQueueTimeoutMS': 5000,
                'w': 'majority',
                'readPreference': 'primaryPreferred'
            }
            
            if replica_set:
                options['replicaSet'] = replica_set
            
            if self.config.get('ssl', True):
                options['ssl'] = True
                options['ssl_cert_reqs'] = ssl.CERT_REQUIRED
                
                if self.config.get('ssl_ca_certs'):
                    options['ssl_ca_certs'] = self.config['ssl_ca_certs']
            
            self.client = MongoClient(connection_string, **options)
            self.db = self.client[database]
            
            # Test connection
            self.client.admin.command('ping')
            logger.info(f"Connected to MongoDB: {database}")
            
        except Exception as e:
            logger.error(f"Failed to connect to MongoDB: {e}")
            raise
    
    def get_collection(self, collection_name: str):
        """Get MongoDB collection"""
        return self.db[collection_name]
    
    def init_collections(self):
        """Initialize MongoDB collections with indexes"""
        try:
            # Documents collection
            documents = self.get_collection('documents')
            documents.create_index([('case_id', 1), ('filename', 1)])
            documents.create_index([('pgp_fingerprint', 1)])
            documents.create_index([('created_at', -1)])
            
            # Chat history collection
            chat_history = self.get_collection('chat_history')
            chat_history.create_index([('case_id', 1), ('created_at', -1)])
            chat_history.create_index([('session_id', 1)])
            
            # Document vectors collection
            document_vectors = self.get_collection('document_vectors')
            document_vectors.create_index([('document_id', 1)])
            document_vectors.create_index([('vector_collection', 1)])
            
            # Case analytics collection
            case_analytics = self.get_collection('case_analytics')
            case_analytics.create_index([('case_id', 1), ('generated_at', -1)])
            
            logger.info("MongoDB collections and indexes initialized")
            
        except Exception as e:
            logger.error(f"Failed to initialize MongoDB collections: {e}")
            raise

class RedisManager:
    """Redis connection manager with clustering and SSL support"""
    
    def __init__(self, config: Dict):
        if not REDIS_AVAILABLE:
            raise RuntimeError("Redis support not available")
        
        self.config = config
        self.client = None
        self._init_connection()
    
    def _init_connection(self):
        """Initialize Redis connection"""
        try:
            connection_params = {
                'host': self.config.get('host', 'localhost'),
                'port': self.config.get('port', 6379),
                'password': self.config.get('password'),
                'db': self.config.get('db', 0),
                'decode_responses': True,
                'socket_timeout': 5,
                'socket_connect_timeout': 5,
                'retry_on_timeout': True,
                'health_check_interval': 30
            }
            
            # SSL configuration
            if self.config.get('ssl', False):
                connection_params.update({
                    'ssl': True,
                    'ssl_cert_reqs': ssl.CERT_REQUIRED,
                    'ssl_ca_certs': self.config.get('ssl_ca_certs'),
                    'ssl_certfile': self.config.get('ssl_certfile'),
                    'ssl_keyfile': self.config.get('ssl_keyfile')
                })
            
            # Sentinel configuration for HA
            sentinels = self.config.get('sentinels')
            if sentinels:
                sentinel = Sentinel(sentinels)
                self.client = sentinel.master_for(
                    self.config.get('service_name', 'mymaster'),
                    **connection_params
                )
            else:
                self.client = redis.Redis(**connection_params)
            
            # Test connection
            self.client.ping()
            logger.info("Connected to Redis successfully")
            
        except Exception as e:
            logger.error(f"Failed to connect to Redis: {e}")
            raise
    
    def set_with_expiry(self, key: str, value: Any, expiry_seconds: int = 3600):
        """Set key with expiration"""
        if isinstance(value, (dict, list)):
            value = json.dumps(value)
        return self.client.setex(key, expiry_seconds, value)
    
    def get_json(self, key: str) -> Optional[Dict]:
        """Get JSON value from Redis"""
        value = self.client.get(key)
        if value:
            try:
                return json.loads(value)
            except json.JSONDecodeError:
                return value
        return None
    
    def delete_pattern(self, pattern: str) -> int:
        """Delete keys matching pattern"""
        keys = self.client.keys(pattern)
        if keys:
            return self.client.delete(*keys)
        return 0

class DatabaseManagerFactory:
    """Factory for creating database managers"""
    
    @staticmethod
    def create_managers(config: Dict) -> Dict:
        """Create all database managers from configuration"""
        managers = {}
        
        # PostgreSQL
        if config.get('postgresql') and POSTGRESQL_AVAILABLE:
            try:
                managers['postgresql'] = PostgreSQLManager(config['postgresql'])
                managers['postgresql'].init_schema()
            except Exception as e:
                logger.error(f"Failed to initialize PostgreSQL: {e}")
        
        # MongoDB
        if config.get('mongodb') and MONGODB_AVAILABLE:
            try:
                managers['mongodb'] = MongoDBManager(config['mongodb'])
                managers['mongodb'].init_collections()
            except Exception as e:
                logger.error(f"Failed to initialize MongoDB: {e}")
        
        # Redis
        if config.get('redis') and REDIS_AVAILABLE:
            try:
                managers['redis'] = RedisManager(config['redis'])
            except Exception as e:
                logger.error(f"Failed to initialize Redis: {e}")
        
        return managers

# Configuration loader
def load_database_config(config_path: str = "config/database.json") -> Dict:
    """Load database configuration from file with environment variable substitution"""
    try:
        with open(config_path, 'r') as f:
            config_text = f.read()
        
        # Substitute environment variables
        import re
        def replace_env_var(match):
            var_name = match.group(1)
            return os.environ.get(var_name, match.group(0))
        
        config_text = re.sub(r'\$\{([^}]+)\}', replace_env_var, config_text)
        
        return json.loads(config_text)
    except FileNotFoundError:
        logger.warning(f"Database config file not found: {config_path}")
        return {}
    except json.JSONDecodeError as e:
        logger.error(f"Invalid JSON in config file: {e}")
        return {}

```

## File: chat_manager.py
```python
"""
Chat Management Module for Legal Case Management System
Handles chat storage, retrieval, and analysis using MongoDB with PGP encryption.
"""

import os
import json
import logging
from datetime import datetime, timezone
from typing import Dict, List, Optional, Any
from uuid import uuid4
from dataclasses import dataclass, asdict

# MongoDB imports
try:
    import pymongo
    from pymongo import MongoClient
    from pymongo.errors import ConnectionFailure, OperationFailure
    MONGODB_AVAILABLE = True
except ImportError:
    MONGODB_AVAILABLE = False
    logging.warning("pymongo not available for chat storage")

# Import our security and database components
from core.database_managers import DatabaseManagerFactory
from security.pgp_manager import DocumentEncryption, PGPKeyManager

logger = logging.getLogger(__name__)

@dataclass
class ChatMessage:
    """Represents a single chat message"""
    message_id: str
    session_id: str
    case_id: str
    timestamp: datetime
    role: str  # 'user', 'assistant', 'system'
    content: str
    message_type: str  # 'text', 'code', 'file_reference', 'error'
    metadata: Dict[str, Any]
    encrypted: bool = False
    content_hash: str = ""

@dataclass
class ChatSession:
    """Represents a chat session"""
    session_id: str
    case_id: str
    created_at: datetime
    updated_at: datetime
    title: str
    description: str
    message_count: int
    metadata: Dict[str, Any]
    tags: List[str]

class ChatManager:
    """Manages chat conversations with MongoDB storage and PGP encryption"""
    
    def __init__(self, mongodb_manager=None, key_manager: PGPKeyManager = None, doc_encryption: DocumentEncryption = None):
        self.mongodb = mongodb_manager
        self.key_manager = key_manager
        self.doc_encryption = doc_encryption
        
        # Initialize collections if MongoDB is available
        if self.mongodb:
            self.init_collections()
    
    def init_collections(self):
        """Initialize MongoDB collections for chat storage"""
        try:
            db = self.mongodb.get_database()
            
            # Chat sessions collection
            if 'chat_sessions' not in db.list_collection_names():
                sessions_collection = db.create_collection('chat_sessions')
                # Create indexes
                sessions_collection.create_index([("session_id", pymongo.ASCENDING)], unique=True)
                sessions_collection.create_index([("case_id", pymongo.ASCENDING)])
                sessions_collection.create_index([("created_at", pymongo.DESCENDING)])
                logger.info("Created chat_sessions collection with indexes")
            
            # Chat messages collection
            if 'chat_messages' not in db.list_collection_names():
                messages_collection = db.create_collection('chat_messages')
                # Create indexes
                messages_collection.create_index([("message_id", pymongo.ASCENDING)], unique=True)
                messages_collection.create_index([("session_id", pymongo.ASCENDING)])
                messages_collection.create_index([("case_id", pymongo.ASCENDING)])
                messages_collection.create_index([("timestamp", pymongo.DESCENDING)])
                messages_collection.create_index([("role", pymongo.ASCENDING)])
                logger.info("Created chat_messages collection with indexes")
            
            logger.info("Chat collections initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize chat collections: {e}")
    
    def create_session(self, case_id: str, title: str = "", description: str = "", metadata: Dict = None) -> str:
        """Create a new chat session"""
        session_id = str(uuid4())
        
        session = ChatSession(
            session_id=session_id,
            case_id=case_id,
            created_at=datetime.now(timezone.utc),
            updated_at=datetime.now(timezone.utc),
            title=title or f"Chat Session {datetime.now().strftime('%Y-%m-%d %H:%M')}",
            description=description,
            message_count=0,
            metadata=metadata or {},
            tags=[]
        )
        
        if self.mongodb:
            try:
                db = self.mongodb.get_database()
                sessions_collection = db.chat_sessions
                sessions_collection.insert_one(asdict(session))
                logger.info(f"Created chat session {session_id} for case {case_id}")
            except Exception as e:
                logger.error(f"Failed to store chat session: {e}")
        
        return session_id
    
    def store_message(self, session_id: str, case_id: str, role: str, content: str, 
                     message_type: str = "text", metadata: Dict = None) -> str:
        """Store a chat message with PGP encryption"""
        message_id = str(uuid4())
        
        # Encrypt content if encryption is available and case_id is provided
        encrypted_content = content
        content_hash = ""
        encrypted = False
        
        if self.doc_encryption and case_id:
            try:
                # Convert content to bytes for encryption
                content_bytes = content.encode('utf-8')
                encrypted_data = self.doc_encryption.encrypt_document(
                    content_bytes, case_id, f"chat_msg_{message_id}"
                )
                encrypted_content = json.dumps(encrypted_data)  # Store as JSON string
                content_hash = encrypted_data['content_hash']
                encrypted = True
                logger.info(f"Message {message_id} encrypted for case {case_id}")
            except Exception as e:
                logger.warning(f"Failed to encrypt message: {e}")
                # Fall back to unencrypted storage
                encrypted_content = content
        
        message = ChatMessage(
            message_id=message_id,
            session_id=session_id,
            case_id=case_id,
            timestamp=datetime.now(timezone.utc),
            role=role,
            content=encrypted_content,
            message_type=message_type,
            metadata=metadata or {},
            encrypted=encrypted,
            content_hash=content_hash
        )
        
        if self.mongodb:
            try:
                db = self.mongodb.get_database()
                messages_collection = db.chat_messages
                messages_collection.insert_one(asdict(message))
                
                # Update session message count
                sessions_collection = db.chat_sessions
                sessions_collection.update_one(
                    {"session_id": session_id},
                    {
                        "$inc": {"message_count": 1},
                        "$set": {"updated_at": datetime.now(timezone.utc)}
                    }
                )
                
                logger.info(f"Stored {'encrypted' if encrypted else 'unencrypted'} message {message_id}")
            except Exception as e:
                logger.error(f"Failed to store chat message: {e}")
        
        return message_id
    
    def get_session_messages(self, session_id: str, decrypt: bool = True, passphrase: str = "") -> List[Dict]:
        """Retrieve all messages from a chat session"""
        if not self.mongodb:
            return []
        
        try:
            db = self.mongodb.get_database()
            messages_collection = db.chat_messages
            
            cursor = messages_collection.find(
                {"session_id": session_id}
            ).sort("timestamp", pymongo.ASCENDING)
            
            messages = []
            for msg_doc in cursor:
                # Convert ObjectId to string for JSON serialization
                msg_doc['_id'] = str(msg_doc['_id'])
                
                # Decrypt content if needed
                if decrypt and msg_doc.get('encrypted', False) and self.doc_encryption:
                    try:
                        encrypted_data = json.loads(msg_doc['content'])
                        decrypted_content = self.doc_encryption.decrypt_document(encrypted_data, passphrase)
                        msg_doc['content'] = decrypted_content.decode('utf-8')
                        msg_doc['decrypted'] = True
                    except Exception as e:
                        logger.warning(f"Failed to decrypt message {msg_doc['message_id']}: {e}")
                        msg_doc['decrypted'] = False
                
                messages.append(msg_doc)
            
            logger.info(f"Retrieved {len(messages)} messages from session {session_id}")
            return messages
            
        except Exception as e:
            logger.error(f"Failed to retrieve session messages: {e}")
            return []
    
    def store_conversation_snapshot(self, conversation_data: List[Dict], case_id: str = "system", 
                                  title: str = "System Conversation") -> str:
        """Store a complete conversation snapshot (like this Claude Code conversation)"""
        session_id = self.create_session(
            case_id=case_id,
            title=title,
            description="Automatically captured conversation snapshot",
            metadata={
                "source": "claude_code",
                "auto_captured": True,
                "message_count": len(conversation_data)
            }
        )
        
        for i, msg_data in enumerate(conversation_data):
            role = msg_data.get('role', 'unknown')
            content = msg_data.get('content', '')
            message_type = msg_data.get('type', 'text')
            
            # Add sequence metadata
            metadata = {
                "sequence": i,
                "original_timestamp": msg_data.get('timestamp', datetime.now().isoformat()),
                **msg_data.get('metadata', {})
            }
            
            self.store_message(
                session_id=session_id,
                case_id=case_id,
                role=role,
                content=content,
                message_type=message_type,
                metadata=metadata
            )
        
        logger.info(f"Stored conversation snapshot with {len(conversation_data)} messages")
        return session_id

def create_chat_manager() -> Optional[ChatManager]:
    """Factory function to create chat manager with database connections"""
    try:
        # Create database managers from config
        import json
        from pathlib import Path
        
        config_path = Path(__file__).parent.parent / "config" / "database.json"
        if config_path.exists():
            with open(config_path, 'r') as f:
                config = json.load(f)
            
            db_managers = DatabaseManagerFactory.create_managers(config)
            mongodb_manager = db_managers.get('mongodb')
        else:
            # Try simple MongoDB connection for development
            mongodb_manager = None
            if MONGODB_AVAILABLE:
                try:
                    from core.database_managers import MongoDBManager
                    mongodb_config = {
                        "hosts": ["localhost:27018"],
                        "database": "legal_cms_documents",
                        "username": "admin",
                        "password": os.getenv("MONGODB_PASSWORD", ""),
                        "ssl": False
                    }
                    mongodb_manager = MongoDBManager(mongodb_config)
                except Exception as e:
                    logger.warning(f"Failed to create MongoDB manager: {e}")
        
        if not mongodb_manager:
            logger.warning("MongoDB not available for chat storage")
            return None
        
        # Get PGP components if available
        key_manager = None
        doc_encryption = None
        
        try:
            from security.pgp_manager import create_pgp_manager
            key_manager, doc_encryption = create_pgp_manager(
                vault_url=os.getenv('VAULT_URL'),
                vault_token=os.getenv('VAULT_TOKEN')
            )
            logger.info("PGP encryption available for chat storage")
        except Exception as e:
            logger.warning(f"PGP not available for chat encryption: {e}")
        
        return ChatManager(mongodb_manager, key_manager, doc_encryption)
        
    except Exception as e:
        logger.error(f"Failed to create chat manager: {e}")
        return None

# Function to store current conversation
def store_current_conversation():
    """Store the current Claude Code conversation in MongoDB"""
    
    # Create the conversation data based on our interaction
    conversation_data = [
        {
            "role": "user",
            "content": "well, i don't want repeated code of something that was already done and needs some tweaks not an entirely new version",
            "type": "text",
            "timestamp": "2025-08-10T19:00:00Z",
            "metadata": {"context": "requesting code cleanup instead of recreation"}
        },
        {
            "role": "assistant", 
            "content": "I understand - you want to modify existing code rather than recreate it. Let me help you identify what needs tweaking in your current Legal Case Management System.",
            "type": "text",
            "timestamp": "2025-08-10T19:00:30Z",
            "metadata": {"action": "clarifying requirements"}
        },
        {
            "role": "user",
            "content": "first of all we decided on modular architecture we created main and more so number one is to rid of files that confuse either you or me or anyone or anything else",
            "type": "text", 
            "timestamp": "2025-08-10T19:01:00Z",
            "metadata": {"context": "cleanup request for modular architecture"}
        },
        {
            "role": "assistant",
            "content": "You're absolutely right - let's clean up the confusing files from the modular architecture transition. I can see there are backup files and potentially conflicting versions that need to be removed.",
            "type": "text",
            "timestamp": "2025-08-10T19:01:30Z", 
            "metadata": {"action": "file cleanup", "tasks_created": 4}
        },
        {
            "role": "user",
            "content": "number one should be we don't run without ssl we don't store or transmit without pgp...or is that too much? just asking",
            "type": "text",
            "timestamp": "2025-08-10T19:10:00Z",
            "metadata": {"context": "security requirements discussion"}
        },
        {
            "role": "assistant",
            "content": "Not too much at all - that's exactly the right security mindset for legal data. Let me check the current security implementation and enhance it to enforce those requirements.",
            "type": "text", 
            "timestamp": "2025-08-10T19:10:30Z",
            "metadata": {"action": "security enforcement implementation", "security_policy": "encrypt_everything"}
        },
        {
            "role": "user",
            "content": "do we not use mongo? we have it, we probably should? or... we can store chats in it including this one I would love if we could updater my local mongo with this very conversation",
            "type": "text",
            "timestamp": "2025-08-10T19:20:00Z", 
            "metadata": {"context": "mongodb integration request", "specific_request": "store_current_conversation"}
        }
    ]
    
    try:
        chat_manager = create_chat_manager()
        if not chat_manager:
            print("❌ Chat manager not available - MongoDB may not be running")
            return False
        
        session_id = chat_manager.store_conversation_snapshot(
            conversation_data=conversation_data,
            case_id="system_development", 
            title="Claude Code Session: Security & Architecture Cleanup"
        )
        
        print(f"✅ Conversation stored in MongoDB with session ID: {session_id}")
        print(f"🔒 Messages encrypted with PGP for case: system_development")
        return True
        
    except Exception as e:
        print(f"❌ Failed to store conversation: {e}")
        return False
```

## File: document_numbering.py
```python
"""
Document Numbering System for Legal Case Management
Provides logical, chronological numbering based on document type and filing date
"""

from datetime import datetime
from typing import Dict, List, Optional
import re

class DocumentNumberingSystem:
    """
    Handles logical document numbering for legal cases
    
    Rules:
    1. Complaint for Divorce = Document #1 (always first)
    2. Subsequent documents numbered chronologically by filing date
    3. Multiple filings on same day get sub-numbers (1a, 1b, etc.)
    4. Document types have priority order for same-day filings
    """
    
    # Document type priority (lower number = higher priority for same day)
    DOCUMENT_PRIORITIES = {
        'complaint_for_divorce': 1,
        'summons': 2,
        'mandatory_disclosure_order': 3,
        'restraining_order': 4,
        'parenting_education_order': 5,
        'waiver_of_counsel': 6,
        'affidavit': 7,
        'motion': 8,
        'response': 9,
        'discovery': 10,
        'financial_disclosure': 11,
        'settlement_agreement': 12,
        'judgment': 13,
        'decree': 14,
        'other': 99
    }
    
    def __init__(self):
        self.documents = []
        
    def classify_document_type(self, filename: str, content: str = "") -> str:
        """Classify document type based on filename and content"""
        filename_lower = filename.lower()
        content_lower = content.lower()
        
        # Check for specific document types
        if 'complaint' in filename_lower and 'divorce' in filename_lower:
            return 'complaint_for_divorce'
        elif 'summons' in filename_lower:
            return 'summons'
        elif 'mandatory' in filename_lower and 'disclosure' in filename_lower:
            return 'mandatory_disclosure_order'
        elif 'restraining' in filename_lower or 'mutual restraining' in filename_lower:
            return 'restraining_order'
        elif 'parenting' in filename_lower and 'education' in filename_lower:
            return 'parenting_education_order'
        elif 'waiver' in filename_lower and ('counsel' in filename_lower or 'legal' in filename_lower):
            return 'waiver_of_counsel'
        elif 'affidavit' in filename_lower:
            return 'affidavit'
        elif 'motion' in filename_lower:
            return 'motion'
        elif 'response' in filename_lower or 'answer' in filename_lower:
            return 'response'
        elif 'discovery' in filename_lower:
            return 'discovery'
        elif 'financial' in filename_lower and 'disclosure' in filename_lower:
            return 'financial_disclosure'
        elif 'settlement' in filename_lower or 'agreement' in filename_lower:
            return 'settlement_agreement'
        elif 'judgment' in filename_lower:
            return 'judgment'
        elif 'decree' in filename_lower:
            return 'decree'
        else:
            return 'other'
    
    def extract_filing_date(self, filename: str, metadata: Dict = None) -> datetime:
        """Extract filing date from filename or metadata"""
        
        # Try to get from metadata first
        if metadata and 'filing_date' in metadata:
            if isinstance(metadata['filing_date'], datetime):
                return metadata['filing_date']
            elif isinstance(metadata['filing_date'], str):
                try:
                    return datetime.strptime(metadata['filing_date'], '%Y-%m-%d')
                except:
                    pass
        
        # Try to extract date from filename
        # Look for patterns like YYYY-MM-DD, MM-DD-YYYY, etc.
        date_patterns = [
            r'(\d{4})-(\d{2})-(\d{2})',  # YYYY-MM-DD
            r'(\d{2})-(\d{2})-(\d{4})',  # MM-DD-YYYY
            r'(\d{2})(\d{2})(\d{4})',    # MMDDYYYY
            r'(\d{4})(\d{2})(\d{2})',    # YYYYMMDD
        ]
        
        for pattern in date_patterns:
            match = re.search(pattern, filename)
            if match:
                try:
                    if len(match.group(1)) == 4:  # Year first
                        year, month, day = match.groups()
                    else:  # Month first
                        month, day, year = match.groups()
                    
                    return datetime(int(year), int(month), int(day))
                except:
                    continue
        
        # Default to current date if no date found
        return datetime.now()
    
    def add_document(self, filename: str, content: str = "", metadata: Dict = None) -> str:
        """Add document and return its logical number"""
        
        doc_type = self.classify_document_type(filename, content)
        filing_date = self.extract_filing_date(filename, metadata)
        priority = self.DOCUMENT_PRIORITIES.get(doc_type, 99)
        
        document = {
            'filename': filename,
            'doc_type': doc_type,
            'filing_date': filing_date,
            'priority': priority,
            'content': content,
            'metadata': metadata or {}
        }
        
        self.documents.append(document)
        
        # Re-sort and re-number all documents
        self._renumber_documents()
        
        # Find and return the number for this document
        for doc in self.documents:
            if doc['filename'] == filename:
                return doc['document_number']
        
        return "Unknown"
    
    def _renumber_documents(self):
        """Re-number all documents based on chronological order and priority"""
        
        # Sort by filing date, then by priority
        self.documents.sort(key=lambda x: (x['filing_date'], x['priority']))
        
        # Assign document numbers
        current_number = 1
        current_date = None
        same_day_counter = 0
        
        for doc in self.documents:
            if current_date != doc['filing_date'].date():
                # New day
                current_date = doc['filing_date'].date()
                same_day_counter = 0
                doc['document_number'] = str(current_number)
                current_number += 1
            else:
                # Same day - use sub-numbering
                same_day_counter += 1
                base_number = current_number - 1
                sub_letter = chr(ord('a') + same_day_counter - 1)
                doc['document_number'] = f"{base_number}{sub_letter}"
    
    def get_document_list(self) -> List[Dict]:
        """Get sorted list of all documents with their numbers"""
        return sorted(self.documents, key=lambda x: x['document_number'])
    
    def get_document_by_number(self, number: str) -> Optional[Dict]:
        """Get document by its logical number"""
        for doc in self.documents:
            if doc['document_number'] == number:
                return doc
        return None
    
    def get_next_expected_documents(self) -> List[str]:
        """Get list of documents typically expected next in divorce proceedings"""
        
        existing_types = {doc['doc_type'] for doc in self.documents}
        
        expected_sequence = [
            'complaint_for_divorce',
            'summons', 
            'mandatory_disclosure_order',
            'waiver_of_counsel',
            'financial_disclosure',
            'settlement_agreement',
            'judgment',
            'decree'
        ]
        
        # Find what's missing from the typical sequence
        missing = []
        for doc_type in expected_sequence:
            if doc_type not in existing_types:
                missing.append(doc_type.replace('_', ' ').title())
        
        return missing[:3]  # Return next 3 expected documents

# Global instance for the application
document_numbering = DocumentNumberingSystem()

```

## File: database.py
```python
"""
Database Management Module for Legal Case Management System
Handles all database operations, connections, and schema management.
"""

import sqlite3
import logging
from typing import List, Dict, Optional, Any
from datetime import datetime
import os
from contextlib import contextmanager

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DatabaseManager:
    """Centralized database management"""
    
    def __init__(self, db_path: str = "legal_cases.db"):
        self.db_path = db_path
        self.init_database()
    
    def init_database(self):
        """Initialize database with all required tables"""
        logger.info("[DATABASE] Starting init_database")
        
        with self.get_connection() as conn:
            cursor = conn.cursor()
            
            # Cases table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS cases (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL,
                    case_number TEXT UNIQUE,
                    description TEXT,
                    status TEXT DEFAULT 'active',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    created_by INTEGER,
                    client_name TEXT,
                    opposing_party TEXT,
                    court_name TEXT,
                    case_type TEXT,
                    priority TEXT DEFAULT 'medium'
                )
            ''')
            
            # Documents table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS documents (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    case_id INTEGER NOT NULL,
                    filename TEXT NOT NULL,
                    original_filename TEXT,
                    file_type TEXT,
                    file_size INTEGER,
                    upload_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    uploaded_by INTEGER,
                    content_preview TEXT,
                    document_type TEXT,
                    is_processed BOOLEAN DEFAULT 0,
                    ocr_text TEXT,
                    metadata TEXT,
                    FOREIGN KEY (case_id) REFERENCES cases (id) ON DELETE CASCADE
                )
            ''')
            
            # URL monitoring table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS url_monitoring (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    case_id INTEGER NOT NULL,
                    url TEXT NOT NULL,
                    description TEXT,
                    last_checked TIMESTAMP,
                    last_content_hash TEXT,
                    status TEXT DEFAULT 'active',
                    check_frequency INTEGER DEFAULT 3600,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (case_id) REFERENCES cases (id) ON DELETE CASCADE
                )
            ''')
            
            # User actions log
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS user_actions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER,
                    action TEXT NOT NULL,
                    details TEXT,
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    ip_address TEXT,
                    case_id INTEGER,
                    FOREIGN KEY (case_id) REFERENCES cases (id)
                )
            ''')
            
            # System monitoring
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS system_monitoring (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    metric_name TEXT NOT NULL,
                    metric_value REAL,
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    details TEXT
                )
            ''')
            
            # Case notes
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS case_notes (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    case_id INTEGER NOT NULL,
                    note_text TEXT NOT NULL,
                    created_by INTEGER,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    note_type TEXT DEFAULT 'general',
                    is_private BOOLEAN DEFAULT 0,
                    FOREIGN KEY (case_id) REFERENCES cases (id) ON DELETE CASCADE
                )
            ''')
            
            # Deadlines and reminders
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS deadlines (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    case_id INTEGER NOT NULL,
                    title TEXT NOT NULL,
                    description TEXT,
                    due_date TIMESTAMP NOT NULL,
                    priority TEXT DEFAULT 'medium',
                    status TEXT DEFAULT 'pending',
                    created_by INTEGER,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    reminder_sent BOOLEAN DEFAULT 0,
                    FOREIGN KEY (case_id) REFERENCES cases (id) ON DELETE CASCADE
                )
            ''')
            
            conn.commit()
        
        logger.info("[DATABASE] Completed init_database")
    
    @contextmanager
    def get_connection(self):
        """Get database connection with proper error handling"""
        conn = None
        try:
            conn = sqlite3.connect(self.db_path)
            conn.row_factory = sqlite3.Row  # Enable column access by name
            yield conn
        except Exception as e:
            if conn:
                conn.rollback()
            logger.error(f"Database error: {e}")
            raise
        finally:
            if conn:
                conn.close()
    
    def execute_query(self, query: str, params: tuple = (), fetch: str = None) -> Any:
        """Execute a database query with error handling"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute(query, params)
                
                if fetch == 'one':
                    return cursor.fetchone()
                elif fetch == 'all':
                    return cursor.fetchall()
                else:
                    conn.commit()
                    return cursor.lastrowid
        except Exception as e:
            logger.error(f"Query execution error: {e}")
            raise
    
    # Case operations
    def create_case(self, name: str, case_number: str = None, description: str = None, 
                   created_by: int = None, **kwargs) -> int:
        """Create a new case"""
        query = '''
            INSERT INTO cases (name, case_number, description, created_by, client_name, 
                             opposing_party, court_name, case_type, priority)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        '''
        params = (
            name, case_number, description, created_by,
            kwargs.get('client_name'), kwargs.get('opposing_party'),
            kwargs.get('court_name'), kwargs.get('case_type', 'general'),
            kwargs.get('priority', 'medium')
        )
        
        case_id = self.execute_query(query, params)
        logger.info(f"Created case: {name} (ID: {case_id})")
        return case_id
    
    def get_case(self, case_id: int) -> Optional[Dict]:
        """Get case by ID"""
        query = "SELECT * FROM cases WHERE id = ?"
        result = self.execute_query(query, (case_id,), fetch='one')
        return dict(result) if result else None
    
    def get_all_cases(self, created_by: int = None) -> List[Dict]:
        """Get all cases, optionally filtered by creator"""
        if created_by:
            query = "SELECT * FROM cases WHERE created_by = ? ORDER BY updated_at DESC"
            params = (created_by,)
        else:
            query = "SELECT * FROM cases ORDER BY updated_at DESC"
            params = ()
        
        results = self.execute_query(query, params, fetch='all')
        return [dict(row) for row in results] if results else []
    
    def update_case(self, case_id: int, **kwargs) -> bool:
        """Update case information"""
        # Build dynamic update query
        fields = []
        values = []
        
        for key, value in kwargs.items():
            if key in ['name', 'case_number', 'description', 'status', 'client_name', 
                      'opposing_party', 'court_name', 'case_type', 'priority']:
                fields.append(f"{key} = ?")
                values.append(value)
        
        if not fields:
            return False
        
        fields.append("updated_at = CURRENT_TIMESTAMP")
        values.append(case_id)
        
        query = f"UPDATE cases SET {', '.join(fields)} WHERE id = ?"
        self.execute_query(query, tuple(values))
        
        logger.info(f"Updated case ID: {case_id}")
        return True
    
    def delete_case(self, case_id: int) -> bool:
        """Delete case and all related data"""
        query = "DELETE FROM cases WHERE id = ?"
        self.execute_query(query, (case_id,))
        logger.info(f"Deleted case ID: {case_id}")
        return True
    
    # Document operations
    def add_document(self, case_id: int, filename: str, file_type: str = None, 
                    file_size: int = None, uploaded_by: int = None, **kwargs) -> int:
        """Add document to case"""
        query = '''
            INSERT INTO documents (case_id, filename, original_filename, file_type, 
                                 file_size, uploaded_by, content_preview, document_type, 
                                 ocr_text, metadata)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        '''
        params = (
            case_id, filename, kwargs.get('original_filename', filename),
            file_type, file_size, uploaded_by, kwargs.get('content_preview'),
            kwargs.get('document_type', 'general'), kwargs.get('ocr_text'),
            kwargs.get('metadata')
        )
        
        doc_id = self.execute_query(query, params)
        logger.info(f"Added document: {filename} to case {case_id}")
        return doc_id
    
    def get_case_documents(self, case_id: int) -> List[Dict]:
        """Get all documents for a case"""
        query = "SELECT * FROM documents WHERE case_id = ? ORDER BY upload_date DESC"
        results = self.execute_query(query, (case_id,), fetch='all')
        return [dict(row) for row in results] if results else []
    
    def get_document(self, doc_id: int) -> Optional[Dict]:
        """Get document by ID"""
        query = "SELECT * FROM documents WHERE id = ?"
        result = self.execute_query(query, (doc_id,), fetch='one')
        return dict(result) if result else None
    
    def update_document(self, doc_id: int, **kwargs) -> bool:
        """Update document information"""
        fields = []
        values = []
        
        for key, value in kwargs.items():
            if key in ['filename', 'file_type', 'content_preview', 'document_type', 
                      'is_processed', 'ocr_text', 'metadata']:
                fields.append(f"{key} = ?")
                values.append(value)
        
        if not fields:
            return False
        
        values.append(doc_id)
        query = f"UPDATE documents SET {', '.join(fields)} WHERE id = ?"
        self.execute_query(query, tuple(values))
        
        logger.info(f"Updated document ID: {doc_id}")
        return True
    
    def delete_document(self, doc_id: int) -> bool:
        """Delete document"""
        query = "DELETE FROM documents WHERE id = ?"
        self.execute_query(query, (doc_id,))
        logger.info(f"Deleted document ID: {doc_id}")
        return True
    
    # URL monitoring operations
    def add_url_monitor(self, case_id: int, url: str, description: str = None, 
                       check_frequency: int = 3600) -> int:
        """Add URL to monitor for case"""
        query = '''
            INSERT INTO url_monitoring (case_id, url, description, check_frequency)
            VALUES (?, ?, ?, ?)
        '''
        params = (case_id, url, description, check_frequency)
        
        monitor_id = self.execute_query(query, params)
        logger.info(f"Added URL monitor: {url} for case {case_id}")
        return monitor_id
    
    def get_case_url_monitors(self, case_id: int) -> List[Dict]:
        """Get all URL monitors for a case"""
        query = "SELECT * FROM url_monitoring WHERE case_id = ? AND status = 'active'"
        results = self.execute_query(query, (case_id,), fetch='all')
        return [dict(row) for row in results] if results else []
    
    def update_url_monitor(self, monitor_id: int, **kwargs) -> bool:
        """Update URL monitor"""
        fields = []
        values = []
        
        for key, value in kwargs.items():
            if key in ['url', 'description', 'last_checked', 'last_content_hash', 
                      'status', 'check_frequency']:
                fields.append(f"{key} = ?")
                values.append(value)
        
        if not fields:
            return False
        
        values.append(monitor_id)
        query = f"UPDATE url_monitoring SET {', '.join(fields)} WHERE id = ?"
        self.execute_query(query, tuple(values))
        
        return True
    
    # Logging operations
    def log_user_action(self, user_id: int, action: str, details: str = None, 
                       case_id: int = None, ip_address: str = None):
        """Log user action"""
        query = '''
            INSERT INTO user_actions (user_id, action, details, case_id, ip_address)
            VALUES (?, ?, ?, ?, ?)
        '''
        params = (user_id, action, details, case_id, ip_address)
        self.execute_query(query, params)
    
    def get_user_actions(self, user_id: int = None, case_id: int = None, 
                        limit: int = 100) -> List[Dict]:
        """Get user actions with optional filtering"""
        conditions = []
        params = []
        
        if user_id:
            conditions.append("user_id = ?")
            params.append(user_id)
        
        if case_id:
            conditions.append("case_id = ?")
            params.append(case_id)
        
        where_clause = " WHERE " + " AND ".join(conditions) if conditions else ""
        params.append(limit)
        
        query = f'''
            SELECT * FROM user_actions 
            {where_clause}
            ORDER BY timestamp DESC 
            LIMIT ?
        '''
        
        results = self.execute_query(query, tuple(params), fetch='all')
        return [dict(row) for row in results] if results else []
    
    # System monitoring
    def log_system_metric(self, metric_name: str, metric_value: float, details: str = None):
        """Log system metric"""
        query = '''
            INSERT INTO system_monitoring (metric_name, metric_value, details)
            VALUES (?, ?, ?)
        '''
        params = (metric_name, metric_value, details)
        self.execute_query(query, params)
    
    def get_system_metrics(self, metric_name: str = None, hours: int = 24) -> List[Dict]:
        """Get system metrics"""
        if metric_name:
            query = '''
                SELECT * FROM system_monitoring 
                WHERE metric_name = ? AND timestamp > datetime('now', '-{} hours')
                ORDER BY timestamp DESC
            '''.format(hours)
            params = (metric_name,)
        else:
            query = '''
                SELECT * FROM system_monitoring 
                WHERE timestamp > datetime('now', '-{} hours')
                ORDER BY timestamp DESC
            '''.format(hours)
            params = ()
        
        results = self.execute_query(query, params, fetch='all')
        return [dict(row) for row in results] if results else []
    
    # Case notes operations
    def add_case_note(self, case_id: int, note_text: str, created_by: int = None, 
                     note_type: str = 'general', is_private: bool = False) -> int:
        """Add note to case"""
        query = '''
            INSERT INTO case_notes (case_id, note_text, created_by, note_type, is_private)
            VALUES (?, ?, ?, ?, ?)
        '''
        params = (case_id, note_text, created_by, note_type, is_private)
        
        note_id = self.execute_query(query, params)
        logger.info(f"Added note to case {case_id}")
        return note_id
    
    def get_case_notes(self, case_id: int, include_private: bool = True) -> List[Dict]:
        """Get all notes for a case"""
        if include_private:
            query = "SELECT * FROM case_notes WHERE case_id = ? ORDER BY created_at DESC"
            params = (case_id,)
        else:
            query = "SELECT * FROM case_notes WHERE case_id = ? AND is_private = 0 ORDER BY created_at DESC"
            params = (case_id,)
        
        results = self.execute_query(query, params, fetch='all')
        return [dict(row) for row in results] if results else []
    
    # Deadlines operations
    def add_deadline(self, case_id: int, title: str, due_date: str, description: str = None,
                    priority: str = 'medium', created_by: int = None) -> int:
        """Add deadline to case"""
        query = '''
            INSERT INTO deadlines (case_id, title, description, due_date, priority, created_by)
            VALUES (?, ?, ?, ?, ?, ?)
        '''
        params = (case_id, title, description, due_date, priority, created_by)
        
        deadline_id = self.execute_query(query, params)
        logger.info(f"Added deadline to case {case_id}: {title}")
        return deadline_id
    
    def get_case_deadlines(self, case_id: int, status: str = None) -> List[Dict]:
        """Get deadlines for a case"""
        if status:
            query = "SELECT * FROM deadlines WHERE case_id = ? AND status = ? ORDER BY due_date ASC"
            params = (case_id, status)
        else:
            query = "SELECT * FROM deadlines WHERE case_id = ? ORDER BY due_date ASC"
            params = (case_id,)
        
        results = self.execute_query(query, params, fetch='all')
        return [dict(row) for row in results] if results else []
    
    def get_upcoming_deadlines(self, days_ahead: int = 7) -> List[Dict]:
        """Get upcoming deadlines across all cases"""
        query = '''
            SELECT d.*, c.name as case_name 
            FROM deadlines d
            JOIN cases c ON d.case_id = c.id
            WHERE d.due_date BETWEEN datetime('now') AND datetime('now', '+{} days')
            AND d.status = 'pending'
            ORDER BY d.due_date ASC
        '''.format(days_ahead)
        
        results = self.execute_query(query, (), fetch='all')
        return [dict(row) for row in results] if results else []

# Global database manager instance
db_manager = DatabaseManager()

```


Please analyze this code and help me understand its structure, functionality, and provide suggestions for improvements or answer any questions I have about it.
