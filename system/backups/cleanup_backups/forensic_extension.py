"""
Forensic Extension for Legal Case Management System
Extends existing database managers with forensic document tracking and tampering detection
Uses the existing PostgreSQL, MongoDB, and Redis infrastructure
"""

import hashlib
import json
import difflib
from datetime import datetime, timezone
from typing import Dict, <PERSON>, Optional, Tuple
from dataclasses import dataclass
import uuid
import logging

from .database_managers import DatabaseManagerFactory

logger = logging.getLogger(__name__)

@dataclass
class DocumentSnapshot:
    """Immutable document snapshot for forensic comparison"""
    document_id: str
    case_id: int
    filing_date: str
    document_type: str
    original_text: str
    hash_sha256: str
    timestamp: datetime
    source_url: str
    filing_party: str
    docket_sequence: int

@dataclass
class TamperingEvidence:
    """Evidence of document tampering or unauthorized changes"""
    incident_id: str
    document_id: str
    case_id: int
    tamper_type: str
    detection_time: datetime
    original_hash: str
    modified_hash: str
    differences: List[str]
    severity: str
    evidence_preserved: bool
    judicial_actor: Optional[str]
    misconduct_indicators: List[str]

class ForensicExtension:
    """
    Forensic extension that adds tampering detection to existing database managers
    """
    
    def __init__(self, db_managers: Dict):
        self.logger = logging.getLogger(__name__)
        self.db_managers = db_managers
        self.postgresql = db_managers.get('postgresql')
        self.mongodb = db_managers.get('mongodb')
        self.redis = db_managers.get('redis')
        
        if not self.postgresql:
            raise RuntimeError("PostgreSQL manager required for forensic extension")
            
    def init_forensic_schema(self):
        """Add forensic tracking tables to existing PostgreSQL schema"""
        forensic_schema = """
        -- Forensic document tracking tables
        CREATE TABLE IF NOT EXISTS document_snapshots (
            id SERIAL PRIMARY KEY,
            document_id VARCHAR(255) NOT NULL,
            case_id INTEGER REFERENCES cases(id),
            filing_date DATE NOT NULL,
            document_type VARCHAR(100) NOT NULL,
            hash_sha256 VARCHAR(64) NOT NULL,
            timestamp TIMESTAMPTZ DEFAULT NOW(),
            source_url TEXT,
            filing_party VARCHAR(100),
            docket_sequence INTEGER,
            created_at TIMESTAMPTZ DEFAULT NOW()
        );
        
        CREATE TABLE IF NOT EXISTS tampering_evidence (
            id SERIAL PRIMARY KEY,
            incident_id VARCHAR(255) UNIQUE NOT NULL,
            document_id VARCHAR(255) NOT NULL,
            case_id INTEGER REFERENCES cases(id),
            tamper_type VARCHAR(50) NOT NULL,
            detection_time TIMESTAMPTZ DEFAULT NOW(),
            original_hash VARCHAR(64) NOT NULL,
            modified_hash VARCHAR(64),
            severity VARCHAR(20) NOT NULL,
            evidence_preserved BOOLEAN DEFAULT TRUE,
            judicial_actor VARCHAR(200),
            reported_to_authorities BOOLEAN DEFAULT FALSE,
            investigation_status VARCHAR(50) DEFAULT 'OPEN',
            created_at TIMESTAMPTZ DEFAULT NOW()
        );
        
        CREATE TABLE IF NOT EXISTS judicial_misconduct (
            id SERIAL PRIMARY KEY,
            case_id INTEGER REFERENCES cases(id),
            incident_type VARCHAR(100) NOT NULL,
            description TEXT NOT NULL,
            evidence_documents TEXT[],
            judicial_officer VARCHAR(200),
            incident_date TIMESTAMPTZ NOT NULL,
            severity VARCHAR(20) NOT NULL,
            status VARCHAR(50) DEFAULT 'DOCUMENTED',
            witness_accounts TEXT[],
            legal_violations TEXT[],
            disciplinary_referral BOOLEAN DEFAULT FALSE,
            created_at TIMESTAMPTZ DEFAULT NOW()
        );

        -- Forensic indexes
        CREATE INDEX IF NOT EXISTS idx_document_snapshots_case ON document_snapshots(case_id);
        CREATE INDEX IF NOT EXISTS idx_document_snapshots_hash ON document_snapshots(hash_sha256);
        CREATE INDEX IF NOT EXISTS idx_tampering_evidence_case ON tampering_evidence(case_id);
        CREATE INDEX IF NOT EXISTS idx_tampering_severity ON tampering_evidence(severity);
        CREATE INDEX IF NOT EXISTS idx_judicial_misconduct_case ON judicial_misconduct(case_id);
        CREATE INDEX IF NOT EXISTS idx_judicial_misconduct_officer ON judicial_misconduct(judicial_officer);
        """
        
        try:
            with self.postgresql.get_connection() as conn:
                with conn.cursor() as cursor:
                    cursor.execute(forensic_schema)
                    conn.commit()
            self.logger.info("🔒 Forensic schema initialized successfully")
        except Exception as e:
            self.logger.error(f"❌ Failed to initialize forensic schema: {e}")
            raise
            
    def init_mongo_collections(self):
        """Initialize MongoDB collections for forensic document storage"""
        if not self.mongodb:
            self.logger.warning("MongoDB not available for forensic storage")
            return
            
        try:
            # Get MongoDB client from manager
            db = self.mongodb.get_database()
            
            # Document versions collection
            db.document_versions.create_index([
                ("document_id", 1),
                ("version", 1),
                ("timestamp", -1)
            ])
            
            # Original filings collection (immutable)
            db.original_filings.create_index([
                ("case_number", 1),
                ("filing_date", 1),
                ("document_type", 1)
            ])
            
            # Tampering evidence collection
            db.tampering_evidence.create_index([
                ("incident_id", 1),
                ("case_id", 1),
                ("detection_time", -1)
            ])
            
            self.logger.info("🔒 Forensic MongoDB collections initialized")
        except Exception as e:
            self.logger.error(f"❌ Failed to initialize MongoDB collections: {e}")
            
    def preserve_document_snapshot(self, docket_entry: Dict, case_id: int) -> DocumentSnapshot:
        """Create immutable forensic snapshot of document"""
        try:
            # Extract document content
            document_text = self.extract_document_content(docket_entry)
            
            # Create cryptographic hash
            hash_sha256 = hashlib.sha256(document_text.encode('utf-8')).hexdigest()
            
            # Create snapshot
            snapshot = DocumentSnapshot(
                document_id=f"{case_id}_{docket_entry.get('Docket Number', 'unknown')}",
                case_id=case_id,
                filing_date=docket_entry.get('Filing Date', 'unknown'),
                document_type=docket_entry.get('Type', 'unknown'),
                original_text=document_text,
                hash_sha256=hash_sha256,
                timestamp=datetime.now(timezone.utc),
                source_url=docket_entry.get('source_url', ''),
                filing_party=docket_entry.get('Side', 'unknown'),
                docket_sequence=int(docket_entry.get('Docket Number', 0))
            )
            
            # Store in PostgreSQL
            self.store_snapshot_postgresql(snapshot)
            
            # Store full content in MongoDB if available
            if self.mongodb:
                self.store_snapshot_mongodb(snapshot, docket_entry)
            
            # Cache in Redis if available
            if self.redis:
                self.cache_snapshot_redis(snapshot)
            
            self.logger.info(f"📸 Document snapshot preserved: {snapshot.document_id}")
            return snapshot
            
        except Exception as e:
            self.logger.error(f"❌ Failed to preserve document snapshot: {e}")
            raise
            
    def detect_document_tampering(self, current_entry: Dict, case_id: int) -> Optional[TamperingEvidence]:
        """Compare current document against stored snapshots to detect tampering"""
        try:
            document_id = f"{case_id}_{current_entry.get('Docket Number', 'unknown')}"
            
            # Get original snapshot
            original_snapshot = self.get_original_snapshot(document_id)
            if not original_snapshot:
                self.logger.warning(f"⚠️ No original snapshot found for {document_id}")
                return None
                
            # Extract current content
            current_text = self.extract_document_content(current_entry)
            current_hash = hashlib.sha256(current_text.encode('utf-8')).hexdigest()
            
            # Compare hashes
            if current_hash == original_snapshot['hash_sha256']:
                return None  # No tampering detected
                
            # Analyze differences
            differences = self.analyze_document_differences(
                original_snapshot.get('original_text', ''), 
                current_text
            )
            
            # Determine tampering type and severity
            tamper_type, severity, misconduct_indicators = self.classify_tampering(
                differences, current_entry, original_snapshot
            )
            
            # Create tampering evidence
            evidence = TamperingEvidence(
                incident_id=str(uuid.uuid4()),
                document_id=document_id,
                case_id=case_id,
                tamper_type=tamper_type,
                detection_time=datetime.now(timezone.utc),
                original_hash=original_snapshot['hash_sha256'],
                modified_hash=current_hash,
                differences=differences,
                severity=severity,
                evidence_preserved=True,
                judicial_actor=self.identify_judicial_actor(current_entry),
                misconduct_indicators=misconduct_indicators
            )
            
            # Store tampering evidence
            self.store_tampering_evidence(evidence)
            
            # Store detailed evidence in MongoDB
            if self.mongodb:
                self.store_detailed_evidence_mongo(evidence, original_snapshot, current_text)
            
            # Alert for critical tampering
            if severity == 'CRITICAL':
                self.trigger_critical_alert(evidence)
                
            self.logger.critical(f"🚨 DOCUMENT TAMPERING DETECTED: {evidence.incident_id}")
            return evidence
            
        except Exception as e:
            self.logger.error(f"❌ Failed to detect tampering: {e}")
            return None
            
    def extract_document_content(self, docket_entry: Dict) -> str:
        """Extract full text content from docket entry"""
        content_parts = []
        
        # Include all available text fields
        for key, value in docket_entry.items():
            if isinstance(value, str) and value.strip():
                content_parts.append(f"{key}: {value}")
                
        return "\n".join(content_parts)
        
    def analyze_document_differences(self, original: str, current: str) -> List[str]:
        """Analyze specific differences between document versions"""
        differences = []
        
        # Line-by-line comparison
        original_lines = original.splitlines()
        current_lines = current.splitlines()
        
        diff = list(difflib.unified_diff(
            original_lines, current_lines,
            fromfile='original', tofile='current',
            lineterm=''
        ))
        
        for line in diff:
            if line.startswith('+ ') or line.startswith('- '):
                differences.append(line)
                
        return differences
        
    def classify_tampering(self, differences: List[str], current_entry: Dict, original: Dict) -> Tuple[str, str, List[str]]:
        """Classify type and severity of tampering"""
        misconduct_indicators = []
        
        # Check for judicial misconduct patterns
        if any('judgment entry' in diff.lower() for diff in differences):
            if current_entry.get('Side') != original.get('filing_party'):
                misconduct_indicators.append("UNAUTHORIZED_AUTHOR_CHANGE")
                
        if any('stricken' in diff.lower() for diff in differences):
            misconduct_indicators.append("EVIDENCE_SUPPRESSION")
            
        if len(differences) > 50:  # Extensive changes
            misconduct_indicators.append("SUBSTANTIAL_ALTERATION")
            
        # Determine tampering type
        if "UNAUTHORIZED_AUTHOR_CHANGE" in misconduct_indicators:
            tamper_type = "DOCUMENT_REPLACED"
            severity = "CRITICAL"
        elif "EVIDENCE_SUPPRESSION" in misconduct_indicators:
            tamper_type = "STRICKEN_EVIDENCE"
            severity = "CRITICAL"
        elif len(differences) > 10:
            tamper_type = "CONTENT_ALTERED"
            severity = "HIGH"
        else:
            tamper_type = "MINOR_EDIT"
            severity = "MEDIUM"
            
        return tamper_type, severity, misconduct_indicators
        
    def identify_judicial_actor(self, entry: Dict) -> Optional[str]:
        """Identify judicial officer responsible for changes"""
        description = entry.get('Description', '').lower()
        
        if 'magistrate' in description:
            if 'charmine dose' in description:
                return "MAGISTRATE CHARMINE DOSE"
            elif 'jason p. parker' in description:
                return "MAGISTRATE JASON P. PARKER"
                
        if 'judge' in description:
            if 'colleen ann reali' in description:
                return "JUDGE COLLEEN ANN REALI"
                
        return None
        
    def store_snapshot_postgresql(self, snapshot: DocumentSnapshot):
        """Store snapshot metadata in PostgreSQL"""
        sql = """
        INSERT INTO document_snapshots 
        (document_id, case_id, filing_date, document_type, hash_sha256, 
         timestamp, source_url, filing_party, docket_sequence)
        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
        """
        
        with self.postgresql.get_connection() as conn:
            with conn.cursor() as cursor:
                cursor.execute(sql, (
                    snapshot.document_id, snapshot.case_id, snapshot.filing_date,
                    snapshot.document_type, snapshot.hash_sha256, snapshot.timestamp,
                    snapshot.source_url, snapshot.filing_party, snapshot.docket_sequence
                ))
                conn.commit()
                
    def store_snapshot_mongodb(self, snapshot: DocumentSnapshot, full_entry: Dict):
        """Store complete document content in MongoDB"""
        document = {
            "document_id": snapshot.document_id,
            "case_id": snapshot.case_id,
            "version": 1,
            "timestamp": snapshot.timestamp,
            "hash_sha256": snapshot.hash_sha256,
            "original_text": snapshot.original_text,
            "full_entry": full_entry,
            "preserved_for": "FORENSIC_EVIDENCE",
            "immutable": True
        }
        
        db = self.mongodb.get_database()
        db.original_filings.insert_one(document)
        
    def cache_snapshot_redis(self, snapshot: DocumentSnapshot):
        """Cache snapshot in Redis for fast access"""
        if not self.redis:
            return
            
        key = f"forensic:snapshot:{snapshot.document_id}"
        data = {
            "hash": snapshot.hash_sha256,
            "timestamp": snapshot.timestamp.isoformat(),
            "type": snapshot.document_type
        }
        
        client = self.redis.get_client()
        client.hset(key, mapping=data)
        client.expire(key, 86400)  # 24 hour cache
        
    def get_original_snapshot(self, document_id: str) -> Optional[Dict]:
        """Retrieve original document snapshot"""
        sql = """
        SELECT * FROM document_snapshots 
        WHERE document_id = %s 
        ORDER BY timestamp ASC 
        LIMIT 1
        """
        
        with self.postgresql.get_connection() as conn:
            with conn.cursor() as cursor:
                cursor.execute(sql, (document_id,))
                row = cursor.fetchone()
                
                if row:
                    # Convert to dict
                    columns = [desc[0] for desc in cursor.description]
                    result = dict(zip(columns, row))
                    
                    # Get original text from MongoDB if available
                    if self.mongodb:
                        db = self.mongodb.get_database()
                        mongo_doc = db.original_filings.find_one({
                            "document_id": document_id
                        })
                        if mongo_doc:
                            result['original_text'] = mongo_doc.get('original_text', '')
                    
                    return result
                    
        return None
        
    def store_tampering_evidence(self, evidence: TamperingEvidence):
        """Store tampering evidence in PostgreSQL"""
        sql = """
        INSERT INTO tampering_evidence 
        (incident_id, document_id, case_id, tamper_type, detection_time,
         original_hash, modified_hash, severity, evidence_preserved, judicial_actor)
        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        """
        
        with self.postgresql.get_connection() as conn:
            with conn.cursor() as cursor:
                cursor.execute(sql, (
                    evidence.incident_id, evidence.document_id, evidence.case_id,
                    evidence.tamper_type, evidence.detection_time, evidence.original_hash,
                    evidence.modified_hash, evidence.severity, evidence.evidence_preserved,
                    evidence.judicial_actor
                ))
                conn.commit()
                
    def store_detailed_evidence_mongo(self, evidence: TamperingEvidence, original: Dict, current_text: str):
        """Store detailed tampering evidence in MongoDB"""
        evidence_doc = {
            "incident_id": evidence.incident_id,
            "document_id": evidence.document_id,
            "case_id": evidence.case_id,
            "tamper_type": evidence.tamper_type,
            "detection_time": evidence.detection_time,
            "differences": evidence.differences,
            "misconduct_indicators": evidence.misconduct_indicators,
            "original_content": original.get('original_text', ''),
            "modified_content": current_text,
            "evidence_chain": "PRESERVED",
            "forensic_hash": evidence.modified_hash
        }
        
        db = self.mongodb.get_database()
        db.tampering_evidence.insert_one(evidence_doc)
        
    def trigger_critical_alert(self, evidence: TamperingEvidence):
        """Trigger critical alert for severe tampering"""
        if not self.redis:
            return
            
        alert_key = f"critical:tampering:{evidence.case_id}"
        alert_data = {
            "incident_id": evidence.incident_id,
            "tamper_type": evidence.tamper_type,
            "judicial_actor": evidence.judicial_actor or "UNKNOWN",
            "detection_time": evidence.detection_time.isoformat(),
            "severity": "CRITICAL_JUDICIAL_MISCONDUCT"
        }
        
        client = self.redis.get_client()
        client.hset(alert_key, mapping=alert_data)
        client.expire(alert_key, 604800)  # Keep for 7 days
        
        self.logger.critical(f"🚨 CRITICAL JUDICIAL MISCONDUCT DETECTED: {evidence.incident_id}")
        
    def generate_misconduct_report(self, case_id: int) -> Dict:
        """Generate comprehensive misconduct evidence report"""
        sql_tampering = """
        SELECT * FROM tampering_evidence 
        WHERE case_id = %s 
        ORDER BY detection_time DESC
        """
        
        sql_misconduct = """
        SELECT * FROM judicial_misconduct 
        WHERE case_id = %s 
        ORDER BY incident_date DESC
        """
        
        with self.postgresql.get_connection() as conn:
            with conn.cursor() as cursor:
                # Get tampering evidence
                cursor.execute(sql_tampering, (case_id,))
                tampering_rows = cursor.fetchall()
                tampering_columns = [desc[0] for desc in cursor.description]
                tampering_evidence = [dict(zip(tampering_columns, row)) for row in tampering_rows]
                
                # Get misconduct incidents
                cursor.execute(sql_misconduct, (case_id,))
                misconduct_rows = cursor.fetchall()
                misconduct_columns = [desc[0] for desc in cursor.description]
                misconduct_incidents = [dict(zip(misconduct_columns, row)) for row in misconduct_rows]
        
        return {
            "case_id": case_id,
            "report_generated": datetime.now(timezone.utc).isoformat(),
            "tampering_incidents": len(tampering_evidence),
            "critical_incidents": sum(1 for e in tampering_evidence if e['severity'] == 'CRITICAL'),
            "evidence_preserved": True,
            "legal_violations": self.identify_legal_violations(tampering_evidence),
            "recommended_actions": self.generate_legal_recommendations(tampering_evidence),
            "detailed_evidence": tampering_evidence,
            "misconduct_summary": misconduct_incidents
        }
        
    def identify_legal_violations(self, tampering_evidence: List[Dict]) -> List[str]:
        """Identify specific legal violations from evidence"""
        violations = []
        
        for evidence in tampering_evidence:
            if evidence['tamper_type'] == 'DOCUMENT_REPLACED':
                violations.append("FRAUDULENT_COURT_RECORD_ALTERATION")
            if evidence['tamper_type'] == 'STRICKEN_EVIDENCE':
                violations.append("EVIDENCE_SUPPRESSION_WITHOUT_PROPER_ORDER")
            if evidence['judicial_actor']:
                violations.append("JUDICIAL_MISCONDUCT_IN_OFFICE")
                
        return list(set(violations))
        
    def generate_legal_recommendations(self, tampering_evidence: List[Dict]) -> List[str]:
        """Generate legal action recommendations"""
        recommendations = []
        
        if len(tampering_evidence) > 5:
            recommendations.append("FILE_JUDICIAL_CONDUCT_COMPLAINT")
        if any(e['severity'] == 'CRITICAL' for e in tampering_evidence):
            recommendations.append("REQUEST_IMMEDIATE_CASE_TRANSFER")
        if any('DOCUMENT_REPLACED' in e['tamper_type'] for e in tampering_evidence):
            recommendations.append("MOTION_FOR_SANCTIONS_JUDICIAL_MISCONDUCT")
            
        recommendations.append("PRESERVE_ALL_DIGITAL_EVIDENCE")
        recommendations.append("CONTACT_STATE_BAR_ASSOCIATION")
        
        return recommendations


def create_forensic_extension(database_config: Dict) -> ForensicExtension:
    """Factory function to create forensic extension with existing database managers"""
    # Create database managers using existing factory
    db_managers = DatabaseManagerFactory.create_managers(database_config)
    
    # Create forensic extension
    forensic_ext = ForensicExtension(db_managers)
    
    # Initialize forensic schema
    forensic_ext.init_forensic_schema()
    if 'mongodb' in db_managers:
        forensic_ext.init_mongo_collections()
    
    return forensic_ext