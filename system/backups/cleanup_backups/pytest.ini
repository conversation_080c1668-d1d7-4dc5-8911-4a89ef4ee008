[tool:pytest]
# Pytest configuration for Legal Case Management System

# Test discovery
testpaths = .
python_files = test_*.py
python_classes = Test*
python_functions = test_*

# Markers
markers =
    functionality: Functionality tests
    usability: Usability and UX tests
    performance: Performance tests
    accessibility: Accessibility tests
    integration: Integration tests
    slow: Slow running tests
    smoke: Smoke tests for quick validation

# Output options
addopts =
    -v
    --strict-markers
    --tb=short
    --durations=10
    --color=yes

# Minimum version
minversion = 6.0

# Test timeout (in seconds)
timeout = 300

# Warnings
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
    ignore::UserWarning:streamlit.*

# Log configuration
log_cli = true
log_cli_level = INFO
log_cli_format = %(asctime)s [%(levelname)8s] %(name)s: %(message)s
log_cli_date_format = %Y-%m-%d %H:%M:%S
