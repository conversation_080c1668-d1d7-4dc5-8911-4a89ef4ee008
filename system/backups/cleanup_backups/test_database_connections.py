#!/usr/bin/env python3
"""
Quick database connection test script
Tests actual database connections with your running containers
"""

import os
import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_postgresql_connection():
    """Test PostgreSQL connection"""
    print("🔗 Testing PostgreSQL connection...")
    
    try:
        import psycopg2
        
        # Use your actual database credentials
        conn = psycopg2.connect(
            host='localhost',
            port=5435,
            database='legal_cms_main',
            user='legal_cms_user',
            password='cWO7LRL29U8tNfDgjG14RJSCA',
            sslmode='prefer'
        )
        
        cursor = conn.cursor()
        cursor.execute("SELECT version();")
        version = cursor.fetchone()
        
        print(f"✅ PostgreSQL connected successfully!")
        print(f"   Version: {version[0]}")
        
        cursor.close()
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ PostgreSQL connection failed: {e}")
        return False

def test_mongodb_connection():
    """Test MongoDB connection"""
    print("🔗 Testing MongoDB connection...")
    
    try:
        from pymongo import MongoClient
        
        # Test with admin authentication
        try:
            client = MongoClient(
                'localhost', 
                27018,
                username='admin',
                password='VDFyrKwyAkith2PXF3qHJgO6e',
                authSource='admin'
            )
            db = client.legal_cms_documents
            result = db.command('ping')
            print(f"✅ MongoDB connected successfully with admin authentication!")
            print(f"   Authentication: ENABLED")
            print(f"   User: admin")
            print(f"   AuthSource: admin")
            client.close()
            return True
        except Exception as e:
            print(f"❌ MongoDB authentication failed: {e}")
            return False
            
    except ImportError:
        print("❌ pymongo not installed")
        return False
    except Exception as e:
        print(f"❌ MongoDB connection failed: {e}")
        return False

def test_redis_connection():
    """Test Redis connection"""
    print("🔗 Testing Redis connection...")
    
    try:
        import redis
        
        # Test with password
        r = redis.Redis(
            host='localhost',
            port=6382,
            password='QFVchUWhPsZDLtnM0NmrcWvdW',
            decode_responses=True
        )
        
        result = r.ping()
        print(f"✅ Redis connected successfully!")
        
        return True
        
    except ImportError:
        print("❌ redis not installed")
        return False
    except Exception as e:
        print(f"❌ Redis connection failed: {e}")
        return False

def test_qdrant_connection():
    """Test Qdrant connection"""
    print("🔗 Testing Qdrant connection...")
    
    try:
        import requests
        
        response = requests.get('http://localhost:6334/collections')
        
        if response.status_code == 200:
            print(f"✅ Qdrant connected successfully!")
            collections = response.json()
            print(f"   Collections: {len(collections.get('result', {}).get('collections', []))}")
            return True
        else:
            print(f"❌ Qdrant connection failed: HTTP {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Qdrant connection failed: {e}")
        return False

def main():
    """Test all database connections"""
    print("🧪 DATABASE CONNECTION TESTS")
    print("=" * 50)
    
    results = []
    
    results.append(("PostgreSQL", test_postgresql_connection()))
    results.append(("MongoDB", test_mongodb_connection()))
    results.append(("Redis", test_redis_connection()))
    results.append(("Qdrant", test_qdrant_connection()))
    
    print("\n📊 RESULTS SUMMARY:")
    print("=" * 50)
    
    for db_name, success in results:
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{db_name:15}: {status}")
    
    total_pass = sum(1 for _, success in results if success)
    print(f"\nOverall: {total_pass}/{len(results)} databases connected successfully")
    
    if total_pass == len(results):
        print("🎉 All databases are working!")
    else:
        print("⚠️  Some databases need attention")

if __name__ == "__main__":
    main()