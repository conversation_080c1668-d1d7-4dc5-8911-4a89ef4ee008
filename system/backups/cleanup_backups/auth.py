"""
Authentication and Authorization Module for Legal Case Management System
Provides secure user authentication, session management, and access control.
"""

import streamlit as st
import hashlib
import secrets
import sqlite3
import jwt
from datetime import datetime, timedelta
from typing import Optional, Dict, List
import bcrypt
from cryptography.fernet import Fernet
import os
from functools import wraps

# Security Configuration
SECRET_KEY = os.getenv('JWT_SECRET_KEY', secrets.token_urlsafe(32))
ENCRYPTION_KEY = os.getenv('ENCRYPTION_KEY', Fernet.generate_key())
SESSION_TIMEOUT = int(os.getenv('SESSION_TIMEOUT', '3600'))  # 1 hour default

class SecurityManager:
    """Centralized security management for the application"""
    
    def __init__(self, db_path: str = "security.db"):
        self.db_path = db_path
        self.cipher_suite = Fernet(ENCRYPTION_KEY)
        self.init_security_db()
    
    def init_security_db(self):
        """Initialize security database with users, sessions, and audit logs"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Users table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT UNIQUE NOT NULL,
                email TEXT UNIQUE NOT NULL,
                password_hash TEXT NOT NULL,
                salt TEXT NOT NULL,
                role TEXT DEFAULT 'user',
                is_active BOOLEAN DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                last_login TIMESTAMP,
                failed_attempts INTEGER DEFAULT 0,
                locked_until TIMESTAMP
            )
        ''')
        
        # Sessions table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS sessions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER,
                session_token TEXT UNIQUE NOT NULL,
                expires_at TIMESTAMP NOT NULL,
                ip_address TEXT,
                user_agent TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        ''')
        
        # Audit log table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS audit_log (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER,
                action TEXT NOT NULL,
                resource TEXT,
                details TEXT,
                ip_address TEXT,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        ''')
        
        # Case access permissions
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS case_permissions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER,
                case_id INTEGER,
                permission_level TEXT DEFAULT 'read',
                granted_by INTEGER,
                granted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (id),
                FOREIGN KEY (granted_by) REFERENCES users (id)
            )
        ''')
        
        conn.commit()
        conn.close()
    
    def hash_password(self, password: str) -> tuple:
        """Hash password with salt using bcrypt"""
        salt = bcrypt.gensalt()
        password_hash = bcrypt.hashpw(password.encode('utf-8'), salt)
        return password_hash.decode('utf-8'), salt.decode('utf-8')
    
    def verify_password(self, password: str, password_hash: str, salt: str) -> bool:
        """Verify password against hash"""
        return bcrypt.checkpw(password.encode('utf-8'), password_hash.encode('utf-8'))
    
    def create_user(self, username: str, email: str, password: str, role: str = 'user') -> bool:
        """Create a new user with encrypted password"""
        try:
            password_hash, salt = self.hash_password(password)
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO users (username, email, password_hash, salt, role)
                VALUES (?, ?, ?, ?, ?)
            ''', (username, email, password_hash, salt, role))
            
            conn.commit()
            conn.close()
            
            self.log_audit_event(None, "USER_CREATED", f"user:{username}", 
                               f"New user created with role: {role}")
            return True
            
        except sqlite3.IntegrityError:
            return False
    
    def authenticate_user(self, username: str, password: str, ip_address: str = None) -> Optional[Dict]:
        """Authenticate user and create session"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Check if user exists and is not locked
        cursor.execute('''
            SELECT id, username, email, password_hash, salt, role, is_active, 
                   failed_attempts, locked_until
            FROM users 
            WHERE username = ? OR email = ?
        ''', (username, username))
        
        user = cursor.fetchone()
        
        if not user:
            self.log_audit_event(None, "LOGIN_FAILED", f"user:{username}", 
                               "User not found", ip_address)
            conn.close()
            return None
        
        user_id, username, email, password_hash, salt, role, is_active, failed_attempts, locked_until = user
        
        # Check if account is locked
        if locked_until and datetime.fromisoformat(locked_until) > datetime.now():
            self.log_audit_event(user_id, "LOGIN_BLOCKED", f"user:{username}", 
                               "Account locked", ip_address)
            conn.close()
            return None
        
        # Check if account is active
        if not is_active:
            self.log_audit_event(user_id, "LOGIN_BLOCKED", f"user:{username}", 
                               "Account inactive", ip_address)
            conn.close()
            return None
        
        # Verify password
        if not self.verify_password(password, password_hash, salt):
            # Increment failed attempts
            failed_attempts += 1
            locked_until = None
            
            # Lock account after 5 failed attempts
            if failed_attempts >= 5:
                locked_until = (datetime.now() + timedelta(minutes=30)).isoformat()
            
            cursor.execute('''
                UPDATE users 
                SET failed_attempts = ?, locked_until = ?
                WHERE id = ?
            ''', (failed_attempts, locked_until, user_id))
            
            conn.commit()
            
            self.log_audit_event(user_id, "LOGIN_FAILED", f"user:{username}", 
                               f"Invalid password. Attempts: {failed_attempts}", ip_address)
            conn.close()
            return None
        
        # Reset failed attempts on successful login
        cursor.execute('''
            UPDATE users 
            SET failed_attempts = 0, locked_until = NULL, last_login = CURRENT_TIMESTAMP
            WHERE id = ?
        ''', (user_id,))
        
        # Create session
        session_token = self.create_session(user_id, ip_address)
        
        conn.commit()
        conn.close()
        
        self.log_audit_event(user_id, "LOGIN_SUCCESS", f"user:{username}", 
                           "Successful login", ip_address)
        
        return {
            'user_id': user_id,
            'username': username,
            'email': email,
            'role': role,
            'session_token': session_token
        }
    
    def create_session(self, user_id: int, ip_address: str = None, user_agent: str = None) -> str:
        """Create a new session for authenticated user"""
        session_token = secrets.token_urlsafe(32)
        expires_at = (datetime.now() + timedelta(seconds=SESSION_TIMEOUT)).isoformat()
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT INTO sessions (user_id, session_token, expires_at, ip_address, user_agent)
            VALUES (?, ?, ?, ?, ?)
        ''', (user_id, session_token, expires_at, ip_address, user_agent))
        
        conn.commit()
        conn.close()
        
        return session_token
    
    def validate_session(self, session_token: str) -> Optional[Dict]:
        """Validate session token and return user info"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT s.user_id, s.expires_at, u.username, u.email, u.role, u.is_active
            FROM sessions s
            JOIN users u ON s.user_id = u.id
            WHERE s.session_token = ?
        ''', (session_token,))
        
        session = cursor.fetchone()
        conn.close()
        
        if not session:
            return None
        
        user_id, expires_at, username, email, role, is_active = session
        
        # Check if session is expired
        if datetime.fromisoformat(expires_at) < datetime.now():
            self.invalidate_session(session_token)
            return None
        
        # Check if user is still active
        if not is_active:
            self.invalidate_session(session_token)
            return None
        
        return {
            'user_id': user_id,
            'username': username,
            'email': email,
            'role': role
        }
    
    def invalidate_session(self, session_token: str):
        """Invalidate a session"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('DELETE FROM sessions WHERE session_token = ?', (session_token,))
        
        conn.commit()
        conn.close()
    
    def log_audit_event(self, user_id: Optional[int], action: str, resource: str = None, 
                       details: str = None, ip_address: str = None):
        """Log audit event"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT INTO audit_log (user_id, action, resource, details, ip_address)
            VALUES (?, ?, ?, ?, ?)
        ''', (user_id, action, resource, details, ip_address))
        
        conn.commit()
        conn.close()
    
    def encrypt_sensitive_data(self, data: str) -> str:
        """Encrypt sensitive data"""
        return self.cipher_suite.encrypt(data.encode()).decode()
    
    def decrypt_sensitive_data(self, encrypted_data: str) -> str:
        """Decrypt sensitive data"""
        return self.cipher_suite.decrypt(encrypted_data.encode()).decode()
    
    def check_permission(self, user_id: int, case_id: int, required_permission: str = 'read') -> bool:
        """Check if user has permission to access case"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Check user role first
        cursor.execute('SELECT role FROM users WHERE id = ?', (user_id,))
        user_role = cursor.fetchone()
        
        if user_role and user_role[0] == 'admin':
            conn.close()
            return True
        
        # Check specific case permissions
        cursor.execute('''
            SELECT permission_level FROM case_permissions 
            WHERE user_id = ? AND case_id = ?
        ''', (user_id, case_id))
        
        permission = cursor.fetchone()
        conn.close()
        
        if not permission:
            return False
        
        permission_level = permission[0]
        
        # Permission hierarchy: read < write < admin
        permission_levels = {'read': 1, 'write': 2, 'admin': 3}
        
        return permission_levels.get(permission_level, 0) >= permission_levels.get(required_permission, 0)

# Authentication decorators
def require_auth(func):
    """Decorator to require authentication"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        if 'user' not in st.session_state:
            st.error("🔒 Authentication required")
            st.stop()
        return func(*args, **kwargs)
    return wrapper

def require_role(required_role: str):
    """Decorator to require specific role"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            if 'user' not in st.session_state:
                st.error("🔒 Authentication required")
                st.stop()
            
            user_role = st.session_state.user.get('role', 'user')
            if user_role != required_role and user_role != 'admin':
                st.error(f"🚫 Access denied. Required role: {required_role}")
                st.stop()
            
            return func(*args, **kwargs)
        return wrapper
    return decorator

def require_case_permission(case_id_param: str, permission: str = 'read'):
    """Decorator to require case-specific permission"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            if 'user' not in st.session_state:
                st.error("🔒 Authentication required")
                st.stop()
            
            case_id = kwargs.get(case_id_param) or (args[0] if args else None)
            if not case_id:
                st.error("🚫 Case ID required")
                st.stop()
            
            security_manager = SecurityManager()
            user_id = st.session_state.user['user_id']
            
            if not security_manager.check_permission(user_id, case_id, permission):
                st.error(f"🚫 Access denied. Required permission: {permission}")
                st.stop()
            
            return func(*args, **kwargs)
        return wrapper
    return decorator

# Initialize security manager
security_manager = SecurityManager()
