#!/usr/bin/env python3
"""
Test Runner for Legal Case Management System
Runs comprehensive functionality and usability tests
"""

import subprocess
import sys
import argparse
from pathlib import Path
import time

def run_command(command, description):
    """Run a command and handle errors"""
    print(f"\n🚀 {description}")
    print(f"Command: {' '.join(command)}")
    
    try:
        result = subprocess.run(command, check=True, capture_output=True, text=True)
        print(f"✅ {description} completed successfully")
        if result.stdout:
            print("Output:", result.stdout)
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed")
        print("Error:", e.stderr)
        return False

def install_dependencies():
    """Install test dependencies"""
    dependencies = [
        "pytest",
        "pytest-asyncio", 
        "pytest-html",
        "playwright",
        "requests"
    ]
    
    print("📦 Installing test dependencies...")
    for dep in dependencies:
        command = [sys.executable, "-m", "pip", "install", dep]
        if not run_command(command, f"Installing {dep}"):
            return False
    
    # Install Playwright browsers
    command = [sys.executable, "-m", "playwright", "install", "chromium"]
    return run_command(command, "Installing Playwright browsers")

def run_functionality_tests():
    """Run functionality tests"""
    command = [
        sys.executable, "-m", "pytest", 
        "test_app_playwright.py",
        "-v",
        "--tb=short",
        "--html=reports/functionality_report.html",
        "--self-contained-html",
        "-m", "not usability"
    ]
    return run_command(command, "Running functionality tests")

def run_usability_tests():
    """Run usability tests"""
    command = [
        sys.executable, "-m", "pytest",
        "test_usability_playwright.py", 
        "-v",
        "--tb=short",
        "--html=reports/usability_report.html",
        "--self-contained-html"
    ]
    return run_command(command, "Running usability tests")

def run_all_tests():
    """Run all tests"""
    command = [
        sys.executable, "-m", "pytest",
        "test_app_playwright.py",
        "test_usability_playwright.py",
        "-v",
        "--tb=short", 
        "--html=reports/complete_test_report.html",
        "--self-contained-html"
    ]
    return run_command(command, "Running all tests")

def run_quick_tests():
    """Run quick tests (excluding slow ones)"""
    command = [
        sys.executable, "-m", "pytest",
        "test_app_playwright.py",
        "test_usability_playwright.py",
        "-v",
        "--tb=short",
        "--html=reports/quick_test_report.html", 
        "--self-contained-html",
        "-m", "not slow"
    ]
    return run_command(command, "Running quick tests")

def run_accessibility_tests():
    """Run accessibility-focused tests"""
    command = [
        sys.executable, "-m", "pytest",
        "test_app_playwright.py",
        "test_usability_playwright.py",
        "-v",
        "--tb=short",
        "--html=reports/accessibility_report.html",
        "--self-contained-html", 
        "-m", "accessibility"
    ]
    return run_command(command, "Running accessibility tests")

def create_reports_directory():
    """Create reports directory"""
    reports_dir = Path("reports")
    reports_dir.mkdir(exist_ok=True)
    print(f"📁 Reports directory: {reports_dir.absolute()}")

def main():
    """Main test runner"""
    parser = argparse.ArgumentParser(description="Legal Case Management System Test Runner")
    parser.add_argument("--install-deps", action="store_true", help="Install test dependencies")
    parser.add_argument("--functionality", action="store_true", help="Run functionality tests only")
    parser.add_argument("--usability", action="store_true", help="Run usability tests only")
    parser.add_argument("--accessibility", action="store_true", help="Run accessibility tests only")
    parser.add_argument("--quick", action="store_true", help="Run quick tests (exclude slow tests)")
    parser.add_argument("--all", action="store_true", help="Run all tests")
    
    args = parser.parse_args()
    
    print("⚖️ Legal Case Management System - Test Runner")
    print("=" * 60)
    
    # Change to tests directory
    test_dir = Path(__file__).parent
    original_dir = Path.cwd()
    
    try:
        import os
        os.chdir(test_dir)
        
        # Create reports directory
        create_reports_directory()
        
        # Install dependencies if requested
        if args.install_deps:
            if not install_dependencies():
                print("❌ Failed to install dependencies")
                return 1
        
        # Run tests based on arguments
        success = True
        
        if args.functionality:
            success = run_functionality_tests()
        elif args.usability:
            success = run_usability_tests()
        elif args.accessibility:
            success = run_accessibility_tests()
        elif args.quick:
            success = run_quick_tests()
        elif args.all:
            success = run_all_tests()
        else:
            # Default: run all tests
            print("🎯 Running comprehensive test suite...")
            success = run_all_tests()
        
        # Summary
        print("\n" + "=" * 60)
        if success:
            print("✅ All tests completed successfully!")
            print("📊 Check the reports/ directory for detailed results")
        else:
            print("❌ Some tests failed")
            print("📊 Check the reports/ directory for detailed results")
        
        return 0 if success else 1
        
    finally:
        os.chdir(original_dir)

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
