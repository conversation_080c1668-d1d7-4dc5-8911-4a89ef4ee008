# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# Legacy SQLite files (REMOVED - using PostgreSQL/MongoDB only)
# *.db
# *.sqlite
# *.sqlite3
# legal_case_manager.db

# API Keys and sensitive configuration
.env
.env.local
.env.production
.env.staging
config.json
secrets.json
api_keys.json
*.key
*.pem
*.p12

# Case documents and uploads
case_documents/
uploads/
exports/

# Logs
*.log
logs/

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Temporary files
*.tmp
*.temp
temp/
tmp/

# Streamlit
.streamlit/

# PDF files (case documents)
*.pdf

# HTML files (case documents)
*.html
*_files/

CLAUDE.md


# Security - Never commit these files!
*.pfx
config/secrets.json
security_backups/
*.backup
# Database files
# Logs with potential sensitive data
# PGP keys
*.gpg
*.asc
gnupg/
.gnupg/