#!/usr/bin/env python3
"""
Test script for the Enhanced Docket Reconciliation System
Demonstrates comprehensive reconciliation with inappropriate filing and tampering detection
"""

import json
import os
from datetime import datetime
from core.docket_reconciliation import reconcile_docket_with_downloads

def test_docket_reconciliation():
    """Test the enhanced docket reconciliation system"""
    
    print("🔍 ENHANCED DOCKET RECONCILIATION SYSTEM")
    print("=" * 60)
    print("🎯 Features:")
    print("   • Complete document-to-docket reconciliation")
    print("   • Inappropriate party assignment detection")
    print("   • Missing records and tampering analysis")
    print("   • Cross-source integrity verification")
    print("   • Comprehensive HTML reporting")
    print()
    
    # Check for required data files
    print("📁 Checking for required data files...")
    
    # Look for docket scraping results
    docket_files = [
        "efiling_results_latest.json",
        "efiling_data/efiling_DR-25-403973_latest.json",
        "docket_results.json"
    ]
    
    docket_file = None
    for file_path in docket_files:
        if os.path.exists(file_path):
            docket_file = file_path
            print(f"   ✅ Found docket file: {file_path}")
            break
    
    if not docket_file:
        print("   ❌ No docket results file found")
        print("   📝 Expected files: efiling_results_latest.json or similar")
        print("   💡 Run the scrapers first to generate docket data")
        return
    
    # Look for download directories
    download_dirs = [
        "all_downloads",
        "efiling_downloads", 
        "myfilings_downloads",
        "eservice_downloads"
    ]
    
    download_dir = None
    for dir_path in download_dirs:
        if os.path.exists(dir_path) and os.path.isdir(dir_path):
            download_dir = dir_path
            print(f"   ✅ Found download directory: {dir_path}")
            break
    
    if not download_dir:
        print("   ❌ No download directory found")
        print("   📝 Expected directories: all_downloads, efiling_downloads, etc.")
        print("   💡 Run the scrapers with download enabled first")
        return
    
    print("   ✅ All required data found")
    print()
    
    try:
        print("🚀 Starting comprehensive docket reconciliation...")
        print("   This may take a few minutes for large datasets...")
        print()
        
        # Run the reconciliation
        results = reconcile_docket_with_downloads(
            docket_file=docket_file,
            downloads_dir=download_dir,
            case_number="DR-25-403973",
            output_dir="reconciliation_results"
        )
        
        # Display results
        print("✅ RECONCILIATION COMPLETED!")
        print("=" * 60)
        
        # Show generated reports
        print("📊 Generated Reports:")
        for report_type, file_path in results.items():
            print(f"   📄 {report_type.replace('_', ' ').title()}: {file_path}")
        
        print()
        
        # Load and display summary statistics
        if 'reconciliation_report' in results:
            with open(results['reconciliation_report'], 'r') as f:
                report_data = json.load(f)
            
            stats = report_data.get('statistics', {})
            print("📈 Reconciliation Statistics:")
            print(f"   📋 Total docket entries: {stats.get('total_docket_entries', 0)}")
            print(f"   📄 Entries with PDFs: {stats.get('entries_with_pdfs', 0)}")
            print(f"   ❌ Missing PDFs: {stats.get('entries_without_pdfs', 0)}")
            print(f"   📊 PDF coverage: {stats.get('pdf_coverage_percentage', 0):.1f}%")
            print(f"   📁 Total documents: {stats.get('total_documents', 0)}")
            print(f"   🔗 Matched documents: {stats.get('matched_documents', 0)}")
            print(f"   ⚠️  Unmatched documents: {stats.get('unmatched_documents', 0)}")
            print(f"   📈 Matching efficiency: {stats.get('matching_efficiency', 0):.1f}%")
            print()
            
            # Inappropriate filings summary
            inappropriate_filings = report_data.get('inappropriate_filings', [])
            if inappropriate_filings:
                print("🚨 INAPPROPRIATE PARTY ASSIGNMENTS DETECTED!")
                print(f"   ⚠️  Total issues: {len(inappropriate_filings)}")
                
                high_severity = [f for f in inappropriate_filings if f.get('max_severity') == 'high']
                medium_severity = [f for f in inappropriate_filings if f.get('max_severity') == 'medium']
                
                print(f"   🔴 High severity: {len(high_severity)}")
                print(f"   🟡 Medium severity: {len(medium_severity)}")
                print()
                
                # Show sample high severity issues
                if high_severity:
                    print("🔥 Sample High Priority Issues:")
                    for issue in high_severity[:3]:  # Show first 3
                        print(f"   • {issue['date']} - {issue['document_type']}")
                        print(f"     Party: {issue['party']} | Attorney: {issue['attorney']}")
                        for problem in issue.get('issues', []):
                            print(f"     ⚠️  {problem['issue']}")
                        print()
            
            # Missing records analysis
            missing_analysis = report_data.get('missing_records_analysis', {})
            if missing_analysis:
                summary = missing_analysis.get('summary', {})
                if summary.get('total_issues', 0) > 0:
                    print("🚨 MISSING RECORDS & TAMPERING ANALYSIS!")
                    print(f"   📊 Total integrity issues: {summary['total_issues']}")
                    print(f"   🔴 High priority concerns: {summary['high_priority_issues']}")
                    print(f"   ⚠️  Tampering indicators: {summary['potential_tampering_indicators']}")
                    print()
                    
                    # Break down by issue type
                    issue_types = [
                        ('missing_supporting_documents', 'Missing Supporting Documents'),
                        ('entry_sequence_gaps', 'Docket Entry Sequence Gaps'), 
                        ('suspicious_modifications', 'Suspicious Modifications'),
                        ('court_entries_without_support', 'Court Actions Without Documentation'),
                        ('cross_source_discrepancies', 'Cross-Source Discrepancies'),
                        ('orphaned_downloads', 'Orphaned Downloads')
                    ]
                    
                    print("📋 Issue Breakdown:")
                    for issue_key, issue_name in issue_types:
                        count = len(missing_analysis.get(issue_key, []))
                        if count > 0:
                            print(f"   • {issue_name}: {count}")
                    print()
            
            # Party breakdown
            party_breakdown = report_data.get('party_breakdown', {})
            if party_breakdown:
                print("⚖️  Party Breakdown:")
                for party, stats in party_breakdown.items():
                    coverage = (stats['with_pdf'] / stats['total'] * 100) if stats['total'] > 0 else 0
                    print(f"   {party}: {stats['with_pdf']}/{stats['total']} entries have PDFs ({coverage:.1f}%)")
                print()
        
        print("🎉 ANALYSIS COMPLETE!")
        print(f"📋 Open the HTML report for detailed analysis: {results.get('html_report', 'Not generated')}")
        print()
        print("💡 Key Benefits:")
        print("   • Identifies missing documents that should exist")
        print("   • Detects inappropriate party assignments (e.g., Basta filing as P1)")
        print("   • Flags potential record tampering and corruption")
        print("   • Cross-references all data sources for integrity")
        print("   • Provides comprehensive evidence for legal proceedings")
        
        return results
        
    except Exception as e:
        print(f"💥 Reconciliation failed: {e}")
        import traceback
        traceback.print_exc()
        return None

def main():
    """Main function"""
    print("🔍 Enhanced Docket Reconciliation System Test")
    print("=" * 60)
    
    # Check if reconciliation module is available
    try:
        from core.docket_reconciliation import DocketReconciliationEngine
        print("✅ Docket reconciliation module loaded")
    except ImportError as e:
        print(f"❌ Failed to import reconciliation module: {e}")
        return
    
    # Run the test
    test_docket_reconciliation()

if __name__ == "__main__":
    main()