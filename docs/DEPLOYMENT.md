# CUYAHOGA COUNTY PRO SE Litigation Management System - Deployment Guide

## Overview

This deployment guide covers the containerized deployment of the Legal Case Management System using Docker and Docker Compose.

## Prerequisites

- Docker Engine 20.10+
- Docker Compose 2.0+
- At least 4GB RAM available
- At least 10GB disk space

## Quick Start

1. **Clone the repository and navigate to the project directory**
   ```bash
   cd /path/to/lcm
   ```

2. **Ensure the .env file exists with required credentials**
   ```bash
   ls -la .env
   ```

3. **Deploy the application**
   ```bash
   ./deploy.sh deploy
   ```

4. **Access the application**
   - Web Interface: http://localhost:8501
   - PostgreSQL: localhost:5435
   - MongoDB: localhost:27018
   - Redis: localhost:6382
   - Qdrant: localhost:6334

## Architecture

The system consists of the following services:

### Core Services
- **legal-cms-app**: Main Streamlit application (Port 8501)
- **legal-cms-postgresql**: PostgreSQL database (Port 5435)
- **legal-cms-mongodb**: MongoDB document store (Port 27018)
- **legal-cms-redis**: Redis cache (Port 6382)
- **legal-cms-qdrant**: Vector database (Port 6334)

### Network
- All services run on the `legal-cms-network` bridge network
- Services communicate using container names as hostnames

### Data Persistence
- `postgresql_data`: PostgreSQL data
- `mongodb_data`: MongoDB data
- `redis_data`: Redis data
- `qdrant_data`: Qdrant vector data

## Environment Variables

The following environment variables are required in the `.env` file:

```bash
# Database Credentials
POSTGRES_PASSWORD=SecurePostgresPassword123
MONGODB_PASSWORD=SecureMongoPassword123
REDIS_PASSWORD=SecureRedisPassword123

# PGP Encryption
PGP_MASTER_PASSPHRASE=SecurePGPPassphrase123456789

# API Keys (Optional - add as needed)
# OPENAI_API_KEY=your_openai_key
# ANTHROPIC_API_KEY=your_anthropic_key
```

## Deployment Commands

### Using the deployment script (Recommended)

```bash
# Deploy the application
./deploy.sh deploy

# Show application logs
./deploy.sh logs

# Check container status
./deploy.sh status

# Stop the application
./deploy.sh stop

# Restart the application
./deploy.sh restart
```

### Using Docker Compose directly

```bash
# Deploy
docker compose -f config/docker-compose.yml --env-file .env up -d

# Stop
docker compose -f config/docker-compose.yml --env-file .env down

# View logs
docker compose -f config/docker-compose.yml logs -f legal-cms-app

# Check status
docker compose -f config/docker-compose.yml ps
```

## Troubleshooting

### Common Issues

1. **Port conflicts**
   - Ensure ports 8501, 5435, 27018, 6382, 6334 are not in use
   - Modify port mappings in docker-compose.yml if needed

2. **Permission issues**
   - Ensure the deploy.sh script is executable: `chmod +x deploy.sh`
   - Check Docker daemon permissions

3. **Memory issues**
   - Ensure at least 4GB RAM is available
   - Monitor container resource usage: `docker stats`

4. **Environment variables not loading**
   - Verify .env file exists and has correct format
   - Check for special characters in passwords

### Viewing Logs

```bash
# All services
docker compose -f config/docker-compose.yml logs

# Specific service
docker logs legal-cms-app
docker logs legal-cms-postgresql
docker logs legal-cms-mongodb
docker logs legal-cms-redis
docker logs legal-cms-qdrant
```

### Health Checks

The application includes health checks for all services. Check status with:
```bash
docker compose -f config/docker-compose.yml ps
```

## Security Considerations

1. **Change default passwords** in the .env file
2. **Use strong passwords** for all database services
3. **Restrict network access** in production environments
4. **Enable SSL/TLS** for production deployments
5. **Regular backups** of persistent volumes

## Backup and Recovery

### Backup Data Volumes
```bash
# Create backup directory
mkdir -p backups/$(date +%Y%m%d)

# Backup PostgreSQL
docker exec legal-cms-postgresql pg_dumpall -U postgres > backups/$(date +%Y%m%d)/postgres_backup.sql

# Backup MongoDB
docker exec legal-cms-mongodb mongodump --out /tmp/backup
docker cp legal-cms-mongodb:/tmp/backup backups/$(date +%Y%m%d)/mongodb_backup
```

### Restore Data
```bash
# Restore PostgreSQL
docker exec -i legal-cms-postgresql psql -U postgres < backups/YYYYMMDD/postgres_backup.sql

# Restore MongoDB
docker cp backups/YYYYMMDD/mongodb_backup legal-cms-mongodb:/tmp/restore
docker exec legal-cms-mongodb mongorestore /tmp/restore
```

## Monitoring

Monitor the application using:
```bash
# Resource usage
docker stats

# Container health
docker compose -f config/docker-compose.yml ps

# Application logs
./deploy.sh logs
```

## Support

For issues and support:
1. Check the troubleshooting section above
2. Review container logs for error messages
3. Ensure all prerequisites are met
4. Verify environment configuration
