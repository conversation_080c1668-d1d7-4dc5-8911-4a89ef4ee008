# Container Naming Standards

**OFFICIAL CONTAINER NAMING CONVENTION**: `legal-cms-[service]`

## ✅ STANDARDIZED CONTAINERS:

All containers MUST follow the pattern: `legal-cms-[service]`

### Core Database Services:
- `legal-cms-postgresql` - Primary relational database (port 5435)
- `legal-cms-mongodb` - Document storage database (port 27018)  
- `legal-cms-redis` - Cache and session store (port 6382)
- `legal-cms-qdrant` - Vector database for embeddings (port 6334)

### Infrastructure Services:
- `legal-cms-nginx` - HTTPS reverse proxy (ports 80, 443)
- `legal-cms-vault` - HashiCorp Vault secrets management (port 8200)

### Support Services:
- `legal-cms-app` - Main Streamlit application (when containerized)

## 🚨 DEPRECATED PATTERNS:

**NEVER USE THESE PATTERNS AGAIN:**
- ❌ `lcm-[service]` - Too abbreviated
- ❌ `LCM-[service]` - Inconsistent casing  
- ❌ `[service]-lcm` - Wrong order
- ❌ Mixed naming patterns

## 📋 CURRENT STATUS:

✅ **COMPLIANT CONTAINERS:**
- legal-cms-postgresql ✅
- legal-cms-mongodb ✅
- legal-cms-redis ✅
- legal-cms-qdrant ✅
- legal-cms-nginx ✅

## 🔧 DOCKER COMPOSE STANDARDS:

All services in docker-compose.yml use:
```yaml
services:
  legal-cms-[service]:
    container_name: legal-cms-[service]
    # ... rest of config
```

## 🌐 NETWORK STANDARDS:

All containers connect to: `legal-cms-network`

## 📦 VOLUME STANDARDS:

All persistent volumes use: `[service]_data`
- postgresql_data
- mongodb_data  
- redis_data
- qdrant_data

## ⚠️ ENFORCEMENT:

This naming convention is FINAL and must be maintained across:
- Docker containers
- Docker Compose services
- Configuration files
- Documentation
- Scripts
- Connection strings

**NO MORE CHANGES TO NAMING PATTERNS!**