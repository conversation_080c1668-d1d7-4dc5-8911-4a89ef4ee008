# 🔒 CUYAHOGA COUNTY PRO SE LITIGATION MANAGEMENT SYSTEM
## Security Architecture Migration Plan

### 🎯 Migration Overview
**FROM**: SQLite + File System + Basic Encryption  
**TO**: MongoDB + PostgreSQL + Redis + PGP Encryption + Vault VPS + Private GitHub

---

## 🏗️ NEW ARCHITECTURE DESIGN

### 1. **Database Layer Architecture**

#### **PostgreSQL (Primary Relational Data)**
```sql
-- Core relational entities
DATABASES:
├── legal_cms_main          -- Primary database
│   ├── users               -- User accounts & authentication
│   ├── cases               -- Case metadata & relationships
│   ├── sessions            -- Active user sessions
│   ├── audit_logs          -- Security & action logging
│   ├── permissions         -- Role-based access control
│   └── system_config       -- Application configuration

-- Connection Pool: 20 connections
-- SSL: Required (TLS 1.3)
-- Backup: Daily automated with encryption
```

#### **MongoDB (Document Storage & Metadata)**
```javascript
// Document-oriented data
COLLECTIONS:
├── documents {             // Document metadata & encrypted refs
│   _id: ObjectId,
│   case_id: String,
│   filename: String,
│   encrypted_content_ref: String,  // GitHub repo reference
│   pgp_fingerprint: String,
│   metadata: {
│     file_type: String,
│     file_size: Number,
│     upload_date: Date,
│     ocr_text_encrypted: String,
│     content_hash: String
│   },
│   encryption_info: {
│     algorithm: "PGP",
│     key_id: String,
│     vault_key_ref: String
│   }
│ }
├── chat_history {          // Encrypted chat conversations
│   _id: ObjectId,
│   case_id: String,
│   messages: [encrypted_message],
│   session_id: String,
│   created_at: Date
│ }
├── document_vectors {      // Qdrant integration metadata
│   _id: ObjectId,
│   document_id: String,
│   vector_collection: String,
│   embedding_model: String,
│   last_indexed: Date
│ }
└── case_analytics {        // Case analysis & insights
    _id: ObjectId,
    case_id: String,
    analytics_data: Object,  // Encrypted
    generated_at: Date
  }

// Replica Set: 3 nodes
// Authentication: SCRAM-SHA-256
// Encryption: At-rest + in-transit
```

#### **Redis (Caching & Session Management)**
```redis
# Cache structure
KEYS:
├── session:{token}         -- User session data (TTL: 1h)
├── case:{id}:cache         -- Case data cache (TTL: 30m)
├── user:{id}:permissions   -- User permissions cache (TTL: 15m)
├── document:{id}:metadata  -- Document metadata cache (TTL: 1h)
├── pgp:keys:{fingerprint}  -- PGP key cache (TTL: 24h)
└── vault:tokens:{user_id}  -- Vault access tokens (TTL: 15m)

# Configuration
├── Persistence: RDB + AOF
├── SSL/TLS: Required
├── AUTH: Password + ACL
└── Memory: 4GB allocated
```

### 2. **PGP Encryption System**

#### **Key Management Architecture**
```
PGP KEY HIERARCHY:
├── Master Key Pair (4096-bit RSA)
│   ├── Stored in Vault VPS (HSM-backed)
│   ├── Used for signing sub-keys
│   └── Offline backup in secure location
├── Document Encryption Keys (2048-bit RSA)
│   ├── Per-case key pairs
│   ├── Rotated every 90 days
│   └── Stored in Vault with access policies
└── User Keys (2048-bit RSA)
    ├── Individual user key pairs
    ├── Used for document access
    └── Managed through Vault API
```

#### **Encryption Workflow**
```python
# Document encryption process
def encrypt_document(file_content: bytes, case_id: str, user_id: str) -> dict:
    """
    1. Generate symmetric AES-256 key for file
    2. Encrypt file with AES key
    3. Encrypt AES key with case PGP public key
    4. Store encrypted file in private GitHub repo
    5. Store metadata in MongoDB with encryption references
    """
    pass
```

### 3. **Vault VPS Architecture**

#### **HashiCorp Vault Configuration**
```hcl
# vault.hcl
storage "postgresql" {
  connection_url = "postgres://vault:password@localhost/vault"
}

listener "tcp" {
  address     = "0.0.0.0:8200"
  tls_cert_file = "/etc/vault/tls/vault.crt"
  tls_key_file  = "/etc/vault/tls/vault.key"
}

seal "pgp" {
  keys = ["vault-key-1", "vault-key-2", "vault-key-3"]
  threshold = 2
}

api_addr = "https://vault.legal-cms.internal:8200"
cluster_addr = "https://vault.legal-cms.internal:8201"
```

#### **Secret Engines & Policies**
```bash
# Enable secret engines
vault secrets enable -path=legal-cms kv-v2
vault secrets enable -path=pgp-keys kv-v2
vault secrets enable -path=api-keys kv-v2

# Authentication methods
vault auth enable userpass
vault auth enable approle  # For applications

# Policies
vault policy write legal-cms-admin - <<EOF
path "legal-cms/*" {
  capabilities = ["create", "read", "update", "delete", "list"]
}
path "pgp-keys/*" {
  capabilities = ["create", "read", "update", "delete", "list"]
}
EOF
```

### 4. **Private GitHub Integration**

#### **Repository Structure**
```
PRIVATE REPOSITORIES:
├── legal-cms-documents-{case_id}
│   ├── encrypted/
│   │   ├── {document_id}.pgp     -- Encrypted documents
│   │   └── metadata.json.pgp     -- Encrypted metadata
│   ├── checksums/
│   │   └── {document_id}.sha256  -- File integrity checks
│   └── README.md                 -- Repository info (encrypted)
└── legal-cms-backups
    ├── database-backups/
    ├── vault-backups/
    └── system-configs/
```

#### **GitHub API Integration**
```python
class SecureGitHubStorage:
    """Secure document storage in private GitHub repositories"""
    
    def __init__(self, vault_client, pgp_manager):
        self.vault = vault_client
        self.pgp = pgp_manager
        self.github_token = vault_client.read('api-keys/github')['data']['token']
    
    def store_document(self, document: bytes, case_id: str, doc_id: str):
        """
        1. Encrypt document with PGP
        2. Create/update private repo for case
        3. Upload encrypted document
        4. Update MongoDB with GitHub reference
        """
        pass
```

---

## 🔄 MIGRATION PHASES

### **Phase 1: Infrastructure Setup** (Week 1-2)
- [ ] Deploy PostgreSQL cluster with SSL
- [ ] Deploy MongoDB replica set with encryption
- [ ] Deploy Redis cluster with authentication
- [ ] Set up Vault VPS with HSM backing
- [ ] Configure private GitHub repositories

### **Phase 2: PGP System Implementation** (Week 2-3)
- [ ] Implement PGP key generation and management
- [ ] Create document encryption/decryption services
- [ ] Integrate with Vault for key storage
- [ ] Build key rotation mechanisms

### **Phase 3: Data Migration** (Week 3-4)
- [ ] Create migration scripts for existing data
- [ ] Encrypt and migrate documents to GitHub
- [ ] Migrate relational data to PostgreSQL
- [ ] Migrate document metadata to MongoDB
- [ ] Validate data integrity

### **Phase 4: Application Updates** (Week 4-5)
- [ ] Update database connection layers
- [ ] Implement new security middleware
- [ ] Update document upload/retrieval flows
- [ ] Integrate Vault authentication

### **Phase 5: Testing & Validation** (Week 5-6)
- [ ] Security penetration testing
- [ ] Performance benchmarking
- [ ] Data integrity validation
- [ ] Disaster recovery testing

---

## 🛡️ SECURITY ENHANCEMENTS

### **Encryption Standards**
- **At Rest**: AES-256 + PGP-4096
- **In Transit**: TLS 1.3 + Certificate Pinning
- **Key Management**: HSM-backed Vault
- **Document Storage**: PGP encrypted in private repos

### **Access Control**
- **Multi-Factor Authentication**: Required for all users
- **Role-Based Access**: Granular permissions per case
- **API Security**: OAuth 2.0 + JWT with short expiry
- **Network Security**: VPN + IP whitelisting

### **Audit & Compliance**
- **Complete Audit Trail**: All actions logged immutably
- **Data Retention**: Configurable per jurisdiction
- **Compliance**: GDPR, HIPAA, SOX ready
- **Incident Response**: Automated alerting & response

---

## 📊 PERFORMANCE TARGETS

| Metric | Current | Target | Improvement |
|--------|---------|--------|-------------|
| Document Upload | 5s | 2s | 60% faster |
| Search Response | 3s | 0.5s | 83% faster |
| Page Load | 2s | 0.8s | 60% faster |
| Concurrent Users | 10 | 100 | 10x scale |
| Data Security | Basic | Military-grade | ∞ improvement |

---

## 💰 INFRASTRUCTURE COSTS (Monthly)

| Component | Specification | Cost |
|-----------|---------------|------|
| PostgreSQL | 4 vCPU, 16GB RAM, 500GB SSD | $200 |
| MongoDB | 3-node replica, 8GB RAM each | $300 |
| Redis | 2 vCPU, 8GB RAM, HA setup | $150 |
| Vault VPS | 2 vCPU, 4GB RAM, HSM | $250 |
| GitHub Private | Unlimited repos, 5 users | $25 |
| **TOTAL** | | **$925/month** |

---

## 🚀 NEXT STEPS

1. **Review and approve this migration plan**
2. **Provision infrastructure components**
3. **Begin Phase 1 implementation**
4. **Set up monitoring and alerting**
5. **Create rollback procedures**

---

*This migration will transform the Legal Case Management System into a military-grade secure platform suitable for handling the most sensitive legal documents and case information.*
