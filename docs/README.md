# Legal Case Management System

A comprehensive AI-powered legal case management system built with Streamlit, featuring multi-agent legal analysis, document processing, and case organization.

## Features

### 🤖 AI-Powered Legal Team
- **Legal Researcher**: Finds and cites relevant legal cases and precedents
- **Contract Analyst**: Reviews documents and identifies key terms and issues
- **Criminal Defense Attorney**: Analyzes criminal charges, defenses, and trial strategies
- **Procedural Violation Specialist**: Identifies constitutional and procedural violations
- **Family Law Attorney**: Handles divorce, custody, and domestic relations matters
- **State Prosecutor**: Provides prosecution perspective and evidence analysis
- **Attorney General Counsel**: Constitutional law and state government legal affairs
- **Legal Strategist**: Develops comprehensive legal strategies and recommendations
- **Team Coordinator**: Manages comprehensive analysis across all legal specialists

### 📚 Document Management
- Upload single PDFs, multiple files, or ZIP archives
- Automatic OCR processing for searchable text
- Document deduplication using file hashes
- Timeline visualization of case events
- Full-text search across all documents

### 💬 Interactive Analysis
- Chat interface with specialized legal agents
- Multiple analysis types: General Discussion, Document Review, Legal Research, Case Strategy, Criminal Defense Analysis, Procedural Violations Review, Family Law Matters, Prosecution Perspective, Constitutional Law Analysis, Risk Assessment, Compliance Check
- Persistent chat history with session management
- Context-aware responses using previous discussions

### 📊 Advanced Features
- **Timeline Visualization**: Interactive charts showing case progression
- **OCR Document Search**: Search through processed document text
- **Export Capabilities**: Generate Word reports, JSON backups, database exports
- **Case Statistics**: Track documents, messages, and OCR processing status

## Requirements

### Hardware Recommendations
- **GPU**: GTX 780 (2GB VRAM) or better
- **RAM**: 8GB+ recommended
- **Storage**: 5GB+ for models and documents

### Software Dependencies
- Python 3.10+
- Docker (for Qdrant vector database)
- Ollama (for local LLM models)
- Tesseract OCR (for document processing)

## Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd lcm
   ```

2. **Install Python dependencies**
   ```bash
   pip install -r requirements.txt
   ```

3. **Start Qdrant vector database**
   ```bash
   docker run -d --name qdrant-legal -p 6333:6333 qdrant/qdrant
   ```

4. **Install and configure Ollama models**
   ```bash
   # Install Ollama from https://ollama.ai
   ollama pull llama3.2:3b          # Recommended for tool support
   ollama pull phi3:mini            # Alternative with tool support
   ollama pull nomic-embed-text     # For embeddings
   ```

5. **Install Tesseract OCR**
   - **Linux**: `sudo apt-get install tesseract-ocr`
   - **macOS**: `brew install tesseract`
   - **Windows**: Download from [GitHub](https://github.com/UB-Mannheim/tesseract/wiki)

## Usage

1. **Start the application**
   ```bash
   streamlit run legal_case_manager.py
   ```

2. **Access the web interface**
   - Open http://localhost:8501 in your browser

3. **Create a case**
   - Use the sidebar to create a new case
   - Add case name, number, and description

4. **Upload documents**
   - Upload PDF files individually, in bulk, or as ZIP archives
   - Documents are automatically processed with OCR

5. **Initialize legal team**
   - Select an appropriate model (llama3.2:3b recommended)
   - Click "Initialize Legal Team" to set up AI agents

6. **Start analysis**
   - Use the chat interface to ask questions
   - Select analysis type for specialized responses
   - Export results as needed

## Model Compatibility

### ✅ Recommended Models (Tool Support)
- **llama3.2:3b**: Best balance of performance and capability
- **phi3:mini**: Alternative option with good performance

### ⚠️ Limited Models (No Tool Support)
- **gemma2:2b**: Fast but limited functionality
- **qwen3:0.6b**: Fastest but basic responses only

## Architecture

- **Frontend**: Streamlit web interface
- **Database**: SQLite for case and chat storage
- **Vector DB**: Qdrant for document embeddings
- **AI Framework**: Agno for multi-agent coordination
- **LLM**: Ollama for local model inference
- **OCR**: Tesseract + PyMuPDF for document processing

## File Structure

```
lcm/
├── legal_case_manager.py    # Main application
├── requirements.txt         # Python dependencies
├── README.md               # This file
├── .gitignore             # Git ignore rules
├── case_documents/        # Uploaded documents (ignored)
├── legal_cases.db        # SQLite database (ignored)
└── exports/              # Generated reports (ignored)
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is licensed under the MIT License.

## Support

For issues and questions:
1. Check the GitHub issues
2. Review the documentation
3. Create a new issue with detailed information

---

**Note**: This system is designed for legal document analysis and case management. Always verify AI-generated legal advice with qualified legal professionals.
