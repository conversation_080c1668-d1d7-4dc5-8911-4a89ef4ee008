# 🔧 CODEBASE REFACTORING PLAN
## Security Hardening & Code Cleanup

### 🚨 **PHASE 1: CRITICAL SECURITY FIXES (IMMEDIATE)**

#### **1.1 Remove Hardcoded Credentials**
```bash
# Files to fix:
- secure_app.py (lines 835-849)
- config/database.json (review all passwords)
```

**Actions:**
- [ ] Move all credentials to environment variables
- [ ] Enable SSL/TLS for all database connections
- [ ] Implement proper secret management
- [ ] Add credential rotation mechanism

#### **1.2 Fix Database Security**
```python
# Current (INSECURE):
'sslmode': 'disable'
'password': 'cDBGCyHKzc6F'

# Target (SECURE):
'sslmode': 'require'
'password': os.getenv('POSTGRES_PASSWORD')  # No fallback!
```

#### **1.3 PGP Implementation Review**
- [ ] Fix key signing functionality
- [ ] Implement proper key validation
- [ ] Add key rotation mechanism
- [ ] Test encryption/decryption workflows

### 🗑️ **PHASE 2: REMOVE DUPLICATE/UNUSED CODE**

#### **2.1 Database Layer Consolidation**
```bash
# Remove:
- core/database.py (486 lines) ❌
- Global db_manager instance ❌

# Keep:
- core/database_managers.py ✅
- DatabaseManagerFactory pattern ✅
```

#### **2.2 Test Infrastructure Cleanup**
```bash
# Remove:
- tests/test_app_playwright.py ❌ (broken async)
- tests/run_tests.py ❌ (old runner)
- tests/test_usability_playwright.py ❌ (broken)

# Keep:
- tests/test_working_functionality.py ✅
- tests/run_simple_tests.py ✅
- tests/test_diagnostic.py ✅ (for debugging)
```

#### **2.3 Utility Code Cleanup**
```bash
# Remove:
- core/c2p.py ❌ (duplicate functionality)
- core/OUT.put ❌ (generated file)
- fix_database_errors.py ❌ (one-time utility)
- test_database_connections.py ❌ (integrate into main tests)
```

#### **2.4 Incomplete Features**
```bash
# Remove (incomplete/unused):
- core/forensic_extension.py ❌ (578 lines, incomplete)
- core/error_recovery.py ❌ (582 lines, partial)
- core/queue_manager.py ❌ (546 lines, not integrated)
- security/auth.py ❌ (conflicts with new architecture)
```

### 🏗️ **PHASE 3: ARCHITECTURE IMPROVEMENTS**

#### **3.1 Fix Import Dependencies**
```python
# Fix in core/document_processor.py:
from core.database import db_manager  # ❌ OLD
from core.database_managers import DatabaseManagerFactory  # ✅ NEW

# Fix in main.py:
'legal_case_manager.py'  # ❌ DOESN'T EXIST
'secure_app.py'          # ✅ CORRECT
```

#### **3.2 Consolidate Configuration**
```bash
# Current (scattered):
- config/database.json
- secure_app.py (hardcoded configs)
- Multiple .env references

# Target (centralized):
- config/app_config.py (single source)
- Environment variable validation
- Configuration schema validation
```

#### **3.3 Security Enforcement**
```python
# Implement in all modules:
- Mandatory encryption for all data
- Input validation and sanitization
- Proper error handling without info leakage
- Audit logging for all operations
```

### 📊 **PHASE 4: PERFORMANCE OPTIMIZATION**

#### **4.1 Database Optimization**
- [ ] Connection pooling optimization
- [ ] Query optimization and indexing
- [ ] Caching strategy implementation
- [ ] Connection leak prevention

#### **4.2 Memory Management**
- [ ] Large file processing optimization
- [ ] Memory leak detection and fixes
- [ ] Garbage collection optimization
- [ ] Resource cleanup automation

### 🧪 **PHASE 5: TESTING & VALIDATION**

#### **5.1 Security Testing**
```bash
# Implement:
- Penetration testing automation
- Vulnerability scanning
- Encryption validation tests
- Authentication/authorization tests
```

#### **5.2 Performance Testing**
```bash
# Implement:
- Load testing for database operations
- Memory usage profiling
- Response time benchmarking
- Scalability testing
```

### 📈 **EXPECTED OUTCOMES**

#### **Code Reduction:**
- **Remove ~3,000 lines** of duplicate/unused code
- **Consolidate 15+ files** into 8 core modules
- **Eliminate 5 security vulnerabilities**

#### **Performance Improvements:**
- **50% faster startup** (less code to load)
- **30% memory reduction** (no duplicate managers)
- **100% secure connections** (no plaintext)

#### **Maintainability:**
- **Single source of truth** for each functionality
- **Clear separation of concerns**
- **Comprehensive test coverage**
- **Automated security validation**

### 🎯 **IMPLEMENTATION PRIORITY**

1. **🚨 CRITICAL (Day 1)**: Fix hardcoded credentials
2. **🔒 HIGH (Day 2)**: Enable SSL/TLS everywhere  
3. **🗑️ MEDIUM (Week 1)**: Remove duplicate code
4. **🏗️ LOW (Week 2)**: Architecture improvements
5. **🧪 ONGOING**: Testing and validation

### 📋 **SUCCESS CRITERIA**

- [ ] Zero hardcoded credentials
- [ ] All connections encrypted
- [ ] No duplicate functionality
- [ ] All tests passing
- [ ] Performance benchmarks met
- [ ] Security audit passed
- [ ] Documentation updated
- [ ] Deployment automated
