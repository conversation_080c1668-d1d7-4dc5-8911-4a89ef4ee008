#!/usr/bin/env python3
"""
Migration Script: SQLite to Secure Multi-Database Architecture
Migrates existing Legal Case Management System data to the new secure architecture.
"""

import os
import sys
import json
import sqlite3
import logging
import hashlib
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Tuple
import argparse
import getpass

# Add project root to path
sys.path.insert(0, str(Path(__file__).parent.parent))

# Import new architecture components
from core.database_managers import DatabaseManagerFactory, load_database_config
from security.pgp_manager import create_pgp_manager
from integrations.github_storage import create_github_storage

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('migration.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class SecureMigration:
    """Handles migration from SQLite to secure multi-database architecture"""
    
    def __init__(self, config_path: str = "config/database.json"):
        self.config = load_database_config(config_path)
        self.managers = {}
        self.pgp_manager = None
        self.doc_encryption = None
        self.github_storage = None
        self.migration_stats = {
            'users': 0,
            'cases': 0,
            'documents': 0,
            'chat_history': 0,
            'errors': 0,
            'start_time': None,
            'end_time': None
        }
    
    def initialize_new_architecture(self, vault_token: str = None, github_token: str = None):
        """Initialize new database managers and security components"""
        logger.info("Initializing new secure architecture...")
        
        try:
            # Initialize database managers
            self.managers = DatabaseManagerFactory.create_managers(self.config)
            logger.info(f"Initialized {len(self.managers)} database managers")
            
            # Initialize PGP manager
            vault_url = self.config.get('vault', {}).get('url')
            self.pgp_manager, self.doc_encryption = create_pgp_manager(vault_url, vault_token)
            logger.info("PGP encryption system initialized")
            
            # Initialize GitHub storage
            if github_token:
                organization = self.config.get('github', {}).get('organization', 'legal-cms-secure')
                self.github_storage = create_github_storage(github_token, organization, vault_token)
                logger.info("GitHub storage initialized")
            
        except Exception as e:
            logger.error(f"Failed to initialize new architecture: {e}")
            raise
    
    def migrate_users(self, sqlite_db_path: str) -> int:
        """Migrate users from SQLite to PostgreSQL"""
        logger.info("Migrating users...")
        
        if 'postgresql' not in self.managers:
            logger.warning("PostgreSQL not available, skipping user migration")
            return 0
        
        pg_manager = self.managers['postgresql']
        migrated_count = 0
        
        try:
            # Connect to SQLite
            sqlite_conn = sqlite3.connect(sqlite_db_path)
            sqlite_conn.row_factory = sqlite3.Row
            cursor = sqlite_conn.cursor()
            
            # Get users from SQLite (if users table exists)
            try:
                cursor.execute("SELECT * FROM users")
                users = cursor.fetchall()
            except sqlite3.OperationalError:
                logger.info("No users table found in SQLite, creating default admin user")
                users = []
            
            # Migrate each user
            for user in users:
                try:
                    # Insert user into PostgreSQL
                    query = """
                        INSERT INTO users (username, email, password_hash, salt, role, is_active, 
                                         created_at, last_login, failed_attempts, mfa_enabled)
                        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                        ON CONFLICT (username) DO UPDATE SET
                            email = EXCLUDED.email,
                            password_hash = EXCLUDED.password_hash,
                            salt = EXCLUDED.salt,
                            role = EXCLUDED.role,
                            is_active = EXCLUDED.is_active
                    """
                    
                    params = (
                        user['username'],
                        user.get('email', f"{user['username']}@legal-cms.local"),
                        user.get('password_hash', ''),
                        user.get('salt', ''),
                        user.get('role', 'user'),
                        bool(user.get('is_active', 1)),
                        user.get('created_at', datetime.now().isoformat()),
                        user.get('last_login'),
                        user.get('failed_attempts', 0),
                        False  # MFA disabled by default
                    )
                    
                    pg_manager.execute_query(query, params)
                    migrated_count += 1
                    logger.debug(f"Migrated user: {user['username']}")
                    
                except Exception as e:
                    logger.error(f"Failed to migrate user {user.get('username', 'unknown')}: {e}")
                    self.migration_stats['errors'] += 1
            
            # Create default admin user if no users exist
            if migrated_count == 0:
                admin_password = getpass.getpass("Enter password for default admin user: ")
                from security.auth import SecurityManager
                
                # Create temporary security manager to hash password
                temp_security = SecurityManager()
                password_hash, salt = temp_security.hash_password(admin_password)
                
                query = """
                    INSERT INTO users (username, email, password_hash, salt, role, is_active, mfa_enabled)
                    VALUES (%s, %s, %s, %s, %s, %s, %s)
                    ON CONFLICT (username) DO NOTHING
                """
                
                params = (
                    'admin',
                    '<EMAIL>',
                    password_hash,
                    salt,
                    'admin',
                    True,
                    False
                )
                
                pg_manager.execute_query(query, params)
                migrated_count = 1
                logger.info("Created default admin user")
            
            sqlite_conn.close()
            logger.info(f"Migrated {migrated_count} users")
            return migrated_count
            
        except Exception as e:
            logger.error(f"User migration failed: {e}")
            self.migration_stats['errors'] += 1
            return 0
    
    def migrate_cases(self, sqlite_db_path: str) -> int:
        """Migrate cases from SQLite to PostgreSQL"""
        logger.info("Migrating cases...")
        
        if 'postgresql' not in self.managers:
            logger.warning("PostgreSQL not available, skipping case migration")
            return 0
        
        pg_manager = self.managers['postgresql']
        migrated_count = 0
        
        try:
            sqlite_conn = sqlite3.connect(sqlite_db_path)
            sqlite_conn.row_factory = sqlite3.Row
            cursor = sqlite_conn.cursor()
            
            # Get cases from SQLite
            cursor.execute("SELECT * FROM cases")
            cases = cursor.fetchall()
            
            for case in cases:
                try:
                    # Encrypt sensitive case metadata
                    encrypted_metadata = None
                    if case.get('description'):
                        metadata = {
                            'description': case['description'],
                            'migrated_from': 'sqlite',
                            'migration_date': datetime.now().isoformat()
                        }
                        encrypted_metadata = json.dumps(metadata)
                    
                    query = """
                        INSERT INTO cases (name, case_number, status, created_at, updated_at,
                                         created_by, client_name, opposing_party, court_name,
                                         case_type, priority, encrypted_metadata)
                        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                        ON CONFLICT (case_number) DO UPDATE SET
                            name = EXCLUDED.name,
                            status = EXCLUDED.status,
                            updated_at = EXCLUDED.updated_at
                    """
                    
                    params = (
                        case['name'],
                        case.get('case_number'),
                        case.get('status', 'active'),
                        case.get('created_at', datetime.now().isoformat()),
                        case.get('updated_at', datetime.now().isoformat()),
                        case.get('created_by', 1),  # Default to admin user
                        case.get('client_name'),
                        case.get('opposing_party'),
                        case.get('court_name'),
                        case.get('case_type', 'general'),
                        case.get('priority', 'medium'),
                        encrypted_metadata
                    )
                    
                    pg_manager.execute_query(query, params)
                    migrated_count += 1
                    logger.debug(f"Migrated case: {case['name']}")
                    
                    # Create GitHub repository for case if GitHub storage is available
                    if self.github_storage:
                        try:
                            repo_info = self.github_storage.create_case_repository(
                                str(case['id']), case['name']
                            )
                            logger.debug(f"Created GitHub repo for case {case['id']}: {repo_info['repo_name']}")
                        except Exception as e:
                            logger.warning(f"Failed to create GitHub repo for case {case['id']}: {e}")
                    
                except Exception as e:
                    logger.error(f"Failed to migrate case {case.get('name', 'unknown')}: {e}")
                    self.migration_stats['errors'] += 1
            
            sqlite_conn.close()
            logger.info(f"Migrated {migrated_count} cases")
            return migrated_count
            
        except Exception as e:
            logger.error(f"Case migration failed: {e}")
            self.migration_stats['errors'] += 1
            return 0
    
    def migrate_documents(self, sqlite_db_path: str, documents_dir: str = "case_documents") -> int:
        """Migrate documents from file system to encrypted GitHub storage"""
        logger.info("Migrating documents...")
        
        if not self.github_storage or 'mongodb' not in self.managers:
            logger.warning("GitHub storage or MongoDB not available, skipping document migration")
            return 0
        
        mongo_manager = self.managers['mongodb']
        documents_collection = mongo_manager.get_collection('documents')
        migrated_count = 0
        
        try:
            sqlite_conn = sqlite3.connect(sqlite_db_path)
            sqlite_conn.row_factory = sqlite3.Row
            cursor = sqlite_conn.cursor()
            
            # Get documents from SQLite
            cursor.execute("SELECT * FROM documents")
            documents = cursor.fetchall()
            
            for doc in documents:
                try:
                    # Find document file
                    case_dir = Path(documents_dir) / f"case_{doc['case_id']}"
                    doc_path = case_dir / doc['filename']
                    
                    if not doc_path.exists():
                        logger.warning(f"Document file not found: {doc_path}")
                        continue
                    
                    # Read document content
                    with open(doc_path, 'rb') as f:
                        content = f.read()
                    
                    # Prepare metadata
                    metadata = {
                        'original_filename': doc.get('original_filename', doc['filename']),
                        'file_type': doc.get('file_type'),
                        'file_size': doc.get('file_size', len(content)),
                        'uploaded_by': doc.get('uploaded_by'),
                        'upload_date': doc.get('upload_date', datetime.now().isoformat()),
                        'document_type': doc.get('document_type', 'general'),
                        'ocr_text': doc.get('ocr_text'),
                        'content_preview': doc.get('content_preview'),
                        'migrated_from': 'sqlite',
                        'migration_date': datetime.now().isoformat()
                    }
                    
                    # Store in GitHub with encryption
                    storage_result = self.github_storage.store_document(
                        str(doc['case_id']),
                        str(doc['id']),
                        doc['filename'],
                        content,
                        metadata
                    )
                    
                    # Store metadata in MongoDB
                    mongo_doc = {
                        '_id': str(doc['id']),
                        'case_id': str(doc['case_id']),
                        'filename': doc['filename'],
                        'encrypted_content_ref': storage_result['document_path'],
                        'pgp_fingerprint': storage_result['pgp_fingerprint'],
                        'metadata': metadata,
                        'encryption_info': {
                            'algorithm': 'AES-256-CBC + PGP-RSA',
                            'key_id': storage_result['pgp_fingerprint'],
                            'vault_key_ref': f"case-key-{doc['case_id']}"
                        },
                        'github_info': {
                            'repository': storage_result['repository'],
                            'document_path': storage_result['document_path'],
                            'checksum_path': storage_result['checksum_path'],
                            'content_hash': storage_result['content_hash']
                        },
                        'created_at': datetime.now(),
                        'file_size': storage_result['file_size'],
                        'encrypted_size': storage_result['encrypted_size']
                    }
                    
                    documents_collection.insert_one(mongo_doc)
                    migrated_count += 1
                    logger.debug(f"Migrated document: {doc['filename']}")
                    
                except Exception as e:
                    logger.error(f"Failed to migrate document {doc.get('filename', 'unknown')}: {e}")
                    self.migration_stats['errors'] += 1
            
            sqlite_conn.close()
            logger.info(f"Migrated {migrated_count} documents")
            return migrated_count
            
        except Exception as e:
            logger.error(f"Document migration failed: {e}")
            self.migration_stats['errors'] += 1
            return 0
    
    def migrate_chat_history(self, sqlite_db_path: str) -> int:
        """Migrate chat history to encrypted MongoDB storage"""
        logger.info("Migrating chat history...")
        
        if 'mongodb' not in self.managers:
            logger.warning("MongoDB not available, skipping chat history migration")
            return 0
        
        mongo_manager = self.managers['mongodb']
        chat_collection = mongo_manager.get_collection('chat_history')
        migrated_count = 0
        
        try:
            sqlite_conn = sqlite3.connect(sqlite_db_path)
            sqlite_conn.row_factory = sqlite3.Row
            cursor = sqlite_conn.cursor()
            
            # Get chat history from SQLite
            cursor.execute("SELECT * FROM chat_history ORDER BY case_id, timestamp")
            chat_records = cursor.fetchall()
            
            # Group by case_id and session
            current_case = None
            current_session = []
            
            for record in chat_records:
                if current_case != record['case_id']:
                    # Save previous session
                    if current_session:
                        self._save_chat_session(chat_collection, current_case, current_session)
                        migrated_count += 1
                    
                    # Start new session
                    current_case = record['case_id']
                    current_session = []
                
                current_session.append({
                    'role': record['role'],
                    'content': record['content'],
                    'timestamp': record['timestamp']
                })
            
            # Save last session
            if current_session:
                self._save_chat_session(chat_collection, current_case, current_session)
                migrated_count += 1
            
            sqlite_conn.close()
            logger.info(f"Migrated {migrated_count} chat sessions")
            return migrated_count
            
        except Exception as e:
            logger.error(f"Chat history migration failed: {e}")
            self.migration_stats['errors'] += 1
            return 0
    
    def _save_chat_session(self, collection, case_id: str, messages: List[Dict]):
        """Save encrypted chat session to MongoDB"""
        try:
            # Encrypt chat messages
            messages_json = json.dumps(messages)
            # Note: In production, encrypt this with case-specific key
            
            session_doc = {
                'case_id': str(case_id),
                'messages': messages,  # In production: encrypt this
                'session_id': hashlib.md5(f"{case_id}_{messages[0]['timestamp']}".encode()).hexdigest(),
                'created_at': datetime.now(),
                'message_count': len(messages),
                'migrated_from': 'sqlite'
            }
            
            collection.insert_one(session_doc)
            
        except Exception as e:
            logger.error(f"Failed to save chat session for case {case_id}: {e}")
            raise
    
    def run_migration(self, sqlite_db_path: str, documents_dir: str = "case_documents"):
        """Run complete migration process"""
        self.migration_stats['start_time'] = datetime.now()
        logger.info("Starting secure architecture migration...")
        
        try:
            # Migrate users
            self.migration_stats['users'] = self.migrate_users(sqlite_db_path)
            
            # Migrate cases
            self.migration_stats['cases'] = self.migrate_cases(sqlite_db_path)
            
            # Migrate documents
            self.migration_stats['documents'] = self.migrate_documents(sqlite_db_path, documents_dir)
            
            # Migrate chat history
            self.migration_stats['chat_history'] = self.migrate_chat_history(sqlite_db_path)
            
            self.migration_stats['end_time'] = datetime.now()
            duration = self.migration_stats['end_time'] - self.migration_stats['start_time']
            
            logger.info("Migration completed successfully!")
            logger.info(f"Migration Statistics:")
            logger.info(f"  Users: {self.migration_stats['users']}")
            logger.info(f"  Cases: {self.migration_stats['cases']}")
            logger.info(f"  Documents: {self.migration_stats['documents']}")
            logger.info(f"  Chat Sessions: {self.migration_stats['chat_history']}")
            logger.info(f"  Errors: {self.migration_stats['errors']}")
            logger.info(f"  Duration: {duration}")
            
            return True
            
        except Exception as e:
            logger.error(f"Migration failed: {e}")
            return False

def main():
    parser = argparse.ArgumentParser(description='Migrate Legal CMS to secure architecture')
    parser.add_argument('--sqlite-db', required=True, help='Path to SQLite database file')
    parser.add_argument('--documents-dir', default='case_documents', help='Path to documents directory')
    parser.add_argument('--config', default='config/database.json', help='Path to database config file')
    parser.add_argument('--vault-token', help='Vault authentication token')
    parser.add_argument('--github-token', help='GitHub API token')
    
    args = parser.parse_args()
    
    # Validate inputs
    if not Path(args.sqlite_db).exists():
        logger.error(f"SQLite database not found: {args.sqlite_db}")
        return 1
    
    if not Path(args.config).exists():
        logger.error(f"Config file not found: {args.config}")
        return 1
    
    # Get tokens if not provided
    vault_token = args.vault_token or os.getenv('VAULT_TOKEN')
    github_token = args.github_token or os.getenv('GITHUB_TOKEN')
    
    if not vault_token:
        vault_token = getpass.getpass("Enter Vault token: ")
    
    if not github_token:
        github_token = getpass.getpass("Enter GitHub token: ")
    
    # Run migration
    migration = SecureMigration(args.config)
    
    try:
        migration.initialize_new_architecture(vault_token, github_token)
        success = migration.run_migration(args.sqlite_db, args.documents_dir)
        return 0 if success else 1
        
    except Exception as e:
        logger.error(f"Migration initialization failed: {e}")
        return 1

if __name__ == '__main__':
    sys.exit(main())
