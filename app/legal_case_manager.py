#!/usr/bin/env python3
"""
Legal Case Management System - Secure Modular Architecture ONLY
PRODUCTION-READY: PostgreSQL + MongoDB + Redis + PGP Encryption
NO LEGACY CODE - Modular components only
"""

import streamlit as st
import sys
from pathlib import Path
import os

# Load environment variables from .env file
try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    pass

# Load API keys from database into environment variables
def load_api_keys_to_env():
    """Load API keys from database into environment variables"""
    try:
        import sqlite3
        
        # Check if API keys database exists
        db_path = os.path.join(os.path.dirname(__file__), '..', 'data', 'databases', 'api_keys.db')
        if not os.path.exists(db_path):
            return
            
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        cursor.execute("SELECT provider, api_key FROM api_keys WHERE api_key != ''")
        
        for provider, api_key in cursor.fetchall():
            if provider == 'openai' and api_key.strip():
                os.environ['OPENAI_API_KEY'] = api_key.strip()
            elif provider == 'anthropic' and api_key.strip():
                os.environ['ANTHROPIC_API_KEY'] = api_key.strip()
            elif provider == 'huggingface' and api_key.strip():
                os.environ['HUGGINGFACE_API_KEY'] = api_key.strip()
                
        conn.close()
        
    except Exception as e:
        # Silently fail - application should work without API keys for basic functions
        pass

# Load API keys at startup
load_api_keys_to_env()

# Add project root to Python path
project_root = Path(__file__).parent.parent  # Go up one level from app/ to project root
sys.path.insert(0, str(project_root))

def main():
    """Main application entry point - Secure modular architecture only"""
    
    # MANDATORY: Security validation before any operation
    from core.security_enforcer import perform_startup_security_check
    if not perform_startup_security_check():
        st.error("🚫 **SECURITY VALIDATION FAILED**")
        st.error("**Modular architecture requires secure components:**")
        st.write("**Missing Requirements:**")
        st.write("- SSL/TLS certificates")
        st.write("- PGP encryption (python-gnupg)")
        st.write("- Docker: PostgreSQL, MongoDB, Redis")
        st.write("\n**Deploy secure stack:**")
        st.code("./deployment/deploy_secure_architecture.sh deploy", language="bash")
        st.stop()
    
    # Configure Streamlit page
    st.set_page_config(
        page_title="Legal Case Management System",
        page_icon="⚖️",
        layout="wide",
        initial_sidebar_state="expanded"
    )
    
    # Load secure modular application
    from secure_app import run_secure_application
    run_secure_application()

if __name__ == "__main__":
    main()