#!/usr/bin/env python3
"""
Legal Case Management System - Main Entry Point
Unified CLI/GUI interface for the Legal Case Management System
Supports both command-line interface and web GUI modes
"""

import os
import sys
import argparse
import logging
import subprocess
import signal
import time
import json
from pathlib import Path
from typing import Optional, Dict, Any

# Add current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

class LegalCaseManager:
    """Main application controller for Legal Case Management System"""
    
    def __init__(self):
        self.app_name = "Legal Case Management System"
        self.version = "2.0.0"
        self.config_dir = Path("config")
        self.logs_dir = Path("logs")
        self.setup_logging()
        
    def setup_logging(self):
        """Setup logging for the application"""
        self.logs_dir.mkdir(exist_ok=True)
        
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(self.logs_dir / 'main.log'),
                logging.StreamHandler(sys.stdout)
            ]
        )
        self.logger = logging.getLogger(__name__)

def print_banner():
    """Print application banner"""
    print("=" * 70)
    print("⚖️  CUYAHOGA COUNTY PRO SE LITIGATION MANAGEMENT SYSTEM")
    print("=" * 70)
    print("🔒 Secure Architecture | MongoDB + PostgreSQL + Redis")
    print("🤖 AI Legal Team | Multi-Model Analysis")
    print("📄 Document Management | PGP Encrypted Storage")
    print("🌐 HTTPS Web Interface | Professional Grade")
    print("=" * 70)

def check_docker_services():
    """Check if Docker services are running"""
    print("🐳 Checking Docker services...")
    
    required_services = [
        "legal-cms-postgresql",
        "legal-cms-mongodb", 
        "legal-cms-redis",
        "legal-cms-qdrant"
    ]
    
    running_services = []
    
    try:
        result = subprocess.run(['docker', 'ps', '--format', '{{.Names}}'], 
                              capture_output=True, text=True)
        running_containers = result.stdout.strip().split('\n')
        
        for service in required_services:
            if service in running_containers:
                print(f"  ✅ {service}")
                running_services.append(service)
            else:
                print(f"  ❌ {service}")
        
        return len(running_services) == len(required_services)
        
    except Exception as e:
        print(f"  ❌ Docker check failed: {e}")
        return False

def start_docker_services():
    """Start Docker services"""
    print("🚀 Starting Docker services...")
    
    try:
        # Start services using docker-compose
        result = subprocess.run(['docker-compose', 'up', '-d'], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            print("  ✅ Docker services started")
            time.sleep(5)  # Wait for services to initialize
            return True
        else:
            print(f"  ❌ Failed to start services: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"  ❌ Docker startup failed: {e}")
        return False

def check_ssl_certificates():
    """Check SSL certificate status"""
    print("🔒 Checking SSL certificates...")
    
    cert_files = [
        "localhost.pem",
        "localhost-key.pem"
    ]
    
    all_present = True
    for cert_file in cert_files:
        if os.path.exists(cert_file):
            print(f"  ✅ {cert_file}")
        else:
            print(f"  ❌ {cert_file}")
            all_present = False
    
    return all_present

def show_system_stats():
    """Show system statistics"""
    print("\n📊 SYSTEM STATISTICS")
    print("-" * 40)
    
    # Check services
    services_ok = check_docker_services()
    ssl_ok = check_ssl_certificates()
    
    print(f"Docker Services: {'✅ Running' if services_ok else '❌ Issues'}")
    print(f"SSL Certificates: {'✅ Present' if ssl_ok else '❌ Missing'}")
    
    # Check database connections
    try:
        from core.database_managers import PostgreSQLManager, MongoDBManager
        
        # Test PostgreSQL
        try:
            config = {
                'host': 'localhost', 'port': 5435, 'database': 'legal_cms_main',
                'user': 'legal_cms_user', 'password': 'cDBGCyHKzc6F', 'sslmode': 'prefer'
            }
            pg = PostgreSQLManager(config)
            print("PostgreSQL: ✅ Connected")
        except:
            print("PostgreSQL: ❌ Connection failed")
        
        # Test MongoDB
        try:
            mongo_config = {
                'host': 'localhost', 'port': 27018, 'database': 'legal_cms_documents',
                'username': 'admin', 'password': 'AxWuQaxIGX8g', 'auth_source': 'admin'
            }
            mongo = MongoDBManager(mongo_config)
            print("MongoDB: ✅ Connected")
        except:
            print("MongoDB: ❌ Connection failed")
            
    except ImportError:
        print("Database Managers: ❌ Import failed")
    
    print("-" * 40)

def launch_web_app():
    """Launch the Streamlit web application"""
    print("🌐 Launching web application...")
    print("📍 URL: https://localhost")
    print("🔄 Starting Streamlit server...")
    
    try:
        # Launch Streamlit
        subprocess.run(['streamlit', 'run', 'secure_app.py', '--server.port=8501'])
    except KeyboardInterrupt:
        print("\n👋 Application stopped by user")
    except Exception as e:
        print(f"❌ Failed to launch application: {e}")

def show_menu():
    """Show main menu"""
    while True:
        print("\n" + "=" * 50)
        print("📋 MAIN MENU")
        print("=" * 50)
        print("1. 🚀 Launch Web Application")
        print("2. 📊 Show System Statistics") 
        print("3. 🐳 Start Docker Services")
        print("4. 🔒 Check SSL Status")
        print("5. 🛠️  System Configuration")
        print("6. 📖 Help & Documentation")
        print("0. 🚪 Exit")
        print("=" * 50)
        
        choice = input("Enter your choice (0-6): ").strip()
        
        if choice == "1":
            launch_web_app()
        elif choice == "2":
            show_system_stats()
        elif choice == "3":
            start_docker_services()
        elif choice == "4":
            check_ssl_certificates()
        elif choice == "5":
            print("🛠️  Configuration options coming soon...")
        elif choice == "6":
            print("📖 Documentation: https://github.com/george-shepov/lcm")
        elif choice == "0":
            print("👋 Goodbye!")
            sys.exit(0)
        else:
            print("❌ Invalid choice. Please try again.")
        
        input("\nPress Enter to continue...")

def main():
    """Main entry point"""
    print_banner()
    
    # Quick system check
    print("🔍 Performing system check...")
    services_ok = check_docker_services()
    
    if not services_ok:
        print("\n⚠️  Some Docker services are not running.")
        print("Would you like to start them? (y/n): ", end="")
        if input().lower().startswith('y'):
            start_docker_services()
    
    # Show menu
    show_menu()

if __name__ == "__main__":
    main()
