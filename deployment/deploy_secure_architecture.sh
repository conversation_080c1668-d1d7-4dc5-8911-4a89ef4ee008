#!/bin/bash
# Deployment Script for Secure Legal Case Management System
# Deploys PostgreSQL, MongoDB, Redis, Vault, and the application

set -e  # Exit on any error

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
CONFIG_DIR="$PROJECT_ROOT/config"
LOG_FILE="$PROJECT_ROOT/legal-cms-deployment.log"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a "$LOG_FILE"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" | tee -a "$LOG_FILE"
    exit 1
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1" | tee -a "$LOG_FILE"
}

info() {
    echo -e "${BLUE}[INFO]${NC} $1" | tee -a "$LOG_FILE"
}

# Check if running as root
check_root() {
    if [[ $EUID -eq 0 ]]; then
        error "This script should not be run as root for security reasons"
    fi
}

# Check system requirements
check_requirements() {
    log "Checking system requirements..."
    
    # Check OS
    if [[ "$OSTYPE" != "linux-gnu"* ]]; then
        error "This script is designed for Linux systems only"
    fi
    
    # Check required commands
    local required_commands=("docker" "python3" "pip3" "openssl" "curl")
    for cmd in "${required_commands[@]}"; do
        if ! command -v "$cmd" &> /dev/null; then
            error "Required command not found: $cmd"
        fi
    done
    
    # Check Python version
    local python_version=$(python3 -c "import sys; print(f'{sys.version_info.major}.{sys.version_info.minor}')")
    local major=$(echo $python_version | cut -d. -f1)
    local minor=$(echo $python_version | cut -d. -f2)
    if [[ $major -lt 3 ]] || [[ $major -eq 3 && $minor -lt 8 ]]; then
        error "Python 3.8 or higher is required. Found: $python_version"
    fi
    
    log "System requirements check passed"
}

# Generate SSL certificates
generate_ssl_certificates() {
    log "Generating SSL certificates..."
    
    local ssl_dir="/etc/ssl/legal-cms"
    sudo mkdir -p "$ssl_dir"/{certs,private}
    
    # Generate CA certificate
    if [[ ! -f "$ssl_dir/certs/ca.crt" ]]; then
        sudo openssl genrsa -out "$ssl_dir/private/ca.key" 4096
        sudo openssl req -new -x509 -days 3650 -key "$ssl_dir/private/ca.key" \
            -out "$ssl_dir/certs/ca.crt" \
            -subj "/C=US/ST=OH/L=Cleveland/O=Legal CMS/OU=IT/CN=Legal CMS CA"
    fi
    
    # Generate server certificates for each service
    local services=("postgresql" "mongodb" "redis" "vault" "nginx")
    for service in "${services[@]}"; do
        if [[ ! -f "$ssl_dir/certs/${service}.crt" ]]; then
            sudo openssl genrsa -out "$ssl_dir/private/${service}.key" 2048
            sudo openssl req -new -key "$ssl_dir/private/${service}.key" \
                -out "$ssl_dir/certs/${service}.csr" \
                -subj "/C=US/ST=OH/L=Cleveland/O=Legal CMS/OU=IT/CN=${service}.legal-cms.internal"
            sudo openssl x509 -req -days 365 -in "$ssl_dir/certs/${service}.csr" \
                -CA "$ssl_dir/certs/ca.crt" -CAkey "$ssl_dir/private/ca.key" \
                -CAcreateserial -out "$ssl_dir/certs/${service}.crt"
            sudo rm "$ssl_dir/certs/${service}.csr"
        fi
    done
    
    # Set proper permissions
    sudo chmod 600 "$ssl_dir/private"/*
    sudo chmod 644 "$ssl_dir/certs"/*
    
    log "SSL certificates generated successfully"
}

# Create Docker Compose configuration
create_docker_compose() {
    log "Creating Docker Compose configuration..."
    
    cat > "$PROJECT_ROOT/docker-compose.yml" << 'EOF'
version: '3.8'

services:
  # PostgreSQL Primary Database
  postgresql:
    image: postgres:15-alpine
    container_name: legal-cms-postgresql
    environment:
      POSTGRES_DB: legal_cms_main
      POSTGRES_USER: legal_cms_user
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      POSTGRES_INITDB_ARGS: "--auth-host=scram-sha-256"
    volumes:
      - postgresql_data:/var/lib/postgresql/data
      - /etc/ssl/legal-cms/certs:/etc/ssl/certs:ro
      - /etc/ssl/legal-cms/private:/etc/ssl/private:ro
      - ./config/postgresql.conf:/etc/postgresql/postgresql.conf
    ports:
      - "5432:5432"
    networks:
      - legal-cms-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U legal_cms_user -d legal_cms_main"]
      interval: 30s
      timeout: 10s
      retries: 3

  # MongoDB Replica Set
  mongodb-primary:
    image: mongo:7.0
    container_name: legal-cms-mongodb-primary
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: ${MONGODB_ROOT_PASSWORD}
      MONGO_INITDB_DATABASE: legal_cms_documents
    volumes:
      - mongodb_primary_data:/data/db
      - /etc/ssl/legal-cms/certs:/etc/ssl/certs:ro
      - /etc/ssl/legal-cms/private:/etc/ssl/private:ro
      - ./config/mongod.conf:/etc/mongod.conf
    ports:
      - "27017:27017"
    networks:
      - legal-cms-network
    restart: unless-stopped
    command: mongod --config /etc/mongod.conf --replSet legal-cms-rs

  # Redis Cluster
  redis:
    image: redis:7-alpine
    container_name: legal-cms-redis
    environment:
      REDIS_PASSWORD: ${REDIS_PASSWORD}
    volumes:
      - redis_data:/data
      - /etc/ssl/legal-cms/certs:/etc/ssl/certs:ro
      - /etc/ssl/legal-cms/private:/etc/ssl/private:ro
      - ./config/redis.conf:/etc/redis/redis.conf
    ports:
      - "6379:6379"
    networks:
      - legal-cms-network
    restart: unless-stopped
    command: redis-server /etc/redis/redis.conf

  # HashiCorp Vault
  vault:
    image: vault:1.15
    container_name: legal-cms-vault
    environment:
      VAULT_DEV_ROOT_TOKEN_ID: ${VAULT_ROOT_TOKEN}
      VAULT_DEV_LISTEN_ADDRESS: 0.0.0.0:8200
    volumes:
      - vault_data:/vault/data
      - /etc/ssl/legal-cms/certs:/etc/ssl/certs:ro
      - /etc/ssl/legal-cms/private:/etc/ssl/private:ro
      - ./config/vault.hcl:/vault/config/vault.hcl
    ports:
      - "8200:8200"
    networks:
      - legal-cms-network
    restart: unless-stopped
    cap_add:
      - IPC_LOCK
    command: vault server -config=/vault/config/vault.hcl

  # Legal CMS Application
  legal-cms-app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: legal-cms-app
    environment:
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - MONGODB_PASSWORD=${MONGODB_PASSWORD}
      - REDIS_PASSWORD=${REDIS_PASSWORD}
      - VAULT_TOKEN=${VAULT_ROOT_TOKEN}
      - GITHUB_TOKEN=${GITHUB_TOKEN}
      - ENCRYPTION_KEY=${ENCRYPTION_KEY}
      - JWT_SECRET_KEY=${JWT_SECRET_KEY}
    volumes:
      - app_data:/app/data
      - /etc/ssl/legal-cms/certs:/etc/ssl/certs:ro
    ports:
      - "8501:8501"
    networks:
      - legal-cms-network
    restart: unless-stopped
    depends_on:
      - postgresql
      - mongodb-primary
      - redis
      - vault
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8501/_stcore/health"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  postgresql_data:
  mongodb_primary_data:
  redis_data:
  vault_data:
  app_data:

networks:
  legal-cms-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
EOF

    log "Docker Compose configuration created"
}

# Generate environment variables
generate_env_file() {
    log "Generating environment variables..."
    
    local env_file="$PROJECT_ROOT/.env"
    
    # Generate secure passwords and keys
    local postgres_password=$(openssl rand -base64 32 | tr -d '=+/')
    local mongodb_password=$(openssl rand -base64 32 | tr -d '=+/')
    local redis_password=$(openssl rand -base64 32 | tr -d '=+/')
    local vault_token=$(openssl rand -base64 32 | tr -d '=+/')
    local encryption_key=$(python3 -c "from cryptography.fernet import Fernet; print(Fernet.generate_key().decode())")
    local jwt_secret=$(openssl rand -base64 64 | tr -d '\n' | tr -d '=' | head -c 64)
    
    cat > "$env_file" << EOF
# Database Passwords
POSTGRES_PASSWORD=$postgres_password
MONGODB_ROOT_PASSWORD=$mongodb_password
MONGODB_PASSWORD=$mongodb_password
REDIS_PASSWORD=$redis_password

# Vault Configuration
VAULT_ROOT_TOKEN=$vault_token
VAULT_URL=https://vault.legal-cms.internal:8200

# GitHub Integration
GITHUB_TOKEN=${GITHUB_TOKEN:-}
GITHUB_ORGANIZATION=legal-cms-secure

# Encryption Keys
ENCRYPTION_KEY=$encryption_key
JWT_SECRET_KEY=$jwt_secret

# Application Configuration
ENVIRONMENT=production
DEBUG=false
LOG_LEVEL=INFO

# SSL Configuration
SSL_CERT_PATH=/etc/ssl/legal-cms/certs
SSL_KEY_PATH=/etc/ssl/legal-cms/private
EOF

    chmod 600 "$env_file"
    log "Environment file created with secure passwords"
}

# Create Dockerfile
create_dockerfile() {
    log "Creating Dockerfile..."
    
    cat > "$PROJECT_ROOT/Dockerfile" << 'EOF'
FROM python:3.11-slim

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    libpq-dev \
    libssl-dev \
    libffi-dev \
    curl \
    gnupg \
    tesseract-ocr \
    && rm -rf /var/lib/apt/lists/*

# Create app user
RUN useradd -m -u 1000 appuser

# Set working directory
WORKDIR /app

# Copy requirements and install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY . .

# Change ownership to app user
RUN chown -R appuser:appuser /app

# Switch to app user
USER appuser

# Expose port
EXPOSE 8501

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8501/_stcore/health || exit 1

# Run application
CMD ["streamlit", "run", "main.py", "--server.port=8501", "--server.address=0.0.0.0"]
EOF

    log "Dockerfile created"
}

# Deploy infrastructure
deploy_infrastructure() {
    log "Deploying infrastructure..."
    
    # Create necessary directories
    sudo mkdir -p /var/log/legal-cms
    sudo mkdir -p /var/lib/legal-cms/{backups,uploads}
    
    # Set up log rotation
    sudo tee /etc/logrotate.d/legal-cms > /dev/null << 'EOF'
/var/log/legal-cms/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 appuser appuser
}
EOF

    # Start services
    cd "$PROJECT_ROOT"
    docker compose up -d
    
    # Wait for services to be ready
    log "Waiting for services to start..."
    sleep 30
    
    # Check service health
    local services=("postgresql" "mongodb-primary" "redis" "vault")
    for service in "${services[@]}"; do
        if ! docker compose ps "$service" | grep -q "Up"; then
            error "Service $service failed to start"
        fi
    done
    
    log "Infrastructure deployed successfully"
}

# Initialize Vault
initialize_vault() {
    log "Initializing Vault..."
    
    # Wait for Vault to be ready
    local max_attempts=30
    local attempt=0
    
    while [[ $attempt -lt $max_attempts ]]; do
        if curl -s -k https://localhost:8200/v1/sys/health > /dev/null; then
            break
        fi
        sleep 2
        ((attempt++))
    done
    
    if [[ $attempt -eq $max_attempts ]]; then
        error "Vault failed to start within expected time"
    fi
    
    # Initialize Vault (if not already initialized)
    if ! docker exec legal-cms-vault vault status | grep -q "Initialized.*true"; then
        docker exec legal-cms-vault vault operator init -key-shares=3 -key-threshold=2 > vault-keys.txt
        warning "Vault keys saved to vault-keys.txt - SECURE THIS FILE!"
    fi
    
    log "Vault initialized successfully"
}

# Run migration
run_migration() {
    log "Running data migration..."
    
    if [[ -f "$PROJECT_ROOT/legal_cases.db" ]]; then
        python3 "$PROJECT_ROOT/migration/migrate_to_secure_architecture.py" \
            --sqlite-db "$PROJECT_ROOT/legal_cases.db" \
            --documents-dir "$PROJECT_ROOT/case_documents" \
            --config "$PROJECT_ROOT/config/database.json"
    else
        info "No existing SQLite database found, skipping migration"
    fi
    
    log "Migration completed"
}

# Main deployment function
main() {
    log "Starting Legal CMS Secure Architecture Deployment"
    
    check_root
    check_requirements
    generate_ssl_certificates
    create_docker_compose
    generate_env_file
    create_dockerfile
    deploy_infrastructure
    initialize_vault
    run_migration
    
    log "Deployment completed successfully!"
    info "Access the application at: https://localhost:8501"
    info "Vault UI available at: https://localhost:8200"
    warning "Remember to secure the vault-keys.txt file and .env file!"
}

# Handle script arguments
case "${1:-deploy}" in
    "deploy")
        main
        ;;
    "stop")
        log "Stopping Legal CMS services..."
        cd "$PROJECT_ROOT"
        docker compose down
        ;;
    "restart")
        log "Restarting Legal CMS services..."
        cd "$PROJECT_ROOT"
        docker compose restart
        ;;
    "logs")
        cd "$PROJECT_ROOT"
        docker compose logs -f "${2:-}"
        ;;
    "status")
        cd "$PROJECT_ROOT"
        docker compose ps
        ;;
    *)
        echo "Usage: $0 {deploy|stop|restart|logs|status}"
        exit 1
        ;;
esac
