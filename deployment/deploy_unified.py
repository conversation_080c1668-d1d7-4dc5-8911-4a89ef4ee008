#!/usr/bin/env python3
"""
Unified Deployment Manager
Ensures consistent container naming and deployment across all services
Follows the OFFICIAL naming convention: legal-cms-[service]
"""

import subprocess
import sys
import json
import time
from pathlib import Path

class UnifiedDeploymentManager:
    """
    Manages all Legal Case Management System containers with consistent naming
    """
    
    def __init__(self):
        self.container_prefix = "legal-cms"
        self.network_name = "legal-cms-network"
        
        # OFFICIAL container definitions
        self.containers = {
            'postgresql': {
                'name': f'{self.container_prefix}-postgresql',
                'image': 'postgres:15-alpine',
                'ports': ['5435:5432'],
                'required': True,
                'description': 'Primary relational database'
            },
            'mongodb': {
                'name': f'{self.container_prefix}-mongodb', 
                'image': 'mongo:7.0',
                'ports': ['27018:27017'],
                'required': True,
                'description': 'Document storage database'
            },
            'redis': {
                'name': f'{self.container_prefix}-redis',
                'image': 'redis:7-alpine',
                'ports': ['6382:6379'],
                'required': True,
                'description': 'Cache and session store'
            },
            'qdrant': {
                'name': f'{self.container_prefix}-qdrant',
                'image': 'qdrant/qdrant',
                'ports': ['6334:6333'],
                'required': True,
                'description': 'Vector database for embeddings'
            },
            'nginx': {
                'name': f'{self.container_prefix}-nginx',
                'image': 'nginx:alpine',
                'ports': ['80:80', '443:443'],
                'required': False,
                'description': 'HTTPS reverse proxy'
            },
            'vault': {
                'name': f'{self.container_prefix}-vault',
                'image': 'hashicorp/vault:latest',
                'ports': ['8200:8200'],
                'required': False,
                'description': 'HashiCorp Vault secrets management'
            }
        }
    
    def check_container_status(self):
        """Check status of all containers"""
        print(f"📊 Legal Case Management System - Container Status")
        print("=" * 60)
        
        try:
            # Get all running containers
            result = subprocess.run(['docker', 'ps', '--format', 
                                   'table {{.Names}}\t{{.Image}}\t{{.Status}}\t{{.Ports}}'],
                                  capture_output=True, text=True)
            
            running_containers = result.stdout.lower()
            
            for service_name, config in self.containers.items():
                container_name = config['name']
                status = "🟢 RUNNING" if container_name.lower() in running_containers else "🔴 STOPPED"
                required = "🔥 REQUIRED" if config['required'] else "⚪ OPTIONAL"
                
                print(f"{status:<12} {container_name:<25} {required:<12} {config['description']}")
            
            print("\n" + "=" * 60)
            
            # Check for containers with wrong naming
            all_containers = subprocess.run(['docker', 'ps', '-a', '--format', '{{.Names}}'],
                                          capture_output=True, text=True).stdout
            
            wrong_names = []
            for line in all_containers.split('\n'):
                line = line.strip()
                if line and ('lcm' in line.lower() or 'legal' in line.lower()) and not line.startswith(self.container_prefix):
                    wrong_names.append(line)
            
            if wrong_names:
                print("⚠️  INCONSISTENT NAMING DETECTED:")
                for name in wrong_names:
                    print(f"   🚨 {name} - Should be renamed to follow legal-cms-[service] pattern")
                print("\n💡 Run 'python deploy_unified.py fix-naming' to correct these")
        
        except Exception as e:
            print(f"❌ Error checking container status: {e}")
    
    def deploy_core_services(self):
        """Deploy core database services using docker-compose"""
        print("🚀 Deploying Core Database Services...")
        
        try:
            # Use the standardized docker-compose.yml
            subprocess.run(['docker-compose', 'up', '-d'], check=True)
            print("✅ Core services deployed successfully")
            
            # Wait a moment for services to start
            time.sleep(5)
            
            # Check health
            self.check_container_status()
            
        except subprocess.CalledProcessError as e:
            print(f"❌ Deployment failed: {e}")
            return False
        
        return True
    
    def deploy_nginx(self):
        """Deploy nginx reverse proxy"""
        print("🌐 Deploying NGINX Reverse Proxy...")
        
        try:
            subprocess.run(['docker-compose', '-f', 'docker-compose.nginx.yml', 'up', '-d'], check=True)
            print("✅ NGINX deployed successfully")
        except Exception as e:
            print(f"❌ NGINX deployment failed: {e}")
    
    def deploy_vault(self):
        """Deploy HashiCorp Vault"""
        print("🔒 Deploying HashiCorp Vault...")
        
        try:
            subprocess.run(['python', 'setup_vault.py'], check=True)
            print("✅ Vault deployment initiated")
        except Exception as e:
            print(f"❌ Vault deployment failed: {e}")
    
    def fix_naming_inconsistencies(self):
        """Fix containers with inconsistent naming"""
        print("🔧 Fixing Container Naming Inconsistencies...")
        
        # This would require stopping, renaming, and restarting containers
        # For safety, just recommend manual intervention
        print("⚠️  For safety, please manually stop and recreate containers with wrong names")
        print("💡 Use 'docker stop [container]' and redeploy with correct naming")
    
    def deploy_all(self):
        """Deploy all services with consistent naming"""
        print("🏗️  UNIFIED LEGAL CASE MANAGEMENT DEPLOYMENT")
        print("=" * 60)
        print("📋 Deploying with STANDARDIZED naming: legal-cms-[service]")
        print()
        
        success = True
        
        # Deploy core services
        if not self.deploy_core_services():
            success = False
        
        # Deploy optional services
        try:
            self.deploy_nginx()
        except:
            print("⚠️  NGINX deployment skipped")
        
        try:
            self.deploy_vault()  
        except:
            print("⚠️  Vault deployment skipped")
        
        print("\n" + "=" * 60)
        if success:
            print("🎉 UNIFIED DEPLOYMENT COMPLETE!")
            print("📊 Final container status:")
            self.check_container_status()
        else:
            print("❌ Deployment had issues - check logs above")
    
    def stop_all(self):
        """Stop all containers"""
        print("🛑 Stopping all Legal Case Management containers...")
        
        for service_name, config in self.containers.items():
            container_name = config['name']
            try:
                subprocess.run(['docker', 'stop', container_name], 
                             capture_output=True, check=True)
                print(f"✅ Stopped {container_name}")
            except subprocess.CalledProcessError:
                print(f"⚠️  {container_name} was not running")
            except Exception as e:
                print(f"❌ Error stopping {container_name}: {e}")

def main():
    """Main deployment manager interface"""
    
    manager = UnifiedDeploymentManager()
    
    if len(sys.argv) < 2:
        print("📋 Legal Case Management - Unified Deployment Manager")
        print("Usage: python deploy_unified.py [command]")
        print()
        print("Commands:")
        print("  status     - Show container status")
        print("  deploy     - Deploy all services") 
        print("  core       - Deploy only core database services")
        print("  nginx      - Deploy NGINX reverse proxy")
        print("  vault      - Deploy HashiCorp Vault")
        print("  stop       - Stop all containers")
        print("  fix-naming - Fix naming inconsistencies")
        print()
        manager.check_container_status()
        return
    
    command = sys.argv[1].lower()
    
    if command == 'status':
        manager.check_container_status()
    elif command == 'deploy':
        manager.deploy_all()
    elif command == 'core':
        manager.deploy_core_services()
    elif command == 'nginx':
        manager.deploy_nginx()
    elif command == 'vault':
        manager.deploy_vault()
    elif command == 'stop':
        manager.stop_all()
    elif command == 'fix-naming':
        manager.fix_naming_inconsistencies()
    else:
        print(f"❌ Unknown command: {command}")

if __name__ == "__main__":
    main()