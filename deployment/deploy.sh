#!/bin/bash

# CUYAHOGA COUNTY PRO SE Litigation Management System
# Deployment Script

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Docker and Docker Compose are installed
check_dependencies() {
    print_status "Checking dependencies..."
    
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed. Please install Docker first."
        exit 1
    fi
    
    if ! command -v docker compose &> /dev/null; then
        print_error "Docker Compose is not installed. Please install Docker Compose first."
        exit 1
    fi
    
    print_success "Dependencies check passed"
}

# Check if .env file exists
check_env_file() {
    if [ ! -f ".env" ]; then
        print_error ".env file not found. Please create it with required environment variables."
        exit 1
    fi
    print_success ".env file found"
}

# Deploy the application
deploy() {
    print_status "Starting deployment..."
    
    # Stop existing containers
    print_status "Stopping existing containers..."
    docker compose -f config/docker-compose.yml --env-file .env down --remove-orphans
    
    # Build and start containers
    print_status "Building and starting containers..."
    docker compose -f config/docker-compose.yml --env-file .env up --build -d
    
    # Wait for services to be ready
    print_status "Waiting for services to be ready..."
    sleep 10
    
    # Check container status
    print_status "Checking container status..."
    docker compose -f config/docker-compose.yml ps
    
    print_success "Deployment completed!"
    print_status "Application is available at: http://localhost:8501"
}

# Show logs
show_logs() {
    print_status "Showing application logs..."
    docker compose -f config/docker-compose.yml logs -f legal-cms-app
}

# Stop the application
stop() {
    print_status "Stopping application..."
    docker compose -f config/docker-compose.yml --env-file .env down
    print_success "Application stopped"
}

# Show status
status() {
    print_status "Container status:"
    docker compose -f config/docker-compose.yml ps
}

# Main script logic
case "$1" in
    "deploy")
        check_dependencies
        check_env_file
        deploy
        ;;
    "logs")
        show_logs
        ;;
    "stop")
        stop
        ;;
    "status")
        status
        ;;
    "restart")
        check_dependencies
        check_env_file
        stop
        deploy
        ;;
    *)
        echo "CUYAHOGA COUNTY PRO SE Litigation Management System"
        echo "Usage: $0 {deploy|logs|stop|status|restart}"
        echo ""
        echo "Commands:"
        echo "  deploy   - Deploy the application"
        echo "  logs     - Show application logs"
        echo "  stop     - Stop the application"
        echo "  status   - Show container status"
        echo "  restart  - Restart the application"
        exit 1
        ;;
esac
