#!/bin/bash
# User-space Deployment Script for Secure Legal Case Management System
# Deploys PostgreSQL, MongoDB, Redis, and the application without sudo

set -e  # Exit on any error

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
CONFIG_DIR="$PROJECT_ROOT/config"
LOG_FILE="$PROJECT_ROOT/legal-cms-deployment.log"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a "$LOG_FILE"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" | tee -a "$LOG_FILE"
    exit 1
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1" | tee -a "$LOG_FILE"
}

info() {
    echo -e "${BLUE}[INFO]${NC} $1" | tee -a "$LOG_FILE"
}

# Check system requirements
check_requirements() {
    log "Checking system requirements..."
    
    # Check required commands
    local required_commands=("docker" "python3" "pip3")
    for cmd in "${required_commands[@]}"; do
        if ! command -v "$cmd" &> /dev/null; then
            error "Required command not found: $cmd"
        fi
    done
    
    # Check Docker Compose V2
    if ! docker compose version &> /dev/null; then
        error "Docker Compose V2 not available. Please install or update Docker."
    fi
    
    # Check Python version
    local python_version=$(python3 -c "import sys; print(f'{sys.version_info.major}.{sys.version_info.minor}')")
    local major=$(echo $python_version | cut -d. -f1)
    local minor=$(echo $python_version | cut -d. -f2)
    if [[ $major -lt 3 ]] || [[ $major -eq 3 && $minor -lt 8 ]]; then
        error "Python 3.8 or higher is required. Found: $python_version"
    fi
    
    log "System requirements check passed"
}

# Create Docker Compose configuration (simplified without SSL)
create_docker_compose() {
    log "Creating Docker Compose configuration..."
    
    cat > "$PROJECT_ROOT/docker-compose.yml" << 'EOF'
version: '3.8'

services:
  # PostgreSQL Primary Database
  legal-cms-postgresql:
    image: postgres:15-alpine
    container_name: legal-cms-postgresql
    environment:
      POSTGRES_DB: legal_cms_main
      POSTGRES_USER: legal_cms_user
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
    volumes:
      - postgresql_data:/var/lib/postgresql/data
    ports:
      - "5435:5432"
    networks:
      - legal-cms-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U legal_cms_user -d legal_cms_main"]
      interval: 30s
      timeout: 10s
      retries: 3

  # MongoDB Document Store
  legal-cms-mongodb:
    image: mongo:7.0
    container_name: legal-cms-mongodb
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: ${MONGODB_ROOT_PASSWORD}
      MONGO_INITDB_DATABASE: legal_cms_documents
    volumes:
      - mongodb_data:/data/db
    ports:
      - "27018:27017"
    networks:
      - legal-cms-network
    restart: unless-stopped

  # Redis Cache
  legal-cms-redis:
    image: redis:7-alpine
    container_name: legal-cms-redis
    environment:
      REDIS_PASSWORD: ${REDIS_PASSWORD}
    volumes:
      - redis_data:/data
    ports:
      - "6382:6379"
    networks:
      - legal-cms-network
    restart: unless-stopped
    command: redis-server --requirepass ${REDIS_PASSWORD}

  # Qdrant Vector Database
  legal-cms-qdrant:
    image: qdrant/qdrant
    container_name: legal-cms-qdrant
    volumes:
      - qdrant_data:/qdrant/storage
    ports:
      - "6334:6333"
    networks:
      - legal-cms-network
    restart: unless-stopped

volumes:
  postgresql_data:
  mongodb_data:
  redis_data:
  qdrant_data:

networks:
  legal-cms-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
EOF

    log "Docker Compose configuration created"
}

# Generate environment variables
generate_env_file() {
    log "Generating environment variables..."
    
    local env_file="$PROJECT_ROOT/.env"
    
    # Generate secure passwords
    local postgres_password=$(openssl rand -base64 32 | tr -d "=+/" | cut -c1-25)
    local mongodb_password=$(openssl rand -base64 32 | tr -d "=+/" | cut -c1-25)
    local redis_password=$(openssl rand -base64 32 | tr -d "=+/" | cut -c1-25)
    local encryption_key=$(python3 -c "from cryptography.fernet import Fernet; import base64; key = Fernet.generate_key(); print(base64.urlsafe_b64encode(base64.urlsafe_b64decode(key)).decode())")
    local jwt_secret=$(openssl rand -base64 48 | tr -d "=+/" | tr -d '\n')
    
    cat > "$env_file" << EOF
# Database Passwords
POSTGRES_PASSWORD=$postgres_password
MONGODB_ROOT_PASSWORD=$mongodb_password
MONGODB_PASSWORD=$mongodb_password
REDIS_PASSWORD=$redis_password

# Encryption Keys
ENCRYPTION_KEY=$encryption_key
JWT_SECRET_KEY=$jwt_secret

# Database Connection URLs (for new architecture)
POSTGRES_URL=postgresql://legal_cms_user:$postgres_password@localhost:5435/legal_cms_main
MONGODB_URL=*********************************************************************
REDIS_URL=redis://:$redis_password@localhost:6382/0
QDRANT_URL=http://localhost:6334

# Application Configuration
ENVIRONMENT=production
DEBUG=false
LOG_LEVEL=INFO
USE_SECURE_ARCHITECTURE=true
EOF

    chmod 600 "$env_file"
    log "Environment file created with secure passwords"
}

# Deploy infrastructure
deploy_infrastructure() {
    log "Deploying infrastructure..."
    
    # Stop any running Streamlit
    log "Stopping current Streamlit application..."
    pkill -f "streamlit run" || true
    sleep 5
    
    # Start services
    cd "$PROJECT_ROOT"
    docker compose up -d
    
    # Wait for services to be ready
    log "Waiting for services to start..."
    sleep 45
    
    # Check service health
    local services=("legal-cms-postgresql" "legal-cms-mongodb" "legal-cms-redis" "legal-cms-qdrant")
    for service in "${services[@]}"; do
        if ! docker compose ps "$service" | grep -q "Up"; then
            warning "Service $service may not be fully ready yet"
            docker compose logs "$service" | tail -10
        else
            log "Service $service is running"
        fi
    done
    
    log "Infrastructure deployed successfully"
}

# Run migration
run_migration() {
    log "Running data migration..."
    
    if [[ -f "$PROJECT_ROOT/legal_cases.db" ]]; then
        # Wait a bit more for databases to be fully ready
        log "Waiting for databases to be ready for migration..."
        sleep 30
        
        # Load environment variables
        source "$PROJECT_ROOT/.env"
        export $(grep -v '^#' "$PROJECT_ROOT/.env" | xargs)
        
        python3 "$PROJECT_ROOT/migration/migrate_to_secure_architecture.py" \
            --sqlite-db "$PROJECT_ROOT/legal_cases.db" \
            --documents-dir "$PROJECT_ROOT/case_documents" \
            --config "$PROJECT_ROOT/config/database.json" || {
                warning "Migration encountered issues, but continuing..."
            }
    else
        info "No existing SQLite database found, skipping migration"
    fi
    
    log "Migration phase completed"
}

# Start new application
start_secure_app() {
    log "Starting secure application..."
    
    # Load environment variables
    source "$PROJECT_ROOT/.env"
    
    # Start the application with new environment
    cd "$PROJECT_ROOT"
    nohup streamlit run legal_case_manager.py --server.port=8502 --server.address=0.0.0.0 > streamlit_secure.log 2>&1 &
    
    sleep 10
    
    if pgrep -f "streamlit.*8502" > /dev/null; then
        log "Secure application started successfully on port 8502"
        info "Original SQLite application (port 8501): Still running for comparison"
        info "New secure application (port 8502): https://localhost:8502"
    else
        warning "Application may not have started properly. Check streamlit_secure.log"
    fi
}

# Main deployment function
main() {
    log "Starting Legal CMS Secure Architecture Deployment (User Mode)"
    
    check_requirements
    create_docker_compose
    generate_env_file
    deploy_infrastructure
    run_migration
    start_secure_app
    
    log "Deployment completed!"
    info "🔒 Secure application: http://localhost:8502"
    info "📊 Original application: http://localhost:8501 (for comparison)"
    info "🐳 Docker services running on ports: 5435 (PostgreSQL), 27018 (MongoDB), 6382 (Redis), 6334 (Qdrant)"
    warning "Store your .env file safely - it contains database passwords!"
}

# Handle script arguments
case "${1:-deploy}" in
    "deploy")
        main
        ;;
    "stop")
        log "Stopping Legal CMS services..."
        cd "$PROJECT_ROOT"
        docker compose down
        pkill -f "streamlit.*8502" || true
        ;;
    "restart")
        log "Restarting Legal CMS services..."
        cd "$PROJECT_ROOT"
        docker compose restart
        ;;
    "logs")
        cd "$PROJECT_ROOT"
        docker compose logs -f "${2:-}"
        ;;
    "status")
        cd "$PROJECT_ROOT"
        docker compose ps
        echo ""
        echo "Streamlit processes:"
        ps aux | grep streamlit | grep -v grep
        ;;
    *)
        echo "Usage: $0 {deploy|stop|restart|logs|status}"
        exit 1
        ;;
esac