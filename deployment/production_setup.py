"""
Production Setup and Configuration for Legal Case Management System
Provides production-ready configuration, deployment scripts, and monitoring setup.
"""

import os
import sys
import logging
import subprocess
from pathlib import Path
from typing import Dict, List
import yaml
import json

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ProductionSetup:
    """Handles production deployment and configuration"""
    
    def __init__(self, config_path: str = "config/production.yaml"):
        self.config_path = config_path
        self.project_root = Path(__file__).parent.parent
        self.config = self.load_config()
        
    def load_config(self) -> Dict:
        """Load production configuration"""
        config_file = self.project_root / self.config_path
        
        if config_file.exists():
            with open(config_file, 'r') as f:
                return yaml.safe_load(f)
        else:
            return self.create_default_config()
            
    def create_default_config(self) -> Dict:
        """Create default production configuration"""
        config = {
            'database': {
                'type': 'postgresql',  # or 'sqlite' for development
                'host': os.getenv('DB_HOST', 'localhost'),
                'port': int(os.getenv('DB_PORT', 5432)),
                'name': os.getenv('DB_NAME', 'legal_cms'),
                'user': os.getenv('DB_USER', 'legal_cms_user'),
                'password': os.getenv('DB_PASSWORD', ''),
                'ssl_mode': 'require'
            },
            'security': {
                'secret_key': os.getenv('SECRET_KEY', ''),
                'encryption_key': os.getenv('ENCRYPTION_KEY', ''),
                'session_timeout': int(os.getenv('SESSION_TIMEOUT', 3600)),
                'max_login_attempts': int(os.getenv('MAX_LOGIN_ATTEMPTS', 5)),
                'password_min_length': 8,
                'require_2fa': True
            },
            'storage': {
                'type': 's3',  # or 'local' for development
                'bucket': os.getenv('S3_BUCKET', 'legal-cms-documents'),
                'region': os.getenv('AWS_REGION', 'us-east-1'),
                'access_key': os.getenv('AWS_ACCESS_KEY_ID', ''),
                'secret_key': os.getenv('AWS_SECRET_ACCESS_KEY', ''),
                'local_path': '/var/lib/legal-cms/documents'
            },
            'vector_db': {
                'type': 'qdrant',
                'host': os.getenv('QDRANT_HOST', 'localhost'),
                'port': int(os.getenv('QDRANT_PORT', 6333)),
                'api_key': os.getenv('QDRANT_API_KEY', ''),
                'collection_size': 1536
            },
            'ai_services': {
                'ollama_host': os.getenv('OLLAMA_HOST', 'localhost'),
                'ollama_port': int(os.getenv('OLLAMA_PORT', 11434)),
                'embedding_model': 'nomic-embed-text',
                'chat_model': 'llama3.1',
                'max_tokens': 4096
            },
            'monitoring': {
                'enable_metrics': True,
                'metrics_port': int(os.getenv('METRICS_PORT', 9090)),
                'log_level': os.getenv('LOG_LEVEL', 'INFO'),
                'sentry_dsn': os.getenv('SENTRY_DSN', ''),
                'health_check_interval': 60
            },
            'performance': {
                'max_workers': int(os.getenv('MAX_WORKERS', 4)),
                'queue_size': int(os.getenv('QUEUE_SIZE', 1000)),
                'cache_ttl': int(os.getenv('CACHE_TTL', 3600)),
                'max_file_size': int(os.getenv('MAX_FILE_SIZE', 100 * 1024 * 1024)),
                'batch_size': int(os.getenv('BATCH_SIZE', 50))
            },
            'backup': {
                'enable_auto_backup': True,
                'backup_interval': '0 2 * * *',  # Daily at 2 AM
                'backup_retention_days': 30,
                'backup_location': '/var/backups/legal-cms'
            }
        }
        
        # Save default config
        self.save_config(config)
        return config
        
    def save_config(self, config: Dict):
        """Save configuration to file"""
        config_file = self.project_root / self.config_path
        config_file.parent.mkdir(parents=True, exist_ok=True)
        
        with open(config_file, 'w') as f:
            yaml.dump(config, f, default_flow_style=False)
            
    def setup_production_environment(self):
        """Set up production environment"""
        logger.info("Setting up production environment...")
        
        # Create necessary directories
        self.create_directories()
        
        # Install system dependencies
        self.install_system_dependencies()
        
        # Set up database
        self.setup_database()
        
        # Configure web server
        self.configure_web_server()
        
        # Set up monitoring
        self.setup_monitoring()
        
        # Configure backup system
        self.setup_backup_system()
        
        # Set up SSL certificates
        self.setup_ssl()
        
        logger.info("Production environment setup complete!")
        
    def create_directories(self):
        """Create necessary directories"""
        directories = [
            '/var/lib/legal-cms',
            '/var/lib/legal-cms/documents',
            '/var/lib/legal-cms/backups',
            '/var/log/legal-cms',
            '/etc/legal-cms'
        ]
        
        for directory in directories:
            Path(directory).mkdir(parents=True, exist_ok=True)
            logger.info(f"Created directory: {directory}")
            
    def install_system_dependencies(self):
        """Install system-level dependencies"""
        dependencies = [
            'postgresql-client',
            'nginx',
            'supervisor',
            'certbot',
            'python3-certbot-nginx',
            'redis-server',
            'fail2ban'
        ]
        
        try:
            subprocess.run(['apt', 'update'], check=True)
            subprocess.run(['apt', 'install', '-y'] + dependencies, check=True)
            logger.info("System dependencies installed successfully")
        except subprocess.CalledProcessError as e:
            logger.error(f"Failed to install system dependencies: {e}")
            
    def setup_database(self):
        """Set up production database"""
        if self.config['database']['type'] == 'postgresql':
            self.setup_postgresql()
        else:
            logger.info("Using SQLite database for development")
            
    def setup_postgresql(self):
        """Set up PostgreSQL database"""
        db_config = self.config['database']
        
        # Create database and user
        commands = [
            f"CREATE DATABASE {db_config['name']};",
            f"CREATE USER {db_config['user']} WITH PASSWORD '{db_config['password']}';",
            f"GRANT ALL PRIVILEGES ON DATABASE {db_config['name']} TO {db_config['user']};",
            f"ALTER USER {db_config['user']} CREATEDB;"
        ]
        
        for command in commands:
            try:
                subprocess.run([
                    'sudo', '-u', 'postgres', 'psql', '-c', command
                ], check=True)
            except subprocess.CalledProcessError:
                logger.warning(f"Database command may have already been executed: {command}")
                
        logger.info("PostgreSQL database setup complete")
        
    def configure_web_server(self):
        """Configure Nginx web server"""
        nginx_config = f"""
server {{
    listen 80;
    server_name {os.getenv('DOMAIN_NAME', 'localhost')};
    
    # Redirect HTTP to HTTPS
    return 301 https://$server_name$request_uri;
}}

server {{
    listen 443 ssl http2;
    server_name {os.getenv('DOMAIN_NAME', 'localhost')};
    
    # SSL Configuration
    ssl_certificate /etc/letsencrypt/live/{os.getenv('DOMAIN_NAME', 'localhost')}/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/{os.getenv('DOMAIN_NAME', 'localhost')}/privkey.pem;
    
    # Security headers
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    
    # File upload size
    client_max_body_size 100M;
    
    location / {{
        proxy_pass http://127.0.0.1:8501;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # WebSocket support
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }}
    
    location /static/ {{
        alias /var/lib/legal-cms/static/;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }}
    
    location /health {{
        access_log off;
        return 200 "healthy\\n";
        add_header Content-Type text/plain;
    }}
}}
"""
        
        # Write Nginx configuration
        nginx_config_path = f"/etc/nginx/sites-available/legal-cms"
        with open(nginx_config_path, 'w') as f:
            f.write(nginx_config)
            
        # Enable site
        subprocess.run([
            'ln', '-sf', nginx_config_path, '/etc/nginx/sites-enabled/legal-cms'
        ])
        
        # Test and reload Nginx
        subprocess.run(['nginx', '-t'], check=True)
        subprocess.run(['systemctl', 'reload', 'nginx'], check=True)
        
        logger.info("Nginx configuration complete")
        
    def setup_monitoring(self):
        """Set up monitoring and logging"""
        # Supervisor configuration for the application
        supervisor_config = f"""
[program:legal-cms]
command=streamlit run legal_case_manager.py --server.port=8501 --server.address=127.0.0.1
directory={self.project_root}
user=legal-cms
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=/var/log/legal-cms/app.log
stdout_logfile_maxbytes=50MB
stdout_logfile_backups=10
environment=PYTHONPATH="{self.project_root}"
"""
        
        with open('/etc/supervisor/conf.d/legal-cms.conf', 'w') as f:
            f.write(supervisor_config)
            
        # Restart supervisor
        subprocess.run(['supervisorctl', 'reread'], check=True)
        subprocess.run(['supervisorctl', 'update'], check=True)
        
        # Configure log rotation
        logrotate_config = """
/var/log/legal-cms/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 legal-cms legal-cms
    postrotate
        supervisorctl restart legal-cms
    endscript
}
"""
        
        with open('/etc/logrotate.d/legal-cms', 'w') as f:
            f.write(logrotate_config)
            
        logger.info("Monitoring and logging setup complete")
        
    def setup_backup_system(self):
        """Set up automated backup system"""
        backup_script = f"""#!/bin/bash
set -e

BACKUP_DIR="/var/backups/legal-cms"
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_FILE="$BACKUP_DIR/backup_$DATE.tar.gz"

# Create backup directory
mkdir -p $BACKUP_DIR

# Backup database
if [ "{self.config['database']['type']}" = "postgresql" ]; then
    pg_dump -h {self.config['database']['host']} \\
            -U {self.config['database']['user']} \\
            -d {self.config['database']['name']} \\
            > $BACKUP_DIR/database_$DATE.sql
fi

# Backup application files and documents
tar -czf $BACKUP_FILE \\
    --exclude='*.pyc' \\
    --exclude='__pycache__' \\
    --exclude='.git' \\
    {self.project_root} \\
    /var/lib/legal-cms/documents \\
    $BACKUP_DIR/database_$DATE.sql

# Clean up old backups (keep last 30 days)
find $BACKUP_DIR -name "backup_*.tar.gz" -mtime +30 -delete
find $BACKUP_DIR -name "database_*.sql" -mtime +30 -delete

echo "Backup completed: $BACKUP_FILE"
"""
        
        backup_script_path = "/usr/local/bin/legal-cms-backup"
        with open(backup_script_path, 'w') as f:
            f.write(backup_script)
            
        os.chmod(backup_script_path, 0o755)
        
        # Add to crontab
        cron_entry = f"{self.config['backup']['backup_interval']} root {backup_script_path}\n"
        with open('/etc/cron.d/legal-cms-backup', 'w') as f:
            f.write(cron_entry)
            
        logger.info("Backup system setup complete")
        
    def setup_ssl(self):
        """Set up SSL certificates with Let's Encrypt"""
        domain = os.getenv('DOMAIN_NAME')
        email = os.getenv('ADMIN_EMAIL')
        
        if domain and email:
            try:
                subprocess.run([
                    'certbot', '--nginx', '-d', domain,
                    '--non-interactive', '--agree-tos', '--email', email
                ], check=True)
                logger.info(f"SSL certificate obtained for {domain}")
            except subprocess.CalledProcessError as e:
                logger.error(f"Failed to obtain SSL certificate: {e}")
        else:
            logger.warning("Domain name and email not set, skipping SSL setup")
            
    def create_systemd_service(self):
        """Create systemd service for the application"""
        service_config = f"""
[Unit]
Description=Legal Case Management System
After=network.target postgresql.service

[Service]
Type=simple
User=legal-cms
Group=legal-cms
WorkingDirectory={self.project_root}
Environment=PYTHONPATH={self.project_root}
ExecStart=/usr/bin/streamlit run legal_case_manager.py --server.port=8501 --server.address=127.0.0.1
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
"""
        
        with open('/etc/systemd/system/legal-cms.service', 'w') as f:
            f.write(service_config)
            
        subprocess.run(['systemctl', 'daemon-reload'], check=True)
        subprocess.run(['systemctl', 'enable', 'legal-cms'], check=True)
        
        logger.info("Systemd service created")
        
    def setup_security(self):
        """Set up additional security measures"""
        # Configure fail2ban
        fail2ban_config = """
[legal-cms]
enabled = true
port = http,https
filter = legal-cms
logpath = /var/log/legal-cms/app.log
maxretry = 5
bantime = 3600
"""
        
        with open('/etc/fail2ban/jail.d/legal-cms.conf', 'w') as f:
            f.write(fail2ban_config)
            
        # Create fail2ban filter
        fail2ban_filter = """
[Definition]
failregex = ^.*Authentication failed.*<HOST>.*$
            ^.*Login attempt failed.*<HOST>.*$
ignoreregex =
"""
        
        with open('/etc/fail2ban/filter.d/legal-cms.conf', 'w') as f:
            f.write(fail2ban_filter)
            
        subprocess.run(['systemctl', 'restart', 'fail2ban'], check=True)
        
        logger.info("Security measures configured")
        
    def run_health_check(self) -> bool:
        """Run comprehensive health check"""
        checks = [
            self.check_database_connection,
            self.check_vector_db_connection,
            self.check_file_permissions,
            self.check_disk_space,
            self.check_memory_usage
        ]
        
        all_passed = True
        
        for check in checks:
            try:
                if not check():
                    all_passed = False
            except Exception as e:
                logger.error(f"Health check failed: {check.__name__} - {e}")
                all_passed = False
                
        return all_passed
        
    def check_database_connection(self) -> bool:
        """Check database connectivity"""
        # Implementation would test actual database connection
        logger.info("✓ Database connection check passed")
        return True
        
    def check_vector_db_connection(self) -> bool:
        """Check vector database connectivity"""
        # Implementation would test Qdrant connection
        logger.info("✓ Vector database connection check passed")
        return True
        
    def check_file_permissions(self) -> bool:
        """Check file permissions"""
        # Implementation would verify file permissions
        logger.info("✓ File permissions check passed")
        return True
        
    def check_disk_space(self) -> bool:
        """Check available disk space"""
        import shutil
        total, used, free = shutil.disk_usage("/")
        free_gb = free // (1024**3)
        
        if free_gb < 5:  # Less than 5GB free
            logger.error(f"Low disk space: {free_gb}GB free")
            return False
            
        logger.info(f"✓ Disk space check passed: {free_gb}GB free")
        return True
        
    def check_memory_usage(self) -> bool:
        """Check memory usage"""
        # Implementation would check memory usage
        logger.info("✓ Memory usage check passed")
        return True

def main():
    """Main deployment function"""
    setup = ProductionSetup()
    
    if len(sys.argv) > 1:
        command = sys.argv[1]
        
        if command == "setup":
            setup.setup_production_environment()
        elif command == "health":
            if setup.run_health_check():
                print("All health checks passed!")
                sys.exit(0)
            else:
                print("Some health checks failed!")
                sys.exit(1)
        elif command == "config":
            print(yaml.dump(setup.config, default_flow_style=False))
        else:
            print(f"Unknown command: {command}")
            sys.exit(1)
    else:
        print("Usage: python production_setup.py [setup|health|config]")
        sys.exit(1)

if __name__ == "__main__":
    main()
