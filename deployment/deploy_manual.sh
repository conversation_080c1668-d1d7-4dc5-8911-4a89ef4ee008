#!/bin/bash
# Manual Docker Deployment for Legal CMS
set -e

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# Colors
GREEN='\033[0;32m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m'

log() {
    echo -e "${GREEN}[$(date +'%H:%M:%S')]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
    exit 1
}

info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

# Generate secure passwords
generate_passwords() {
    log "Generating secure passwords..."
    
    POSTGRES_PASSWORD=$(openssl rand -base64 16 | tr -d "=+/" | cut -c1-12)
    MONGODB_PASSWORD=$(openssl rand -base64 16 | tr -d "=+/" | cut -c1-12)
    REDIS_PASSWORD=$(openssl rand -base64 16 | tr -d "=+/" | cut -c1-12)
    ENCRYPTION_KEY=$(python3 -c "from cryptography.fernet import Fernet; print(Fernet.generate_key().decode())")
    JWT_SECRET=$(openssl rand -base64 32)
    
    cat > "$PROJECT_ROOT/.env" << EOF
# Database passwords (generated $(date))
POSTGRES_PASSWORD=$POSTGRES_PASSWORD
MONGODB_PASSWORD=$MONGODB_PASSWORD
REDIS_PASSWORD=$REDIS_PASSWORD
ENCRYPTION_KEY=$ENCRYPTION_KEY
JWT_SECRET_KEY=$JWT_SECRET
USE_SECURE_ARCHITECTURE=true

# Connection URLs
POSTGRES_URL=postgresql://legal_cms_user:$POSTGRES_PASSWORD@localhost:5435/legal_cms_main
MONGODB_URL=*********************************************************************
REDIS_URL=redis://:$REDIS_PASSWORD@localhost:6382/0
QDRANT_URL=http://localhost:6334
EOF
    
    chmod 600 "$PROJECT_ROOT/.env"
    log "Passwords saved to .env file"
}

# Create docker network
create_network() {
    log "Creating Docker network..."
    docker network create legal-cms-net 2>/dev/null || info "Network already exists"
}

# Deploy PostgreSQL
deploy_postgresql() {
    log "Deploying PostgreSQL..."
    
    docker run -d \
        --name legal-cms-postgresql \
        --network legal-cms-net \
        -p 5435:5432 \
        -e POSTGRES_DB=legal_cms_main \
        -e POSTGRES_USER=legal_cms_user \
        -e POSTGRES_PASSWORD="$POSTGRES_PASSWORD" \
        -v legal_cms_postgres_data:/var/lib/postgresql/data \
        --restart unless-stopped \
        postgres:15-alpine || info "PostgreSQL container already exists"
}

# Deploy MongoDB
deploy_mongodb() {
    log "Deploying MongoDB..."
    
    docker run -d \
        --name legal-cms-mongodb \
        --network legal-cms-net \
        -p 27018:27017 \
        -e MONGO_INITDB_ROOT_USERNAME=admin \
        -e MONGO_INITDB_ROOT_PASSWORD="$MONGODB_PASSWORD" \
        -e MONGO_INITDB_DATABASE=legal_cms_documents \
        -v legal_cms_mongo_data:/data/db \
        --restart unless-stopped \
        mongo:7.0 || info "MongoDB container already exists"
}

# Deploy Redis
deploy_redis() {
    log "Deploying Redis..."
    
    docker run -d \
        --name legal-cms-redis \
        --network legal-cms-net \
        -p 6382:6379 \
        -v legal_cms_redis_data:/data \
        --restart unless-stopped \
        redis:7-alpine redis-server --requirepass "$REDIS_PASSWORD" || info "Redis container already exists"
}

# Deploy Qdrant
deploy_qdrant() {
    log "Deploying Qdrant Vector DB..."
    
    docker run -d \
        --name legal-cms-qdrant \
        --network legal-cms-net \
        -p 6334:6333 \
        -v legal_cms_qdrant_data:/qdrant/storage \
        --restart unless-stopped \
        qdrant/qdrant || info "Qdrant container already exists"
}

# Wait for services
wait_for_services() {
    log "Waiting for services to start..."
    sleep 30
    
    # Check each service
    services=("legal-cms-postgresql" "legal-cms-mongodb" "legal-cms-redis" "legal-cms-qdrant")
    for service in "${services[@]}"; do
        if docker ps | grep -q "$service"; then
            log "✓ $service is running"
        else
            error "✗ $service failed to start"
        fi
    done
}

# Run migration
run_migration() {
    log "Running database migration..."
    
    if [[ -f "$PROJECT_ROOT/legal_cases.db" ]]; then
        cd "$PROJECT_ROOT"
        source .env
        export $(grep -v '^#' .env | xargs)
        
        # Wait a bit longer for databases
        sleep 15
        
        python3 migration/migrate_to_secure_architecture.py \
            --sqlite-db legal_cases.db \
            --documents-dir case_documents \
            --config config/database.json || {
                info "Migration completed with warnings (this is normal for first run)"
            }
    else
        info "No SQLite database found, skipping migration"
    fi
}

# Start secure application
start_application() {
    log "Starting secure application..."
    
    # Kill existing streamlit on 8502 if running
    pkill -f "streamlit.*8502" 2>/dev/null || true
    sleep 2
    
    cd "$PROJECT_ROOT"
    source .env
    
    # Start new secure version
    nohup streamlit run legal_case_manager.py --server.port=8502 --server.address=0.0.0.0 > streamlit_secure.log 2>&1 &
    
    sleep 8
    
    if pgrep -f "streamlit.*8502" > /dev/null; then
        log "🔒 Secure application started on port 8502!"
    else
        error "Failed to start secure application"
    fi
}

# Main deployment
deploy() {
    log "🚀 Starting Legal CMS Secure Deployment"
    
    generate_passwords
    create_network
    
    # Load generated passwords
    source "$PROJECT_ROOT/.env"
    
    deploy_postgresql
    deploy_mongodb
    deploy_redis
    deploy_qdrant
    
    wait_for_services
    run_migration
    start_application
    
    log "🎉 Deployment complete!"
    info "🔒 Secure app: http://localhost:8502"
    info "📊 Original app: http://localhost:8501 (still running)"
    info "💾 Passwords stored in: .env"
    info ""
    info "Database ports:"
    info "  PostgreSQL: localhost:5435"
    info "  MongoDB:    localhost:27018" 
    info "  Redis:      localhost:6382"
    info "  Qdrant:     localhost:6334"
}

# Handle commands
case "${1:-deploy}" in
    "deploy")
        deploy
        ;;
    "status")
        echo "Docker containers:"
        docker ps | grep legal-cms
        echo ""
        echo "Streamlit processes:"
        ps aux | grep streamlit | grep -v grep
        ;;
    "stop")
        log "Stopping Legal CMS services..."
        docker stop legal-cms-postgresql legal-cms-mongodb legal-cms-redis legal-cms-qdrant 2>/dev/null || true
        pkill -f "streamlit.*8502" 2>/dev/null || true
        ;;
    "clean")
        log "Cleaning up Legal CMS deployment..."
        docker stop legal-cms-postgresql legal-cms-mongodb legal-cms-redis legal-cms-qdrant 2>/dev/null || true
        docker rm legal-cms-postgresql legal-cms-mongodb legal-cms-redis legal-cms-qdrant 2>/dev/null || true
        docker network rm legal-cms-net 2>/dev/null || true
        pkill -f "streamlit.*8502" 2>/dev/null || true
        ;;
    *)
        echo "Usage: $0 {deploy|status|stop|clean}"
        exit 1
        ;;
esac