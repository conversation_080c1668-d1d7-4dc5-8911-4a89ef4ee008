#!/bin/bash
# Deploy Zero-Exposure Law Firm SaaS Container
# SECURITY FIRST: Only port 443 exposed to the outside world
# All databases are internal-only with no external access

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}"
echo "╔══════════════════════════════════════════════════════════════╗"
echo "║           ZERO-EXPOSURE LAW FIRM SaaS - HTTPS ONLY           ║"
echo "║              MAXIMUM SECURITY DEPLOYMENT                     ║"
echo "║                Only Port 443 Exposed                        ║"
echo "╚══════════════════════════════════════════════════════════════╝"
echo -e "${NC}"

# Configuration variables
CONTAINER_NAME="zero-exposure-law-firm-saas"
IMAGE_NAME="zero-exposure-law-firm-saas:latest"
DOMAIN=${DOMAIN:-"legal-cms.local"}
SSL_EMAIL=${SSL_EMAIL:-"<EMAIL>"}

echo -e "${YELLOW}📋 Security-First Deployment Configuration:${NC}"
echo "   Container Name: $CONTAINER_NAME"
echo "   Image: $IMAGE_NAME"
echo "   Domain: $DOMAIN"
echo "   SSL Email: $SSL_EMAIL"
echo "   External Ports: 443 ONLY (HTTPS)"
echo "   Database Access: INTERNAL ONLY"
echo ""

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check prerequisites
echo -e "${YELLOW}🔍 Checking prerequisites...${NC}"

if ! command_exists docker; then
    echo -e "${RED}❌ Docker is not installed${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Prerequisites check passed${NC}"

# Create necessary directories
echo -e "${YELLOW}📁 Creating directories...${NC}"
mkdir -p ssl ssl-private config scripts logs

# Stop and remove existing container if it exists
echo -e "${YELLOW}🛑 Stopping existing container if running...${NC}"
docker stop $CONTAINER_NAME 2>/dev/null || true
docker rm $CONTAINER_NAME 2>/dev/null || true

# Remove existing image to force rebuild
echo -e "${YELLOW}🗑️  Removing existing image...${NC}"
docker rmi $IMAGE_NAME 2>/dev/null || true

# Build the zero-exposure container using core modules
echo -e "${YELLOW}🏗️  Building Zero-Exposure Law Firm SaaS container (using core architecture)...${NC}"
echo "   Using modular core components from /app/core/"
docker build -f Dockerfile.zero-exposure -t $IMAGE_NAME .

if [ $? -ne 0 ]; then
    echo -e "${RED}❌ Failed to build container${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Container built successfully${NC}"

# Generate self-signed certificates for development
echo -e "${YELLOW}🔒 Generating SSL certificates...${NC}"
mkdir -p ssl ssl-private

# Generate private key
openssl genrsa -out ssl-private/legal-cms.key 2048

# Generate certificate signing request
openssl req -new -key ssl-private/legal-cms.key -out ssl/legal-cms.csr -subj "/C=US/ST=State/L=City/O=Legal CMS/CN=$DOMAIN"

# Generate self-signed certificate
openssl x509 -req -days 365 -in ssl/legal-cms.csr -signkey ssl-private/legal-cms.key -out ssl/legal-cms.crt

echo -e "${GREEN}✅ SSL certificates generated${NC}"

# Create and start the container with MAXIMUM SECURITY
echo -e "${YELLOW}🚀 Starting Zero-Exposure Law Firm SaaS...${NC}"

docker run -d \
    --name $CONTAINER_NAME \
    --hostname legal-cms-secure \
    --restart unless-stopped \
    -p 443:443 \
    -e DOMAIN=$DOMAIN \
    -e SSL_EMAIL=$SSL_EMAIL \
    -e POSTGRES_PASSWORD=SecureLawFirmPassword2025 \
    -e MONGODB_PASSWORD=SecureMongoLawPassword2025 \
    -e REDIS_PASSWORD=SecureRedisLawPassword2025 \
    -e VAULT_DEV_ROOT_TOKEN_ID=law-firm-vault-token-2025 \
    -v law-firm-app-data:/app/data \
    -v law-firm-case-documents:/app/case_documents \
    -v law-firm-exports:/app/legal_exports \
    -v law-firm-logs:/app/logs \
    -v law-firm-postgres-data:/var/lib/postgresql/data \
    -v law-firm-mongo-data:/data/db \
    -v law-firm-redis-data:/data \
    -v law-firm-qdrant-data:/qdrant/storage \
    -v law-firm-vault-data:/vault/data \
    -v $(pwd)/ssl:/etc/ssl/certs/legal-cms:ro \
    -v $(pwd)/ssl-private:/etc/ssl/private/legal-cms:ro \
    $IMAGE_NAME

if [ $? -ne 0 ]; then
    echo -e "${RED}❌ Failed to start container${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Container started successfully${NC}"

# Wait for services to initialize
echo -e "${YELLOW}⏳ Waiting for services to initialize...${NC}"
sleep 45

# Check container status
if ! docker ps | grep -q $CONTAINER_NAME; then
    echo -e "${RED}❌ Container failed to start properly${NC}"
    echo -e "${YELLOW}📋 Container logs:${NC}"
    docker logs $CONTAINER_NAME
    exit 1
fi

# Test services internally
echo -e "${YELLOW}🧪 Testing services (internal access only)...${NC}"

echo -n "   Testing HTTPS... "
if curl -k -s -o /dev/null -w "%{http_code}" https://localhost:443/ | grep -q "200"; then
    echo -e "${GREEN}✅${NC}"
else
    echo -e "${YELLOW}⚠️  HTTPS not responding yet (may need more time)${NC}"
fi

echo -n "   Testing PostgreSQL (internal)... "
if docker exec $CONTAINER_NAME pg_isready -U legal_cms_user -d legal_cms_main -q; then
    echo -e "${GREEN}✅${NC}"
else
    echo -e "${YELLOW}⚠️  PostgreSQL not ready${NC}"
fi

echo -n "   Testing MongoDB (internal)... "
if docker exec $CONTAINER_NAME mongosh --eval "db.runCommand('ping')" --quiet 2>/dev/null; then
    echo -e "${GREEN}✅${NC}"
else
    echo -e "${YELLOW}⚠️  MongoDB not ready${NC}"
fi

echo -n "   Testing Redis (internal)... "
if docker exec $CONTAINER_NAME redis-cli ping 2>/dev/null | grep -q "PONG"; then
    echo -e "${GREEN}✅${NC}"
else
    echo -e "${YELLOW}⚠️  Redis not ready${NC}"
fi

# Security verification
echo -e "${YELLOW}🔒 Verifying security configuration...${NC}"

echo -n "   Checking port exposure... "
EXPOSED_PORTS=$(docker port $CONTAINER_NAME | wc -l)
if [ "$EXPOSED_PORTS" -eq 1 ]; then
    echo -e "${GREEN}✅ Only 1 port exposed (443)${NC}"
else
    echo -e "${RED}❌ Multiple ports exposed ($EXPOSED_PORTS)${NC}"
fi

echo -n "   Testing database isolation... "
if ! nc -z localhost 5432 2>/dev/null && ! nc -z localhost 27017 2>/dev/null && ! nc -z localhost 6379 2>/dev/null; then
    echo -e "${GREEN}✅ Databases not externally accessible${NC}"
else
    echo -e "${RED}❌ Some databases may be externally accessible${NC}"
fi

# Show deployment summary
echo ""
echo -e "${GREEN}🎉 ZERO-EXPOSURE LAW FIRM SaaS DEPLOYMENT COMPLETE!${NC}"
echo "════════════════════════════════════════════════════════════"
echo ""
echo -e "${BLUE}🌐 Web Interface:${NC}"
echo "   HTTPS ONLY: https://localhost/ (Port 443)"
echo "   HTTP:       BLOCKED (No HTTP access)"
echo ""
echo -e "${BLUE}🔒 Security Features:${NC}"
echo "   ✅ Only port 443 exposed"
echo "   ✅ All databases internal-only"
echo "   ✅ SSL/TLS encryption required"
echo "   ✅ No external database access"
echo "   ✅ Container-isolated services"
echo ""
echo -e "${BLUE}🔧 Management:${NC}"
echo "   Container:     $CONTAINER_NAME"
echo "   Logs:          docker logs $CONTAINER_NAME"
echo "   Stop:          docker stop $CONTAINER_NAME"
echo "   Restart:       docker restart $CONTAINER_NAME"
echo "   Shell Access:  docker exec -it $CONTAINER_NAME /bin/bash"
echo ""
echo -e "${BLUE}🗄️  Database Access (Internal Only):${NC}"
echo "   PostgreSQL: docker exec -it $CONTAINER_NAME psql -U legal_cms_user -d legal_cms_main"
echo "   MongoDB:    docker exec -it $CONTAINER_NAME mongosh"
echo "   Redis:      docker exec -it $CONTAINER_NAME redis-cli"
echo ""
echo -e "${YELLOW}⚠️  Security Notes:${NC}"
echo "   • Databases are NOT accessible from outside the container"
echo "   • Only HTTPS (port 443) is exposed to the network"
echo "   • Use 'docker exec' commands for database administration"
echo "   • Change default passwords in production"
echo "   • Configure proper domain and SSL certificate for production"
echo ""
echo -e "${GREEN}🛡️  Your Zero-Exposure Law Firm SaaS is ready and secure!${NC}"