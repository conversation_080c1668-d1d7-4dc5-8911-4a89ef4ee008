#!/usr/bin/env python3
"""
Containerized Deployment Script for Legal Case Management System
Deploys all services as independent containers with comprehensive testing.
"""

import os
import sys
import time
import json
import subprocess
import logging
from pathlib import Path
from typing import Dict, List, Tuple, Optional

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('deployment.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class ContainerizedDeployment:
    """Manages containerized deployment of Legal CMS"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.compose_file = self.project_root / "config" / "docker-compose.yml"
        self.env_file = self.project_root / ".env"
        
        # Service health check endpoints
        self.health_checks = {
            'postgresql': ('localhost', 5435, self._check_postgresql),
            'mongodb': ('localhost', 27018, self._check_mongodb),
            'redis': ('localhost', 6382, self._check_redis),
            'qdrant': ('localhost', 6334, self._check_qdrant),
            'app': ('localhost', 8501, self._check_streamlit),
            'nginx': ('localhost', 443, self._check_nginx)
        }
    
    def check_prerequisites(self) -> bool:
        """Check if all prerequisites are met"""
        logger.info("🔍 Checking deployment prerequisites...")
        
        # Check Docker
        try:
            result = subprocess.run(['docker', '--version'], capture_output=True, text=True)
            if result.returncode != 0:
                logger.error("❌ Docker not found or not running")
                return False
            logger.info(f"✅ Docker: {result.stdout.strip()}")
        except FileNotFoundError:
            logger.error("❌ Docker not installed")
            return False
        
        # Check Docker Compose
        try:
            result = subprocess.run(['docker', 'compose', 'version'], capture_output=True, text=True)
            if result.returncode != 0:
                logger.error("❌ Docker Compose not found")
                return False
            logger.info(f"✅ Docker Compose: {result.stdout.strip()}")
        except FileNotFoundError:
            logger.error("❌ Docker Compose not installed")
            return False
        
        # Check environment file
        if not self.env_file.exists():
            logger.error(f"❌ Environment file not found: {self.env_file}")
            logger.info("Run: cp config/.env.template .env and configure it")
            return False
        logger.info("✅ Environment file exists")
        
        # Check compose file
        if not self.compose_file.exists():
            logger.error(f"❌ Docker Compose file not found: {self.compose_file}")
            return False
        logger.info("✅ Docker Compose file exists")
        
        return True
    
    def cleanup_existing_containers(self) -> bool:
        """Clean up any existing containers"""
        logger.info("🧹 Cleaning up existing containers...")
        
        try:
            # Stop and remove containers
            subprocess.run([
                'docker', 'compose', '-f', str(self.compose_file),
                'down', '--volumes', '--remove-orphans'
            ], check=True, cwd=self.project_root)
            
            # Remove any dangling images
            subprocess.run(['docker', 'image', 'prune', '-f'], check=True)
            
            logger.info("✅ Cleanup completed")
            return True
            
        except subprocess.CalledProcessError as e:
            logger.error(f"❌ Cleanup failed: {e}")
            return False
    
    def build_and_deploy(self) -> bool:
        """Build and deploy all containers"""
        logger.info("🚀 Building and deploying containers...")
        
        try:
            # Build and start services
            subprocess.run([
                'docker', 'compose', '-f', str(self.compose_file),
                'up', '--build', '-d'
            ], check=True, cwd=self.project_root)
            
            logger.info("✅ Containers deployed successfully")
            return True
            
        except subprocess.CalledProcessError as e:
            logger.error(f"❌ Deployment failed: {e}")
            return False
    
    def wait_for_services(self, timeout: int = 300) -> bool:
        """Wait for all services to be healthy"""
        logger.info("⏳ Waiting for services to be healthy...")
        
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            all_healthy = True
            
            for service, (host, port, check_func) in self.health_checks.items():
                if not check_func(host, port):
                    all_healthy = False
                    logger.info(f"⏳ Waiting for {service}...")
                    break
            
            if all_healthy:
                logger.info("✅ All services are healthy!")
                return True
            
            time.sleep(10)
        
        logger.error("❌ Services failed to become healthy within timeout")
        return False
    
    def _check_postgresql(self, host: str, port: int) -> bool:
        """Check PostgreSQL health"""
        try:
            import psycopg2
            conn = psycopg2.connect(
                host=host, port=port, database='legal_cms_main',
                user='legal_cms_user', password=os.getenv('POSTGRES_PASSWORD')
            )
            conn.close()
            return True
        except:
            return False
    
    def _check_mongodb(self, host: str, port: int) -> bool:
        """Check MongoDB health"""
        try:
            from pymongo import MongoClient
            client = MongoClient(f'mongodb://admin:{os.getenv("MONGODB_PASSWORD")}@{host}:{port}/')
            client.admin.command('ping')
            client.close()
            return True
        except:
            return False
    
    def _check_redis(self, host: str, port: int) -> bool:
        """Check Redis health"""
        try:
            import redis
            r = redis.Redis(host=host, port=port, password=os.getenv('REDIS_PASSWORD'))
            r.ping()
            return True
        except:
            return False
    
    def _check_qdrant(self, host: str, port: int) -> bool:
        """Check Qdrant health"""
        try:
            import requests
            response = requests.get(f'http://{host}:{port}/health', timeout=5)
            return response.status_code == 200
        except:
            return False
    
    def _check_streamlit(self, host: str, port: int) -> bool:
        """Check Streamlit app health"""
        try:
            import requests
            response = requests.get(f'http://{host}:{port}/_stcore/health', timeout=5)
            return response.status_code == 200
        except:
            return False
    
    def _check_nginx(self, host: str, port: int) -> bool:
        """Check Nginx health"""
        try:
            import requests
            response = requests.get(f'https://{host}:{port}/health', 
                                  timeout=5, verify=False)
            return response.status_code == 200
        except:
            return False
    
    def run_tests(self) -> bool:
        """Run comprehensive tests"""
        logger.info("🧪 Running comprehensive tests...")
        
        try:
            # Run the test suite
            result = subprocess.run([
                'python', 'tests/run_simple_tests.py', '--all'
            ], cwd=self.project_root, capture_output=True, text=True)
            
            if result.returncode == 0:
                logger.info("✅ All tests passed!")
                return True
            else:
                logger.error(f"❌ Tests failed: {result.stderr}")
                return False
                
        except Exception as e:
            logger.error(f"❌ Test execution failed: {e}")
            return False
    
    def show_deployment_info(self):
        """Show deployment information"""
        logger.info("\n" + "=" * 60)
        logger.info("🎉 DEPLOYMENT COMPLETE!")
        logger.info("=" * 60)
        
        logger.info("\n📋 Service URLs:")
        logger.info("• Application: https://localhost (Nginx)")
        logger.info("• Application Direct: http://localhost:8501")
        logger.info("• PostgreSQL: localhost:5435")
        logger.info("• MongoDB: localhost:27018")
        logger.info("• Redis: localhost:6382")
        logger.info("• Qdrant: http://localhost:6334")
        
        logger.info("\n🔧 Management Commands:")
        logger.info("• View logs: docker compose -f config/docker-compose.yml logs -f")
        logger.info("• Stop services: docker compose -f config/docker-compose.yml down")
        logger.info("• Restart services: docker compose -f config/docker-compose.yml restart")
        logger.info("• View status: docker compose -f config/docker-compose.yml ps")
        
        logger.info("\n🛡️ Security Notes:")
        logger.info("• All database connections are encrypted")
        logger.info("• SSL/TLS enabled for web interface")
        logger.info("• No hardcoded credentials")
        logger.info("• All data volumes are persistent")
    
    def deploy(self) -> bool:
        """Run complete deployment process"""
        logger.info("🚀 Starting Legal CMS Containerized Deployment")
        logger.info("=" * 60)
        
        steps = [
            ("Check prerequisites", self.check_prerequisites),
            ("Cleanup existing containers", self.cleanup_existing_containers),
            ("Build and deploy containers", self.build_and_deploy),
            ("Wait for services", self.wait_for_services),
            ("Run tests", self.run_tests)
        ]
        
        for step_name, step_func in steps:
            logger.info(f"\n🔄 {step_name}...")
            if not step_func():
                logger.error(f"❌ {step_name} failed!")
                return False
            logger.info(f"✅ {step_name} completed")
        
        self.show_deployment_info()
        return True

def main():
    """Main entry point"""
    deployment = ContainerizedDeployment()
    
    if len(sys.argv) > 1 and sys.argv[1] == '--cleanup-only':
        deployment.cleanup_existing_containers()
        return
    
    success = deployment.deploy()
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
