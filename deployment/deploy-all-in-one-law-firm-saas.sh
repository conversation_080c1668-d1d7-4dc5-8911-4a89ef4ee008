#!/bin/bash
# Deploy All-in-One Law Firm SaaS Container
# Complete legal case management solution with Certbot SSL

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}"
echo "╔══════════════════════════════════════════════════════════════╗"
echo "║                ALL-IN-ONE LAW FIRM SaaS                      ║"
echo "║              Complete Legal Management Solution               ║"
echo "║                    with Certbot SSL                          ║"
echo "╚══════════════════════════════════════════════════════════════╝"
echo -e "${NC}"

# Configuration variables
CONTAINER_NAME="ALL-IN-ONE-LAW-FIRM-SAAS"
IMAGE_NAME="all-in-one-law-firm-saas:latest"
DOMAIN=${DOMAIN:-"law-firm-saas.local"}
SSL_EMAIL=${SSL_EMAIL:-"<EMAIL>"}
DEVELOPMENT=${DEVELOPMENT:-"true"}

echo -e "${YELLOW}📋 Deployment Configuration:${NC}"
echo "   Container Name: $CONTAINER_NAME"
echo "   Image: $IMAGE_NAME"
echo "   Domain: $DOMAIN"
echo "   SSL Email: $SSL_EMAIL"
echo "   Development Mode: $DEVELOPMENT"
echo ""

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check prerequisites
echo -e "${YELLOW}🔍 Checking prerequisites...${NC}"

if ! command_exists docker; then
    echo -e "${RED}❌ Docker is not installed${NC}"
    exit 1
fi

if ! command_exists docker-compose; then
    echo -e "${RED}❌ Docker Compose is not installed${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Prerequisites check passed${NC}"

# Create necessary directories
echo -e "${YELLOW}📁 Creating directories...${NC}"
mkdir -p ssl ssl-private config scripts logs

# Stop and remove existing container if it exists
echo -e "${YELLOW}🛑 Stopping existing container if running...${NC}"
docker stop $CONTAINER_NAME 2>/dev/null || true
docker rm $CONTAINER_NAME 2>/dev/null || true

# Remove existing image to force rebuild
echo -e "${YELLOW}🗑️  Removing existing image...${NC}"
docker rmi $IMAGE_NAME 2>/dev/null || true

# Create the scripts directory if it doesn't exist
mkdir -p scripts

# Build the all-in-one container
echo -e "${YELLOW}🏗️  Building All-in-One Law Firm SaaS container...${NC}"
docker build -f Dockerfile.all-in-one -t $IMAGE_NAME .

if [ $? -ne 0 ]; then
    echo -e "${RED}❌ Failed to build container${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Container built successfully${NC}"

# Create and start the container
echo -e "${YELLOW}🚀 Starting All-in-One Law Firm SaaS...${NC}"

docker run -d \
    --name $CONTAINER_NAME \
    --hostname law-firm-saas \
    --restart unless-stopped \
    -p 80:80 \
    -p 443:443 \
    -p 5435:5432 \
    -p 27018:27017 \
    -p 6382:6379 \
    -p 6334:6333 \
    -p 8200:8200 \
    -e DOMAIN=$DOMAIN \
    -e SSL_EMAIL=$SSL_EMAIL \
    -e DEVELOPMENT=$DEVELOPMENT \
    -e POSTGRES_PASSWORD=SecureLawFirmPassword2025 \
    -e MONGODB_PASSWORD=SecureMongoLawPassword2025 \
    -e REDIS_PASSWORD=SecureRedisLawPassword2025 \
    -e VAULT_DEV_ROOT_TOKEN_ID=law-firm-vault-token-2025 \
    -v law-firm-app-data:/app/data \
    -v law-firm-case-documents:/app/case_documents \
    -v law-firm-exports:/app/legal_exports \
    -v law-firm-logs:/app/logs \
    -v law-firm-postgres-data:/var/lib/postgresql/data \
    -v law-firm-mongo-data:/data/db \
    -v law-firm-redis-data:/data \
    -v law-firm-qdrant-data:/qdrant/storage \
    -v law-firm-vault-data:/vault/data \
    -v law-firm-ssl:/etc/letsencrypt \
    $IMAGE_NAME

if [ $? -ne 0 ]; then
    echo -e "${RED}❌ Failed to start container${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Container started successfully${NC}"

# Wait for services to initialize
echo -e "${YELLOW}⏳ Waiting for services to initialize...${NC}"
sleep 30

# Check container status
if ! docker ps | grep -q $CONTAINER_NAME; then
    echo -e "${RED}❌ Container failed to start properly${NC}"
    echo -e "${YELLOW}📋 Container logs:${NC}"
    docker logs $CONTAINER_NAME
    exit 1
fi

# Initialize SSL certificates
echo -e "${YELLOW}🔒 Initializing SSL certificates...${NC}"
docker exec $CONTAINER_NAME /app/scripts/init-ssl-certbot.sh

# Test services
echo -e "${YELLOW}🧪 Testing services...${NC}"

# Test HTTP redirect
echo -n "   Testing HTTP redirect... "
if curl -s -o /dev/null -w "%{http_code}" http://localhost/ | grep -q "301"; then
    echo -e "${GREEN}✅${NC}"
else
    echo -e "${YELLOW}⚠️  HTTP redirect not working${NC}"
fi

# Test HTTPS
echo -n "   Testing HTTPS... "
if curl -k -s -o /dev/null -w "%{http_code}" https://localhost/ | grep -q "200"; then
    echo -e "${GREEN}✅${NC}"
else
    echo -e "${YELLOW}⚠️  HTTPS not responding yet${NC}"
fi

# Test databases
echo -n "   Testing PostgreSQL... "
if docker exec $CONTAINER_NAME pg_isready -U legal_cms_user -d legal_cms_main -q; then
    echo -e "${GREEN}✅${NC}"
else
    echo -e "${YELLOW}⚠️  PostgreSQL not ready${NC}"
fi

echo -n "   Testing MongoDB... "
if docker exec $CONTAINER_NAME mongosh --eval "db.runCommand('ping')" --quiet; then
    echo -e "${GREEN}✅${NC}"
else
    echo -e "${YELLOW}⚠️  MongoDB not ready${NC}"
fi

echo -n "   Testing Redis... "
if docker exec $CONTAINER_NAME redis-cli ping | grep -q "PONG"; then
    echo -e "${GREEN}✅${NC}"
else
    echo -e "${YELLOW}⚠️  Redis not ready${NC}"
fi

echo -n "   Testing Vault... "
if curl -k -s https://localhost:8200/v1/sys/health | grep -q "initialized"; then
    echo -e "${GREEN}✅${NC}"
else
    echo -e "${YELLOW}⚠️  Vault not ready${NC}"
fi

# Show deployment summary
echo ""
echo -e "${GREEN}🎉 ALL-IN-ONE LAW FIRM SaaS DEPLOYMENT COMPLETE!${NC}"
echo "════════════════════════════════════════════════════════════"
echo ""
echo -e "${BLUE}🌐 Web Interface:${NC}"
echo "   HTTPS: https://localhost/"
echo "   HTTP:  http://localhost/ (redirects to HTTPS)"
echo ""
echo -e "${BLUE}🗄️  Database Access:${NC}"
echo "   PostgreSQL: localhost:5435"
echo "   MongoDB:    localhost:27018"
echo "   Redis:      localhost:6382"
echo "   Qdrant:     localhost:6334"
echo ""
echo -e "${BLUE}🔒 Security Services:${NC}"
echo "   Vault UI:   https://localhost:8200/"
echo "   SSL:        Certbot managed"
echo ""
echo -e "${BLUE}🔧 Management:${NC}"
echo "   Container:  $CONTAINER_NAME"
echo "   Logs:       docker logs $CONTAINER_NAME"
echo "   Stop:       docker stop $CONTAINER_NAME"
echo "   Restart:    docker restart $CONTAINER_NAME"
echo ""
echo -e "${BLUE}📋 Default Credentials:${NC}"
echo "   PostgreSQL: legal_cms_user / SecureLawFirmPassword2025"
echo "   MongoDB:    admin / SecureMongoLawPassword2025"
echo "   Redis:      Password: SecureRedisLawPassword2025"
echo "   Vault:      Token: law-firm-vault-token-2025"
echo ""
echo -e "${YELLOW}⚠️  Security Note:${NC}"
echo "   Change default passwords in production!"
echo "   Configure proper domain and SSL email for production use."
echo ""
echo -e "${GREEN}✨ Your All-in-One Law Firm SaaS is ready!${NC}"