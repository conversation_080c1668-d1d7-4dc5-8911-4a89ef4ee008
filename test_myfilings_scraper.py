#!/usr/bin/env python3
"""
Test script for the My Filings Scraper
Scrapes all 11 pages of user's filed documents with complete table data and downloads
"""

import asyncio
import json
from datetime import datetime
from core.myfilings_scraper import scrape_my_filings

async def test_myfilings_scraper():
    """Test the My Filings scraper with all 11 pages"""
    
    # Get credentials
    username = input("Enter your e-filing username: ")
    password = input("Enter your e-filing password: ")
    
    print(f"🚀 Starting My Filings scraper")
    print("📋 Expected workflow:")
    print("   1. Login to e-filing system")
    print("   2. Navigate to My Filings (EFiling.aspx)")
    print("   3. Detect total pages (11 pages expected)")
    print("   4. Scrape all pages with complete table data")
    print("   5. Extract all filing information")
    print("   6. Download all associated documents/images")
    print("⏳ This may take several minutes for 11 pages...")
    
    try:
        # Run the scraper
        results = await scrape_my_filings(
            username=username,
            password=password,
            headless=False,  # Set to True to hide browser
            download_dir="myfilings_downloads"
        )
        
        # Display results
        print("\n" + "="*60)
        print("📊 MY FILINGS SCRAPING RESULTS")
        print("="*60)
        
        if results.get('error'):
            print(f"❌ Error: {results['error']}")
            return
        
        print(f"📄 Total pages scraped: {results.get('total_pages', 0)}")
        print(f"📝 Total filings found: {results.get('total_filings', 0)}")
        print(f"📋 Total table records: {results.get('total_records', 0)}")
        
        # Count EFile IDs processed
        efile_count = sum(len([f for f in page.get('filings', []) if f.get('efile_id')]) for page in results.get('pages', []))
        pdf_downloads = sum(len(f.get('pdf_downloads', [])) for page in results.get('pages', []) for f in page.get('filings', []))
        print(f"🆔 EFile IDs processed: {efile_count}")
        print(f"📄 PDFs downloaded with original names: {pdf_downloads}")
        
        # Page breakdown
        print(f"\n📋 Page breakdown:")
        for page in results.get('pages', []):
            page_num = page.get('page_number', '?')
            filing_count = len(page.get('filings', []))
            record_count = len(page.get('records', []))
            print(f"   Page {page_num}: {filing_count} filings, {record_count} table records")
        
        # Download results
        downloads = results.get('downloads', {})
        if downloads:
            print(f"\n⬇️  Download summary:")
            print(f"   🔗 Total links found: {downloads.get('total_links', 0)}")
            print(f"   🖼️  Total images found: {downloads.get('total_images', 0)}")
            print(f"   ✅ Successful downloads: {downloads.get('successful_downloads', 0)}")
            print(f"   ❌ Failed downloads: {downloads.get('failed_downloads', 0)}")
            print(f"   📁 Download directory: {results.get('download_directory', 'Unknown')}")
            
            if downloads.get('errors'):
                print(f"\n⚠️  Download errors:")
                for error in downloads['errors'][:3]:  # Show first 3 errors
                    print(f"   - {error}")
                if len(downloads['errors']) > 3:
                    print(f"   ... and {len(downloads['errors']) - 3} more errors")
        
        # Table headers
        headers = results.get('table_headers', [])
        if headers:
            print(f"\n📊 Table headers found: {', '.join(headers)}")
        
        # Sample record data
        if results.get('all_records'):
            print(f"\n📄 Sample filing record:")
            sample_record = results['all_records'][0]
            print(f"   Record from Page {sample_record.get('page_number', '?')}, Row {sample_record.get('row_number', '?')}")
            
            # Show column data
            data = sample_record.get('data', {})
            if data:
                print(f"   Columns found: {list(data.keys())}")
                
                # Show first few data values
                for i, (key, value) in enumerate(data.items()):
                    if i < 4:  # Show first 4 columns
                        print(f"   {key}: {value[:60]}..." if len(str(value)) > 60 else f"   {key}: {value}")
                    elif i == 4:
                        print(f"   ... and {len(data) - 4} more columns")
                        break
            
            # Show links and images count
            links_count = len(sample_record.get('links', []))
            images_count = len(sample_record.get('images', []))
            if links_count > 0 or images_count > 0:
                print(f"   Links in this record: {links_count}")
                print(f"   Images in this record: {images_count}")
        
        # Sample filing info
        if results.get('all_filings'):
            print(f"\n📋 Sample filing item:")
            sample_filing = results['all_filings'][0]
            print(f"   Filing text: {sample_filing.get('text', 'N/A')}")
            print(f"   Filing link: {sample_filing.get('link', 'N/A')}")
            print(f"   Filing title: {sample_filing.get('title', 'N/A')}")
        
        # Save detailed results
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        results_file = f"myfilings_results_{timestamp}.json"
        
        with open(results_file, 'w') as f:
            json.dump(results, f, indent=2)
        
        print(f"\n💾 Detailed results saved to: {results_file}")
        
        # Save HTML table data separately
        if results.get('all_records'):
            html_file = f"myfilings_table_{timestamp}.html"
            with open(html_file, 'w', encoding='utf-8') as f:
                f.write("<html><head><title>My Filings Table Data</title></head><body>")
                f.write(f"<h1>My Filings - Complete Table Data</h1>")
                f.write(f"<p>Scraped on: {results.get('scraped_at', 'Unknown')}</p>")
                
                # Write table headers if available
                headers = results.get('table_headers', [])
                if headers:
                    f.write(f"<h2>Table Headers: {', '.join(headers)}</h2>")
                
                for page in results.get('pages', []):
                    f.write(f"<h2>Page {page.get('page_number', '?')}</h2>")
                    f.write("<table border='1' style='border-collapse: collapse; width: 100%;'>")
                    
                    # Write headers if we have records
                    records = page.get('records', [])
                    if records:
                        first_record = records[0]
                        headers = list(first_record.get('data', {}).keys())
                        f.write("<tr style='background-color: #f0f0f0;'>")
                        for header in headers:
                            f.write(f"<th style='padding: 8px;'>{header}</th>")
                        f.write("<th style='padding: 8px;'>Links</th>")
                        f.write("<th style='padding: 8px;'>Images</th>")
                        f.write("</tr>")
                        
                        # Write data rows
                        for record in records:
                            f.write("<tr>")
                            for header in headers:
                                value = record.get('data', {}).get(header, '')
                                f.write(f"<td style='padding: 8px;'>{value}</td>")
                            
                            # Links column
                            links = record.get('links', [])
                            links_html = '<br>'.join([f'<a href="{link["full_url"]}" target="_blank">{link["text"]}</a>' for link in links if link.get('full_url')])
                            f.write(f"<td style='padding: 8px;'>{links_html}</td>")
                            
                            # Images column  
                            images = record.get('images', [])
                            images_html = '<br>'.join([f'<img src="{img["full_url"]}" alt="{img["alt"]}" style="max-width: 50px;">' for img in images])
                            f.write(f"<td style='padding: 8px;'>{images_html}</td>")
                            
                            f.write("</tr>")
                    
                    f.write("</table><br>")
                
                f.write("</body></html>")
            
            print(f"📋 HTML table saved to: {html_file}")
        
        # Summary statistics
        total_pages = results.get('total_pages', 0)
        total_records = results.get('total_records', 0) 
        expected_pages = 11
        
        if total_pages == expected_pages:
            print(f"🎉 SUCCESS: Found exactly {expected_pages} pages as expected!")
        elif total_pages > 0:
            print(f"⚠️  Found {total_pages} pages (expected {expected_pages})")
        else:
            print("❌ No pages found - check credentials and navigation")
        
        if total_records > 0:
            print(f"📊 Total: {total_records} filing records across {total_pages} pages")
            avg_per_page = total_records / total_pages if total_pages > 0 else 0
            print(f"📈 Average: {avg_per_page:.1f} records per page")
        
        return results
        
    except Exception as e:
        print(f"💥 My Filings scraper failed: {e}")
        return None

def main():
    """Main function"""
    print("📁 My Filings Scraper - All 11 Pages")
    print("=" * 50)
    
    # Check if required packages are available
    try:
        import playwright
        import aiohttp
        import aiofiles
        print("✅ All required packages available")
    except ImportError as e:
        print(f"❌ Missing package: {e}")
        print("Install with: pip install playwright aiohttp aiofiles")
        print("Then run: playwright install chromium")
        return
    
    # Run the test
    asyncio.run(test_myfilings_scraper())

if __name__ == "__main__":
    main()