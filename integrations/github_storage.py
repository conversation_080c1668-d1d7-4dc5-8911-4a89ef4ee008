"""
Secure GitHub Document Storage Integration
Handles encrypted document storage in private GitHub repositories with integrity verification.
"""

import os
import json
import base64
import hashlib
import logging
from datetime import datetime
from typing import Dict, List, Optional, Tuple, Union
from pathlib import Path
import tempfile

# GitHub API imports
try:
    import requests
    from github import Github, GithubException
    GITHUB_AVAILABLE = True
except ImportError:
    GITHUB_AVAILABLE = False
    logging.warning("PyGithub not available. Install with: pip install PyGithub requests")

# PGP integration
from security.pgp_manager import DocumentEncryption, PGPKeyManager

logger = logging.getLogger(__name__)

class SecureGitHubStorage:
    """Secure document storage in private GitHub repositories"""
    
    def __init__(self, github_token: str, organization: str, pgp_manager: PGPKeyManager, doc_encryption: DocumentEncryption):
        if not GITHUB_AVAILABLE:
            raise RuntimeError("GitHub integration not available")
        
        self.github = Github(github_token)
        self.organization = organization
        self.pgp_manager = pgp_manager
        self.doc_encryption = doc_encryption
        self.session = requests.Session()
        self.session.headers.update({
            'Authorization': f'token {github_token}',
            'Accept': 'application/vnd.github.v3+json',
            'User-Agent': 'Legal-CMS-SecureStorage/1.0'
        })
        
        # Verify authentication
        try:
            user = self.github.get_user()
            logger.info(f"GitHub authenticated as: {user.login}")
        except Exception as e:
            logger.error(f"GitHub authentication failed: {e}")
            raise
    
    def create_case_repository(self, case_id: str, case_name: str) -> Dict:
        """Create private repository for case documents"""
        repo_name = f"legal-cms-case-{case_id}"
        
        try:
            # Check if repository already exists
            try:
                org = self.github.get_organization(self.organization)
                existing_repo = org.get_repo(repo_name)
                logger.info(f"Repository {repo_name} already exists")
                return {
                    'repo_name': repo_name,
                    'repo_url': existing_repo.html_url,
                    'clone_url': existing_repo.clone_url,
                    'created': False
                }
            except GithubException as e:
                if e.status != 404:
                    raise
            
            # Create new repository
            org = self.github.get_organization(self.organization)
            repo = org.create_repo(
                name=repo_name,
                description=f"Encrypted documents for case: {case_name}",
                private=True,
                has_issues=False,
                has_wiki=False,
                has_projects=False,
                auto_init=True
            )
            
            # Create initial directory structure
            self._setup_repository_structure(repo, case_id, case_name)
            
            logger.info(f"Created repository: {repo_name}")
            return {
                'repo_name': repo_name,
                'repo_url': repo.html_url,
                'clone_url': repo.clone_url,
                'created': True
            }
            
        except Exception as e:
            logger.error(f"Failed to create repository {repo_name}: {e}")
            raise
    
    def _setup_repository_structure(self, repo, case_id: str, case_name: str):
        """Set up initial repository structure"""
        try:
            # Create README with case information (encrypted)
            readme_content = f"""# Legal Case Documents Repository

**Case ID**: {case_id}
**Case Name**: {case_name}
**Created**: {datetime.now().isoformat()}

## Security Notice
All documents in this repository are encrypted using PGP encryption.
Only authorized personnel with proper decryption keys can access the content.

## Directory Structure
- `encrypted/` - Encrypted document files
- `checksums/` - File integrity checksums
- `metadata/` - Document metadata (encrypted)

## Access Control
This repository is private and access is restricted to authorized legal team members only.
"""
            
            # Encrypt README content
            readme_encrypted = self.doc_encryption.encrypt_document(
                readme_content.encode('utf-8'),
                case_id,
                'README'
            )
            
            # Create files
            repo.create_file(
                "README.md.pgp",
                "Initial repository setup with encrypted README",
                base64.b64encode(readme_encrypted['encrypted_content']).decode('utf-8')
            )
            
            # Create directory structure with placeholder files
            repo.create_file(
                "encrypted/.gitkeep",
                "Create encrypted documents directory",
                "# Encrypted documents directory\n"
            )
            
            repo.create_file(
                "checksums/.gitkeep",
                "Create checksums directory",
                "# File integrity checksums directory\n"
            )
            
            repo.create_file(
                "metadata/.gitkeep",
                "Create metadata directory",
                "# Document metadata directory\n"
            )
            
            logger.info(f"Repository structure created for case {case_id}")
            
        except Exception as e:
            logger.error(f"Failed to setup repository structure: {e}")
            raise
    
    def store_document(self, case_id: str, document_id: str, filename: str, content: bytes, metadata: Dict = None) -> Dict:
        """Store encrypted document in GitHub repository"""
        repo_name = f"legal-cms-case-{case_id}"
        
        try:
            # Get repository
            org = self.github.get_organization(self.organization)
            repo = org.get_repo(repo_name)
            
            # Encrypt document
            encrypted_data = self.doc_encryption.encrypt_document(content, case_id, document_id)
            
            # Encode encrypted content for GitHub
            encoded_content = base64.b64encode(encrypted_data['encrypted_content']).decode('utf-8')
            
            # Store encrypted document
            document_path = f"encrypted/{document_id}.pgp"
            commit_message = f"Add encrypted document: {filename}"
            
            try:
                # Try to update existing file
                existing_file = repo.get_contents(document_path)
                repo.update_file(
                    document_path,
                    commit_message,
                    encoded_content,
                    existing_file.sha
                )
                logger.info(f"Updated document {document_id} in repository")
            except GithubException as e:
                if e.status == 404:
                    # Create new file
                    repo.create_file(document_path, commit_message, encoded_content)
                    logger.info(f"Created document {document_id} in repository")
                else:
                    raise
            
            # Store checksum
            checksum_path = f"checksums/{document_id}.sha256"
            checksum_content = f"{encrypted_data['content_hash']}  {filename}\n"
            
            try:
                existing_checksum = repo.get_contents(checksum_path)
                repo.update_file(
                    checksum_path,
                    f"Update checksum for {filename}",
                    checksum_content,
                    existing_checksum.sha
                )
            except GithubException as e:
                if e.status == 404:
                    repo.create_file(
                        checksum_path,
                        f"Add checksum for {filename}",
                        checksum_content
                    )
                else:
                    raise
            
            # Store encrypted metadata
            if metadata:
                metadata_with_encryption = {
                    **metadata,
                    'encryption_info': {
                        'pgp_fingerprint': encrypted_data['pgp_fingerprint'],
                        'encryption_algorithm': encrypted_data['encryption_algorithm'],
                        'encrypted_at': encrypted_data['encrypted_at']
                    },
                    'github_info': {
                        'repository': repo_name,
                        'document_path': document_path,
                        'checksum_path': checksum_path,
                        'stored_at': datetime.now().isoformat()
                    }
                }
                
                # Encrypt metadata
                metadata_json = json.dumps(metadata_with_encryption, indent=2)
                encrypted_metadata = self.doc_encryption.encrypt_document(
                    metadata_json.encode('utf-8'),
                    case_id,
                    f"{document_id}_metadata"
                )
                
                metadata_path = f"metadata/{document_id}.json.pgp"
                encoded_metadata = base64.b64encode(encrypted_metadata['encrypted_content']).decode('utf-8')
                
                try:
                    existing_metadata = repo.get_contents(metadata_path)
                    repo.update_file(
                        metadata_path,
                        f"Update metadata for {filename}",
                        encoded_metadata,
                        existing_metadata.sha
                    )
                except GithubException as e:
                    if e.status == 404:
                        repo.create_file(
                            metadata_path,
                            f"Add metadata for {filename}",
                            encoded_metadata
                        )
                    else:
                        raise
            
            storage_result = {
                'document_id': document_id,
                'case_id': case_id,
                'repository': repo_name,
                'document_path': document_path,
                'checksum_path': checksum_path,
                'metadata_path': metadata_path if metadata else None,
                'content_hash': encrypted_data['content_hash'],
                'pgp_fingerprint': encrypted_data['pgp_fingerprint'],
                'stored_at': datetime.now().isoformat(),
                'file_size': len(content),
                'encrypted_size': len(encrypted_data['encrypted_content'])
            }
            
            logger.info(f"Document {document_id} stored successfully in GitHub")
            return storage_result
            
        except Exception as e:
            logger.error(f"Failed to store document {document_id}: {e}")
            raise
    
    def retrieve_document(self, case_id: str, document_id: str, passphrase: str) -> Tuple[bytes, Dict]:
        """Retrieve and decrypt document from GitHub repository"""
        repo_name = f"legal-cms-case-{case_id}"
        
        try:
            # Get repository
            org = self.github.get_organization(self.organization)
            repo = org.get_repo(repo_name)
            
            # Get encrypted document
            document_path = f"encrypted/{document_id}.pgp"
            document_file = repo.get_contents(document_path)
            encrypted_content = base64.b64decode(document_file.content)
            
            # Get metadata if available
            metadata = {}
            try:
                metadata_path = f"metadata/{document_id}.json.pgp"
                metadata_file = repo.get_contents(metadata_path)
                encrypted_metadata_content = base64.b64decode(metadata_file.content)
                
                # Decrypt metadata
                decrypted_metadata = self.doc_encryption.decrypt_document({
                    'document_id': f"{document_id}_metadata",
                    'case_id': case_id,
                    'encrypted_content': encrypted_metadata_content,
                    'encrypted_aes_key': None,  # Will be retrieved from encrypted data
                    'content_hash': None  # Will be verified during decryption
                }, passphrase)
                
                metadata = json.loads(decrypted_metadata.decode('utf-8'))
                
            except GithubException as e:
                if e.status != 404:
                    logger.warning(f"Could not retrieve metadata for {document_id}: {e}")
            
            # Prepare decryption data
            encrypted_data = {
                'document_id': document_id,
                'case_id': case_id,
                'encrypted_content': encrypted_content,
                'encrypted_aes_key': None,  # Will be extracted from encrypted content
                'content_hash': None  # Will be verified during decryption
            }
            
            # Decrypt document
            decrypted_content = self.doc_encryption.decrypt_document(encrypted_data, passphrase)
            
            # Verify checksum if available
            try:
                checksum_path = f"checksums/{document_id}.sha256"
                checksum_file = repo.get_contents(checksum_path)
                stored_hash = checksum_file.content.split()[0]
                
                calculated_hash = hashlib.sha256(decrypted_content).hexdigest()
                if calculated_hash != stored_hash:
                    logger.warning(f"Checksum mismatch for document {document_id}")
                else:
                    logger.info(f"Checksum verified for document {document_id}")
                    
            except GithubException as e:
                if e.status != 404:
                    logger.warning(f"Could not verify checksum for {document_id}: {e}")
            
            logger.info(f"Document {document_id} retrieved and decrypted successfully")
            return decrypted_content, metadata
            
        except Exception as e:
            logger.error(f"Failed to retrieve document {document_id}: {e}")
            raise
    
    def list_case_documents(self, case_id: str) -> List[Dict]:
        """List all documents in a case repository"""
        repo_name = f"legal-cms-case-{case_id}"
        
        try:
            org = self.github.get_organization(self.organization)
            repo = org.get_repo(repo_name)
            
            # Get all files in encrypted directory
            encrypted_contents = repo.get_contents("encrypted")
            documents = []
            
            for content in encrypted_contents:
                if content.name.endswith('.pgp') and content.name != '.gitkeep':
                    document_id = content.name.replace('.pgp', '')
                    
                    # Get file info
                    doc_info = {
                        'document_id': document_id,
                        'filename': content.name,
                        'size': content.size,
                        'last_modified': content.last_modified,
                        'sha': content.sha,
                        'download_url': content.download_url
                    }
                    
                    # Try to get checksum
                    try:
                        checksum_path = f"checksums/{document_id}.sha256"
                        checksum_file = repo.get_contents(checksum_path)
                        doc_info['checksum'] = checksum_file.content.split()[0]
                    except GithubException:
                        pass
                    
                    documents.append(doc_info)
            
            logger.info(f"Listed {len(documents)} documents for case {case_id}")
            return documents
            
        except Exception as e:
            logger.error(f"Failed to list documents for case {case_id}: {e}")
            raise
    
    def delete_document(self, case_id: str, document_id: str, reason: str = "Document deletion") -> bool:
        """Delete document from GitHub repository"""
        repo_name = f"legal-cms-case-{case_id}"
        
        try:
            org = self.github.get_organization(self.organization)
            repo = org.get_repo(repo_name)
            
            # Delete document file
            document_path = f"encrypted/{document_id}.pgp"
            try:
                document_file = repo.get_contents(document_path)
                repo.delete_file(document_path, f"Delete document: {reason}", document_file.sha)
                logger.info(f"Deleted document file: {document_path}")
            except GithubException as e:
                if e.status != 404:
                    raise
            
            # Delete checksum file
            checksum_path = f"checksums/{document_id}.sha256"
            try:
                checksum_file = repo.get_contents(checksum_path)
                repo.delete_file(checksum_path, f"Delete checksum: {reason}", checksum_file.sha)
                logger.info(f"Deleted checksum file: {checksum_path}")
            except GithubException as e:
                if e.status != 404:
                    raise
            
            # Delete metadata file
            metadata_path = f"metadata/{document_id}.json.pgp"
            try:
                metadata_file = repo.get_contents(metadata_path)
                repo.delete_file(metadata_path, f"Delete metadata: {reason}", metadata_file.sha)
                logger.info(f"Deleted metadata file: {metadata_path}")
            except GithubException as e:
                if e.status != 404:
                    raise
            
            logger.info(f"Document {document_id} deleted successfully from GitHub")
            return True
            
        except Exception as e:
            logger.error(f"Failed to delete document {document_id}: {e}")
            return False

# Factory function
def create_github_storage(github_token: str, organization: str, vault_client=None) -> SecureGitHubStorage:
    """Create GitHub storage with PGP integration"""
    from security.pgp_manager import create_pgp_manager
    
    key_manager, doc_encryption = create_pgp_manager(vault_client=vault_client)
    return SecureGitHubStorage(github_token, organization, key_manager, doc_encryption)
