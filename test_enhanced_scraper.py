#!/usr/bin/env python3
"""
Test script for the Enhanced E-Filing Images Scraper
Downloads all 63 images from the 5 pages
"""

import asyncio
import json
from datetime import datetime
from core.enhanced_efiling_scraper import scrape_all_case_images

async def test_scraper():
    """Test the enhanced scraper with your specific case"""
    
    # Your case URL: https://efiling.cp.cuyahogacounty.gov/Images.aspx?q=I-fJldkwrgJ3jp2LoiI3Mg2
    case_query = "I-fJldkwrgJ3jp2LoiI3Mg2"
    
    # You'll need to provide your e-filing credentials
    username = input("Enter your e-filing username: ")
    password = input("Enter your e-filing password: ")
    
    print(f"🚀 Starting enhanced scraper for case: {case_query}")
    print("📋 Expected: 63 images across 5 pages (4×15 + 3)")
    print("⏳ This may take several minutes...")
    
    try:
        # Run the scraper
        results = await scrape_all_case_images(
            username=username,
            password=password,
            case_query=case_query,
            headless=False,  # Set to True to hide browser
            download_dir="efiling_downloads"
        )
        
        # Display results
        print("\n" + "="*60)
        print("📊 SCRAPING RESULTS")
        print("="*60)
        
        if results.get('error'):
            print(f"❌ Error: {results['error']}")
            return
        
        print(f"🔍 Total pages scraped: {results.get('total_pages', 0)}")
        print(f"🖼️  Total images found: {results.get('total_images', 0)}")
        print(f"📋 Total table records: {results.get('total_records', 0)}")
        
        # Download results
        downloads = results.get('downloads', {})
        if downloads:
            print(f"⬇️  Download attempts: {downloads.get('total_attempted', 0)}")
            print(f"✅ Successful downloads: {downloads.get('successful_downloads', 0)}")
            print(f"❌ Failed downloads: {downloads.get('failed_downloads', 0)}")
            print(f"📁 Download directory: {results.get('download_directory', 'Unknown')}")
            
            if downloads.get('errors'):
                print(f"\n⚠️  Download errors:")
                for error in downloads['errors'][:5]:  # Show first 5 errors
                    print(f"   - {error}")
                if len(downloads['errors']) > 5:
                    print(f"   ... and {len(downloads['errors']) - 5} more errors")
        
        # Page breakdown
        print(f"\n📄 Page breakdown:")
        for page in results.get('pages', []):
            page_num = page.get('page_number', '?')
            image_count = len(page.get('images', []))
            record_count = len(page.get('records', []))
            print(f"   Page {page_num}: {image_count} images, {record_count} table records")
        
        # Save detailed results
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        results_file = f"efiling_results_{case_query}_{timestamp}.json"
        
        with open(results_file, 'w') as f:
            json.dump(results, f, indent=2)
        
        print(f"\n💾 Detailed results saved to: {results_file}")
        
        # Save HTML table data separately (like E-Service scraper)
        if results.get('all_records'):
            html_file = f"images_table_{case_query}_{timestamp}.html"
            with open(html_file, 'w', encoding='utf-8') as f:
                f.write("<html><head><title>Images Table Data</title></head><body>")
                f.write(f"<h1>Images Table Data for Case Query: {case_query}</h1>")
                
                # Write table headers if available
                headers = results.get('table_headers', [])
                if headers:
                    f.write(f"<h2>Table Headers: {', '.join(headers)}</h2>")
                
                for page in results.get('pages', []):
                    f.write(f"<h2>Page {page.get('page_number', '?')}</h2>")
                    f.write("<table border='1' style='border-collapse: collapse; width: 100%;'>")
                    
                    # Write headers if we have records
                    records = page.get('records', [])
                    if records:
                        first_record = records[0]
                        headers = list(first_record.get('data', {}).keys())
                        f.write("<tr style='background-color: #f0f0f0;'>")
                        for header in headers:
                            f.write(f"<th style='padding: 8px;'>{header}</th>")
                        f.write("<th style='padding: 8px;'>Links</th>")
                        f.write("<th style='padding: 8px;'>Images</th>")
                        f.write("</tr>")
                        
                        # Write data rows
                        for record in records:
                            f.write("<tr>")
                            for header in headers:
                                value = record.get('data', {}).get(header, '')
                                f.write(f"<td style='padding: 8px;'>{value}</td>")
                            
                            # Links column
                            links = record.get('links', [])
                            links_html = '<br>'.join([f'<a href="{link["full_url"]}">{link["text"]}</a>' for link in links])
                            f.write(f"<td style='padding: 8px;'>{links_html}</td>")
                            
                            # Images column
                            images = record.get('images', [])
                            images_html = '<br>'.join([f'<img src="{img["full_url"]}" alt="{img["alt"]}" style="max-width: 50px;">' for img in images])
                            f.write(f"<td style='padding: 8px;'>{images_html}</td>")
                            
                            f.write("</tr>")
                    
                    f.write("</table><br>")
                
                f.write("</body></html>")
            
            print(f"📋 HTML table saved to: {html_file}")
        
        # Sample table data display
        if results.get('all_records'):
            print(f"\n📄 Sample table data:")
            sample_record = results['all_records'][0]
            print(f"   Record from Page {sample_record.get('page_number', '?')}, Row {sample_record.get('row_number', '?')}")
            
            # Show table headers
            headers = results.get('table_headers', [])
            if headers:
                print(f"   Table headers: {', '.join(headers)}")
            
            # Show column data
            data = sample_record.get('data', {})
            if data:
                print(f"   Columns found: {list(data.keys())}")
                
                # Show first few data values
                for i, (key, value) in enumerate(data.items()):
                    if i < 3:  # Show first 3 columns
                        print(f"   {key}: {value[:50]}..." if len(str(value)) > 50 else f"   {key}: {value}")
                    elif i == 3:
                        print(f"   ... and {len(data) - 3} more columns")
                        break
        
        # Check if we got the expected 63 images
        total_found = results.get('total_images', 0)
        if total_found == 63:
            print("🎉 SUCCESS: Found exactly 63 images as expected!")
        elif total_found > 0:
            print(f"⚠️  Found {total_found} images (expected 63)")
        else:
            print("❌ No images found - check credentials and case query")
        
        return results
        
    except Exception as e:
        print(f"💥 Scraper failed: {e}")
        return None

def main():
    """Main function"""
    print("🤖 Enhanced E-Filing Images Scraper")
    print("=" * 50)
    
    # Check if required packages are available
    try:
        import playwright
        import aiohttp
        import aiofiles
        print("✅ All required packages available")
    except ImportError as e:
        print(f"❌ Missing package: {e}")
        print("Install with: pip install playwright aiohttp aiofiles")
        print("Then run: playwright install chromium")
        return
    
    # Run the test
    asyncio.run(test_scraper())

if __name__ == "__main__":
    main()