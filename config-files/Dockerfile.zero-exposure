# ZERO-EXPOSURE LAW FIRM SAAS - COMPLETELY ISOLATED
# No external port exposure - access only via docker exec or internal networking
FROM ubuntu:22.04

LABEL maintainer="Law Firm SaaS Team"
LABEL description="Zero-Exposure Law Firm SaaS - Completely Isolated Container"
LABEL version="1.0.0"

# Prevent interactive prompts
ENV DEBIAN_FRONTEND=noninteractive
ENV TZ=UTC

# Install all services in one container
RUN apt-get update && apt-get install -y \
    python3 python3-pip python3-dev \
    nginx supervisor curl wget gnupg lsb-release \
    redis-server \
    netcat-openbsd \
    && apt-get clean && rm -rf /var/lib/apt/lists/*

# Install PostgreSQL 15
RUN wget --quiet -O - https://www.postgresql.org/media/keys/ACCC4CF8.asc | apt-key add - \
    && echo "deb http://apt.postgresql.org/pub/repos/apt/ $(lsb_release -cs)-pgdg main" > /etc/apt/sources.list.d/pgdg.list \
    && apt-get update \
    && apt-get install -y postgresql-15 postgresql-contrib-15 \
    && apt-get clean && rm -rf /var/lib/apt/lists/*

# Install MongoDB 7.0
RUN wget -qO - https://www.mongodb.org/static/pgp/server-7.0.asc | apt-key add - \
    && echo "deb [ arch=amd64,arm64 ] https://repo.mongodb.org/apt/ubuntu jammy/mongodb-org/7.0 multiverse" | tee /etc/apt/sources.list.d/mongodb-org-7.0.list \
    && apt-get update \
    && apt-get install -y mongodb-org \
    && apt-get clean && rm -rf /var/lib/apt/lists/*

# Create application directories
RUN mkdir -p /app /app/logs /app/data /var/log/supervisor \
    && mkdir -p /var/lib/postgresql/data /data/db

# Set working directory
WORKDIR /app

# Copy application files
COPY . .

# Install Python dependencies
RUN pip3 install --no-cache-dir -r requirements.txt

# Create nginx configuration for internal use only
COPY config/nginx-zero-exposure.conf /etc/nginx/sites-available/default

# Configure Supervisor for all services
COPY config/supervisord-zero-exposure.conf /etc/supervisor/conf.d/supervisord.conf

# Initialize PostgreSQL
USER postgres
RUN /usr/lib/postgresql/15/bin/initdb -D /var/lib/postgresql/data
RUN /usr/lib/postgresql/15/bin/pg_ctl -D /var/lib/postgresql/data -l /var/lib/postgresql/logfile start \
    && /usr/lib/postgresql/15/bin/createdb legal_cms_main \
    && /usr/lib/postgresql/15/bin/psql -c "CREATE USER legal_cms_user WITH SUPERUSER PASSWORD 'SecureLawFirmPassword2025';" \
    && /usr/lib/postgresql/15/bin/pg_ctl -D /var/lib/postgresql/data stop

# Switch back to root
USER root

# Set permissions
RUN chown -R postgres:postgres /var/lib/postgresql \
    && chown -R mongodb:mongodb /data/db \
    && chmod +x /app/scripts/*.sh 2>/dev/null || true

# Create startup script
COPY scripts/zero-exposure-start.sh /app/start.sh
RUN chmod +x /app/start.sh

# NO PORT EXPOSURE - Completely isolated
# EXPOSE - intentionally commented out to ensure zero exposure

# Health check - internal only
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://127.0.0.1/health || exit 1

# Start the completely isolated container
CMD ["/app/start.sh"]