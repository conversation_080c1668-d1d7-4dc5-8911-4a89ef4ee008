version: '3.8'

services:
  ALL-IN-ONE-LAW-FIRM-SAAS:
    image: all-in-one-law-firm-saas:latest
    build:
      context: .
      dockerfile: Dockerfile.all-in-one
    container_name: ALL-IN-ONE-LAW-FIRM-SAAS
    networks:
      - law-firm-network
    ports:
      - "80:80"       # HTTP (redirects to HTTPS)
      - "443:443"     # HTTPS (Nginx + Streamlit)
      - "5435:5432"   # PostgreSQL
      - "27018:27017" # MongoDB  
      - "6382:6379"   # Redis
      - "6334:6333"   # Qdrant Vector DB
      - "8200:8200"   # HashiCorp Vault
    environment:
      # Database Credentials
      - POSTGRES_PASSWORD=SecureLawFirmPassword2025
      - POSTGRES_USER=legal_cms_user
      - POSTGRES_DB=legal_cms_main
      - MONGODB_PASSWORD=SecureMongoLawPassword2025
      - MONGODB_USER=admin
      - MONGODB_DATABASE=legal_cms_documents
      - REDIS_PASSWORD=SecureRedisLawPassword2025
      
      # Vault Configuration
      - VAULT_DEV_ROOT_TOKEN_ID=law-firm-vault-token-2025
      - VAULT_DEV_LISTEN_ADDRESS=0.0.0.0:8200
      - VAULT_ADDR=http://localhost:8200
      - VAULT_TOKEN=law-firm-vault-token-2025
      
      # Application Configuration
      - STREAMLIT_SERVER_PORT=8501
      - STREAMLIT_SERVER_ADDRESS=0.0.0.0
      - STREAMLIT_SERVER_HEADLESS=true
      - STREAMLIT_SERVER_ENABLE_CORS=false
      - STREAMLIT_SERVER_ENABLE_XSRF_PROTECTION=true
      
      # Security Settings
      - SESSION_SECRET_KEY=LawFirmSecureSessionKey2025
      - JWT_SECRET_KEY=LawFirmJWTSecretKey2025
      - ENCRYPTION_KEY=LawFirmEncryptionKey2025
      - ENVIRONMENT=production
      - DEBUG=false
      - LOG_LEVEL=INFO
      
      # SSL/TLS Configuration
      - SSL_ENABLED=true
      - SSL_CERT_PATH=/etc/ssl/certs/law-firm.crt
      - SSL_KEY_PATH=/etc/ssl/private/law-firm.key
      
    volumes:
      # Application data persistence
      - law-firm-app-data:/app/data
      - law-firm-case-documents:/app/case_documents
      - law-firm-exports:/app/legal_exports
      - law-firm-logs:/app/logs
      
      # Database data persistence
      - law-firm-postgres-data:/var/lib/postgresql/data
      - law-firm-mongo-data:/data/db
      - law-firm-redis-data:/data
      - law-firm-qdrant-data:/qdrant/storage
      - law-firm-vault-data:/vault/data
      
      # SSL certificates
      - ./ssl:/etc/ssl/certs:ro
      - ./ssl-private:/etc/ssl/private:ro
      
      # Configuration files
      - ./config:/app/config:ro
      
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "https://localhost:443/", "--insecure"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    
    labels:
      - "com.law-firm.service=all-in-one-saas"
      - "com.law-firm.version=1.0.0"
      - "com.law-firm.description=Complete Law Firm SaaS Solution"

networks:
  law-firm-network:
    driver: bridge
    name: law-firm-network

volumes:
  law-firm-app-data:
    name: law-firm-app-data
  law-firm-case-documents:
    name: law-firm-case-documents
  law-firm-exports:
    name: law-firm-exports
  law-firm-logs:
    name: law-firm-logs
  law-firm-postgres-data:
    name: law-firm-postgres-data
  law-firm-mongo-data:
    name: law-firm-mongo-data
  law-firm-redis-data:
    name: law-firm-redis-data
  law-firm-qdrant-data:
    name: law-firm-qdrant-data
  law-firm-vault-data:
    name: law-firm-vault-data