# ALL-IN-ONE LAW FIRM SAAS - SIMPLIFIED VERSION
# Complete legal case management solution with nginx inside container
FROM ubuntu:22.04

LABEL maintainer="Law Firm SaaS Team"
LABEL description="All-in-One Law Firm SaaS Solution with Nginx Inside"
LABEL version="1.0.0"

# Prevent interactive prompts during package installation
ENV DEBIAN_FRONTEND=noninteractive
ENV TZ=UTC

# Install essential packages only
RUN apt-get update && apt-get install -y \
    python3 python3-pip python3-dev \
    nginx supervisor curl wget gnupg lsb-release \
    redis-server \
    certbot python3-certbot-nginx \
    && apt-get clean && rm -rf /var/lib/apt/lists/*

# Install PostgreSQL 15
RUN wget --quiet -O - https://www.postgresql.org/media/keys/ACCC4CF8.asc | apt-key add - \
    && echo "deb http://apt.postgresql.org/pub/repos/apt/ $(lsb_release -cs)-pgdg main" > /etc/apt/sources.list.d/pgdg.list \
    && apt-get update \
    && apt-get install -y postgresql-15 postgresql-contrib-15 \
    && apt-get clean && rm -rf /var/lib/apt/lists/*

# Install MongoDB
RUN wget -qO - https://www.mongodb.org/static/pgp/server-7.0.asc | apt-key add - \
    && echo "deb [ arch=amd64,arm64 ] https://repo.mongodb.org/apt/ubuntu jammy/mongodb-org/7.0 multiverse" | tee /etc/apt/sources.list.d/mongodb-org-7.0.list \
    && apt-get update && apt-get install -y mongodb-org \
    && apt-get clean && rm -rf /var/lib/apt/lists/*

# Create application directories
RUN mkdir -p /app /app/logs /var/log/supervisor \
    && mkdir -p /var/lib/postgresql/data /data/db

# Set working directory
WORKDIR /app

# Copy application files
COPY . .

# Install Python dependencies
RUN pip3 install --no-cache-dir -r requirements.txt

# Configure Nginx with SSL
COPY config/nginx-all-in-one.conf /etc/nginx/sites-available/default

# Configure Supervisor
COPY config/supervisord-simple.conf /etc/supervisor/conf.d/supervisord.conf

# Initialize PostgreSQL
USER postgres
RUN /usr/lib/postgresql/15/bin/initdb -D /var/lib/postgresql/data
RUN /usr/lib/postgresql/15/bin/pg_ctl -D /var/lib/postgresql/data -l /var/lib/postgresql/logfile start \
    && /usr/lib/postgresql/15/bin/createdb legal_cms_main \
    && /usr/lib/postgresql/15/bin/psql -c "CREATE USER legal_cms_user WITH SUPERUSER PASSWORD 'SecureLawFirmPassword2025';" \
    && /usr/lib/postgresql/15/bin/pg_ctl -D /var/lib/postgresql/data stop

# Switch back to root for service management
USER root

# Set permissions
RUN chown -R postgres:postgres /var/lib/postgresql \
    && chown -R mongodb:mongodb /data/db \
    && chmod +x /app/scripts/*.sh

# Create startup script
RUN echo '#!/bin/bash\n\
echo "🚀 Starting ALL-IN-ONE-LAW-FIRM-SAAS"\n\
\n\
# Generate self-signed SSL certificates\n\
mkdir -p /etc/ssl/law-firm\n\
openssl req -x509 -nodes -days 365 -newkey rsa:2048 \\\n\
    -keyout /etc/ssl/law-firm/law-firm.key \\\n\
    -out /etc/ssl/law-firm/law-firm.crt \\\n\
    -subj "/C=US/ST=State/L=City/O=Law Firm/CN=localhost"\n\
\n\
# Start services with supervisor\n\
exec /usr/bin/supervisord -c /etc/supervisor/conf.d/supervisord.conf' > /app/start.sh \
    && chmod +x /app/start.sh

# Expose ports
EXPOSE 80 443 5432 27017 6379 8501

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f https://localhost:443/ --insecure || exit 1

# Start the container
CMD ["/app/start.sh"]