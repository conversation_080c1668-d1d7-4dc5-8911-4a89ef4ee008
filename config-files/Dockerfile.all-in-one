# ALL-IN-ONE LAW FIRM SaaS CONTAINER
# Complete legal case management solution with all services integrated
FROM ubuntu:22.04

LABEL maintainer="Law Firm SaaS Team"
LABEL description="All-in-One Law Firm SaaS Solution"
LABEL version="1.0.0"

# Prevent interactive prompts during package installation
ENV DEBIAN_FRONTEND=noninteractive
ENV TZ=UTC

# Install system dependencies (excluding MongoDB tools - installed separately)
RUN apt-get update && apt-get install -y \
    # Core system tools
    curl \
    wget \
    gnupg \
    lsb-release \
    software-properties-common \
    apt-transport-https \
    ca-certificates \
    supervisor \
    # Python and development tools
    python3 \
    python3-pip \
    python3-dev \
    python3-venv \
    build-essential \
    # Database clients
    postgresql-client \
    redis-tools \
    # Web server and SSL
    nginx \
    certbot \
    python3-certbot-nginx \
    # OCR and document processing
    tesseract-ocr \
    tesseract-ocr-eng \
    imagemagick \
    poppler-utils \
    # Security and encryption
    gnupg2 \
    gpg-agent \
    # Process management
    procps \
    htop \
    # Network tools
    netcat-openbsd \
    telnet \
    # Cleanup
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Install PostgreSQL 15
RUN wget --quiet -O - https://www.postgresql.org/media/keys/ACCC4CF8.asc | apt-key add - \
    && echo "deb http://apt.postgresql.org/pub/repos/apt/ $(lsb_release -cs)-pgdg main" > /etc/apt/sources.list.d/pgdg.list \
    && apt-get update \
    && apt-get install -y postgresql-15 postgresql-contrib-15 \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Install MongoDB 7.0 and MongoDB Shell (mongosh)
RUN wget -qO - https://www.mongodb.org/static/pgp/server-7.0.asc | apt-key add - \
    && echo "deb [ arch=amd64,arm64 ] https://repo.mongodb.org/apt/ubuntu jammy/mongodb-org/7.0 multiverse" | tee /etc/apt/sources.list.d/mongodb-org-7.0.list \
    && apt-get update \
    && apt-get install -y mongodb-org mongodb-mongosh \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Install Redis 7.x
RUN curl -fsSL https://packages.redis.io/gpg | gpg --dearmor -o /usr/share/keyrings/redis-archive-keyring.gpg \
    && echo "deb [signed-by=/usr/share/keyrings/redis-archive-keyring.gpg] https://packages.redis.io/deb $(lsb_release -cs) main" | tee /etc/apt/sources.list.d/redis.list \
    && apt-get update \
    && apt-get install -y redis \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Install HashiCorp Vault
RUN wget -O- https://apt.releases.hashicorp.com/gpg | gpg --dearmor | tee /usr/share/keyrings/hashicorp-archive-keyring.gpg \
    && echo "deb [signed-by=/usr/share/keyrings/hashicorp-archive-keyring.gpg] https://apt.releases.hashicorp.com $(lsb_release -cs) main" | tee /etc/apt/sources.list.d/hashicorp.list \
    && apt-get update \
    && apt-get install -y vault \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Install Qdrant Vector Database
RUN wget -qO - https://qdrant.to/install | bash \
    && mv qdrant /usr/local/bin/qdrant \
    && chmod +x /usr/local/bin/qdrant

# Create application directories
RUN mkdir -p /app \
    /app/data \
    /app/case_documents \
    /app/legal_exports \
    /app/logs \
    /app/config \
    /etc/supervisor/conf.d \
    /var/lib/postgresql/data \
    /data/db \
    /data \
    /qdrant/storage \
    /vault/data \
    /etc/ssl/law-firm

# Create users for services
RUN useradd -r -s /bin/false postgres || true \
    && useradd -r -s /bin/false mongodb || true \
    && useradd -r -s /bin/false redis || true \
    && useradd -r -s /bin/false vault || true \
    && useradd -r -s /bin/false lawfirm

# Set working directory
WORKDIR /app

# Copy application files
COPY . .

# Install Python dependencies
RUN pip3 install --no-cache-dir -r requirements.txt

# Copy service configuration files
COPY config/nginx-all-in-one.conf /etc/nginx/sites-available/default
COPY config/supervisord.conf /etc/supervisor/conf.d/supervisord.conf
COPY config/vault.hcl /etc/vault/vault.hcl
COPY config/qdrant-config.yaml /qdrant/config.yaml

# Configure nginx
RUN rm -f /etc/nginx/sites-enabled/default && \
    ln -s /etc/nginx/sites-available/default /etc/nginx/sites-enabled/default

# Set permissions
RUN chown -R postgres:postgres /var/lib/postgresql \
    && chown -R mongodb:mongodb /data/db \
    && chown -R redis:redis /data \
    && chown -R vault:vault /vault \
    && chown -R lawfirm:lawfirm /app \
    && chmod +x /app/scripts/*.sh

# Initialize databases
RUN service postgresql start \
    && sudo -u postgres createuser -s legal_cms_user \
    && sudo -u postgres createdb legal_cms_main \
    && sudo -u postgres psql -c "ALTER USER legal_cms_user PASSWORD 'SecureLawFirmPassword2025';" \
    && service postgresql stop

# Configure MongoDB
RUN mkdir -p /data/db \
    && chown -R mongodb:mongodb /data/db

# Configure Redis
RUN mkdir -p /var/lib/redis \
    && chown redis:redis /var/lib/redis

# Configure Vault
RUN mkdir -p /vault/logs /vault/data \
    && chown -R vault:vault /vault

# Configure Nginx for SSL with Certbot
RUN mkdir -p /var/www/certbot \
    && chown -R www-data:www-data /var/www/certbot

# Expose ports
EXPOSE 80 443 5432 27017 6379 6333 8200 8501

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f https://localhost:443/ --insecure || exit 1

# Copy startup script
COPY scripts/container-startup.sh /app/scripts/container-startup.sh
RUN chmod +x /app/scripts/container-startup.sh

# Start container with initialization script
CMD ["/app/scripts/container-startup.sh"]