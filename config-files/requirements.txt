# Core AI and ML libraries
agno
streamlit==1.40.2
qdrant-client==1.12.1
ollama==0.4.4

# Document processing and OCR
PyMuPDF>=1.23.0         # For PDF processing (fitz)
pytesseract>=0.3.10     # OCR engine
Pillow>=10.0.0          # Image processing for OCR
python-docx>=1.1.0      # Word document generation and reading
openpyxl>=3.1.0         # Excel file processing
beautifulsoup4>=4.12.0  # HTML parsing
lxml>=4.9.0             # XML processing (used by beautifulsoup4)

# Data visualization and analysis
plotly>=5.17.0          # Interactive timeline charts
pandas>=2.1.0           # Data manipulation
numpy>=1.25.0           # Numerical operations

# Additional utilities
python-dateutil>=2.8.0 # Date parsing
pytz>=2023.3           # Timezone handling

# E-filing system integration
selenium>=4.15.0        # Web automation for e-filing systems

# Security and Encryption (NEW SECURE ARCHITECTURE)
bcrypt>=4.0.0           # Password hashing
cryptography>=41.0.0    # Encryption primitives
python-gnupg>=0.5.0     # PGP encryption
PyJWT>=2.8.0            # JWT tokens

# Database Connections (NEW SECURE ARCHITECTURE)
psycopg2-binary>=2.9.7  # PostgreSQL adapter
pymongo>=4.5.0          # MongoDB driver
redis>=5.0.0            # Redis client

# Vault Integration (NEW SECURE ARCHITECTURE)
hvac>=1.2.1             # HashiCorp Vault client

# GitHub Integration (NEW SECURE ARCHITECTURE)
PyGithub>=1.59.0        # GitHub API client

# Additional Security Dependencies
python-dotenv>=1.0.0    # Environment variables
requests>=2.31.0        # HTTP client with security features

# Development and Testing (NEW SECURE ARCHITECTURE)
pytest>=7.4.0          # Testing framework
pytest-asyncio>=0.21.0 # Async testing
pytest-cov>=4.1.0      # Coverage reporting
black>=23.7.0           # Code formatting
flake8>=6.0.0           # Code linting

# Production Deployment (NEW SECURE ARCHITECTURE)
gunicorn>=21.2.0        # WSGI server
supervisor>=4.2.0       # Process management
webdriver-manager>=4.0.0 # Automatic Chrome driver management

# System requirements (install separately):
# - Tesseract OCR engine
#   Windows: Download from https://github.com/UB-Mannheim/tesseract/wiki
#   macOS: brew install tesseract
#   Linux: sudo apt-get install tesseract-ocr
#
# - For better OCR accuracy, install additional language packs:
#   sudo apt-get install tesseract-ocr-eng tesseract-ocr-spa (Linux)
#   brew install tesseract-lang (macOS)