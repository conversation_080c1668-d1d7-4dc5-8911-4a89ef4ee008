version: '3.8'

services:
  # PostgreSQL Primary Database
  postgresql:
    image: postgres:15-alpine
    container_name: legal-cms-postgresql
    environment:
      POSTGRES_DB: legal_cms_main
      POSTGRES_USER: legal_cms_user
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      POSTGRES_INITDB_ARGS: "--auth-host=scram-sha-256"
    volumes:
      - postgresql_data:/var/lib/postgresql/data
      - /etc/ssl/legal-cms/certs:/etc/ssl/certs:ro
      - /etc/ssl/legal-cms/private:/etc/ssl/private:ro
      - ./config/postgresql.conf:/etc/postgresql/postgresql.conf
    ports:
      - "5432:5432"
    networks:
      - legal-cms-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U legal_cms_user -d legal_cms_main"]
      interval: 30s
      timeout: 10s
      retries: 3

  # MongoDB Replica Set
  mongodb-primary:
    image: mongo:7.0
    container_name: legal-cms-mongodb-primary
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: ${MONGODB_ROOT_PASSWORD}
      MONGO_INITDB_DATABASE: legal_cms_documents
    volumes:
      - mongodb_primary_data:/data/db
      - /etc/ssl/legal-cms/certs:/etc/ssl/certs:ro
      - /etc/ssl/legal-cms/private:/etc/ssl/private:ro
      - ./config/mongod.conf:/etc/mongod.conf
    ports:
      - "27017:27017"
    networks:
      - legal-cms-network
    restart: unless-stopped
    command: mongod --config /etc/mongod.conf --replSet legal-cms-rs

  # Redis Cluster
  redis:
    image: redis:7-alpine
    container_name: legal-cms-redis
    environment:
      REDIS_PASSWORD: ${REDIS_PASSWORD}
    volumes:
      - redis_data:/data
      - /etc/ssl/legal-cms/certs:/etc/ssl/certs:ro
      - /etc/ssl/legal-cms/private:/etc/ssl/private:ro
      - ./config/redis.conf:/etc/redis/redis.conf
    ports:
      - "6379:6379"
    networks:
      - legal-cms-network
    restart: unless-stopped
    command: redis-server /etc/redis/redis.conf

  # HashiCorp Vault
  vault:
    image: vault:1.15
    container_name: legal-cms-vault
    environment:
      VAULT_DEV_ROOT_TOKEN_ID: ${VAULT_ROOT_TOKEN}
      VAULT_DEV_LISTEN_ADDRESS: 0.0.0.0:8200
    volumes:
      - vault_data:/vault/data
      - /etc/ssl/legal-cms/certs:/etc/ssl/certs:ro
      - /etc/ssl/legal-cms/private:/etc/ssl/private:ro
      - ./config/vault.hcl:/vault/config/vault.hcl
    ports:
      - "8200:8200"
    networks:
      - legal-cms-network
    restart: unless-stopped
    cap_add:
      - IPC_LOCK
    command: vault server -config=/vault/config/vault.hcl

  # Legal CMS Application
  legal-cms-app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: legal-cms-app
    environment:
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - MONGODB_PASSWORD=${MONGODB_PASSWORD}
      - REDIS_PASSWORD=${REDIS_PASSWORD}
      - VAULT_TOKEN=${VAULT_ROOT_TOKEN}
      - GITHUB_TOKEN=${GITHUB_TOKEN}
      - ENCRYPTION_KEY=${ENCRYPTION_KEY}
      - JWT_SECRET_KEY=${JWT_SECRET_KEY}
    volumes:
      - app_data:/app/data
      - /etc/ssl/legal-cms/certs:/etc/ssl/certs:ro
    ports:
      - "8501:8501"
    networks:
      - legal-cms-network
    restart: unless-stopped
    depends_on:
      - postgresql
      - mongodb-primary
      - redis
      - vault
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8501/_stcore/health"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  postgresql_data:
  mongodb_primary_data:
  redis_data:
  vault_data:
  app_data:

networks:
  legal-cms-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
