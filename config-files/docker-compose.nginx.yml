version: '3.8'

services:
  # Nginx Reverse Proxy
  legal-cms-nginx:
    image: nginx:alpine
    container_name: legal-cms-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/conf.d/default.conf
      - ./localhost+2.pem:/etc/ssl/certs/localhost+2.pem
      - ./localhost+2-key.pem:/etc/ssl/private/localhost+2-key.pem
      - certbot_webroot:/var/www/certbot
      - letsencrypt_certs:/etc/letsencrypt
    extra_hosts:
      - "host.docker.internal:host-gateway"
    restart: unless-stopped

  # Certbot for Let's Encrypt
  legal-cms-certbot:
    image: certbot/certbot
    container_name: legal-cms-certbot
    volumes:
      - certbot_webroot:/var/www/certbot
      - letsencrypt_certs:/etc/letsencrypt
    command: certonly --webroot --webroot-path=/var/www/certbot --email <EMAIL> --agree-tos --no-eff-email --force-renewal -d legal-cms.local
    networks:
      - legal-cms-network

  # Note: Application runs on host at port 8503
  # Run: streamlit run secure_app.py --server.port 8503

volumes:
  certbot_webroot:
  letsencrypt_certs:

networks:
  legal-cms-network:
    driver: bridge