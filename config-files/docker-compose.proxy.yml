version: '3.8'

services:
  # Nginx Reverse Proxy with SSL
  nginx:
    image: nginx:alpine
    container_name: legal-cms-nginx-proxy
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/conf.d/default.conf:ro
      - ./localhost+2.pem:/etc/nginx/ssl/localhost+2.pem:ro
      - ./localhost+2-key.pem:/etc/nginx/ssl/localhost+2-key.pem:ro
    networks:
      - legal-cms-proxy
    restart: unless-stopped
    extra_hosts:
      - "host.docker.internal:host-gateway"

networks:
  legal-cms-proxy:
    driver: bridge