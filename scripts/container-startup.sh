#!/bin/bash
# ALL-IN-ONE LAW FIRM SAAS Container Startup Script
set -e

echo "🚀 Starting ALL-IN-ONE-LAW-FIRM-SAAS Container"
echo "=============================================="

# Initialize SSL certificates first
echo "🔒 Initializing SSL certificates..."
/app/scripts/init-ssl-certbot.sh

# Initialize databases
echo "🗄️ Initializing databases..."

# Start PostgreSQL temporarily for initialization
service postgresql start
sleep 5

# Create database and user if they don't exist
sudo -u postgres psql -c "SELECT 1 FROM pg_roles WHERE rolname='legal_cms_user'" | grep -q 1 || \
    sudo -u postgres createuser -s legal_cms_user

sudo -u postgres psql -c "SELECT 1 FROM pg_database WHERE datname='legal_cms_main'" | grep -q 1 || \
    sudo -u postgres createdb legal_cms_main

sudo -u postgres psql -c "ALTER USER legal_cms_user PASSWORD 'SecureLawFirmPassword2025';" || true

# Stop PostgreSQL (will be managed by supervisor)
service postgresql stop

# Initialize MongoDB
echo "📋 Setting up MongoDB..."
mkdir -p /data/db
chown -R mongodb:mongodb /data/db

# Initialize Redis
echo "💾 Setting up Redis..."
mkdir -p /var/lib/redis
chown -R redis:redis /var/lib/redis

# Initialize Vault
echo "🔒 Setting up Vault..."
mkdir -p /vault/data /vault/logs
chown -R vault:vault /vault

# Create application logs directory
mkdir -p /app/logs
chown -R lawfirm:lawfirm /app/logs

# Initialize Qdrant storage
echo "🔍 Setting up Qdrant..."
mkdir -p /qdrant/storage
chown -R lawfirm:lawfirm /qdrant

echo "✅ All services initialized successfully"
echo "🎯 Starting ALL-IN-ONE-LAW-FIRM-SAAS with Supervisor..."

# Start all services with supervisor
exec /usr/bin/supervisord -c /etc/supervisor/conf.d/supervisord.conf