#!/usr/bin/env python3
"""
Simple HashiCorp Vault startup script
Starts Vault in development mode for testing
"""

import subprocess
import time
import os
import requests

def start_vault_dev():
    """Start Vault in development mode"""
    
    print("🔒 Starting HashiCorp Vault in Development Mode")
    print("=" * 50)
    
    # Check if Vault is already running
    try:
        response = requests.get('http://localhost:8200/v1/sys/health', timeout=2)
        print("✅ Vault is already running!")
        print(f"🌐 Vault UI: http://localhost:8200")
        return True
    except:
        pass
    
    # Check if container exists
    try:
        result = subprocess.run(['docker', 'ps', '-a', '--filter', 'name=legal-cms-vault', '--format', '{{.Names}}'], 
                              capture_output=True, text=True)
        if 'legal-cms-vault' in result.stdout:
            print("🔄 Starting existing Vault container...")
            subprocess.run(['docker', 'start', 'legal-cms-vault'], check=True)
        else:
            print("🚀 Creating new Vault container...")
            # Run Vault in development mode
            vault_command = [
                'docker', 'run', '-d',
                '--name', 'legal-cms-vault',
                '--network', 'legal-cms-network',
                '-p', '8200:8200',
                '-e', 'VAULT_DEV_ROOT_TOKEN_ID=legal-cms-dev-token',
                '-e', 'VAULT_DEV_LISTEN_ADDRESS=0.0.0.0:8200',
                'hashicorp/vault:latest'
            ]
            
            subprocess.run(vault_command, check=True)
            print("✅ Vault container created and started")
        
        # Wait for Vault to be ready
        print("⏳ Waiting for Vault to be ready...")
        for i in range(10):
            try:
                response = requests.get('http://localhost:8200/v1/sys/health', timeout=2)
                if response.status_code in [200, 429, 472, 473]:
                    print("✅ Vault is ready!")
                    break
            except:
                time.sleep(2)
                print(f"   Attempt {i+1}/10...")
        else:
            print("⚠️ Vault may not be fully ready yet")
        
        # Add environment variables
        env_content = """
# HashiCorp Vault Configuration (Development Mode)
VAULT_ADDR=http://localhost:8200
VAULT_TOKEN=legal-cms-dev-token
"""
        
        try:
            with open('.env', 'a') as f:
                f.write(env_content)
            print("✅ Added Vault configuration to .env")
        except:
            print("⚠️ Could not update .env file")
        
        print("\n🎉 Vault Setup Complete!")
        print("🌐 Vault UI: http://localhost:8200")
        print("🔑 Development Token: legal-cms-dev-token")
        print("💡 This is for development only - use proper auth in production")
        
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to start Vault: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def stop_vault():
    """Stop Vault container"""
    try:
        subprocess.run(['docker', 'stop', 'legal-cms-vault'], check=True)
        print("✅ Vault stopped")
    except:
        print("ℹ️ Vault was not running")

def vault_status():
    """Check Vault status"""
    try:
        response = requests.get('http://localhost:8200/v1/sys/health', timeout=2)
        if response.status_code in [200, 429, 472, 473]:
            print("✅ Vault is running and healthy")
            print(f"🌐 Vault UI: http://localhost:8200")
        else:
            print(f"⚠️ Vault responded with status: {response.status_code}")
    except Exception as e:
        print(f"❌ Vault is not accessible: {e}")

def main():
    """Main function"""
    import sys
    
    if len(sys.argv) > 1:
        command = sys.argv[1].lower()
        
        if command == 'start':
            start_vault_dev()
        elif command == 'stop':
            stop_vault()
        elif command == 'status':
            vault_status()
        else:
            print("Usage: python start_vault_simple.py [start|stop|status]")
    else:
        start_vault_dev()

if __name__ == "__main__":
    main()