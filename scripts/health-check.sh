#!/bin/bash

# CUYAHOGA COUNTY PRO SE Litigation Management System
# Health Check Script

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if a service is responding
check_service() {
    local service_name=$1
    local url=$2
    local expected_status=${3:-200}
    
    print_status "Checking $service_name..."
    
    if curl -s -o /dev/null -w "%{http_code}" "$url" | grep -q "$expected_status"; then
        print_success "$service_name is healthy"
        return 0
    else
        print_error "$service_name is not responding"
        return 1
    fi
}

# Check container status
check_containers() {
    print_status "Checking container status..."
    
    local containers=("legal-cms-app" "legal-cms-postgresql" "legal-cms-mongodb" "legal-cms-redis" "legal-cms-qdrant")
    local all_healthy=true
    
    for container in "${containers[@]}"; do
        if docker ps --filter "name=$container" --filter "status=running" | grep -q "$container"; then
            print_success "$container is running"
        else
            print_error "$container is not running"
            all_healthy=false
        fi
    done
    
    if $all_healthy; then
        return 0
    else
        return 1
    fi
}

# Check service endpoints
check_endpoints() {
    print_status "Checking service endpoints..."
    
    local all_healthy=true
    
    # Check Streamlit app
    if ! check_service "Streamlit App" "http://localhost:8501/_stcore/health"; then
        all_healthy=false
    fi
    
    # Check Qdrant
    if ! check_service "Qdrant" "http://localhost:6334/"; then
        all_healthy=false
    fi
    
    # Check PostgreSQL (basic connection test)
    if docker exec legal-cms-postgresql pg_isready -U postgres > /dev/null 2>&1; then
        print_success "PostgreSQL is healthy"
    else
        print_error "PostgreSQL is not responding"
        all_healthy=false
    fi
    
    # Check MongoDB (basic connection test)
    if docker exec legal-cms-mongodb mongosh --eval "db.adminCommand('ping')" > /dev/null 2>&1; then
        print_success "MongoDB is healthy"
    else
        print_error "MongoDB is not responding"
        all_healthy=false
    fi
    
    # Check Redis (basic connection test)
    if docker exec legal-cms-redis redis-cli -a "${REDIS_PASSWORD:-SecureRedisPassword123}" ping 2>/dev/null | grep -q "PONG"; then
        print_success "Redis is healthy"
    else
        print_error "Redis is not responding"
        all_healthy=false
    fi
    
    if $all_healthy; then
        return 0
    else
        return 1
    fi
}

# Show resource usage
show_resources() {
    print_status "Resource usage:"
    docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.MemPerc}}"
}

# Main health check
main() {
    echo "CUYAHOGA COUNTY PRO SE Litigation Management System - Health Check"
    echo "=================================================================="
    
    local overall_health=true
    
    # Check if Docker is running
    if ! docker info > /dev/null 2>&1; then
        print_error "Docker is not running"
        exit 1
    fi
    
    # Check containers
    if ! check_containers; then
        overall_health=false
    fi
    
    # Wait a moment for services to be ready
    sleep 2
    
    # Check endpoints
    if ! check_endpoints; then
        overall_health=false
    fi
    
    # Show resources
    echo ""
    show_resources
    
    echo ""
    if $overall_health; then
        print_success "All services are healthy!"
        echo ""
        print_status "Application URLs:"
        echo "  - Web Interface: http://localhost:8501"
        echo "  - Qdrant Dashboard: http://localhost:6334/dashboard"
        echo ""
        print_status "Database Connections:"
        echo "  - PostgreSQL: localhost:5435"
        echo "  - MongoDB: localhost:27018"
        echo "  - Redis: localhost:6382"
        exit 0
    else
        print_error "Some services are not healthy. Check the logs for more details."
        echo ""
        print_status "To view logs, run: ./deploy.sh logs"
        exit 1
    fi
}

# Run main function
main
