#!/usr/bin/env python3
"""
Codebase Cleanup Script for Legal Case Management System
Removes duplicate functionality, unused code, and consolidates architecture.

CLEANUP ACTIONS:
1. Remove duplicate database implementations
2. Clean up test infrastructure
3. Remove unused utility files
4. Fix broken imports and references
5. Consolidate configuration
"""

import os
import sys
import shutil
import logging
from pathlib import Path
from typing import List, Dict, Tuple

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class CodebaseCleanup:
    """Codebase cleanup utilities"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.cleanup_log = []
        
    def log_action(self, action: str, file_path: str, reason: str):
        """Log cleanup action"""
        self.cleanup_log.append({
            'action': action,
            'file': file_path,
            'reason': reason
        })
        logger.info(f"{action}: {file_path} - {reason}")
    
    def safe_remove_file(self, file_path: Path, reason: str) -> bool:
        """Safely remove a file with logging"""
        if not file_path.exists():
            return True
            
        try:
            # Create backup in cleanup_backups directory
            backup_dir = self.project_root / "cleanup_backups"
            backup_dir.mkdir(exist_ok=True)
            
            backup_path = backup_dir / file_path.name
            shutil.copy2(file_path, backup_path)
            
            # Remove original file
            file_path.unlink()
            self.log_action("REMOVED", str(file_path), reason)
            return True
            
        except Exception as e:
            logger.error(f"Failed to remove {file_path}: {e}")
            return False
    
    def remove_duplicate_database_code(self):
        """Remove duplicate database implementations"""
        logger.info("🗄️ Removing duplicate database code...")
        
        # Remove legacy database.py (SQLite-based)
        legacy_db = self.project_root / "core" / "database.py"
        if legacy_db.exists():
            self.safe_remove_file(legacy_db, "Legacy SQLite implementation, replaced by database_managers.py")
        
        # Fix imports that reference the old database module
        files_to_fix = [
            self.project_root / "core" / "document_processor.py",
            self.project_root / "secure_app.py"
        ]
        
        for file_path in files_to_fix:
            if file_path.exists():
                content = file_path.read_text()
                
                # Replace old import
                old_import = "from core.database import db_manager"
                new_import = "from core.database_managers import DatabaseManagerFactory"
                
                if old_import in content:
                    content = content.replace(old_import, new_import)
                    file_path.write_text(content)
                    self.log_action("FIXED", str(file_path), "Updated database import")
        
        return True
    
    def cleanup_test_infrastructure(self):
        """Clean up duplicate and broken test files"""
        logger.info("🧪 Cleaning up test infrastructure...")
        
        # Remove broken test files
        broken_tests = [
            "tests/test_app_playwright.py",  # Broken async implementation
            "tests/test_usability_playwright.py",  # Broken implementation
            "tests/run_tests.py",  # Old test runner
            "tests/conftest.py",  # Broken async fixtures
            "tests/pytest.ini"  # Old configuration
        ]
        
        for test_file in broken_tests:
            file_path = self.project_root / test_file
            if file_path.exists():
                self.safe_remove_file(file_path, "Broken or duplicate test implementation")
        
        # Keep working test files:
        # - tests/test_working_functionality.py ✅
        # - tests/run_simple_tests.py ✅
        # - tests/test_diagnostic.py ✅ (for debugging)
        
        return True
    
    def remove_unused_utilities(self):
        """Remove unused utility files"""
        logger.info("🗑️ Removing unused utility files...")
        
        unused_files = [
            "core/c2p.py",  # Duplicate code analysis tool
            "core/OUT.put",  # Generated analysis file
            "fix_database_errors.py",  # One-time utility
            "test_database_connections.py",  # Standalone, not integrated
        ]
        
        for unused_file in unused_files:
            file_path = self.project_root / unused_file
            if file_path.exists():
                self.safe_remove_file(file_path, "Unused utility or duplicate functionality")
        
        return True
    
    def remove_incomplete_features(self):
        """Remove incomplete or unused feature implementations"""
        logger.info("🚧 Removing incomplete features...")
        
        incomplete_files = [
            "core/forensic_extension.py",  # 578 lines, incomplete
            "core/error_recovery.py",  # 582 lines, partial implementation
            "core/queue_manager.py",  # 546 lines, not integrated
            "security/auth.py",  # Conflicts with new architecture
        ]
        
        for incomplete_file in incomplete_files:
            file_path = self.project_root / incomplete_file
            if file_path.exists():
                self.safe_remove_file(file_path, "Incomplete implementation or architectural conflict")
        
        return True
    
    def fix_broken_references(self):
        """Fix broken file references"""
        logger.info("🔧 Fixing broken references...")
        
        # Fix main.py reference to non-existent file
        main_py = self.project_root / "main.py"
        if main_py.exists():
            content = main_py.read_text()
            
            # Fix Streamlit app reference
            old_ref = "legal_case_manager.py"
            new_ref = "secure_app.py"
            
            if old_ref in content:
                content = content.replace(old_ref, new_ref)
                main_py.write_text(content)
                self.log_action("FIXED", str(main_py), f"Updated reference from {old_ref} to {new_ref}")
        
        return True
    
    def clean_empty_files(self):
        """Remove empty test data files"""
        logger.info("📄 Cleaning empty files...")
        
        # Remove empty test files (based on supervisor info)
        empty_files = [
            "test_document.txt",
            "test_legal_data.csv"
        ]
        
        for empty_file in empty_files:
            file_path = self.project_root / empty_file
            if file_path.exists() and file_path.stat().st_size == 0:
                self.safe_remove_file(file_path, "Empty test file")
        
        return True
    
    def consolidate_configuration(self):
        """Consolidate scattered configuration"""
        logger.info("⚙️ Consolidating configuration...")
        
        # Create centralized config directory structure
        config_dir = self.project_root / "config"
        config_dir.mkdir(exist_ok=True)
        
        # Move scattered config files to config directory
        config_files = [
            ("docker-compose.yml", "Infrastructure configuration"),
            (".env.template", "Environment template")
        ]
        
        for config_file, description in config_files:
            source = self.project_root / config_file
            target = config_dir / config_file
            
            if source.exists() and not target.exists():
                shutil.move(str(source), str(target))
                self.log_action("MOVED", f"{source} -> {target}", description)
        
        return True
    
    def generate_cleanup_report(self):
        """Generate cleanup report"""
        logger.info("📊 Generating cleanup report...")
        
        report_path = self.project_root / "CLEANUP_REPORT.md"
        
        report_content = f"""# 🧹 CODEBASE CLEANUP REPORT
Generated: {os.popen('date').read().strip()}

## Summary
- **Total Actions**: {len(self.cleanup_log)}
- **Files Removed**: {len([a for a in self.cleanup_log if a['action'] == 'REMOVED'])}
- **Files Fixed**: {len([a for a in self.cleanup_log if a['action'] == 'FIXED'])}
- **Files Moved**: {len([a for a in self.cleanup_log if a['action'] == 'MOVED'])}

## Detailed Actions

"""
        
        for action_info in self.cleanup_log:
            report_content += f"### {action_info['action']}: {action_info['file']}\n"
            report_content += f"**Reason**: {action_info['reason']}\n\n"
        
        report_content += """
## Remaining Core Files

### ✅ **Keep - Core Functionality**
- `secure_app.py` - Main Streamlit application
- `core/database_managers.py` - Multi-database management
- `core/document_processor.py` - Document processing
- `core/efiling_scraper.py` - E-filing integration
- `core/efiling_ui.py` - E-filing UI components
- `core/docket_viewer.py` - Court docket display
- `core/document_numbering.py` - Document numbering system
- `core/chat_manager.py` - AI chat functionality
- `core/document_change_monitor.py` - Change detection
- `core/security_enforcer.py` - Security enforcement

### ✅ **Keep - Security**
- `security/pgp_manager.py` - PGP encryption
- `config/database.json` - Database configuration

### ✅ **Keep - Testing**
- `tests/test_working_functionality.py` - Working Playwright tests
- `tests/run_simple_tests.py` - Test runner
- `tests/test_diagnostic.py` - Diagnostic utilities

### ✅ **Keep - Infrastructure**
- `deployment/deploy_secure_architecture.sh` - Deployment script
- `requirements.txt` - Python dependencies
- `main.py` - CLI interface

## Next Steps
1. Run security hardening: `python security_hardening.py`
2. Test remaining functionality
3. Update documentation
4. Deploy cleaned codebase
"""
        
        report_path.write_text(report_content)
        logger.info(f"✅ Cleanup report generated: {report_path}")
        
        return True
    
    def run_cleanup(self):
        """Run complete cleanup process"""
        logger.info("🧹 Starting Codebase Cleanup Process")
        logger.info("=" * 60)
        
        cleanup_steps = [
            ("Remove duplicate database code", self.remove_duplicate_database_code),
            ("Clean up test infrastructure", self.cleanup_test_infrastructure),
            ("Remove unused utilities", self.remove_unused_utilities),
            ("Remove incomplete features", self.remove_incomplete_features),
            ("Fix broken references", self.fix_broken_references),
            ("Clean empty files", self.clean_empty_files),
            ("Consolidate configuration", self.consolidate_configuration),
            ("Generate cleanup report", self.generate_cleanup_report)
        ]
        
        results = []
        for step_name, step_func in cleanup_steps:
            logger.info(f"🔄 {step_name}...")
            try:
                success = step_func()
                results.append((step_name, success))
                if success:
                    logger.info(f"✅ {step_name} completed")
                else:
                    logger.error(f"❌ {step_name} failed")
            except Exception as e:
                logger.error(f"❌ {step_name} failed with error: {e}")
                results.append((step_name, False))
        
        # Summary
        logger.info("\n" + "=" * 60)
        logger.info("🧹 Cleanup Summary")
        logger.info("=" * 60)
        
        successful = sum(1 for _, success in results if success)
        total = len(results)
        
        for step_name, success in results:
            status = "✅ PASS" if success else "❌ FAIL"
            logger.info(f"{step_name:35}: {status}")
        
        logger.info(f"\nOverall: {successful}/{total} cleanup steps completed")
        logger.info(f"Actions performed: {len(self.cleanup_log)}")
        
        if successful == total:
            logger.info("🎉 Codebase cleanup completed successfully!")
            logger.info("\n📋 NEXT STEPS:")
            logger.info("1. Review CLEANUP_REPORT.md")
            logger.info("2. Run security hardening: python security_hardening.py")
            logger.info("3. Test remaining functionality")
            logger.info("4. Update documentation")
        else:
            logger.error("⚠️ Some cleanup steps failed")
        
        return successful == total

def main():
    """Main entry point"""
    cleanup = CodebaseCleanup()
    success = cleanup.run_cleanup()
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
