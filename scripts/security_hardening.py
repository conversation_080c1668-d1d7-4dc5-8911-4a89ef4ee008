#!/usr/bin/env python3
"""
Security Hardening Script for Legal Case Management System
Fixes critical security vulnerabilities and removes hardcoded credentials.

CRITICAL FIXES:
1. Remove hardcoded database passwords
2. Enable SSL/TLS for all connections
3. Implement proper environment variable handling
4. Add credential validation
"""

import os
import sys
import json
import secrets
import logging
from pathlib import Path
from typing import Dict, List, Optional

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class SecurityHardening:
    """Security hardening utilities"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.backup_dir = self.project_root / "security_backups"
        self.backup_dir.mkdir(exist_ok=True)
        
    def create_backup(self, file_path: Path) -> Path:
        """Create backup of file before modification"""
        if not file_path.exists():
            return None
            
        backup_path = self.backup_dir / f"{file_path.name}.backup"
        backup_path.write_text(file_path.read_text())
        logger.info(f"Created backup: {backup_path}")
        return backup_path
    
    def generate_secure_password(self, length: int = 32) -> str:
        """Generate cryptographically secure password"""
        import string
        alphabet = string.ascii_letters + string.digits + "!@#$%^&*"
        return ''.join(secrets.choice(alphabet) for _ in range(length))
    
    def fix_hardcoded_credentials(self):
        """Fix hardcoded credentials in secure_app.py"""
        logger.info("🔒 Fixing hardcoded credentials...")
        
        secure_app_path = self.project_root / "secure_app.py"
        if not secure_app_path.exists():
            logger.error("secure_app.py not found!")
            return False
        
        # Create backup
        self.create_backup(secure_app_path)
        
        # Read current content
        content = secure_app_path.read_text()
        
        # Fix PostgreSQL password
        old_postgres = "'password': os.getenv('POSTGRES_PASSWORD', 'cDBGCyHKzc6F')"
        new_postgres = "'password': os.getenv('POSTGRES_PASSWORD')"
        content = content.replace(old_postgres, new_postgres)
        
        # Fix MongoDB password  
        old_mongodb = "'password': os.getenv('MONGODB_PASSWORD', 'AxWuQaxIGX8g')"
        new_mongodb = "'password': os.getenv('MONGODB_PASSWORD')"
        content = content.replace(old_mongodb, new_mongodb)
        
        # Enable SSL for PostgreSQL
        old_ssl = "'sslmode': 'disable'"
        new_ssl = "'sslmode': 'require'"
        content = content.replace(old_ssl, new_ssl)
        
        # Write fixed content
        secure_app_path.write_text(content)
        logger.info("✅ Fixed hardcoded credentials in secure_app.py")
        return True
    
    def create_secure_env_template(self):
        """Create secure .env template"""
        logger.info("🔐 Creating secure environment template...")
        
        env_template = self.project_root / ".env.template"
        
        # Generate secure passwords
        postgres_password = self.generate_secure_password()
        mongodb_password = self.generate_secure_password()
        redis_password = self.generate_secure_password()
        pgp_passphrase = self.generate_secure_password(64)
        
        env_content = f"""# Legal Case Management System - Environment Variables
# SECURITY: Never commit this file to version control!

# Database Credentials (REQUIRED)
POSTGRES_PASSWORD={postgres_password}
MONGODB_PASSWORD={mongodb_password}
REDIS_PASSWORD={redis_password}

# PGP Encryption
PGP_MASTER_PASSPHRASE={pgp_passphrase}

# GitHub Integration (Optional)
GITHUB_TOKEN=your_github_token_here
GITHUB_REPO_OWNER=your_username
GITHUB_REPO_NAME=legal-documents-encrypted

# Vault Integration (Optional)
VAULT_URL=https://vault.example.com
VAULT_TOKEN=your_vault_token_here

# SSL/TLS Certificates (Production)
SSL_CERT_PATH=/path/to/cert.pem
SSL_KEY_PATH=/path/to/key.pem
SSL_CA_PATH=/path/to/ca.pem

# Security Settings
SESSION_SECRET_KEY={secrets.token_urlsafe(64)}
JWT_SECRET_KEY={secrets.token_urlsafe(64)}
ENCRYPTION_KEY={secrets.token_urlsafe(32)}

# Application Settings
ENVIRONMENT=development
DEBUG=false
LOG_LEVEL=INFO
"""
        
        env_template.write_text(env_content)
        logger.info(f"✅ Created secure environment template: {env_template}")
        
        # Create actual .env if it doesn't exist
        env_file = self.project_root / ".env"
        if not env_file.exists():
            env_file.write_text(env_content)
            logger.info(f"✅ Created .env file: {env_file}")
            logger.warning("🚨 IMPORTANT: Update .env with your actual credentials!")
        
        return True
    
    def fix_database_config(self):
        """Fix database configuration security"""
        logger.info("🗄️ Fixing database configuration...")
        
        config_path = self.project_root / "config" / "database.json"
        if not config_path.exists():
            logger.warning("Database config not found, skipping...")
            return True
        
        # Create backup
        self.create_backup(config_path)
        
        # Load current config
        with open(config_path, 'r') as f:
            config = json.load(f)
        
        # Fix PostgreSQL config
        if 'postgresql' in config:
            pg_config = config['postgresql']
            pg_config['sslmode'] = 'require'
            pg_config['sslcert'] = '${SSL_CERT_PATH}'
            pg_config['sslkey'] = '${SSL_KEY_PATH}'
            pg_config['sslrootcert'] = '${SSL_CA_PATH}'
            
            # Remove hardcoded passwords
            if 'password' in pg_config:
                pg_config['password'] = '${POSTGRES_PASSWORD}'
        
        # Fix MongoDB config
        if 'mongodb' in config:
            mongo_config = config['mongodb']
            mongo_config['ssl'] = True
            mongo_config['ssl_cert_reqs'] = 'CERT_REQUIRED'
            
            # Remove hardcoded passwords
            if 'password' in mongo_config:
                mongo_config['password'] = '${MONGODB_PASSWORD}'
        
        # Fix Redis config
        if 'redis' in config:
            redis_config = config['redis']
            redis_config['ssl'] = True
            redis_config['ssl_cert_reqs'] = 'required'
            
            # Remove hardcoded passwords
            if 'password' in redis_config:
                redis_config['password'] = '${REDIS_PASSWORD}'
        
        # Write fixed config
        with open(config_path, 'w') as f:
            json.dump(config, f, indent=2)
        
        logger.info("✅ Fixed database configuration security")
        return True
    
    def create_gitignore_security(self):
        """Ensure sensitive files are in .gitignore"""
        logger.info("🔒 Updating .gitignore for security...")
        
        gitignore_path = self.project_root / ".gitignore"
        
        security_entries = [
            "# Security - Never commit these files!",
            ".env",
            ".env.local", 
            ".env.production",
            "*.key",
            "*.pem",
            "*.p12",
            "*.pfx",
            "config/secrets.json",
            "security_backups/",
            "*.backup",
            "",
            "# Database files",
            "*.db",
            "*.sqlite",
            "*.sqlite3",
            "",
            "# Logs with potential sensitive data",
            "*.log",
            "logs/",
            "",
            "# PGP keys",
            "*.gpg",
            "*.asc",
            "gnupg/",
            ".gnupg/",
        ]
        
        if gitignore_path.exists():
            current_content = gitignore_path.read_text()
        else:
            current_content = ""
        
        # Add missing security entries
        for entry in security_entries:
            if entry not in current_content:
                current_content += f"\n{entry}"
        
        gitignore_path.write_text(current_content)
        logger.info("✅ Updated .gitignore with security entries")
        return True
    
    def validate_environment(self):
        """Validate environment variables are set"""
        logger.info("🔍 Validating environment configuration...")
        
        required_vars = [
            'POSTGRES_PASSWORD',
            'MONGODB_PASSWORD', 
            'PGP_MASTER_PASSPHRASE',
            'SESSION_SECRET_KEY',
            'JWT_SECRET_KEY'
        ]
        
        missing_vars = []
        for var in required_vars:
            if not os.getenv(var):
                missing_vars.append(var)
        
        if missing_vars:
            logger.error(f"❌ Missing required environment variables: {missing_vars}")
            logger.error("Please update your .env file with the required variables")
            return False
        
        logger.info("✅ All required environment variables are set")
        return True
    
    def run_security_hardening(self):
        """Run complete security hardening process"""
        logger.info("🛡️ Starting Security Hardening Process")
        logger.info("=" * 60)
        
        steps = [
            ("Fix hardcoded credentials", self.fix_hardcoded_credentials),
            ("Create secure environment template", self.create_secure_env_template),
            ("Fix database configuration", self.fix_database_config),
            ("Update .gitignore", self.create_gitignore_security),
            ("Validate environment", self.validate_environment)
        ]
        
        results = []
        for step_name, step_func in steps:
            logger.info(f"🔄 {step_name}...")
            try:
                success = step_func()
                results.append((step_name, success))
                if success:
                    logger.info(f"✅ {step_name} completed")
                else:
                    logger.error(f"❌ {step_name} failed")
            except Exception as e:
                logger.error(f"❌ {step_name} failed with error: {e}")
                results.append((step_name, False))
        
        # Summary
        logger.info("\n" + "=" * 60)
        logger.info("🛡️ Security Hardening Summary")
        logger.info("=" * 60)
        
        successful = sum(1 for _, success in results if success)
        total = len(results)
        
        for step_name, success in results:
            status = "✅ PASS" if success else "❌ FAIL"
            logger.info(f"{step_name:30}: {status}")
        
        logger.info(f"\nOverall: {successful}/{total} steps completed successfully")
        
        if successful == total:
            logger.info("🎉 Security hardening completed successfully!")
            logger.info("\n📋 NEXT STEPS:")
            logger.info("1. Review and update .env file with your credentials")
            logger.info("2. Test database connections")
            logger.info("3. Restart the application")
            logger.info("4. Run security tests")
        else:
            logger.error("⚠️ Some security hardening steps failed")
            logger.error("Please review the errors above and fix manually")
        
        return successful == total

def main():
    """Main entry point"""
    hardening = SecurityHardening()
    success = hardening.run_security_hardening()
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
