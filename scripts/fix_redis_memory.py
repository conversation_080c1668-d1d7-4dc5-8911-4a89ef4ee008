#!/usr/bin/env python3
"""
Fix Redis Memory Overcommit Issue
Simple script to resolve the Redis memory warning
"""

import subprocess
import sys

def fix_redis_memory_overcommit():
    """
    Fix the Redis memory overcommit warning
    This resolves: "WARNING Memory overcommit must be enabled!"
    """
    
    print("🔧 Fixing Redis Memory Overcommit Issue...")
    print()
    
    # Check current setting
    try:
        result = subprocess.run(['sysctl', 'vm.overcommit_memory'], 
                              capture_output=True, text=True)
        current_setting = result.stdout.strip()
        print(f"📊 Current setting: {current_setting}")
        
        if 'vm.overcommit_memory = 1' in current_setting:
            print("✅ Memory overcommit is already properly configured!")
            return True
            
    except Exception as e:
        print(f"❌ Error checking current setting: {e}")
    
    print()
    print("🚨 Redis Memory Overcommit needs to be fixed!")
    print()
    print("This warning appears in Redis logs:")
    print("  'WARNING Memory overcommit must be enabled!'")
    print()
    print("To fix this, you need to run ONE of these commands:")
    print()
    print("🔧 TEMPORARY FIX (until reboot):")
    print("   sudo sysctl vm.overcommit_memory=1")
    print()
    print("🔧 PERMANENT FIX (survives reboot):")
    print("   echo 'vm.overcommit_memory = 1' | sudo tee -a /etc/sysctl.conf")
    print("   sudo sysctl -p")
    print()
    print("💡 What this does:")
    print("   - Allows Redis to allocate memory more efficiently")
    print("   - Prevents background save failures")
    print("   - Eliminates the Redis warning")
    print("   - Improves overall Redis performance")
    print()
    
    # Attempt automatic fix
    try:
        print("🤖 Attempting automatic fix...")
        
        # Try the temporary fix first
        result = subprocess.run(['sudo', 'sysctl', 'vm.overcommit_memory=1'], 
                               capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            print("✅ Successfully set vm.overcommit_memory=1 (temporary)")
            print()
            print("🔄 To make this permanent, run:")
            print("   echo 'vm.overcommit_memory = 1' | sudo tee -a /etc/sysctl.conf")
            return True
        else:
            print(f"❌ Automatic fix failed: {result.stderr}")
            
    except subprocess.TimeoutExpired:
        print("⏰ Command timed out (probably waiting for sudo password)")
    except Exception as e:
        print(f"❌ Automatic fix failed: {e}")
    
    print()
    print("📝 Manual fix required - please run the commands above manually.")
    return False

if __name__ == "__main__":
    print("🚀 Redis Memory Overcommit Fixer")
    print("=" * 40)
    fix_redis_memory_overcommit()
    print()
    print("🎉 Redis optimization guidance complete!")
    print()
    print("After fixing, restart your Redis container:")
    print("   docker restart legal-cms-redis")