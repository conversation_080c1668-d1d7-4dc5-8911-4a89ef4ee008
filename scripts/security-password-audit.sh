#!/bin/bash
# Security Password Audit Script
# Scans for empty passwords and weak password patterns in the codebase

set -e

echo "🔒 SECURITY PASSWORD AUDIT"
echo "=========================="
echo "Scanning for empty passwords and weak patterns..."
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Initialize counters
EMPTY_PASSWORDS=0
WEAK_PATTERNS=0
TOTAL_ISSUES=0

echo -e "${BLUE}🔍 Scanning for empty password assignments...${NC}"

# Search for empty password assignments
EMPTY_PASSWORD_FILES=$(grep -r "password\s*=\s*[\"']\s*[\"']" . --include="*.py" --include="*.js" --include="*.sh" --include="*.env*" --include="*.conf" 2>/dev/null | grep -v "__pycache__" | grep -v ".git" | grep -v "node_modules" | head -20)

if [ ! -z "$EMPTY_PASSWORD_FILES" ]; then
    echo -e "${RED}❌ CRITICAL: Empty password assignments found:${NC}"
    echo "$EMPTY_PASSWORD_FILES" | while read line; do
        echo -e "   ${RED}→ $line${NC}"
        EMPTY_PASSWORDS=$((EMPTY_PASSWORDS + 1))
    done
    echo ""
else
    echo -e "${GREEN}✅ No empty password assignments found${NC}"
fi

echo -e "${BLUE}🔍 Scanning for weak default passwords...${NC}"

# Common weak passwords to flag
WEAK_PASSWORDS=("password" "admin" "123456" "test" "demo" "default" "changeme" "qwerty")

for weak_pass in "${WEAK_PASSWORDS[@]}"; do
    WEAK_FOUND=$(grep -r "password.*=.*[\"']$weak_pass[\"']" . --include="*.py" --include="*.js" --include="*.sh" --include="*.env*" 2>/dev/null | grep -v "__pycache__" | grep -v ".git" | head -5)
    if [ ! -z "$WEAK_FOUND" ]; then
        echo -e "${YELLOW}⚠️  Warning: Weak password '$weak_pass' found:${NC}"
        echo "$WEAK_FOUND" | while read line; do
            echo -e "   ${YELLOW}→ $line${NC}"
            WEAK_PATTERNS=$((WEAK_PATTERNS + 1))
        done
    fi
done

echo ""
echo -e "${BLUE}🔍 Scanning for hardcoded credentials...${NC}"

# Search for suspicious hardcoded credentials
HARDCODED=$(grep -r -i "password.*=.*[\"'][^\"']*[\"']" . --include="*.py" --include="*.js" --include="*.sh" 2>/dev/null | grep -v "__pycache__" | grep -v ".git" | grep -v "placeholder" | grep -v "example" | grep -v "test" | head -10)

if [ ! -z "$HARDCODED" ]; then
    echo -e "${YELLOW}⚠️  Potential hardcoded credentials found (review needed):${NC}"
    echo "$HARDCODED" | while read line; do
        echo -e "   ${YELLOW}→ $line${NC}"
    done
else
    echo -e "${GREEN}✅ No obvious hardcoded credentials detected${NC}"
fi

echo ""
echo -e "${BLUE}🔍 Checking for secure password generation...${NC}"

# Check for proper password generation
SECURE_GENERATION=$(grep -r "openssl rand\|secrets\|random" . --include="*.py" --include="*.sh" 2>/dev/null | grep -v "__pycache__" | grep -v ".git" | head -5)

if [ ! -z "$SECURE_GENERATION" ]; then
    echo -e "${GREEN}✅ Secure password generation found:${NC}"
    echo "$SECURE_GENERATION" | while read line; do
        echo -e "   ${GREEN}→ $line${NC}"
    done
else
    echo -e "${YELLOW}⚠️  No secure password generation detected${NC}"
fi

echo ""
echo "📋 SECURITY RECOMMENDATIONS:"
echo "============================"
echo -e "${GREEN}✅ DO:${NC}"
echo "   • Use strong, unique passwords like: 00.01.10.11.aBrA~CADABRA4U"
echo "   • Generate passwords with: openssl rand -base64 32"
echo "   • Store credentials in environment variables"
echo "   • Use placeholder values instead of empty strings"
echo "   • Implement proper credential validation"
echo ""
echo -e "${RED}❌ DON'T:${NC}"
echo "   • Never use empty passwords: password = ''"
echo "   • Never use weak passwords: password = 'admin'"
echo "   • Never hardcode production passwords in source code"
echo "   • Never commit .env files with real credentials"
echo ""

if [ $EMPTY_PASSWORDS -gt 0 ] || [ $WEAK_PATTERNS -gt 0 ]; then
    echo -e "${RED}🚨 SECURITY ISSUES FOUND - IMMEDIATE ACTION REQUIRED${NC}"
    exit 1
else
    echo -e "${GREEN}🔒 Password security audit passed!${NC}"
    exit 0
fi