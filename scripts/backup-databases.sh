#!/bin/bash
# Database Backup Script for All-in-One Law Firm SaaS
set -e

BACKUP_DIR="/app/data/backups"
DATE=$(date +%Y%m%d_%H%M%S)

# Create backup directory
mkdir -p "$BACKUP_DIR"

echo "🔄 Starting database backups..."

# PostgreSQL Backup
echo "📊 Backing up PostgreSQL..."
pg_dump -U legal_cms_user -h localhost legal_cms_main > "$BACKUP_DIR/postgresql_$DATE.sql"

# MongoDB Backup
echo "📋 Backing up MongoDB..."
mongodump --host localhost:27017 --db legal_cms_documents --out "$BACKUP_DIR/mongodb_$DATE"

# Redis Backup
echo "💾 Backing up Redis..."
redis-cli --rdb "$BACKUP_DIR/redis_$DATE.rdb"

# Compress backups
echo "🗜️  Compressing backups..."
tar -czf "$BACKUP_DIR/law_firm_backup_$DATE.tar.gz" -C "$BACKUP_DIR" \
    "postgresql_$DATE.sql" \
    "mongodb_$DATE" \
    "redis_$DATE.rdb"

# Clean up individual files
rm -f "$BACKUP_DIR/postgresql_$DATE.sql"
rm -rf "$BACKUP_DIR/mongodb_$DATE"
rm -f "$BACKUP_DIR/redis_$DATE.rdb"

# Keep only last 7 days of backups
find "$BACKUP_DIR" -name "law_firm_backup_*.tar.gz" -mtime +7 -delete

echo "✅ Database backup completed: law_firm_backup_$DATE.tar.gz"