#!/usr/bin/env python3
"""
Setup script for daily monitoring baseline
Run this once to establish the initial docket state for monitoring
"""

import asyncio
import os
from core.daily_monitor import DailyDocketMonitor

async def setup_monitoring():
    """Set up initial monitoring baseline"""
    print("🔧 Setting up daily monitoring for DR-25-403973...")
    
    # Check if credentials are available
    username = os.getenv('EFILING_USERNAME')
    password = os.getenv('EFILING_PASSWORD')
    
    if not username or not password:
        print("⚠️ E-filing credentials not found in environment variables")
        print("📋 Daily monitoring will use the existing scraped data for baseline")
        return
    
    try:
        monitor = DailyDocketMonitor("DR-25-403973")
        
        # Establish baseline
        print("📊 Establishing baseline docket state...")
        await monitor.establish_baseline(force_update=True)
        
        print("✅ Daily monitoring setup complete!")
        print("🔄 Automated checks will run every day at 6:00 AM")
        print("🛡️ Your docket is now protected against tampering")
        
    except Exception as e:
        print(f"❌ Setup failed: {e}")
        print("💡 You can set up monitoring later through the Daily Monitoring tab")

if __name__ == "__main__":
    asyncio.run(setup_monitoring())