#!/usr/bin/env python3
"""
Code2Prompt - Convert codebases into LLM-friendly prompts
A tool to extract and format code from projects for AI model consumption
"""

import os
import argparse
import fnmatch
import mimetypes
from pathlib import Path
from typing import List, Set, Dict, Optional
import tiktoken
import json

class Code2Prompt:
    def __init__(self):
        # Common file extensions to include
        self.default_extensions = {
            '.py', '.js', '.jsx', '.ts', '.tsx', '.java', '.cpp', '.c', '.h', 
            '.cs', '.php', '.rb', '.go', '.rs', '.swift', '.kt', '.scala',
            '.html', '.css', '.scss', '.sass', '.less', '.vue', '.svelte',
            '.json', '.xml', '.yaml', '.yml', '.toml', '.ini', '.cfg',
            '.md', '.txt', '.rst', '.sql', '.sh', '.bash', '.zsh',
            '.dockerfile', '.makefile', '.cmake', '.gradle', '.maven'
        }
        
        # Common directories/files to ignore
        self.default_ignore_patterns = {
            'node_modules/*', '.git/*', '__pycache__/*', '*.pyc', '.env',
            'venv/*', 'env/*', '.venv/*', 'build/*', 'dist/*', 'target/*',
            '.next/*', '.nuxt/*', 'coverage/*', '.nyc_output/*', 'logs/*',
            '*.log', '.DS_Store', 'Thumbs.db', '*.tmp', '*.temp',
            '.idea/*', '.vscode/*', '*.swp', '*.swo', '*~'
        }
        
        # Initialize tokenizer for cost estimation
        try:
            self.tokenizer = tiktoken.get_encoding("cl100k_base")  # GPT-4 tokenizer
        except:
            self.tokenizer = None
    
    def should_include_file(self, file_path: Path, extensions: Set[str], 
                           ignore_patterns: Set[str]) -> bool:
        """Check if a file should be included based on extension and ignore patterns"""
        
        # Check ignore patterns
        for pattern in ignore_patterns:
            if fnmatch.fnmatch(str(file_path), pattern) or \
               fnmatch.fnmatch(file_path.name, pattern):
                return False
        
        # Check if it's a text file
        if not self.is_text_file(file_path):
            return False
        
        # Check extension
        if extensions:
            return file_path.suffix.lower() in extensions
        
        return True
    
    def is_text_file(self, file_path: Path) -> bool:
        """Check if a file is likely a text file"""
        try:
            # Check MIME type
            mime_type, _ = mimetypes.guess_type(str(file_path))
            if mime_type and mime_type.startswith('text'):
                return True
            
            # Check by reading a small sample
            with open(file_path, 'rb') as f:
                sample = f.read(1024)
                try:
                    sample.decode('utf-8')
                    return True
                except UnicodeDecodeError:
                    return False
        except:
            return False
    
    def extract_code_from_directory(self, directory: Path, extensions: Set[str] = None,
                                  ignore_patterns: Set[str] = None, 
                                  max_file_size: int = 1024*1024) -> Dict:
        """Extract code from a directory structure"""
        
        if extensions is None:
            extensions = self.default_extensions
        
        if ignore_patterns is None:
            ignore_patterns = self.default_ignore_patterns
        
        files_data = []
        total_size = 0
        skipped_files = []
        
        for root, dirs, files in os.walk(directory):
            root_path = Path(root)
            
            # Filter directories to avoid walking into ignored ones
            dirs[:] = [d for d in dirs if not any(
                fnmatch.fnmatch(d, pattern.rstrip('/*')) 
                for pattern in ignore_patterns if '/*' in pattern
            )]
            
            for file in files:
                file_path = root_path / file
                
                if not self.should_include_file(file_path, extensions, ignore_patterns):
                    continue
                
                try:
                    file_size = file_path.stat().st_size
                    if file_size > max_file_size:
                        skipped_files.append({
                            'path': str(file_path.relative_to(directory)),
                            'reason': f'File too large ({file_size} bytes)'
                        })
                        continue
                    
                    with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                        content = f.read()
                    
                    files_data.append({
                        'path': str(file_path.relative_to(directory)),
                        'content': content,
                        'size': file_size,
                        'lines': len(content.splitlines())
                    })
                    total_size += file_size
                    
                except Exception as e:
                    skipped_files.append({
                        'path': str(file_path.relative_to(directory)),
                        'reason': f'Error reading file: {e}'
                    })
        
        return {
            'files': files_data,
            'total_size': total_size,
            'total_files': len(files_data),
            'skipped_files': skipped_files
        }
    
    def generate_prompt(self, data: Dict, project_name: str = None, 
                       include_structure: bool = True,
                       prompt_template: str = None) -> str:
        """Generate a formatted prompt from extracted code data"""
        
        if prompt_template is None:
            prompt_template = self.get_default_template()
        
        # Generate project structure
        structure = ""
        if include_structure:
            structure = self.generate_tree_structure(data['files'])
        
        # Generate file contents
        file_contents = []
        for file_info in data['files']:
            content = f"## File: {file_info['path']}\n"
            content += f"```{self.get_language_from_extension(file_info['path'])}\n"
            content += file_info['content']
            content += "\n```\n"
            file_contents.append(content)
        
        # Fill template
        prompt = prompt_template.format(
            project_name=project_name or "Code Project",
            total_files=data['total_files'],
            total_size=self.format_size(data['total_size']),
            project_structure=structure,
            file_contents="\n".join(file_contents),
            stats=self.generate_stats(data)
        )
        
        return prompt
    
    def generate_tree_structure(self, files: List[Dict]) -> str:
        """Generate a tree-like structure of the project"""
        structure = {}
        
        for file_info in files:
            parts = file_info['path'].split(os.sep)
            current = structure
            
            for part in parts[:-1]:  # directories
                if part not in current:
                    current[part] = {}
                current = current[part]
            
            # Add file
            current[parts[-1]] = f"({file_info['lines']} lines)"
        
        return self.format_tree(structure)
    
    def format_tree(self, structure: Dict, prefix: str = "") -> str:
        """Format dictionary as tree structure"""
        result = []
        items = sorted(structure.items())
        
        for i, (name, content) in enumerate(items):
            is_last = i == len(items) - 1
            current_prefix = "└── " if is_last else "├── "
            
            if isinstance(content, dict):
                result.append(f"{prefix}{current_prefix}{name}/")
                next_prefix = prefix + ("    " if is_last else "│   ")
                result.append(self.format_tree(content, next_prefix))
            else:
                result.append(f"{prefix}{current_prefix}{name} {content}")
        
        return "\n".join(filter(None, result))
    
    def get_language_from_extension(self, file_path: str) -> str:
        """Get language identifier for syntax highlighting"""
        ext_to_lang = {
            '.py': 'python', '.js': 'javascript', '.jsx': 'jsx', 
            '.ts': 'typescript', '.tsx': 'tsx', '.java': 'java',
            '.cpp': 'cpp', '.c': 'c', '.h': 'c', '.cs': 'csharp',
            '.php': 'php', '.rb': 'ruby', '.go': 'go', '.rs': 'rust',
            '.swift': 'swift', '.kt': 'kotlin', '.scala': 'scala',
            '.html': 'html', '.css': 'css', '.scss': 'scss',
            '.json': 'json', '.xml': 'xml', '.yaml': 'yaml', '.yml': 'yaml',
            '.sql': 'sql', '.sh': 'bash', '.bash': 'bash',
            '.md': 'markdown', '.dockerfile': 'dockerfile'
        }
        
        ext = Path(file_path).suffix.lower()
        return ext_to_lang.get(ext, 'text')
    
    def generate_stats(self, data: Dict) -> str:
        """Generate statistics about the codebase"""
        stats = []
        stats.append(f"Total files: {data['total_files']}")
        stats.append(f"Total size: {self.format_size(data['total_size'])}")
        
        total_lines = sum(f['lines'] for f in data['files'])
        stats.append(f"Total lines: {total_lines:,}")
        
        # Language breakdown
        lang_stats = {}
        for file_info in data['files']:
            ext = Path(file_info['path']).suffix.lower()
            lang = self.get_language_from_extension(file_info['path'])
            if lang not in lang_stats:
                lang_stats[lang] = {'files': 0, 'lines': 0}
            lang_stats[lang]['files'] += 1
            lang_stats[lang]['lines'] += file_info['lines']
        
        stats.append("\nLanguage breakdown:")
        for lang, counts in sorted(lang_stats.items()):
            stats.append(f"  {lang}: {counts['files']} files, {counts['lines']:,} lines")
        
        if data['skipped_files']:
            stats.append(f"\nSkipped files: {len(data['skipped_files'])}")
        
        return "\n".join(stats)
    
    def format_size(self, size_bytes: int) -> str:
        """Format byte size in human readable format"""
        for unit in ['B', 'KB', 'MB', 'GB']:
            if size_bytes < 1024.0:
                return f"{size_bytes:.1f} {unit}"
            size_bytes /= 1024.0
        return f"{size_bytes:.1f} TB"
    
    def estimate_tokens(self, text: str) -> int:
        """Estimate token count for cost calculation"""
        if self.tokenizer:
            return len(self.tokenizer.encode(text))
        else:
            # Rough estimation: ~4 characters per token
            return len(text) // 4
    
    def estimate_costs(self, text: str) -> Dict[str, float]:
        """Estimate costs for different AI models"""
        tokens = self.estimate_tokens(text)
        
        # Pricing per 1M tokens (as of 2024) - input tokens
        pricing = {
            'gpt-4': 30.00,      # GPT-4 Turbo
            'gpt-3.5': 0.50,     # GPT-3.5 Turbo
            'claude-3-opus': 15.00,
            'claude-3-sonnet': 3.00,
            'claude-3-haiku': 0.25,
            'gemini-pro': 0.50,
        }
        
        costs = {}
        for model, price_per_million in pricing.items():
            cost = (tokens / 1_000_000) * price_per_million
            costs[model] = cost
        
        return {
            'tokens': tokens,
            'estimated_costs': costs
        }
    
    def get_default_template(self) -> str:
        """Get the default prompt template"""
        return """# {project_name}

I need you to analyze this codebase. Here's an overview:

{stats}

## Project Structure
```
{project_structure}
```

## Code Files

{file_contents}

Please analyze this code and help me understand its structure, functionality, and provide suggestions for improvements or answer any questions I have about it.
"""

def main():
    parser = argparse.ArgumentParser(description='Convert codebase to AI-friendly prompt')
    parser.add_argument('directory', help='Directory to process')
    parser.add_argument('-o', '--output', help='Output file (default: stdout)')
    parser.add_argument('-n', '--name', help='Project name')
    parser.add_argument('-e', '--extensions', 
                       help='File extensions to include (comma-separated)')
    parser.add_argument('--ignore', 
                       help='Additional ignore patterns (comma-separated)')
    parser.add_argument('--max-size', type=int, default=1024*1024,
                       help='Maximum file size in bytes (default: 1MB)')
    parser.add_argument('--no-structure', action='store_true',
                       help='Skip project structure in output')
    parser.add_argument('--estimate-cost', action='store_true',
                       help='Estimate AI model costs')
    parser.add_argument('--template', help='Custom template file')
    
    args = parser.parse_args()
    
    # Initialize processor
    processor = Code2Prompt()
    
    # Parse extensions
    extensions = None
    if args.extensions:
        extensions = {f'.{ext.strip().lstrip(".")}' 
                     for ext in args.extensions.split(',')}
    
    # Parse additional ignore patterns
    ignore_patterns = processor.default_ignore_patterns.copy()
    if args.ignore:
        ignore_patterns.update(args.ignore.split(','))
    
    # Extract code
    directory = Path(args.directory)
    if not directory.exists():
        print(f"Error: Directory '{directory}' does not exist")
        return 1
    
    data = processor.extract_code_from_directory(
        directory, extensions, ignore_patterns, args.max_size
    )
    
    # Load custom template if provided
    template = None
    if args.template:
        with open(args.template, 'r') as f:
            template = f.read()
    
    # Generate prompt
    prompt = processor.generate_prompt(
        data, 
        args.name or directory.name,
        not args.no_structure,
        template
    )
    
    # Output
    if args.output:
        with open(args.output, 'w') as f:
            f.write(prompt)
        print(f"Prompt written to {args.output}")
    else:
        print(prompt)
    
    # Cost estimation
    if args.estimate_cost:
        costs = processor.estimate_costs(prompt)
        print(f"\n{'='*50}")
        print("COST ESTIMATION")
        print(f"{'='*50}")
        print(f"Estimated tokens: {costs['tokens']:,}")
        print("\nEstimated costs (input only):")
        for model, cost in costs['estimated_costs'].items():
            print(f"  {model}: ${cost:.4f}")
        print("\nNote: These are rough estimates for input tokens only.")
        print("Actual costs may vary based on model updates and usage.")
    
    return 0

if __name__ == '__main__':
    exit(main())