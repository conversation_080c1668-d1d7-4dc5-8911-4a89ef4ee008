#!/bin/bash
echo "🚀 Starting ZERO-EXPOSURE-LAW-FIRM-SAAS"
echo "========================================="
echo "🔒 Security Level: MAXIMUM - Zero external ports exposed"
echo "🏠 Access Method: Container internal only"
echo "🌐 Web Interface: Available at http://localhost/ (inside container)"
echo ""
echo "🔍 Health Check: docker exec -it ALL-IN-ONE-LAW-FIRM-SAAS curl http://localhost/health"
echo "🔧 Debug Info:   docker exec -it ALL-IN-ONE-LAW-FIRM-SAAS curl http://localhost/debug"
echo "💻 Shell Access: docker exec -it ALL-IN-ONE-LAW-FIRM-SAAS bash"
echo ""
echo "⚡ Starting all services with supervisor..."

# Start all services with supervisor
exec /usr/bin/supervisord -c /etc/supervisor/conf.d/supervisord.conf