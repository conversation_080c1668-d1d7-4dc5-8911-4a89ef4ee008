#!/usr/bin/env python3
"""
Security Monitoring Script for All-in-One Law Firm SaaS
Monitors system security, failed logins, and suspicious activities
"""

import time
import json
import logging
import subprocess
import psutil
from datetime import datetime, timedelta
from pathlib import Path

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('/app/logs/security-monitor.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

class SecurityMonitor:
    def __init__(self):
        self.alerts_file = Path('/app/data/security-alerts.json')
        self.last_check = datetime.now() - timedelta(minutes=5)
        
    def check_failed_logins(self):
        """Monitor failed login attempts"""
        try:
            # Check auth logs for failed attempts
            result = subprocess.run(
                ['grep', 'Failed password', '/var/log/auth.log'],
                capture_output=True, text=True
            )
            
            if result.returncode == 0:
                failed_attempts = len(result.stdout.splitlines())
                if failed_attempts > 5:  # Threshold
                    self.create_alert(
                        'HIGH',
                        'Multiple Failed Login Attempts',
                        f'{failed_attempts} failed login attempts detected'
                    )
            
        except Exception as e:
            logger.error(f"Error checking failed logins: {e}")
    
    def check_system_resources(self):
        """Monitor system resource usage"""
        try:
            # CPU usage
            cpu_percent = psutil.cpu_percent(interval=1)
            if cpu_percent > 90:
                self.create_alert(
                    'MEDIUM',
                    'High CPU Usage',
                    f'CPU usage at {cpu_percent}%'
                )
            
            # Memory usage
            memory = psutil.virtual_memory()
            if memory.percent > 90:
                self.create_alert(
                    'MEDIUM',
                    'High Memory Usage',
                    f'Memory usage at {memory.percent}%'
                )
            
            # Disk usage
            disk = psutil.disk_usage('/')
            disk_percent = (disk.used / disk.total) * 100
            if disk_percent > 90:
                self.create_alert(
                    'HIGH',
                    'Low Disk Space',
                    f'Disk usage at {disk_percent:.1f}%'
                )
                
        except Exception as e:
            logger.error(f"Error checking system resources: {e}")
    
    def check_database_connections(self):
        """Monitor database connection attempts"""
        try:
            # Check PostgreSQL connections
            pg_connections = subprocess.run(
                ['netstat', '-tn', '|', 'grep', ':5432', '|', 'wc', '-l'],
                shell=True, capture_output=True, text=True
            )
            
            if pg_connections.returncode == 0:
                conn_count = int(pg_connections.stdout.strip())
                if conn_count > 100:  # Threshold
                    self.create_alert(
                        'MEDIUM',
                        'High Database Connections',
                        f'{conn_count} PostgreSQL connections active'
                    )
                    
        except Exception as e:
            logger.error(f"Error checking database connections: {e}")
    
    def check_ssl_certificates(self):
        """Monitor SSL certificate expiration"""
        try:
            cert_path = '/etc/letsencrypt/live/law-firm-saas/fullchain.pem'
            if Path(cert_path).exists():
                result = subprocess.run(
                    ['openssl', 'x509', '-in', cert_path, '-noout', '-enddate'],
                    capture_output=True, text=True
                )
                
                if result.returncode == 0:
                    # Parse expiration date and check if within 30 days
                    date_str = result.stdout.split('=')[1].strip()
                    # Additional certificate monitoring logic here
                    
        except Exception as e:
            logger.error(f"Error checking SSL certificates: {e}")
    
    def create_alert(self, severity, title, description):
        """Create security alert"""
        alert = {
            'timestamp': datetime.now().isoformat(),
            'severity': severity,
            'title': title,
            'description': description,
            'hostname': subprocess.run(['hostname'], capture_output=True, text=True).stdout.strip()
        }
        
        # Log the alert
        logger.warning(f"SECURITY ALERT [{severity}] {title}: {description}")
        
        # Save to alerts file
        alerts = []
        if self.alerts_file.exists():
            with open(self.alerts_file, 'r') as f:
                alerts = json.load(f)
        
        alerts.append(alert)
        
        # Keep only last 1000 alerts
        alerts = alerts[-1000:]
        
        with open(self.alerts_file, 'w') as f:
            json.dump(alerts, f, indent=2)
    
    def run_monitoring_cycle(self):
        """Run a complete monitoring cycle"""
        logger.info("Starting security monitoring cycle")
        
        self.check_failed_logins()
        self.check_system_resources()
        self.check_database_connections()
        self.check_ssl_certificates()
        
        logger.info("Security monitoring cycle completed")

def main():
    """Main monitoring loop"""
    monitor = SecurityMonitor()
    
    logger.info("Security Monitor started for All-in-One Law Firm SaaS")
    
    while True:
        try:
            monitor.run_monitoring_cycle()
            time.sleep(300)  # Check every 5 minutes
            
        except KeyboardInterrupt:
            logger.info("Security Monitor stopped by user")
            break
        except Exception as e:
            logger.error(f"Error in monitoring loop: {e}")
            time.sleep(60)  # Wait 1 minute before retrying

if __name__ == "__main__":
    main()