#!/bin/bash
# SSL Certificate Initialization for All-in-One Law Firm SaaS
set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${GREEN}🔒 Initializing SSL Certificates for Law Firm SaaS${NC}"
echo "=================================================="

# Default domain (can be overridden via environment variable)
DOMAIN=${DOMAIN:-law-firm-saas.local}
EMAIL=${SSL_EMAIL:-<EMAIL>}

echo -e "${YELLOW}Domain: $DOMAIN${NC}"
echo -e "${YELLOW}Email: $EMAIL${NC}"

# Create necessary directories
mkdir -p /etc/letsencrypt/live/law-firm-saas
mkdir -p /etc/ssl/certs
mkdir -p /var/www/certbot

# Generate DH parameters for enhanced security
if [ ! -f /etc/ssl/certs/dhparam.pem ]; then
    echo -e "${YELLOW}⏳ Generating DH parameters (this may take a while)...${NC}"
    openssl dhparam -out /etc/ssl/certs/dhparam.pem 2048
    echo -e "${GREEN}✅ DH parameters generated${NC}"
fi

# Check if we're in development mode (no real domain)
if [[ "$DOMAIN" == *".local" ]] || [[ "$DOMAIN" == "localhost" ]] || [[ "$DEVELOPMENT" == "true" ]]; then
    echo -e "${YELLOW}🛠️  Development mode detected - generating self-signed certificates${NC}"
    
    # Generate self-signed certificate for development
    openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
        -keyout /etc/letsencrypt/live/law-firm-saas/privkey.pem \
        -out /etc/letsencrypt/live/law-firm-saas/fullchain.pem \
        -subj "/C=US/ST=Development/L=Development/O=Law Firm SaaS/OU=Development/CN=$DOMAIN"
    
    # Create chain file (same as fullchain for self-signed)
    cp /etc/letsencrypt/live/law-firm-saas/fullchain.pem /etc/letsencrypt/live/law-firm-saas/chain.pem
    
    echo -e "${GREEN}✅ Self-signed certificates generated for development${NC}"
    
else
    echo -e "${YELLOW}🌐 Production mode - obtaining Let's Encrypt certificates${NC}"
    
    # First, start nginx with a basic HTTP configuration for ACME challenge
    cat > /etc/nginx/sites-available/temp-ssl << 'EOF'
server {
    listen 80;
    server_name _;
    
    location /.well-known/acme-challenge/ {
        root /var/www/certbot;
        try_files $uri =404;
    }
    
    location / {
        return 200 'SSL Setup in Progress';
        add_header Content-Type text/plain;
    }
}
EOF
    
    # Enable the temporary configuration
    ln -sf /etc/nginx/sites-available/temp-ssl /etc/nginx/sites-enabled/default
    
    # Test nginx configuration
    nginx -t
    
    # Start nginx for ACME challenge
    service nginx start
    
    # Wait for nginx to be ready
    sleep 5
    
    # Obtain SSL certificate from Let's Encrypt
    echo -e "${YELLOW}⏳ Requesting SSL certificate from Let's Encrypt...${NC}"
    
    certbot certonly \
        --webroot \
        --webroot-path=/var/www/certbot \
        --email $EMAIL \
        --agree-tos \
        --no-eff-email \
        --domains $DOMAIN \
        --cert-name law-firm-saas \
        --non-interactive \
        --expand
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ SSL certificate obtained successfully${NC}"
    else
        echo -e "${RED}❌ Failed to obtain SSL certificate${NC}"
        echo -e "${YELLOW}🛠️  Falling back to self-signed certificate...${NC}"
        
        # Fallback to self-signed
        mkdir -p /etc/letsencrypt/live/law-firm-saas
        openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
            -keyout /etc/letsencrypt/live/law-firm-saas/privkey.pem \
            -out /etc/letsencrypt/live/law-firm-saas/fullchain.pem \
            -subj "/C=US/ST=Fallback/L=Fallback/O=Law Firm SaaS/OU=Fallback/CN=$DOMAIN"
        
        cp /etc/letsencrypt/live/law-firm-saas/fullchain.pem /etc/letsencrypt/live/law-firm-saas/chain.pem
    fi
    
    # Stop nginx (will be restarted by supervisor with full config)
    service nginx stop
fi

# Set proper permissions
chown -R root:root /etc/letsencrypt
chmod -R 755 /etc/letsencrypt
chmod 600 /etc/letsencrypt/live/law-firm-saas/privkey.pem
chmod 644 /etc/letsencrypt/live/law-firm-saas/fullchain.pem
chmod 644 /etc/letsencrypt/live/law-firm-saas/chain.pem

# Install the production nginx configuration
cp /app/config/nginx-all-in-one.conf /etc/nginx/sites-available/default

# Test the configuration
nginx -t

echo -e "${GREEN}🎉 SSL certificate setup completed successfully!${NC}"
echo -e "${GREEN}📋 Certificate details:${NC}"
echo -e "   Domain: $DOMAIN"
echo -e "   Certificate: /etc/letsencrypt/live/law-firm-saas/fullchain.pem"
echo -e "   Private Key: /etc/letsencrypt/live/law-firm-saas/privkey.pem"
echo ""

# Set up automatic renewal (for production certificates)
if [[ "$DOMAIN" != *".local" ]] && [[ "$DOMAIN" != "localhost" ]] && [[ "$DEVELOPMENT" != "true" ]]; then
    echo -e "${YELLOW}⏰ Setting up automatic certificate renewal...${NC}"
    
    # Create renewal script
    cat > /etc/cron.daily/certbot-renewal << 'EOF'
#!/bin/bash
# Auto-renewal script for Let's Encrypt certificates
/usr/bin/certbot renew --quiet --deploy-hook "supervisorctl restart nginx"
EOF
    
    chmod +x /etc/cron.daily/certbot-renewal
    echo -e "${GREEN}✅ Automatic renewal configured${NC}"
fi

echo -e "${GREEN}🚀 Ready to start All-in-One Law Firm SaaS with SSL!${NC}"