#!/bin/bash
# Secure Database Ports Script - Bind databases to localhost only for security
# This ensures remote users cannot access databases directly, only through the web app

set -e

echo "🔒 Securing database ports - restricting to localhost access only..."
echo "=========================================================="

# Stop all database containers
echo "⏸️  Stopping database containers..."
docker stop legal-cms-postgresql legal-cms-mongodb legal-cms-redis legal-cms-qdrant 2>/dev/null || true

# Wait for containers to stop
sleep 5

# Remove containers to recreate with secure port bindings
echo "🗑️  Removing containers to recreate with secure bindings..."
docker rm legal-cms-postgresql legal-cms-mongodb legal-cms-redis legal-cms-qdrant 2>/dev/null || true

# Restart PostgreSQL with localhost-only binding
echo "🗄️  Starting PostgreSQL (localhost:5435 only)..."
docker run -d \
  --name legal-cms-postgresql \
  --network config_legal-cms-network \
  -p 127.0.0.1:5435:5432 \
  -e POSTGRES_DB=legal_cms_main \
  -e POSTGRES_USER=legal_cms_user \
  -e POSTGRES_PASSWORD=cWO7LRL29U8tNfDgjG14RJSCA \
  -v legal-cms-postgres-data:/var/lib/postgresql/data \
  --restart unless-stopped \
  --health-cmd="pg_isready -U legal_cms_user -d legal_cms_main" \
  --health-interval=10s \
  --health-timeout=5s \
  --health-retries=5 \
  postgres:15-alpine

# Restart MongoDB with localhost-only binding  
echo "📋 Starting MongoDB (localhost:27018 only)..."
docker run -d \
  --name legal-cms-mongodb \
  --network config_legal-cms-network \
  -p 127.0.0.1:27018:27017 \
  -e MONGO_INITDB_ROOT_USERNAME=admin \
  -e MONGO_INITDB_ROOT_PASSWORD=AxWuQaxIGX8g \
  -e MONGO_INITDB_DATABASE=legal_cms_documents \
  -v legal-cms-mongo-data:/data/db \
  --restart unless-stopped \
  --health-cmd="mongosh --eval 'db.runCommand(\"ping\")'" \
  --health-interval=10s \
  --health-timeout=5s \
  --health-retries=5 \
  mongo:7.0

# Restart Redis with localhost-only binding
echo "💾 Starting Redis (localhost:6382 only)..."
docker run -d \
  --name legal-cms-redis \
  --network config_legal-cms-network \
  -p 127.0.0.1:6382:6379 \
  -v legal-cms-redis-data:/data \
  --restart unless-stopped \
  --health-cmd="redis-cli ping" \
  --health-interval=10s \
  --health-timeout=5s \
  --health-retries=5 \
  redis:7-alpine

# Restart Qdrant with localhost-only binding
echo "🔍 Starting Qdrant Vector DB (localhost:6334 only)..."
docker run -d \
  --name legal-cms-qdrant \
  --network config_legal-cms-network \
  -p 127.0.0.1:6334:6333 \
  -v legal-cms-qdrant-data:/qdrant/storage \
  --restart unless-stopped \
  qdrant/qdrant:latest

# Wait for services to be ready
echo "⏳ Waiting for services to be ready..."
sleep 30

# Test localhost access
echo "🧪 Testing localhost access..."
echo -n "PostgreSQL: "
if nc -z 127.0.0.1 5435; then
    echo "✅ Available on localhost:5435"
else
    echo "❌ Not accessible"
fi

echo -n "MongoDB: "
if nc -z 127.0.0.1 27018; then
    echo "✅ Available on localhost:27018"
else
    echo "❌ Not accessible"
fi

echo -n "Redis: "
if nc -z 127.0.0.1 6382; then
    echo "✅ Available on localhost:6382"
else
    echo "❌ Not accessible"
fi

echo -n "Qdrant: "
if nc -z 127.0.0.1 6334; then
    echo "✅ Available on localhost:6334"
else
    echo "❌ Not accessible"
fi

echo ""
echo "🔐 SECURITY STATUS:"
echo "  ✅ Database ports now bound to localhost only"
echo "  ✅ Remote users can only access the web application"
echo "  ✅ Local debugging access preserved for developers"
echo ""
echo "🌐 Access Summary:"
echo "  - Web App (All Users):    https://localhost/"
echo "  - Debug/Admin (Local):    https://localhost/admin, /monitoring, /health"
echo "  - Databases (Local):      localhost:5435, localhost:27018, localhost:6382, localhost:6334"
echo ""
echo "✅ Database security configuration complete!"