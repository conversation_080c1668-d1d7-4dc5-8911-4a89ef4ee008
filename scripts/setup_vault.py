#!/usr/bin/env python3
"""
HashiCorp Vault Setup Script
Completes the promised Vault integration for secure secret management
"""

import subprocess
import os
import json
import time
from pathlib import Path

def setup_hashicorp_vault():
    """Setup HashiCorp Vault container and basic configuration"""
    
    print("🔒 Setting up HashiCorp Vault for Secret Management")
    print("=" * 50)
    
    # Check if Vault is already running
    try:
        result = subprocess.run(['docker', 'ps', '--filter', 'name=legal-cms-vault', '--format', 'table {{.Names}}'], 
                              capture_output=True, text=True)
        if 'legal-cms-vault' in result.stdout:
            print("✅ Vault container already running")
            return
    except:
        pass
    
    print("🚀 Starting HashiCorp Vault container...")
    
    # Create Vault data directory
    vault_data_dir = Path("vault_data")
    vault_data_dir.mkdir(exist_ok=True)
    
    # Run Vault container
    vault_command = [
        'docker', 'run', '-d',
        '--name', 'legal-cms-vault',
        '--cap-add=IPC_LOCK',
        '-p', '8200:8200',
        '-v', f'{vault_data_dir.absolute()}:/vault/data',
        '-e', 'VAULT_DEV_ROOT_TOKEN_ID=legal-cms-root-token',
        '-e', 'VAULT_DEV_LISTEN_ADDRESS=0.0.0.0:8200',
        'hashicorp/vault:latest'
    ]
    
    try:
        result = subprocess.run(vault_command, capture_output=True, text=True, check=True)
        print("✅ Vault container started successfully")
        
        # Wait for Vault to be ready
        print("⏳ Waiting for Vault to initialize...")
        time.sleep(5)
        
        # Set up environment variables
        vault_env = {
            'VAULT_ADDR': 'http://localhost:8200',
            'VAULT_TOKEN': 'legal-cms-root-token'
        }
        
        # Update .env file
        env_file = Path('.env')
        env_content = ""
        
        if env_file.exists():
            with open(env_file, 'r') as f:
                env_content = f.read()
        
        # Add Vault configuration
        vault_config = f"""
# HashiCorp Vault Configuration
VAULT_ADDR=http://localhost:8200
VAULT_TOKEN=legal-cms-root-token
"""
        
        if 'VAULT_ADDR' not in env_content:
            with open(env_file, 'a') as f:
                f.write(vault_config)
            print("✅ Added Vault configuration to .env file")
        
        # Create initial secret structure
        setup_vault_secrets(vault_env)
        
        print("🎉 HashiCorp Vault setup complete!")
        print()
        print("🔗 Vault UI: http://localhost:8200")
        print("🔑 Root Token: legal-cms-root-token")
        print()
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to start Vault container: {e}")
        print("Error output:", e.stderr)
    except Exception as e:
        print(f"❌ Setup error: {e}")

def setup_vault_secrets(vault_env):
    """Setup initial secret structure in Vault"""
    
    print("🔐 Setting up initial secret structure...")
    
    try:
        # Enable KV secrets engine
        kv_command = [
            'docker', 'exec', 'legal-cms-vault',
            'vault', 'secrets', 'enable', '-path=legal-cms', 'kv-v2'
        ]
        
        env_vars = {**os.environ, **vault_env}
        
        result = subprocess.run(kv_command, env=env_vars, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ KV secrets engine enabled")
        else:
            print(f"⚠️ KV engine setup: {result.stderr}")
        
        # Create initial secrets
        secrets = {
            'database/postgresql': {
                'username': 'legal_cms_user',
                'password': 'cWO7LRL29U8tNfDgjG14RJSCA',
                'url': 'postgresql://legal_cms_user:cWO7LRL29U8tNfDgjG14RJSCA@localhost:5435/legal_cms_main'
            },
            'database/mongodb': {
                'username': 'admin',
                'password': 'VDFyrKwyAkith2PXF3qHJgO6e',
                'url': '**********************************************************************************************'
            },
            'database/redis': {
                'password': 'QFVchUWhPsZDLtnM0NmrcWvdW',
                'url': 'redis://:QFVchUWhPsZDLtnM0NmrcWvdW@localhost:6382/0'
            },
            'efiling/credentials': {
                'username': 'GSHEPOV',
                'password': 'VhodaNet4u'
            },
            'encryption/keys': {
                'encryption_key': 'vAkyJ5lXrisnw6ou_aBsQt71NSVR0QaEqxVVuoJMYQw=',
                'jwt_secret': 'Gbbz9IQSxeDiFAVterlfpDnXVzfBfoGHAfZ4GhlMyX0MKJQHMRDhZPaXYxhX5kM'
            }
        }
        
        for secret_path, secret_data in secrets.items():
            put_secret_command = [
                'docker', 'exec', 'legal-cms-vault',
                'vault', 'kv', 'put', f'legal-cms/{secret_path}'
            ]
            
            # Add key-value pairs
            for key, value in secret_data.items():
                put_secret_command.extend([f'{key}={value}'])
            
            result = subprocess.run(put_secret_command, env=env_vars, capture_output=True, text=True)
            
            if result.returncode == 0:
                print(f"✅ Created secret: {secret_path}")
            else:
                print(f"⚠️ Failed to create secret {secret_path}: {result.stderr}")
        
        print("🔐 Initial secrets structure created")
        
    except Exception as e:
        print(f"❌ Secret setup error: {e}")

def show_vault_status():
    """Show current Vault status"""
    
    print("🔍 HashiCorp Vault Status")
    print("-" * 30)
    
    try:
        # Check if container is running
        result = subprocess.run(['docker', 'ps', '--filter', 'name=legal-cms-vault'], 
                              capture_output=True, text=True)
        
        if 'legal-cms-vault' in result.stdout:
            print("✅ Vault container: Running")
            
            # Check Vault health
            health_command = [
                'docker', 'exec', 'legal-cms-vault',
                'vault', 'status'
            ]
            
            health_result = subprocess.run(health_command, 
                                         env={**os.environ, 'VAULT_ADDR': 'http://localhost:8200'}, 
                                         capture_output=True, text=True)
            
            print("📊 Vault Status:")
            print(health_result.stdout)
            
        else:
            print("❌ Vault container: Not running")
            print("💡 Run 'python setup_vault.py' to start Vault")
            
    except Exception as e:
        print(f"❌ Status check error: {e}")

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == 'status':
        show_vault_status()
    else:
        setup_hashicorp_vault()