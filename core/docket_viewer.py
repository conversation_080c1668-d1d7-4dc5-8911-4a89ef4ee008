"""
Docket Viewer Component - Displays docket in court-style table format
Matches the exact appearance of Cuyahoga County E-Filing system
"""

import logging
import streamlit as st
import pandas as pd
from typing import List, Dict, Optional

# Configure module-level logger
logger = logging.getLogger(__name__)

def render_court_style_docket(docket_data: Dict, case_number: str = "DR-25-403973") -> None:
    """
    Render docket in the exact style of the court system table
    
    Args:
        docket_data: Docket data from scraper
        case_number: Case number for display
    """
    
    st.subheader(f"📋 **Case Docket: {case_number}**")
    
    if not docket_data or 'entries' not in docket_data:
        st.warning("No docket data available")
        return
        
    entries = docket_data['entries']
    if not entries:
        st.warning("No docket entries found")
        return
    
    # Display summary stats
    col1, col2, col3, col4 = st.columns(4)
    with col1:
        st.metric("Total Entries", len(entries))
    with col2:
        entries_with_images = sum(1 for e in entries if e.get('Has_Image'))
        st.metric("Documents with Images", entries_with_images)
    with col3:
        stricken_entries = sum(1 for e in entries if e.get('Stricken_Status'))
        st.metric("⚠️ Stricken Records", stricken_entries, delta_color="inverse")
    with col4:
        latest_entry = entries[0] if entries else None
        latest_date = latest_entry.get('Filing Date', 'Unknown') if latest_entry else 'Unknown'
        st.metric("Latest Filing", latest_date)
    
    # Show alert for stricken records
    stricken_records = [e for e in entries if e.get('Stricken_Status')]
    if stricken_records:
        st.error(f"⚠️ **ALERT: {len(stricken_records)} records have been STRICKEN from this case!**")
        st.write("**These records contained evidence that was removed from the case docket.** Click below to investigate:")
        
        if st.expander("🔍 **INVESTIGATE STRICKEN RECORDS** (Click to expand)", expanded=False):
            st.warning("**CRITICAL: Evidence has been removed from your case!**")
            for record in stricken_records:
                with st.container():
                    st.markdown(f"**🚨 STRICKEN RECORD - {record.get('Filing Date', 'Unknown Date')}**")
                    st.write(f"**Description:** {record.get('Description', 'N/A')}")
                    if record.get('Original_Docket_ID'):
                        st.write(f"**Original Docket ID:** {record['Original_Docket_ID']}")
                    st.write(f"**Issue Type:** {record.get('Issue_Type', 'Evidence Manipulation')}")
                    st.divider()
            
            st.info("💡 **Recommendation:** Contact your attorney immediately. Stricken records may indicate procedural violations or evidence tampering.")
    
    st.divider()
    
    # Render court-style table
    render_docket_table_html(entries)
    
    # Show downloadable data
    if st.checkbox("📥 **Show Downloadable Data**"):
        df = pd.DataFrame(entries)
        st.dataframe(df, use_container_width=True)
        
        # CSV download
        csv = df.to_csv(index=False)
        st.download_button(
            label="📄 Download Docket as CSV",
            data=csv,
            file_name=f"docket_{case_number.replace('-', '_')}.csv",
            mime="text/csv"
        )

def render_docket_table_html(entries: List[Dict]) -> None:
    """
    Render HTML table that matches the court system exactly
    """
    
    # Create HTML table with exact court styling
    html_table = """
    <style>
    .court-docket-table {
        border-collapse: collapse;
        width: 100%;
        margin: 20px 0;
        font-family: Arial, sans-serif;
        font-size: 11px;
    }
    
    .court-docket-table th {
        background-color: #f0f0f0;
        border: 1px solid #ccc;
        padding: 8px;
        text-align: left;
        font-weight: bold;
        font-size: 12px;
    }
    
    .court-docket-table td {
        border: 1px solid #ccc;
        padding: 8px;
        text-align: left;
        vertical-align: top;
        line-height: 1.4;
    }
    
    .court-docket-table .row-lightgrey {
        background-color: #D3D3D3;
    }
    
    .court-docket-table .row-gainsboro {
        background-color: #DCDCDC;
    }
    
    .court-docket-table .date-col {
        width: 75px;
        white-space: nowrap;
    }
    
    .court-docket-table .side-col {
        width: 40px;
        text-align: center;
    }
    
    .court-docket-table .type-col {
        width: 40px;
        text-align: center;
    }
    
    .court-docket-table .desc-col {
        max-width: 400px;
        word-wrap: break-word;
    }
    
    .court-docket-table .image-col {
        width: 60px;
        text-align: center;
    }
    
    .image-button {
        background: none;
        border: none;
        cursor: pointer;
    }
    
    .image-button img {
        width: 16px;
        height: 16px;
    }
    
    .has-image {
        color: #0066cc;
        font-weight: bold;
    }
    
    .priority-1 {
        background-color: #fffacd !important;
        border-left: 4px solid #ffd700;
    }
    
    .stricken-record {
        background-color: #ffebee !important;
        border-left: 4px solid #f44336 !important;
        border-right: 4px solid #f44336 !important;
    }
    
    .stricken-record td {
        color: #b71c1c !important;
        font-weight: bold;
    }
    
    .evidence-alert {
        background-color: #fff3e0 !important;
        border: 2px solid #ff9800 !important;
    }
    </style>
    
    <table class="court-docket-table">
        <thead>
            <tr>
                <th class="date-col">Filing Date</th>
                <th class="side-col">Side</th>
                <th class="type-col">Type</th>
                <th class="desc-col">Description</th>
                <th class="image-col">Image</th>
            </tr>
        </thead>
        <tbody>
    """
    
    # Add rows alternating colors like court system
    for i, entry in enumerate(entries):
        # Determine row style
        row_class = "row-lightgrey" if i % 2 == 0 else "row-gainsboro"
        
        # Special highlighting for important records
        if entry.get('Priority') == 1:
            row_class += " priority-1"
        elif entry.get('Stricken_Status'):
            row_class = "stricken-record"  # Override normal coloring for stricken records
        
        # Extract data with proper formatting
        filing_date = entry.get('Filing Date', 'Unknown')
        side = entry.get('Side', 'N/A')  
        doc_type = entry.get('Type', 'N/A')
        description = entry.get('Description', entry.get('Docket Description', 'No description'))
        has_image = entry.get('Has_Image', False)
        
        # Format description - handle line breaks
        formatted_desc = str(description).replace('<br>', '<br/>').replace('\n', '<br/>')
        
        # Truncate very long descriptions
        if len(formatted_desc) > 500:
            formatted_desc = formatted_desc[:500] + "..."
        
        # Image column content
        image_cell = ""
        if has_image:
            image_cell = '<span class="has-image">📄 VIEW</span>'
        
        # Add the row
        html_table += f"""
            <tr class="{row_class}">
                <td class="date-col">{filing_date}</td>
                <td class="side-col">{side}</td>
                <td class="type-col">{doc_type}</td>
                <td class="desc-col">{formatted_desc}</td>
                <td class="image-col">{image_cell}</td>
            </tr>
        """
    
    html_table += """
        </tbody>
    </table>
    """
    
    # Display the HTML table
    st.markdown(html_table, unsafe_allow_html=True)
    
    # Add legend
    st.markdown("""
    **Legend:**
    - 🏅 **Gold highlight**: Complaint for Divorce (Document #1)
    - 🚨 **Red highlight**: STRICKEN RECORDS - Evidence removed from case
    - 📄 **VIEW**: Document has images/PDF available
    - **Side codes**: P1 = Plaintiff, D1 = Defendant, N/A = Court/System
    - **Type codes**: CP = Complaint, JE = Judgment Entry, MO = Motion, etc.
    
    ⚠️ **IMPORTANT**: Stricken records indicate evidence that was removed from your case. These require immediate legal review.
    """)

def get_document_type_description(type_code: str) -> str:
    """
    Convert court type codes to full descriptions
    """
    type_mappings = {
        'CP': 'Complaint',
        'JE': 'Judgment Entry', 
        'MO': 'Motion',
        'AN': 'Answer',
        'OB': 'Objection',
        'NT': 'Notice',
        'SR': 'Service Record',
        'SC': 'Scheduling',
        'AF': 'Affidavit',
        'CL': 'Clerical Entry',
        'SF': 'System Filing',
        'AP': 'Application',
        'EV': 'Evidence/Waiver'
    }
    
    return type_mappings.get(type_code.upper(), type_code)

def get_side_description(side_code: str) -> str:
    """
    Convert side codes to full descriptions
    """
    side_mappings = {
        'P1': 'Plaintiff',
        'D1': 'Defendant', 
        'N/A': 'Court/System',
        'CT': 'Court',
        'SYS': 'System'
    }
    
    return side_mappings.get(side_code.upper(), side_code)