"""
Enhanced E-Service Queue Scraper for Cuyahoga County E-Filing System
Handles the complete workflow: uncheck filter, search case, scrape all pages, download images
"""

import asyncio
import os
import time
import logging
import re
import aiohttp
import aiofiles
from datetime import datetime
from typing import List, Dict, Optional, Tuple
from urllib.parse import urljoin, urlparse, parse_qs
from pathlib import Path
import json

try:
    from playwright.async_api import async_playwright, <PERSON><PERSON><PERSON>, Page
    PLAYWRIGHT_AVAILABLE = True
except ImportError:
    PLAYWRIGHT_AVAILABLE = False
    logging.warning("Playwright not available. Install with: pip install playwright")

class EServiceQueueScraper:
    """
    Enhanced scraper for E-Service Queue with complete workflow automation
    
    Workflow:
    1. Login to e-filing system
    2. Navigate to E-Service Queue
    3. Uncheck "Unread Notices Only" 
    4. Enter case number (DR 25 403973)
    5. Click Search
    6. Scrape all 4 pages of 15 records each (60 total records)
    7. Download all associated images/documents
    """
    
    def __init__(self, username: str, password: str, headless: bool = False, download_dir: str = None):
        self.username = username
        self.password = password
        self.headless = headless
        self.base_url = "https://efiling.cp.cuyahogacounty.gov"
        self.login_url = f"{self.base_url}/Login.aspx"
        self.eservice_url = f"{self.base_url}/EService_Queue.aspx"
        
        # Setup download directory
        if download_dir:
            self.download_dir = Path(download_dir)
        else:
            self.download_dir = Path("eservice_downloads") / datetime.now().strftime("%Y%m%d_%H%M%S")
        
        self.download_dir.mkdir(parents=True, exist_ok=True)
        
        self.browser = None
        self.page = None
        self.logged_in = False
        self.session_cookies = None
        
        # Setup logging
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
    
    async def start_browser(self):
        """Start Playwright browser"""
        if not PLAYWRIGHT_AVAILABLE:
            raise RuntimeError("Playwright not available")
        
        self.playwright = await async_playwright().start()
        self.browser = await self.playwright.chromium.launch(
            headless=self.headless,
            args=['--no-sandbox', '--disable-dev-shm-usage']
        )
        self.page = await self.browser.new_page()
        
        # Set reasonable timeouts
        self.page.set_default_timeout(30000)  # 30 seconds
        
    async def login(self) -> bool:
        """Login to the e-filing system"""
        try:
            await self.page.goto(self.login_url, wait_until='networkidle')
            
            # Fill in login credentials
            await self.page.fill('input[name="ctl00$ContentPlaceHolder1$txtUserName"]', self.username)
            await self.page.fill('input[name="ctl00$ContentPlaceHolder1$txtPassword"]', self.password)
            
            # Click login button
            await self.page.click('input[name="ctl00$ContentPlaceHolder1$btnLogin"]')
            
            # Wait for redirect to home page
            await self.page.wait_for_url('**/Home.aspx', timeout=15000)
            
            self.logged_in = True
            
            # Get session cookies for downloads
            self.session_cookies = await self.page.context.cookies()
            self.logger.info("Successfully logged in and captured session cookies")
            return True
            
        except Exception as e:
            self.logger.error(f"Login failed: {e}")
            return False
    
    async def scrape_eservice_case(self, case_number: str = "DR 25 403973") -> Dict:
        """
        Complete E-Service scraping workflow for a specific case
        
        Args:
            case_number: Case number to search for (default: "DR 25 403973")
            
        Returns:
            Complete dictionary with all records and downloaded files
        """
        if not self.logged_in:
            await self.login()
        
        try:
            # Navigate to E-Service Queue
            self.logger.info("Navigating to E-Service Queue...")
            await self.page.goto(self.eservice_url, wait_until='networkidle')
            await asyncio.sleep(3)  # Allow page to fully load
            
            # Initialize results structure
            results = {
                'case_number': case_number,
                'scraped_at': datetime.now().isoformat(),
                'base_url': self.eservice_url,
                'total_pages': 0,
                'total_records': 0,
                'pages': [],
                'all_records': [],
                'downloads': None,
                'download_directory': str(self.download_dir)
            }
            
            # Step 1: Uncheck "Unread Notices Only"
            await self.uncheck_unread_filter()
            
            # Step 2: Enter case number and search
            await self.search_case(case_number)
            
            # Step 3: Detect total pages
            total_pages = await self.detect_total_pages()
            results['total_pages'] = total_pages
            
            self.logger.info(f"Found {total_pages} pages of E-Service records")
            
            # Step 4: Scrape all pages
            for page_num in range(1, total_pages + 1):
                self.logger.info(f"Scraping page {page_num} of {total_pages}")
                
                page_data = await self.scrape_single_eservice_page(page_num)
                results['pages'].append(page_data)
                results['all_records'].extend(page_data['records'])
                
                # Navigate to next page if not the last page
                if page_num < total_pages:
                    await self.navigate_to_next_page()
                
                # Small delay between pages
                await asyncio.sleep(2)
            
            results['total_records'] = len(results['all_records'])
            
            self.logger.info(f"Scraped {results['total_records']} total records from {total_pages} pages")
            
            # Step 5: Download all associated documents/images
            if results['all_records']:
                self.logger.info("Starting download of all documents/images...")
                download_results = await self.download_all_documents(results['all_records'], case_number)
                results['downloads'] = download_results
            
            return results
            
        except Exception as e:
            self.logger.error(f"Failed to scrape E-Service records: {e}")
            return {
                'error': str(e),
                'case_number': case_number,
                'scraped_at': datetime.now().isoformat()
            }
    
    async def uncheck_unread_filter(self):
        """Uncheck the 'Unread Notices Only' checkbox"""
        try:
            # Look for the checkbox with various possible selectors
            checkbox_selectors = [
                'input[type="checkbox"]:has-text("Unread")',
                'input[type="checkbox"][id*="Unread"]',
                'input[type="checkbox"][name*="Unread"]',
                'input[type="checkbox"]:near(:text("Unread Notices Only"))',
                'input[type="checkbox"]'  # Fallback - check all checkboxes
            ]
            
            for selector in checkbox_selectors:
                try:
                    checkboxes = await self.page.query_selector_all(selector)
                    for checkbox in checkboxes:
                        # Check if this is the unread filter checkbox
                        checkbox_id = await checkbox.get_attribute('id')
                        checkbox_name = await checkbox.get_attribute('name')
                        
                        # Look for common patterns in the checkbox attributes
                        if checkbox_id and ('unread' in checkbox_id.lower() or 'filter' in checkbox_id.lower()):
                            is_checked = await checkbox.is_checked()
                            if is_checked:
                                await checkbox.uncheck()
                                self.logger.info(f"Unchecked 'Unread Notices Only' filter: {checkbox_id}")
                                return
                        
                        if checkbox_name and ('unread' in checkbox_name.lower() or 'filter' in checkbox_name.lower()):
                            is_checked = await checkbox.is_checked()
                            if is_checked:
                                await checkbox.uncheck()
                                self.logger.info(f"Unchecked 'Unread Notices Only' filter: {checkbox_name}")
                                return
                    
                    # If we found checkboxes but couldn't identify the unread filter,
                    # look for text context
                    if checkboxes:
                        for checkbox in checkboxes:
                            # Check nearby text for "Unread Notices Only"
                            parent = await checkbox.query_selector('xpath=..')
                            if parent:
                                parent_text = await parent.text_content()
                                if parent_text and 'unread notices only' in parent_text.lower():
                                    is_checked = await checkbox.is_checked()
                                    if is_checked:
                                        await checkbox.uncheck()
                                        self.logger.info("Unchecked 'Unread Notices Only' filter by context")
                                        return
                        
                except Exception as e:
                    self.logger.warning(f"Error with selector {selector}: {e}")
                    continue
            
            self.logger.warning("Could not find 'Unread Notices Only' checkbox to uncheck")
            
        except Exception as e:
            self.logger.error(f"Error unchecking unread filter: {e}")
    
    async def search_case(self, case_number: str):
        """Enter case number and click search"""
        try:
            # Look for case number input field
            input_selectors = [
                'input[name*="Case"], input[id*="Case"]',
                'input[name*="txtCase"], input[id*="txtCase"]',
                'input[placeholder*="Case"], input[title*="Case"]',
                'input[type="text"]'  # Fallback
            ]
            
            case_input = None
            for selector in input_selectors:
                try:
                    inputs = await self.page.query_selector_all(selector)
                    for input_field in inputs:
                        input_id = await input_field.get_attribute('id') or ''
                        input_name = await input_field.get_attribute('name') or ''
                        input_placeholder = await input_field.get_attribute('placeholder') or ''
                        
                        if any('case' in attr.lower() for attr in [input_id, input_name, input_placeholder]):
                            case_input = input_field
                            break
                    
                    if case_input:
                        break
                        
                except Exception as e:
                    continue
            
            if case_input:
                # Clear any existing text and enter case number
                await case_input.clear()
                await case_input.fill(case_number)
                self.logger.info(f"Entered case number: {case_number}")
            else:
                self.logger.error("Could not find case number input field")
                return
            
            # Look for search button
            search_selectors = [
                'input[type="submit"][value*="Search"]',
                'button:has-text("Search")',
                'input[name*="Search"], input[id*="Search"]',
                'input[type="button"][value*="Search"]'
            ]
            
            search_button = None
            for selector in search_selectors:
                try:
                    search_button = await self.page.query_selector(selector)
                    if search_button:
                        break
                except:
                    continue
            
            if search_button:
                # Click search and wait for results
                await search_button.click()
                await self.page.wait_for_load_state('networkidle')
                await asyncio.sleep(3)  # Allow results to load
                self.logger.info("Clicked search button and waiting for results")
            else:
                self.logger.error("Could not find search button")
                
        except Exception as e:
            self.logger.error(f"Error searching for case: {e}")
    
    async def detect_total_pages(self) -> int:
        """Detect the total number of pages from pagination controls"""
        try:
            # Look for pagination info
            pagination_selectors = [
                'span:has-text("Page")',
                'div:has-text("Page")', 
                'span[id*="page"]',
                'div[id*="page"]',
                'span[id*="Label"]',
                'td:has-text("Page")'
            ]
            
            for selector in pagination_selectors:
                try:
                    elements = await self.page.query_selector_all(selector)
                    for element in elements:
                        text = await element.text_content()
                        if text and 'page' in text.lower():
                            # Look for "Page X of Y" pattern
                            page_match = re.search(r'page\s+(\d+)\s+of\s+(\d+)', text.lower())
                            if page_match:
                                total_pages = int(page_match.group(2))
                                self.logger.info(f"Found pagination info: {text} -> {total_pages} pages")
                                return total_pages
                except:
                    continue
            
            # Alternative: Count page number links
            page_links = await self.page.query_selector_all('a:regex(\\d+)')
            if page_links:
                max_page = 0
                for page_link in page_links:
                    text = await page_link.text_content()
                    if text and text.isdigit():
                        max_page = max(max_page, int(text))
                if max_page > 0:
                    return max_page
            
            # Check for presence of Next button to indicate multiple pages
            next_buttons = await self.page.query_selector_all('a:has-text("Next"), input[value*="Next"]')
            if next_buttons:
                # If we can't determine exact page count, assume 4 pages based on user description
                self.logger.info("Found Next button, assuming 4 pages as described")
                return 4
            
            # Default: assume 1 page if no pagination detected
            self.logger.warning("Could not detect pagination, assuming 1 page")
            return 1
            
        except Exception as e:
            self.logger.error(f"Error detecting pages: {e}")
            return 1
    
    async def scrape_single_eservice_page(self, page_num: int) -> Dict:
        """Scrape a single page of E-Service records"""
        
        page_data = {
            'page_number': page_num,
            'url': self.page.url,
            'scraped_at': datetime.now().isoformat(),
            'records': []
        }
        
        try:
            # Look for the main table containing E-Service records
            table_selectors = [
                'table[id*="GridView"]',
                'table[id*="DataGrid"]', 
                'table[class*="grid"]',
                'table:has(tr:has(td))'  # Any table with data rows
            ]
            
            main_table = None
            for selector in table_selectors:
                try:
                    tables = await self.page.query_selector_all(selector)
                    for table in tables:
                        # Check if table has multiple rows (indicating data)
                        rows = await table.query_selector_all('tr')
                        if len(rows) > 1:  # More than just header row
                            main_table = table
                            break
                    if main_table:
                        break
                except:
                    continue
            
            if not main_table:
                self.logger.warning(f"Could not find data table on page {page_num}")
                return page_data
            
            # Extract table headers
            header_row = await main_table.query_selector('tr:first-child')
            headers = []
            if header_row:
                header_cells = await header_row.query_selector_all('th, td')
                for cell in header_cells:
                    text = await cell.text_content()
                    headers.append(text.strip() if text else '')
            
            # Extract data rows
            data_rows = await main_table.query_selector_all('tr:not(:first-child)')
            
            for row_index, row in enumerate(data_rows):
                try:
                    record = {
                        'row_number': row_index + 1,
                        'page_number': page_num,
                        'raw_html': await row.inner_html(),
                        'data': {},
                        'links': [],
                        'images': []
                    }
                    
                    # Extract cell data
                    cells = await row.query_selector_all('td')
                    for cell_index, cell in enumerate(cells):
                        cell_text = await cell.text_content()
                        cell_text = cell_text.strip() if cell_text else ''
                        
                        # Use header name if available, otherwise use column index
                        column_name = headers[cell_index] if cell_index < len(headers) else f'Column_{cell_index + 1}'
                        record['data'][column_name] = cell_text
                        
                        # Look for links in this cell
                        links = await cell.query_selector_all('a')
                        for link in links:
                            href = await link.get_attribute('href')
                            link_text = await link.text_content()
                            if href:
                                link_info = {
                                    'url': href,
                                    'full_url': urljoin(self.base_url, href),
                                    'text': link_text.strip() if link_text else '',
                                    'column': column_name
                                }
                                record['links'].append(link_info)
                        
                        # Look for images in this cell
                        images = await cell.query_selector_all('img')
                        for img in images:
                            src = await img.get_attribute('src')
                            alt = await img.get_attribute('alt')
                            if src:
                                img_info = {
                                    'src': src,
                                    'full_url': urljoin(self.base_url, src),
                                    'alt': alt or '',
                                    'column': column_name
                                }
                                record['images'].append(img_info)
                    
                    page_data['records'].append(record)
                    
                except Exception as e:
                    self.logger.warning(f"Error processing row {row_index + 1} on page {page_num}: {e}")
                    continue
            
            self.logger.info(f"Page {page_num}: Extracted {len(page_data['records'])} records")
            return page_data
            
        except Exception as e:
            self.logger.error(f"Error scraping page {page_num}: {e}")
            return page_data
    
    async def navigate_to_next_page(self) -> bool:
        """Navigate to the next page"""
        try:
            # Look for Next button or link
            next_selectors = [
                'a:has-text("Next")',
                'input[value*="Next"]',
                'button:has-text("Next")',
                'a[title*="Next"]'
            ]
            
            for selector in next_selectors:
                try:
                    next_button = await self.page.query_selector(selector)
                    if next_button:
                        # Check if it's enabled/clickable
                        is_disabled = await next_button.get_attribute('disabled')
                        if not is_disabled:
                            await next_button.click()
                            await self.page.wait_for_load_state('networkidle')
                            await asyncio.sleep(2)
                            return True
                except:
                    continue
            
            return False
            
        except Exception as e:
            self.logger.error(f"Error navigating to next page: {e}")
            return False
    
    async def download_all_documents(self, all_records: List[Dict], case_number: str) -> Dict:
        """Download all documents/images found in the records"""
        download_results = {
            'total_links': 0,
            'total_images': 0,
            'successful_downloads': 0,
            'failed_downloads': 0,
            'downloaded_files': [],
            'errors': []
        }
        
        # Collect all downloadable items
        download_items = []
        
        for record in all_records:
            # Add links
            for link in record.get('links', []):
                if link.get('full_url'):
                    download_items.append({
                        'type': 'link',
                        'url': link['full_url'],
                        'text': link.get('text', ''),
                        'record_info': f"Page {record['page_number']}, Row {record['row_number']}"
                    })
                    download_results['total_links'] += 1
            
            # Add images
            for img in record.get('images', []):
                if img.get('full_url'):
                    download_items.append({
                        'type': 'image',
                        'url': img['full_url'],
                        'alt': img.get('alt', ''),
                        'record_info': f"Page {record['page_number']}, Row {record['row_number']}"
                    })
                    download_results['total_images'] += 1
        
        if not download_items:
            self.logger.info("No downloadable items found")
            return download_results
        
        # Download all items
        timeout = aiohttp.ClientTimeout(total=60)
        
        async with aiohttp.ClientSession(timeout=timeout) as session:
            # Add cookies to session
            if self.session_cookies:
                for cookie in self.session_cookies:
                    session.cookie_jar.update_cookies({cookie['name']: cookie['value']}, 
                                                     response_url=cookie.get('domain', self.base_url))
            
            # Download with concurrency control
            semaphore = asyncio.Semaphore(3)
            
            download_tasks = []
            for i, item in enumerate(download_items):
                task = self.download_single_item(session, item, i + 1, case_number, semaphore)
                download_tasks.append(task)
            
            # Execute all downloads
            results_list = await asyncio.gather(*download_tasks, return_exceptions=True)
            
            # Process results
            for result in results_list:
                if isinstance(result, Exception):
                    download_results['failed_downloads'] += 1
                    download_results['errors'].append(str(result))
                elif result and result.get('success'):
                    download_results['successful_downloads'] += 1
                    download_results['downloaded_files'].append(result['filename'])
                else:
                    download_results['failed_downloads'] += 1
                    if result and result.get('error'):
                        download_results['errors'].append(result['error'])
        
        self.logger.info(f"Download complete: {download_results['successful_downloads']}/{len(download_items)} successful")
        return download_results
    
    async def download_single_item(self, session: aiohttp.ClientSession, item: Dict, 
                                  item_number: int, case_number: str, semaphore: asyncio.Semaphore) -> Dict:
        """Download a single document/image"""
        async with semaphore:
            try:
                download_url = item['url']
                item_type = item['type']
                
                self.logger.info(f"Downloading {item_type} {item_number}: {download_url}")
                
                async with session.get(download_url) as response:
                    if response.status == 200:
                        # Determine file extension
                        content_type = response.headers.get('content-type', '')
                        file_extension = self.get_file_extension(content_type, download_url)
                        
                        # Generate filename
                        safe_case = case_number.replace(' ', '_').replace('-', '_')
                        filename = f"{safe_case}_eservice_{item_type}_{item_number:03d}{file_extension}"
                        filepath = self.download_dir / filename
                        
                        # Save file
                        async with aiofiles.open(filepath, 'wb') as f:
                            async for chunk in response.content.iter_chunked(8192):
                                await f.write(chunk)
                        
                        file_size = filepath.stat().st_size
                        self.logger.info(f"Downloaded: {filename} ({file_size} bytes)")
                        
                        return {
                            'success': True,
                            'filename': filename,
                            'filepath': str(filepath),
                            'size': file_size,
                            'url': download_url,
                            'type': item_type
                        }
                    else:
                        error_msg = f"HTTP {response.status} for {download_url}"
                        self.logger.error(error_msg)
                        return {'success': False, 'error': error_msg}
                        
            except Exception as e:
                error_msg = f"Failed to download {item_type} {item_number}: {str(e)}"
                self.logger.error(error_msg)
                return {'success': False, 'error': error_msg}
    
    def get_file_extension(self, content_type: str, url: str) -> str:
        """Determine file extension from content type or URL"""
        # Check content type first
        if 'pdf' in content_type.lower():
            return '.pdf'
        elif 'image/jpeg' in content_type.lower() or 'image/jpg' in content_type.lower():
            return '.jpg'
        elif 'image/png' in content_type.lower():
            return '.png'
        elif 'image/gif' in content_type.lower():
            return '.gif'
        elif 'text/html' in content_type.lower():
            return '.html'
        
        # Check URL extension
        if url:
            url_lower = url.lower()
            if '.pdf' in url_lower:
                return '.pdf'
            elif '.jpg' in url_lower or '.jpeg' in url_lower:
                return '.jpg'
            elif '.png' in url_lower:
                return '.png'
            elif '.gif' in url_lower:
                return '.gif'
            elif '.html' in url_lower or '.htm' in url_lower:
                return '.html'
        
        # Default to .pdf for court documents
        return '.pdf'
    
    async def close(self):
        """Clean up browser resources"""
        if self.page:
            await self.page.close()
        if self.browser:
            await self.browser.close()
        if hasattr(self, 'playwright'):
            await self.playwright.stop()

# Convenience function
async def scrape_eservice_case(username: str, password: str, case_number: str = "DR 25 403973", 
                              headless: bool = False, download_dir: str = None) -> Dict:
    """
    Convenience function to scrape E-Service records for a case
    
    Args:
        username: E-filing login username
        password: E-filing login password  
        case_number: Case number to search for
        headless: Whether to run browser in headless mode
        download_dir: Directory to save downloaded files
        
    Returns:
        Complete results dictionary with all records and downloads
    """
    scraper = EServiceQueueScraper(username, password, headless, download_dir)
    
    try:
        await scraper.start_browser()
        results = await scraper.scrape_eservice_case(case_number)
        return results
    finally:
        await scraper.close()

# Example usage
if __name__ == "__main__":
    async def main():
        case_number = "DR 25 403973"
        
        results = await scrape_eservice_case(
            username="your_username",
            password="your_password", 
            case_number=case_number,
            headless=False,
            download_dir="eservice_downloads"
        )
        
        print(f"Total records found: {results.get('total_records', 0)}")
        print(f"Total pages: {results.get('total_pages', 0)}")
        
        # Save results
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"eservice_results_{case_number.replace(' ', '_')}_{timestamp}.json"
        
        with open(filename, 'w') as f:
            json.dump(results, f, indent=2)
        
        print(f"Results saved to: {filename}")
    
    asyncio.run(main())