"""
My Filings Scraper for Cuyahoga County E-Filing System
Handles scraping all pages of user's filed documents with complete table data and downloads
"""

import asyncio
import os
import time
import logging
import re
import aiohttp
import aiofiles
from datetime import datetime
from typing import List, Dict, Optional, Tuple
from urllib.parse import urljoin, urlparse, parse_qs
from pathlib import Path
import json

try:
    from playwright.async_api import async_playwright, Browser, Page
    PLAYWRIGHT_AVAILABLE = True
except ImportError:
    PLAYWRIGHT_AVAILABLE = False
    logging.warning("Playwright not available. Install with: pip install playwright")

class MyFilingsScraper:
    """
    Enhanced scraper for My Filings section with complete workflow automation
    
    Workflow:
    1. Login to e-filing system
    2. Navigate to My Filings/EFiling.aspx
    3. Detect total pages (11 pages expected)
    4. Scrape all pages with complete table data
    5. Download all associated documents/images
    """
    
    def __init__(self, username: str, password: str, headless: bool = False, download_dir: str = None):
        self.username = username
        self.password = password
        self.headless = headless
        self.base_url = "https://efiling.cp.cuyahogacounty.gov"
        self.login_url = f"{self.base_url}/Login.aspx"
        self.myfilings_url = f"{self.base_url}/EFiling.aspx"
        
        # Setup download directory
        if download_dir:
            self.download_dir = Path(download_dir)
        else:
            self.download_dir = Path("myfilings_downloads") / datetime.now().strftime("%Y%m%d_%H%M%S")
        
        self.download_dir.mkdir(parents=True, exist_ok=True)
        
        self.browser = None
        self.page = None
        self.logged_in = False
        self.session_cookies = None
        
        # Setup logging
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
    
    async def start_browser(self):
        """Start Playwright browser"""
        if not PLAYWRIGHT_AVAILABLE:
            raise RuntimeError("Playwright not available")
        
        self.playwright = await async_playwright().start()
        self.browser = await self.playwright.chromium.launch(
            headless=self.headless,
            args=['--no-sandbox', '--disable-dev-shm-usage']
        )
        self.page = await self.browser.new_page()
        
        # Set reasonable timeouts
        self.page.set_default_timeout(30000)  # 30 seconds
        
    async def login(self) -> bool:
        """Login to the e-filing system"""
        try:
            await self.page.goto(self.login_url, wait_until='networkidle')
            
            # Fill in login credentials
            await self.page.fill('input[name="ctl00$ContentPlaceHolder1$txtUserName"]', self.username)
            await self.page.fill('input[name="ctl00$ContentPlaceHolder1$txtPassword"]', self.password)
            
            # Click login button
            await self.page.click('input[name="ctl00$ContentPlaceHolder1$btnLogin"]')
            
            # Wait for redirect to home page
            await self.page.wait_for_url('**/Home.aspx', timeout=15000)
            
            self.logged_in = True
            
            # Get session cookies for downloads
            self.session_cookies = await self.page.context.cookies()
            self.logger.info("Successfully logged in and captured session cookies")
            return True
            
        except Exception as e:
            self.logger.error(f"Login failed: {e}")
            return False
    
    async def scrape_all_my_filings(self) -> Dict:
        """
        Complete My Filings scraping workflow
        
        Returns:
            Complete dictionary with all filings records and downloaded files
        """
        if not self.logged_in:
            await self.login()
        
        try:
            # Navigate to My Filings
            self.logger.info("Navigating to My Filings...")
            await self.page.goto(self.myfilings_url, wait_until='networkidle')
            await asyncio.sleep(3)  # Allow page to fully load
            
            # Initialize results structure
            results = {
                'scraped_at': datetime.now().isoformat(),
                'base_url': self.myfilings_url,
                'total_pages': 0,
                'total_filings': 0,
                'total_records': 0,
                'pages': [],
                'all_filings': [],
                'all_records': [],
                'table_headers': [],
                'downloads': None,
                'download_directory': str(self.download_dir)
            }
            
            # Detect total pages
            total_pages = await self.detect_total_pages()
            results['total_pages'] = total_pages
            
            self.logger.info(f"Found {total_pages} pages of My Filings (expected: 11)")
            
            # Scrape all pages
            for page_num in range(1, total_pages + 1):
                self.logger.info(f"Scraping page {page_num} of {total_pages}")
                
                page_data = await self.scrape_single_filings_page(page_num)
                results['pages'].append(page_data)
                results['all_filings'].extend(page_data.get('filings', []))
                results['all_records'].extend(page_data.get('records', []))
                
                # Navigate to next page if not the last page
                if page_num < total_pages:
                    await self.navigate_to_next_page()
                
                # Small delay between pages
                await asyncio.sleep(2)
            
            results['total_filings'] = len(results['all_filings'])
            results['total_records'] = len(results['all_records'])
            
            # Extract table headers from first page if available
            if results['pages'] and results['pages'][0].get('table_headers'):
                results['table_headers'] = results['pages'][0]['table_headers']
            
            self.logger.info(f"Scraped {results['total_filings']} total filings and {results['total_records']} table records from {total_pages} pages")
            
            # Download all associated documents/images
            if results['all_records']:
                self.logger.info("Starting download of all documents/images...")
                download_results = await self.download_all_documents(results['all_records'])
                results['downloads'] = download_results
            
            return results
            
        except Exception as e:
            self.logger.error(f"Failed to scrape My Filings: {e}")
            return {
                'error': str(e),
                'scraped_at': datetime.now().isoformat()
            }
    
    async def detect_total_pages(self) -> int:
        """Detect the total number of pages from pagination controls"""
        try:
            # Look for pagination info
            pagination_selectors = [
                'span:has-text("Page")',
                'div:has-text("Page")', 
                'span[id*="page"]',
                'div[id*="page"]',
                'span[id*="Label"]',
                'td:has-text("Page")'
            ]
            
            for selector in pagination_selectors:
                try:
                    elements = await self.page.query_selector_all(selector)
                    for element in elements:
                        text = await element.text_content()
                        if text and 'page' in text.lower():
                            # Look for "Page X of Y" pattern
                            page_match = re.search(r'page\s+(\d+)\s+of\s+(\d+)', text.lower())
                            if page_match:
                                total_pages = int(page_match.group(2))
                                self.logger.info(f"Found pagination info: {text} -> {total_pages} pages")
                                return total_pages
                except:
                    continue
            
            # Alternative: Count page number links
            page_links = await self.page.query_selector_all('a:regex(\\d+)')
            if page_links:
                max_page = 0
                for page_link in page_links:
                    text = await page_link.text_content()
                    if text and text.isdigit():
                        max_page = max(max_page, int(text))
                if max_page > 0:
                    return max_page
            
            # Check for presence of Next button to indicate multiple pages
            next_buttons = await self.page.query_selector_all('a:has-text("Next"), input[value*="Next"]')
            if next_buttons:
                # If we can't determine exact page count, assume 11 pages based on user description
                self.logger.info("Found Next button, assuming 11 pages as described")
                return 11
            
            # Default: assume 1 page if no pagination detected
            self.logger.warning("Could not detect pagination, assuming 1 page")
            return 1
            
        except Exception as e:
            self.logger.error(f"Error detecting pages: {e}")
            return 1
    
    async def scrape_single_filings_page(self, page_num: int) -> Dict:
        """Scrape a single page of My Filings records"""
        
        page_data = {
            'page_number': page_num,
            'url': self.page.url,
            'scraped_at': datetime.now().isoformat(),
            'filings': [],
            'records': [],
            'table_headers': []
        }
        
        try:
            # Extract complete table data
            await self.extract_table_data(page_data)
            
            # Extract specific filing information
            await self.extract_filing_info(page_data)
            
            self.logger.info(f"Page {page_num}: Found {len(page_data['filings'])} filings and {len(page_data['records'])} table records")
            return page_data
            
        except Exception as e:
            self.logger.error(f"Error scraping page {page_num}: {e}")
            return page_data
    
    async def extract_table_data(self, page_data: Dict):
        """Extract complete table data from the My Filings page"""
        try:
            # Look for the main table containing filing records
            table_selectors = [
                'table[id*="GridView"]',
                'table[id*="DataGrid"]', 
                'table[class*="grid"]',
                'table:has(tr:has(td))'  # Any table with data rows
            ]
            
            main_table = None
            for selector in table_selectors:
                try:
                    tables = await self.page.query_selector_all(selector)
                    for table in tables:
                        # Check if table has multiple rows (indicating data)
                        rows = await table.query_selector_all('tr')
                        if len(rows) > 1:  # More than just header row
                            main_table = table
                            break
                    if main_table:
                        break
                except:
                    continue
            
            if not main_table:
                self.logger.info(f"No data table found on page {page_data['page_number']} - trying alternative approach")
                await self.extract_alternative_table_data(page_data)
                return
            
            # Extract table headers
            header_row = await main_table.query_selector('tr:first-child')
            headers = []
            if header_row:
                header_cells = await header_row.query_selector_all('th, td')
                for cell in header_cells:
                    text = await cell.text_content()
                    headers.append(text.strip() if text else '')
                page_data['table_headers'] = headers
            
            # Extract data rows
            data_rows = await main_table.query_selector_all('tr:not(:first-child)')
            
            for row_index, row in enumerate(data_rows):
                try:
                    record = {
                        'row_number': row_index + 1,
                        'page_number': page_data['page_number'],
                        'raw_html': await row.inner_html(),
                        'data': {},
                        'links': [],
                        'images': []
                    }
                    
                    # Extract cell data
                    cells = await row.query_selector_all('td')
                    for cell_index, cell in enumerate(cells):
                        cell_text = await cell.text_content()
                        cell_text = cell_text.strip() if cell_text else ''
                        
                        # Use header name if available, otherwise use column index
                        column_name = headers[cell_index] if cell_index < len(headers) else f'Column_{cell_index + 1}'
                        record['data'][column_name] = cell_text
                        
                        # Look for links in this cell
                        links = await cell.query_selector_all('a')
                        for link in links:
                            href = await link.get_attribute('href')
                            link_text = await link.text_content()
                            onclick = await link.get_attribute('onclick')
                            if href or onclick:
                                link_info = {
                                    'url': href or '',
                                    'full_url': urljoin(self.base_url, href) if href else '',
                                    'text': link_text.strip() if link_text else '',
                                    'onclick': onclick or '',
                                    'column': column_name
                                }
                                record['links'].append(link_info)
                        
                        # Look for images in this cell
                        images = await cell.query_selector_all('img')
                        for img in images:
                            src = await img.get_attribute('src')
                            alt = await img.get_attribute('alt')
                            if src:
                                img_info = {
                                    'src': src,
                                    'full_url': urljoin(self.base_url, src),
                                    'alt': alt or '',
                                    'column': column_name
                                }
                                record['images'].append(img_info)
                    
                    page_data['records'].append(record)
                    
                except Exception as e:
                    self.logger.warning(f"Error processing table row {row_index + 1} on page {page_data['page_number']}: {e}")
                    continue
            
            self.logger.info(f"Page {page_data['page_number']}: Extracted {len(page_data['records'])} table records")
            
        except Exception as e:
            self.logger.error(f"Error extracting table data from page {page_data['page_number']}: {e}")
    
    async def extract_alternative_table_data(self, page_data: Dict):
        """Alternative method to extract structured data when no formal table is found"""
        try:
            # Look for any structured rows that might contain filing information
            row_selectors = [
                'tr',  # Table rows
                'div[class*="row"]',  # Div-based rows
                'li',  # List items
                'div:has(a)',  # Divs containing links
                'span[class*="filing"], div[class*="filing"]'  # Filing-specific elements
            ]
            
            record_count = 0
            for selector in row_selectors:
                try:
                    elements = await self.page.query_selector_all(selector)
                    
                    for element_index, element in enumerate(elements):
                        # Check if this element contains useful information
                        text_content = await element.text_content()
                        if not text_content or len(text_content.strip()) < 10:
                            continue
                        
                        # Look for links and images in this element
                        links = await element.query_selector_all('a')
                        images = await element.query_selector_all('img')
                        
                        if links or images or 'filing' in text_content.lower():
                            record = {
                                'row_number': record_count + 1,
                                'page_number': page_data['page_number'],
                                'raw_html': await element.inner_html(),
                                'data': {
                                    'text_content': text_content.strip(),
                                    'element_type': selector
                                },
                                'links': [],
                                'images': []
                            }
                            
                            # Extract links
                            for link in links:
                                href = await link.get_attribute('href')
                                link_text = await link.text_content()
                                onclick = await link.get_attribute('onclick')
                                if href or onclick:
                                    link_info = {
                                        'url': href or '',
                                        'full_url': urljoin(self.base_url, href) if href else '',
                                        'text': link_text.strip() if link_text else '',
                                        'onclick': onclick or '',
                                        'column': 'extracted_link'
                                    }
                                    record['links'].append(link_info)
                            
                            # Extract images
                            for img in images:
                                src = await img.get_attribute('src')
                                alt = await img.get_attribute('alt')
                                if src:
                                    img_info = {
                                        'src': src,
                                        'full_url': urljoin(self.base_url, src),
                                        'alt': alt or '',
                                        'column': 'extracted_image'
                                    }
                                    record['images'].append(img_info)
                            
                            page_data['records'].append(record)
                            record_count += 1
                            
                            if record_count >= 25:  # Limit to avoid too many records
                                break
                    
                    if record_count > 0:
                        break  # Found structured data, no need to try other selectors
                        
                except Exception as e:
                    continue
            
            if record_count > 0:
                self.logger.info(f"Page {page_data['page_number']}: Extracted {record_count} alternative structured records")
            else:
                self.logger.warning(f"Page {page_data['page_number']}: No structured data found")
                
        except Exception as e:
            self.logger.error(f"Error extracting alternative table data: {e}")
    
    async def extract_filing_info(self, page_data: Dict):
        """Extract specific filing information and process EFile IDs"""
        try:
            # Look for EFile ID links to click and process
            efile_selectors = [
                'a[href*="EFile"]',
                'a[href*="efiling"]',
                'a:has-text("EF")',  # EFile IDs often start with EF
                'a[onclick*="EFile"]'
            ]
            
            filings = []
            processed_efiles = set()  # Avoid processing same EFile ID multiple times
            
            for selector in efile_selectors:
                try:
                    elements = await self.page.query_selector_all(selector)
                    
                    for element in elements:
                        # Get the EFile ID link
                        href = await element.get_attribute('href')
                        text = await element.text_content()
                        
                        if href and text and text.strip() not in processed_efiles:
                            efile_id = text.strip()
                            processed_efiles.add(efile_id)
                            
                            self.logger.info(f"Processing EFile ID: {efile_id}")
                            
                            # Process this specific EFile ID
                            filing_details = await self.process_efile_id(element, efile_id)
                            if filing_details:
                                filings.append(filing_details)
                                
                            # Small delay between processing EFile IDs
                            await asyncio.sleep(1)
                            
                except Exception as e:
                    self.logger.warning(f"Error processing EFile selector {selector}: {e}")
                    continue
            
            # Also look for other filing elements (fallback)
            other_filing_selectors = [
                'a[href*="DisplayImage"]',
                'a[href*="PDF"]', 
                'a[title*="View"]',
                'input[type="submit"][value*="View"]'
            ]
            
            for selector in other_filing_selectors:
                try:
                    elements = await self.page.query_selector_all(selector)
                    
                    for element in elements:
                        filing_info = await self.extract_filing_details(element)
                        if filing_info:
                            filings.append(filing_info)
                            
                except Exception as e:
                    continue
            
            page_data['filings'] = filings
            self.logger.info(f"Page {page_data['page_number']}: Processed {len([f for f in filings if f.get('efile_id')])} EFile IDs and {len(filings)} total filing items")
            
        except Exception as e:
            self.logger.error(f"Error extracting filing info: {e}")
    
    async def process_efile_id(self, efile_element, efile_id: str) -> Optional[Dict]:
        """Process a specific EFile ID by clicking it and extracting document information"""
        try:
            # Store current page for returning later
            original_url = self.page.url
            
            # Click on the EFile ID link
            await efile_element.click()
            await self.page.wait_for_load_state('networkidle')
            await asyncio.sleep(2)  # Allow page to fully load
            
            filing_details = {
                'efile_id': efile_id,
                'original_url': original_url,
                'details_url': self.page.url,
                'scraped_at': datetime.now().isoformat(),
                'document_information': {},
                'download_links': [],
                'pdf_downloads': []
            }
            
            # Extract all document information from the details page
            await self.extract_document_information(filing_details)
            
            # Look for the magnifying glass icon and process hover download
            await self.process_magnifying_glass_download(filing_details)
            
            # Go back to the original page
            await self.page.goto(original_url, wait_until='networkidle')
            await asyncio.sleep(1)
            
            return filing_details
            
        except Exception as e:
            self.logger.error(f"Error processing EFile ID {efile_id}: {e}")
            try:
                # Try to return to original page if something went wrong
                await self.page.goto(original_url, wait_until='networkidle')
            except:
                pass
            return None
    
    async def extract_document_information(self, filing_details: Dict):
        """Extract all document information from the EFile details page"""
        try:
            # Look for DOCUMENT INFORMATION section
            doc_info_selectors = [
                'div:has-text("DOCUMENT INFORMATION")',
                'table:has(td:has-text("DOCUMENT INFORMATION"))',
                'span:has-text("DOCUMENT INFORMATION")',
                'h2:has-text("DOCUMENT INFORMATION"), h3:has-text("DOCUMENT INFORMATION")'
            ]
            
            doc_info_section = None
            for selector in doc_info_selectors:
                try:
                    doc_info_section = await self.page.query_selector(selector)
                    if doc_info_section:
                        break
                except:
                    continue
            
            if doc_info_section:
                # Extract all text and structured data from the document information section
                parent_container = doc_info_section
                
                # Try to find the parent container that holds all the document info
                for _ in range(3):  # Go up to 3 levels to find the container
                    try:
                        parent = await parent_container.query_selector('xpath=..')
                        if parent:
                            parent_container = parent
                    except:
                        break
                
                # Extract all key-value pairs from the document information
                await self.extract_key_value_pairs(parent_container, filing_details['document_information'])
                
            else:
                # Fallback: extract all visible text from the page
                page_text = await self.page.text_content()
                filing_details['document_information']['full_page_text'] = page_text
                
                # Try to extract structured information from the full text
                await self.extract_info_from_text(page_text, filing_details['document_information'])
            
            self.logger.info(f"Extracted document information for EFile ID: {filing_details['efile_id']}")
            
        except Exception as e:
            self.logger.error(f"Error extracting document information: {e}")
    
    async def extract_key_value_pairs(self, container, info_dict: Dict):
        """Extract key-value pairs from a container element"""
        try:
            # Look for table rows with key-value pairs
            rows = await container.query_selector_all('tr')
            for row in rows:
                cells = await row.query_selector_all('td, th')
                if len(cells) >= 2:
                    key_cell = cells[0]
                    value_cell = cells[1]
                    
                    key = await key_cell.text_content()
                    value = await value_cell.text_content()
                    
                    if key and value:
                        key = key.strip().rstrip(':')
                        value = value.strip()
                        if key and value:
                            info_dict[key] = value
            
            # Look for labeled spans/divs
            labels = await container.query_selector_all('label, span[class*="label"], div[class*="label"]')
            for label in labels:
                label_text = await label.text_content()
                if label_text and ':' in label_text:
                    parts = label_text.split(':', 1)
                    if len(parts) == 2:
                        key = parts[0].strip()
                        value = parts[1].strip()
                        if key and value:
                            info_dict[key] = value
            
            # Extract any other structured data
            all_text = await container.text_content()
            if all_text:
                info_dict['section_text'] = all_text.strip()
                
        except Exception as e:
            self.logger.warning(f"Error extracting key-value pairs: {e}")
    
    async def extract_info_from_text(self, text: str, info_dict: Dict):
        """Extract structured information from plain text"""
        try:
            lines = text.split('\n')
            for line in lines:
                line = line.strip()
                if ':' in line and len(line) < 200:  # Reasonable length for a key-value pair
                    parts = line.split(':', 1)
                    if len(parts) == 2:
                        key = parts[0].strip()
                        value = parts[1].strip()
                        if key and value and len(key) < 50:  # Reasonable key length
                            info_dict[key] = value
                            
        except Exception as e:
            self.logger.warning(f"Error extracting info from text: {e}")
    
    async def process_magnifying_glass_download(self, filing_details: Dict):
        """Find table with magnifying glass download links and extract document info"""
        try:
            # Look for tables in the DOCUMENT INFORMATION section
            doc_info_tables = []
            
            # Find tables near DOCUMENT INFORMATION
            doc_info_area = await self.page.query_selector('*:has-text("DOCUMENT INFORMATION")')
            if doc_info_area:
                # Look for tables within or near the document information area
                tables = await self.page.query_selector_all('table')
                for table in tables:
                    table_text = await table.text_content()
                    # Check if this table seems to contain document download information
                    if any(keyword in table_text.lower() for keyword in ['document', 'file', 'type', 'download']):
                        doc_info_tables.append(table)
            
            if not doc_info_tables:
                # Fallback: find all tables and check each one
                all_tables = await self.page.query_selector_all('table')
                for table in all_tables:
                    rows = await table.query_selector_all('tr')
                    if len(rows) > 1:  # Has data rows
                        # Check if any row has a magnifying glass icon
                        for row in rows:
                            cells = await row.query_selector_all('td')
                            if cells:
                                first_cell = cells[0]
                                magnifying_elements = await first_cell.query_selector_all('img[src*="magnif"], img[alt*="magnif"], a[title*="magnif"], img[src*="zoom"], img[alt*="view"]')
                                if magnifying_elements:
                                    doc_info_tables.append(table)
                                    break
            
            # Process each table with document information
            for table in doc_info_tables:
                await self.process_document_table(table, filing_details)
            
            if not doc_info_tables:
                self.logger.warning("Could not find any tables with document information")
                
        except Exception as e:
            self.logger.error(f"Error processing magnifying glass download: {e}")
    
    async def process_document_table(self, table, filing_details: Dict):
        """Process a table containing document download information"""
        try:
            rows = await table.query_selector_all('tr')
            
            # Skip header row, process data rows
            data_rows = rows[1:] if len(rows) > 1 else rows
            
            for row_index, row in enumerate(data_rows):
                try:
                    cells = await row.query_selector_all('td')
                    
                    if len(cells) >= 3:  # Expecting at least 3 columns: download, type, filename
                        download_cell = cells[0]  # Column 1: Magnifying glass (download link)
                        type_cell = cells[1]      # Column 2: Document type
                        filename_cell = cells[2]  # Column 3: File name
                        
                        # Extract document type
                        doc_type = await type_cell.text_content()
                        doc_type = doc_type.strip() if doc_type else ''
                        
                        # Extract filename
                        filename = await filename_cell.text_content()  
                        filename = filename.strip() if filename else ''
                        
                        # Look for magnifying glass/download link in first cell
                        download_elements = await download_cell.query_selector_all('img, a, span[onclick], div[onclick]')
                        
                        for element in download_elements:
                            # Check if this is a magnifying glass or download element
                            src = await element.get_attribute('src')
                            alt = await element.get_attribute('alt')
                            title = await element.get_attribute('title')
                            onclick = await element.get_attribute('onclick')
                            href = await element.get_attribute('href')
                            
                            # Check for magnifying glass indicators
                            is_magnifying_glass = any(
                                indicator in str(attr).lower() 
                                for attr in [src, alt, title] 
                                for indicator in ['magnif', 'zoom', 'view', 'download']
                                if attr
                            )
                            
                            if is_magnifying_glass or href or onclick:
                                self.logger.info(f"Processing download element for: {filename} (Type: {doc_type})")
                                
                                # Try hovering first (in case it's a hover-to-reveal)
                                try:
                                    await element.hover()
                                    await asyncio.sleep(2.5)  # Hover for 2.5 seconds
                                except:
                                    pass
                                
                                # Look for direct download link
                                download_link = None
                                if href:
                                    download_link = href
                                elif onclick:
                                    # Extract URL from onclick if present
                                    import re
                                    url_match = re.search(r'["\']([^"\']*\.pdf[^"\']*)["\']', onclick)
                                    if url_match:
                                        download_link = url_match.group(1)
                                
                                # Try clicking the element to see if it reveals a download
                                if not download_link:
                                    try:
                                        # Check for new links after hover
                                        new_links = await download_cell.query_selector_all('a[href*=".pdf"], a[href*="download"]')
                                        if new_links:
                                            link_href = await new_links[0].get_attribute('href')
                                            if link_href:
                                                download_link = link_href
                                    except:
                                        pass
                                
                                if download_link:
                                    download_info = {
                                        'type': 'pdf_download',
                                        'url': download_link,
                                        'full_url': urljoin(self.base_url, download_link),
                                        'document_type': doc_type,
                                        'original_filename': filename,
                                        'row_index': row_index + 1,
                                        'extraction_method': 'table_magnifying_glass'
                                    }
                                    
                                    filing_details['download_links'].append(download_info)
                                    
                                    # Download with original filename
                                    await self.download_pdf_with_original_filename(download_info, filing_details)
                                else:
                                    self.logger.warning(f"Could not find download link for: {filename}")
                
                except Exception as e:
                    self.logger.warning(f"Error processing table row {row_index + 1}: {e}")
                    continue
            
            self.logger.info(f"Processed document table with {len(data_rows)} rows")
            
        except Exception as e:
            self.logger.error(f"Error processing document table: {e}")
    
    async def download_pdf_with_original_filename(self, download_info: Dict, filing_details: Dict):
        """Download PDF using the original filename from the table"""
        try:
            if not self.session_cookies:
                return
            
            download_url = download_info['full_url']
            original_filename = download_info.get('original_filename', f"{filing_details['efile_id']}.pdf")
            
            # Sanitize filename for file system
            import re
            safe_filename = re.sub(r'[<>:"/\\|?*]', '_', original_filename)
            
            # Ensure it has .pdf extension
            if not safe_filename.lower().endswith('.pdf'):
                safe_filename += '.pdf'
            
            # Create session for download with cookies
            timeout = aiohttp.ClientTimeout(total=60)
            
            async with aiohttp.ClientSession(timeout=timeout) as session:
                # Add cookies to session
                for cookie in self.session_cookies:
                    session.cookie_jar.update_cookies({cookie['name']: cookie['value']}, 
                                                     response_url=cookie.get('domain', self.base_url))
                
                self.logger.info(f"Downloading PDF: {safe_filename} from {download_url}")
                
                async with session.get(download_url) as response:
                    if response.status == 200:
                        filepath = self.download_dir / safe_filename
                        
                        # Save file
                        async with aiofiles.open(filepath, 'wb') as f:
                            async for chunk in response.content.iter_chunked(8192):
                                await f.write(chunk)
                        
                        file_size = filepath.stat().st_size
                        self.logger.info(f"Downloaded PDF: {safe_filename} ({file_size} bytes)")
                        
                        pdf_download_info = {
                            'success': True,
                            'original_filename': original_filename,
                            'safe_filename': safe_filename,
                            'filepath': str(filepath),
                            'size': file_size,
                            'url': download_url,
                            'document_type': download_info.get('document_type', ''),
                            'efile_id': filing_details['efile_id']
                        }
                        
                        filing_details['pdf_downloads'].append(pdf_download_info)
                        
                    else:
                        self.logger.error(f"Failed to download PDF {safe_filename}: HTTP {response.status}")
                        
        except Exception as e:
            self.logger.error(f"Error downloading PDF {original_filename}: {e}")
    
    async def download_pdf_with_original_name(self, download_info: Dict, filing_details: Dict):
        """Download PDF with its original filename"""
        try:
            if not self.session_cookies:
                return
            
            download_url = download_info['full_url']
            
            # Create session for download with cookies
            timeout = aiohttp.ClientTimeout(total=60)
            
            async with aiohttp.ClientSession(timeout=timeout) as session:
                # Add cookies to session
                for cookie in self.session_cookies:
                    session.cookie_jar.update_cookies({cookie['name']: cookie['value']}, 
                                                     response_url=cookie.get('domain', self.base_url))
                
                self.logger.info(f"Downloading PDF from revealed link: {download_url}")
                
                async with session.get(download_url) as response:
                    if response.status == 200:
                        # Try to get original filename from Content-Disposition header
                        content_disposition = response.headers.get('content-disposition', '')
                        original_filename = None
                        
                        if content_disposition:
                            import re
                            filename_match = re.search(r'filename[*]?=([^;]+)', content_disposition)
                            if filename_match:
                                original_filename = filename_match.group(1).strip('"\'')
                        
                        # If no original filename, create one based on EFile ID
                        if not original_filename:
                            original_filename = f"{filing_details['efile_id']}.pdf"
                        
                        # Sanitize filename
                        original_filename = re.sub(r'[<>:"/\\|?*]', '_', original_filename)
                        
                        filepath = self.download_dir / original_filename
                        
                        # Save file
                        async with aiofiles.open(filepath, 'wb') as f:
                            async for chunk in response.content.iter_chunked(8192):
                                await f.write(chunk)
                        
                        file_size = filepath.stat().st_size
                        self.logger.info(f"Downloaded PDF: {original_filename} ({file_size} bytes)")
                        
                        pdf_download_info = {
                            'success': True,
                            'original_filename': original_filename,
                            'filepath': str(filepath),
                            'size': file_size,
                            'url': download_url,
                            'efile_id': filing_details['efile_id']
                        }
                        
                        filing_details['pdf_downloads'].append(pdf_download_info)
                        
                    else:
                        self.logger.error(f"Failed to download PDF: HTTP {response.status}")
                        
        except Exception as e:
            self.logger.error(f"Error downloading PDF: {e}")
    
    async def extract_filing_details(self, element) -> Optional[Dict]:
        """Extract detailed information from a filing element"""
        try:
            filing_info = {}
            
            # Get basic attributes
            href = await element.get_attribute('href')
            onclick = await element.get_attribute('onclick')
            title = await element.get_attribute('title')
            value = await element.get_attribute('value')
            
            if href:
                filing_info['link'] = href
                filing_info['full_url'] = urljoin(self.base_url, href)
            
            if onclick:
                filing_info['onclick'] = onclick
            
            filing_info['title'] = title or ''
            filing_info['value'] = value or ''
            
            # Get text content
            text = await element.text_content()
            if text:
                filing_info['text'] = text.strip()
            
            # Get context from parent elements
            parent = await element.query_selector('xpath=..')
            if parent:
                parent_text = await parent.text_content()
                if parent_text:
                    filing_info['context'] = parent_text.strip()
            
            # Only return if we found useful information
            if href or onclick or (text and len(text.strip()) > 3):
                return filing_info
            
            return None
            
        except Exception as e:
            self.logger.warning(f"Error extracting filing details: {e}")
            return None
    
    async def navigate_to_next_page(self) -> bool:
        """Navigate to the next page"""
        try:
            # Look for Next button or link
            next_selectors = [
                'a:has-text("Next")',
                'input[value*="Next"]',
                'button:has-text("Next")',
                'a[title*="Next"]'
            ]
            
            for selector in next_selectors:
                try:
                    next_button = await self.page.query_selector(selector)
                    if next_button:
                        # Check if it's enabled/clickable
                        is_disabled = await next_button.get_attribute('disabled')
                        if not is_disabled:
                            await next_button.click()
                            await self.page.wait_for_load_state('networkidle')
                            await asyncio.sleep(2)
                            return True
                except:
                    continue
            
            return False
            
        except Exception as e:
            self.logger.error(f"Error navigating to next page: {e}")
            return False
    
    async def download_all_documents(self, all_records: List[Dict]) -> Dict:
        """Download all documents/images found in the records"""
        download_results = {
            'total_links': 0,
            'total_images': 0,
            'successful_downloads': 0,
            'failed_downloads': 0,
            'downloaded_files': [],
            'errors': []
        }
        
        # Collect all downloadable items
        download_items = []
        
        for record in all_records:
            # Add links
            for link in record.get('links', []):
                if link.get('full_url') and link['full_url'].startswith('http'):
                    download_items.append({
                        'type': 'link',
                        'url': link['full_url'],
                        'text': link.get('text', ''),
                        'record_info': f"Page {record['page_number']}, Row {record['row_number']}"
                    })
                    download_results['total_links'] += 1
            
            # Add images
            for img in record.get('images', []):
                if img.get('full_url'):
                    download_items.append({
                        'type': 'image',
                        'url': img['full_url'],
                        'alt': img.get('alt', ''),
                        'record_info': f"Page {record['page_number']}, Row {record['row_number']}"
                    })
                    download_results['total_images'] += 1
        
        if not download_items:
            self.logger.info("No downloadable items found")
            return download_results
        
        # Download all items
        timeout = aiohttp.ClientTimeout(total=60)
        
        async with aiohttp.ClientSession(timeout=timeout) as session:
            # Add cookies to session
            if self.session_cookies:
                for cookie in self.session_cookies:
                    session.cookie_jar.update_cookies({cookie['name']: cookie['value']}, 
                                                     response_url=cookie.get('domain', self.base_url))
            
            # Download with concurrency control
            semaphore = asyncio.Semaphore(3)
            
            download_tasks = []
            for i, item in enumerate(download_items):
                task = self.download_single_item(session, item, i + 1, semaphore)
                download_tasks.append(task)
            
            # Execute all downloads
            results_list = await asyncio.gather(*download_tasks, return_exceptions=True)
            
            # Process results
            for result in results_list:
                if isinstance(result, Exception):
                    download_results['failed_downloads'] += 1
                    download_results['errors'].append(str(result))
                elif result and result.get('success'):
                    download_results['successful_downloads'] += 1
                    download_results['downloaded_files'].append(result['filename'])
                else:
                    download_results['failed_downloads'] += 1
                    if result and result.get('error'):
                        download_results['errors'].append(result['error'])
        
        self.logger.info(f"Download complete: {download_results['successful_downloads']}/{len(download_items)} successful")
        return download_results
    
    async def download_single_item(self, session: aiohttp.ClientSession, item: Dict, 
                                  item_number: int, semaphore: asyncio.Semaphore) -> Dict:
        """Download a single document/image"""
        async with semaphore:
            try:
                download_url = item['url']
                item_type = item['type']
                
                self.logger.info(f"Downloading {item_type} {item_number}: {download_url}")
                
                async with session.get(download_url) as response:
                    if response.status == 200:
                        # Determine file extension
                        content_type = response.headers.get('content-type', '')
                        file_extension = self.get_file_extension(content_type, download_url)
                        
                        # Generate filename
                        filename = f"myfilings_{item_type}_{item_number:03d}{file_extension}"
                        filepath = self.download_dir / filename
                        
                        # Save file
                        async with aiofiles.open(filepath, 'wb') as f:
                            async for chunk in response.content.iter_chunked(8192):
                                await f.write(chunk)
                        
                        file_size = filepath.stat().st_size
                        self.logger.info(f"Downloaded: {filename} ({file_size} bytes)")
                        
                        return {
                            'success': True,
                            'filename': filename,
                            'filepath': str(filepath),
                            'size': file_size,
                            'url': download_url,
                            'type': item_type
                        }
                    else:
                        error_msg = f"HTTP {response.status} for {download_url}"
                        self.logger.error(error_msg)
                        return {'success': False, 'error': error_msg}
                        
            except Exception as e:
                error_msg = f"Failed to download {item_type} {item_number}: {str(e)}"
                self.logger.error(error_msg)
                return {'success': False, 'error': error_msg}
    
    def get_file_extension(self, content_type: str, url: str) -> str:
        """Determine file extension from content type or URL"""
        # Check content type first
        if 'pdf' in content_type.lower():
            return '.pdf'
        elif 'image/jpeg' in content_type.lower() or 'image/jpg' in content_type.lower():
            return '.jpg'
        elif 'image/png' in content_type.lower():
            return '.png'
        elif 'image/gif' in content_type.lower():
            return '.gif'
        elif 'text/html' in content_type.lower():
            return '.html'
        elif 'application/json' in content_type.lower():
            return '.json'
        
        # Check URL extension
        if url:
            url_lower = url.lower()
            if '.pdf' in url_lower:
                return '.pdf'
            elif '.jpg' in url_lower or '.jpeg' in url_lower:
                return '.jpg'
            elif '.png' in url_lower:
                return '.png'
            elif '.gif' in url_lower:
                return '.gif'
            elif '.html' in url_lower or '.htm' in url_lower:
                return '.html'
            elif '.json' in url_lower:
                return '.json'
        
        # Default to .pdf for court documents
        return '.pdf'
    
    async def close(self):
        """Clean up browser resources"""
        if self.page:
            await self.page.close()
        if self.browser:
            await self.browser.close()
        if hasattr(self, 'playwright'):
            await self.playwright.stop()

# Convenience function
async def scrape_my_filings(username: str, password: str, headless: bool = False, download_dir: str = None) -> Dict:
    """
    Convenience function to scrape all My Filings
    
    Args:
        username: E-filing login username
        password: E-filing login password  
        headless: Whether to run browser in headless mode
        download_dir: Directory to save downloaded files
        
    Returns:
        Complete results dictionary with all filings and downloads
    """
    scraper = MyFilingsScraper(username, password, headless, download_dir)
    
    try:
        await scraper.start_browser()
        results = await scraper.scrape_all_my_filings()
        return results
    finally:
        await scraper.close()

# Example usage
if __name__ == "__main__":
    async def main():
        results = await scrape_my_filings(
            username="your_username",
            password="your_password", 
            headless=False,
            download_dir="myfilings_downloads"
        )
        
        print(f"Total filings found: {results.get('total_filings', 0)}")
        print(f"Total pages: {results.get('total_pages', 0)}")
        print(f"Total records: {results.get('total_records', 0)}")
        
        # Save results
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"myfilings_results_{timestamp}.json"
        
        with open(filename, 'w') as f:
            json.dump(results, f, indent=2)
        
        print(f"Results saved to: {filename}")
    
    asyncio.run(main())