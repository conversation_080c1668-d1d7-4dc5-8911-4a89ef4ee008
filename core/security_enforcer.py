"""
Security Enforcement Module for Legal Case Management System
Ensures no data is stored or transmitted without proper encryption.
Implements the "encrypt everything" security policy.
"""

import os
import ssl
import logging
from typing import Dict, Any, Optional, Callable
from functools import wraps
from pathlib import Path
from urllib.parse import urlparse
import socket

# Security imports
try:
    from cryptography.fernet import <PERSON><PERSON><PERSON>
    from security.pgp_manager import PGPKeyManager, DocumentEncryption
except ImportError as e:
    logging.error(f"Critical security modules not available: {e}")
    raise

logger = logging.getLogger(__name__)

class SecurityPolicy:
    """Defines and enforces security policies"""
    
    # Security requirements
    REQUIRE_SSL_TLS = True
    REQUIRE_PGP_ENCRYPTION = True
    BLOCK_UNENCRYPTED_STORAGE = True
    BLOCK_UNENCRYPTED_TRANSMISSION = True
    MINIMUM_TLS_VERSION = ssl.TLSVersion.TLSv1_2
    
    @classmethod
    def get_policy_violations(cls) -> Dict[str, bool]:
        """Check for any policy violations in current environment"""
        violations = {}
        
        # Check if SSL context can be created
        try:
            context = ssl.create_default_context()
            context.minimum_version = cls.MINIMUM_TLS_VERSION
            violations['ssl_context'] = False
        except Exception:
            violations['ssl_context'] = True
        
        # Check if PGP components are available
        try:
            import gnupg
            violations['pgp_missing'] = False
        except ImportError:
            violations['pgp_missing'] = True
        
        return violations

class SecureDataHandler:
    """Enforces encryption for all data operations"""
    
    def __init__(self, key_manager: PGPKeyManager, doc_encryption: DocumentEncryption):
        self.key_manager = key_manager
        self.doc_encryption = doc_encryption
        
    def enforce_encrypted_storage(self, func: Callable) -> Callable:
        """Decorator to ensure all storage operations are encrypted"""
        @wraps(func)
        def wrapper(*args, **kwargs):
            # Check if data is being stored
            if 'content' in kwargs or (args and isinstance(args[0], (str, bytes))):
                logger.info("Enforcing PGP encryption for data storage")
                
                # Ensure case_id is available for encryption
                case_id = kwargs.get('case_id') or kwargs.get('case')
                if not case_id:
                    raise SecurityError("Cannot store data without case_id for encryption")
                
                # If raw content is being stored, encrypt it first
                if 'content' in kwargs and not kwargs.get('encrypted', False):
                    content = kwargs['content']
                    if isinstance(content, str):
                        content = content.encode('utf-8')
                    
                    document_id = kwargs.get('document_id', f"doc_{hash(content)}")
                    encrypted_data = self.doc_encryption.encrypt_document(
                        content, str(case_id), document_id
                    )
                    kwargs['content'] = encrypted_data
                    kwargs['encrypted'] = True
            
            return func(*args, **kwargs)
        return wrapper
    
    def enforce_secure_transmission(self, func: Callable) -> Callable:
        """Decorator to ensure all network transmissions use SSL/TLS"""
        @wraps(func)
        def wrapper(*args, **kwargs):
            # Check for URL parameters
            url = kwargs.get('url') or (args and isinstance(args[0], str) and '://' in args[0])
            
            if url:
                parsed_url = urlparse(str(url))
                if parsed_url.scheme not in ['https', 'ftps', 'sftp']:
                    raise SecurityError(f"Unencrypted transmission blocked: {parsed_url.scheme}://")
                
                logger.info(f"Secure transmission validated: {parsed_url.scheme}")
            
            # Ensure SSL context is used
            if 'ssl_context' not in kwargs and url:
                context = ssl.create_default_context()
                context.minimum_version = SecurityPolicy.MINIMUM_TLS_VERSION
                kwargs['ssl_context'] = context
            
            return func(*args, **kwargs)
        return wrapper

class SecurityEnforcer:
    """Main security enforcement class"""
    
    def __init__(self, key_manager: PGPKeyManager, doc_encryption: DocumentEncryption):
        self.data_handler = SecureDataHandler(key_manager, doc_encryption)
        self.violations = []
    
    def validate_startup_security(self) -> Dict[str, Any]:
        """Validate security requirements at application startup"""
        logger.info("🔒 Validating security requirements...")
        
        validation_results = {
            'ssl_tls_available': False,
            'pgp_available': False,
            'vault_available': False,
            'certificates_valid': False,
            'security_violations': [],
            'startup_allowed': False
        }
        
        # Check SSL/TLS availability
        try:
            context = ssl.create_default_context()
            context.minimum_version = SecurityPolicy.MINIMUM_TLS_VERSION
            validation_results['ssl_tls_available'] = True
            logger.info("✅ SSL/TLS support validated")
        except Exception as e:
            validation_results['security_violations'].append(f"SSL/TLS not available: {e}")
            logger.error(f"❌ SSL/TLS validation failed: {e}")
        
        # Check PGP availability
        try:
            import gnupg
            if self.data_handler.key_manager.gpg:
                validation_results['pgp_available'] = True
                logger.info("✅ PGP encryption validated")
        except Exception as e:
            validation_results['security_violations'].append(f"PGP not available: {e}")
            logger.error(f"❌ PGP validation failed: {e}")
        
        # Check Vault availability
        try:
            import hvac
            validation_results['vault_available'] = True
            logger.info("✅ Vault integration available")
        except ImportError:
            logger.warning("⚠️  Vault integration not available (optional)")
        
        # Check certificates
        cert_dir = Path("/etc/ssl/legal-cms/certs")
        if cert_dir.exists() and any(cert_dir.glob("*.crt")):
            validation_results['certificates_valid'] = True
            logger.info("✅ SSL certificates found")
        else:
            validation_results['security_violations'].append("SSL certificates not found")
            logger.warning("⚠️  SSL certificates not found - run deployment script")
        
        # Determine if startup is allowed
        critical_requirements = [
            validation_results['ssl_tls_available'],
            validation_results['pgp_available']
        ]
        
        validation_results['startup_allowed'] = all(critical_requirements)
        
        if validation_results['startup_allowed']:
            logger.info("🔒 Security validation PASSED - startup allowed")
        else:
            logger.error("🚫 Security validation FAILED - startup blocked")
            logger.error("Violations: " + ", ".join(validation_results['security_violations']))
        
        return validation_results
    
    def block_insecure_operations(self):
        """Block operations that don't meet security requirements"""
        if SecurityPolicy.BLOCK_UNENCRYPTED_STORAGE:
            logger.info("🔒 Blocking unencrypted storage operations")
        
        if SecurityPolicy.BLOCK_UNENCRYPTED_TRANSMISSION:
            logger.info("🔒 Blocking unencrypted network transmissions")
    
    def get_secure_decorators(self) -> Dict[str, Callable]:
        """Get security decorators for protecting functions"""
        return {
            'encrypted_storage': self.data_handler.enforce_encrypted_storage,
            'secure_transmission': self.data_handler.enforce_secure_transmission
        }

class SecurityError(Exception):
    """Raised when security policies are violated"""
    pass

# Factory function for easy initialization
def create_security_enforcer(key_manager: PGPKeyManager, doc_encryption: DocumentEncryption) -> SecurityEnforcer:
    """Create security enforcer instance"""
    return SecurityEnforcer(key_manager, doc_encryption)

# Startup security check function
def perform_startup_security_check() -> bool:
    """Perform security check and return whether startup should proceed"""
    try:
        from security.pgp_manager import create_pgp_manager
        
        # Create minimal PGP manager for security check
        key_manager, doc_encryption = create_pgp_manager()
        enforcer = create_security_enforcer(key_manager, doc_encryption)
        
        # Validate security requirements
        validation_results = enforcer.validate_startup_security()
        
        if not validation_results['startup_allowed']:
            print("\n🚫 SECURITY VALIDATION FAILED")
            print("The application cannot start due to missing security requirements:")
            for violation in validation_results['security_violations']:
                print(f"  ❌ {violation}")
            print("\nPlease run the deployment script to set up required security components.")
            return False
        
        print("🔒 Security validation passed - startup allowed")
        return True
        
    except Exception as e:
        print(f"🚫 Security check failed: {e}")
        return False