"""
Document Processing Module for Legal Case Management System
Handles file uploads, OCR, text extraction, and document analysis.
"""

import os
import hashlib
import tempfile
import logging
import requests
import base64
from typing import Dict, List, Optional, Tuple, Any
from pathlib import Path
import mimetypes
from datetime import datetime
import json

# Document processing imports
try:
    import PyPDF2
    import pytesseract
    from PIL import Image
    import docx
    import zipfile
    import pandas as pd
except ImportError as e:
    logging.warning(f"Some document processing libraries not available: {e}")

from core.database_managers import DatabaseManagerFactory

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DocumentProcessor:
    """Handles all document processing operations"""
    
    def __init__(self, upload_dir: str = "case_documents"):
        self.upload_dir = Path(upload_dir)
        self.upload_dir.mkdir(exist_ok=True)
        
        # Supported file types
        self.supported_types = {
            'pdf': ['.pdf'],
            'image': ['.png', '.jpg', '.jpeg', '.tiff', '.bmp', '.gif'],
            'document': ['.docx', '.doc', '.txt', '.rtf'],
            'spreadsheet': ['.xlsx', '.xls', '.csv'],
            'archive': ['.zip', '.rar', '.7z'],
            'other': []
        }
        
        # Maximum file sizes (in bytes)
        self.max_file_sizes = {
            'pdf': 50 * 1024 * 1024,      # 50MB
            'image': 20 * 1024 * 1024,    # 20MB
            'document': 25 * 1024 * 1024, # 25MB
            'spreadsheet': 15 * 1024 * 1024, # 15MB
            'archive': 200 * 1024 * 1024, # 200MB
            'other': 10 * 1024 * 1024     # 10MB
        }
    
    def get_file_type_category(self, filename: str) -> str:
        """Determine file type category from filename"""
        ext = Path(filename).suffix.lower()
        
        for category, extensions in self.supported_types.items():
            if ext in extensions:
                return category
        
        return 'other'
    
    def validate_file(self, filename: str, file_size: int) -> Tuple[bool, str]:
        """Validate file type and size"""
        category = self.get_file_type_category(filename)
        max_size = self.max_file_sizes.get(category, self.max_file_sizes['other'])
        
        if file_size > max_size:
            return False, f"File too large. Maximum size for {category} files: {max_size // (1024*1024)}MB"
        
        # Check for potentially dangerous files
        dangerous_extensions = ['.exe', '.bat', '.cmd', '.scr', '.vbs', '.js']
        if Path(filename).suffix.lower() in dangerous_extensions:
            return False, "File type not allowed for security reasons"
        
        return True, "File is valid"
    
    def generate_safe_filename(self, original_filename: str, case_id: int) -> str:
        """Generate safe filename for storage"""
        # Create hash of original filename + timestamp for uniqueness
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        name_hash = hashlib.md5(original_filename.encode()).hexdigest()[:8]
        
        # Keep original extension
        ext = Path(original_filename).suffix.lower()
        
        # Create safe filename
        safe_name = f"case_{case_id}_{timestamp}_{name_hash}{ext}"
        
        return safe_name
    
    def save_uploaded_file(self, uploaded_file, case_id: int) -> Tuple[bool, str, Dict]:
        """Save uploaded file and return file info"""
        try:
            # Validate file
            is_valid, message = self.validate_file(uploaded_file.name, uploaded_file.size)
            if not is_valid:
                return False, message, {}
            
            # Generate safe filename
            safe_filename = self.generate_safe_filename(uploaded_file.name, case_id)
            file_path = self.upload_dir / safe_filename
            
            # Save file
            with open(file_path, "wb") as f:
                f.write(uploaded_file.getbuffer())
            
            # Get file info
            file_info = {
                'filename': safe_filename,
                'original_filename': uploaded_file.name,
                'file_type': self.get_file_type_category(uploaded_file.name),
                'file_size': uploaded_file.size,
                'mime_type': uploaded_file.type,
                'file_path': str(file_path)
            }
            
            logger.info(f"Saved file: {uploaded_file.name} as {safe_filename}")
            return True, "File saved successfully", file_info
            
        except Exception as e:
            logger.error(f"Error saving file {uploaded_file.name}: {e}")
            return False, f"Error saving file: {str(e)}", {}
    
    def extract_text_from_pdf(self, file_path: str) -> Tuple[bool, str]:
        """Extract text from PDF file"""
        try:
            text_content = ""
            
            with open(file_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                
                for page_num, page in enumerate(pdf_reader.pages):
                    try:
                        page_text = page.extract_text()
                        if page_text.strip():
                            text_content += f"\n--- Page {page_num + 1} ---\n"
                            text_content += page_text
                    except Exception as e:
                        logger.warning(f"Error extracting text from page {page_num + 1}: {e}")
                        continue
            
            if text_content.strip():
                return True, text_content
            else:
                return False, "No text found in PDF"
                
        except Exception as e:
            logger.error(f"Error extracting text from PDF {file_path}: {e}")
            return False, f"Error processing PDF: {str(e)}"
    
    def extract_text_from_image(self, file_path: str) -> Tuple[bool, str]:
        """Extract text from image using OCR"""
        try:
            # Open image
            image = Image.open(file_path)
            
            # Convert to RGB if necessary
            if image.mode != 'RGB':
                image = image.convert('RGB')
            
            # Perform OCR
            text_content = pytesseract.image_to_string(image)
            
            if text_content.strip():
                return True, text_content
            else:
                return False, "No text found in image"
                
        except Exception as e:
            logger.error(f"Error extracting text from image {file_path}: {e}")
            return False, f"Error processing image: {str(e)}"
    
    def extract_text_from_docx(self, file_path: str) -> Tuple[bool, str]:
        """Extract text from DOCX file"""
        try:
            doc = docx.Document(file_path)
            text_content = ""
            
            for paragraph in doc.paragraphs:
                text_content += paragraph.text + "\n"
            
            # Extract text from tables
            for table in doc.tables:
                for row in table.rows:
                    for cell in row.cells:
                        text_content += cell.text + "\t"
                    text_content += "\n"
            
            if text_content.strip():
                return True, text_content
            else:
                return False, "No text found in document"
                
        except Exception as e:
            logger.error(f"Error extracting text from DOCX {file_path}: {e}")
            return False, f"Error processing DOCX: {str(e)}"
    
    def extract_text_from_txt(self, file_path: str) -> Tuple[bool, str]:
        """Extract text from plain text file"""
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as file:
                text_content = file.read()
            
            if text_content.strip():
                return True, text_content
            else:
                return False, "File is empty"
                
        except Exception as e:
            logger.error(f"Error reading text file {file_path}: {e}")
            return False, f"Error reading file: {str(e)}"
    
    def extract_text_from_csv(self, file_path: str) -> Tuple[bool, str]:
        """Extract text from CSV file"""
        try:
            df = pd.read_csv(file_path)
            text_content = df.to_string()
            
            if text_content.strip():
                return True, text_content
            else:
                return False, "CSV file is empty"
                
        except Exception as e:
            logger.error(f"Error reading CSV file {file_path}: {e}")
            return False, f"Error reading CSV: {str(e)}"
    
    def process_archive(self, file_path: str, case_id: int) -> Tuple[bool, str, List[Dict]]:
        """Process archive file and extract contents"""
        try:
            extracted_files = []
            
            with tempfile.TemporaryDirectory() as temp_dir:
                # Extract archive
                if file_path.endswith('.zip'):
                    with zipfile.ZipFile(file_path, 'r') as zip_ref:
                        zip_ref.extractall(temp_dir)
                else:
                    return False, "Unsupported archive format", []
                
                # Process extracted files
                for root, dirs, files in os.walk(temp_dir):
                    for file in files:
                        extracted_path = os.path.join(root, file)
                        
                        # Skip hidden files and directories
                        if file.startswith('.'):
                            continue
                        
                        try:
                            # Copy to upload directory
                            safe_filename = self.generate_safe_filename(file, case_id)
                            dest_path = self.upload_dir / safe_filename
                            
                            with open(extracted_path, 'rb') as src, open(dest_path, 'wb') as dst:
                                dst.write(src.read())
                            
                            # Get file info
                            file_size = os.path.getsize(dest_path)
                            file_info = {
                                'filename': safe_filename,
                                'original_filename': file,
                                'file_type': self.get_file_type_category(file),
                                'file_size': file_size,
                                'file_path': str(dest_path),
                                'extracted_from': os.path.basename(file_path)
                            }
                            
                            extracted_files.append(file_info)
                            
                        except Exception as e:
                            logger.warning(f"Error processing extracted file {file}: {e}")
                            continue
            
            return True, f"Extracted {len(extracted_files)} files", extracted_files
            
        except Exception as e:
            logger.error(f"Error processing archive {file_path}: {e}")
            return False, f"Error processing archive: {str(e)}", []
    
    def extract_text_content(self, file_path: str, file_type: str) -> Tuple[bool, str]:
        """Extract text content based on file type"""
        file_path_obj = Path(file_path)
        
        if not file_path_obj.exists():
            return False, "File not found"
        
        # Determine extraction method based on file type
        if file_type == 'pdf':
            return self.extract_text_from_pdf(file_path)
        elif file_type == 'image':
            return self.extract_text_from_image(file_path)
        elif file_path.endswith('.docx'):
            return self.extract_text_from_docx(file_path)
        elif file_path.endswith(('.txt', '.rtf')):
            return self.extract_text_from_txt(file_path)
        elif file_path.endswith('.csv'):
            return self.extract_text_from_csv(file_path)
        else:
            return False, f"Text extraction not supported for file type: {file_type}"
    
    def process_document(self, case_id: int, file_info: Dict, uploaded_by: int = None) -> Dict:
        """Process a document and extract all relevant information"""
        try:
            file_path = file_info['file_path']
            file_type = file_info['file_type']
            
            # Extract text content
            text_extracted, text_content = self.extract_text_content(file_path, file_type)
            
            # Generate content preview
            content_preview = ""
            if text_extracted and text_content:
                # Create preview (first 500 characters)
                content_preview = text_content[:500] + "..." if len(text_content) > 500 else text_content
            
            # Create metadata
            metadata = {
                'processed_at': datetime.now().isoformat(),
                'text_extracted': text_extracted,
                'text_length': len(text_content) if text_content else 0,
                'mime_type': file_info.get('mime_type'),
                'extraction_method': self._get_extraction_method(file_type)
            }
            
            # Save document to database
            doc_id = db_manager.add_document(
                case_id=case_id,
                filename=file_info['filename'],
                original_filename=file_info['original_filename'],
                file_type=file_type,
                file_size=file_info['file_size'],
                uploaded_by=uploaded_by,
                content_preview=content_preview,
                document_type=self._classify_document_type(file_info['original_filename'], text_content),
                ocr_text=text_content if text_extracted else None,
                metadata=json.dumps(metadata)
            )
            
            # Update processing status
            db_manager.update_document(doc_id, is_processed=True)
            
            logger.info(f"Processed document: {file_info['original_filename']} (ID: {doc_id})")
            
            return {
                'success': True,
                'document_id': doc_id,
                'text_extracted': text_extracted,
                'text_content': text_content,
                'content_preview': content_preview,
                'metadata': metadata
            }
            
        except Exception as e:
            logger.error(f"Error processing document {file_info.get('filename', 'unknown')}: {e}")
            return {
                'success': False,
                'error': str(e),
                'document_id': None
            }
    
    def _get_extraction_method(self, file_type: str) -> str:
        """Get the extraction method used for file type"""
        methods = {
            'pdf': 'PyPDF2',
            'image': 'Tesseract OCR',
            'document': 'python-docx',
            'spreadsheet': 'pandas',
            'other': 'text_reader'
        }
        return methods.get(file_type, 'unknown')
    
    def _classify_document_type(self, filename: str, text_content: str = None) -> str:
        """Classify document type based on filename and content"""
        filename_lower = filename.lower()
        
        # Legal document types based on filename
        if any(keyword in filename_lower for keyword in ['motion', 'pleading', 'brief']):
            return 'motion'
        elif any(keyword in filename_lower for keyword in ['contract', 'agreement']):
            return 'contract'
        elif any(keyword in filename_lower for keyword in ['evidence', 'exhibit']):
            return 'evidence'
        elif any(keyword in filename_lower for keyword in ['correspondence', 'letter', 'email']):
            return 'correspondence'
        elif any(keyword in filename_lower for keyword in ['discovery', 'deposition']):
            return 'discovery'
        elif any(keyword in filename_lower for keyword in ['court', 'order', 'judgment']):
            return 'court_document'
        
        # Content-based classification (if text is available)
        if text_content:
            text_lower = text_content.lower()
            if any(keyword in text_lower for keyword in ['whereas', 'party of the first part', 'agreement']):
                return 'contract'
            elif any(keyword in text_lower for keyword in ['motion for', 'respectfully requests']):
                return 'motion'
            elif any(keyword in text_lower for keyword in ['exhibit', 'evidence']):
                return 'evidence'
        
        return 'general'
    
    def get_document_stats(self, case_id: int) -> Dict:
        """Get document statistics for a case"""
        documents = db_manager.get_case_documents(case_id)
        
        stats = {
            'total_documents': len(documents),
            'total_size': sum(doc['file_size'] or 0 for doc in documents),
            'by_type': {},
            'processed_count': 0,
            'with_text': 0
        }
        
        for doc in documents:
            # Count by type
            doc_type = doc['file_type'] or 'unknown'
            stats['by_type'][doc_type] = stats['by_type'].get(doc_type, 0) + 1
            
            # Count processed documents
            if doc['is_processed']:
                stats['processed_count'] += 1
            
            # Count documents with extracted text
            if doc['ocr_text']:
                stats['with_text'] += 1
        
        return stats
    
    def delete_document_file(self, filename: str) -> bool:
        """Delete document file from storage"""
        try:
            file_path = self.upload_dir / filename
            if file_path.exists():
                file_path.unlink()
                logger.info(f"Deleted file: {filename}")
                return True
            else:
                logger.warning(f"File not found for deletion: {filename}")
                return False
        except Exception as e:
            logger.error(f"Error deleting file {filename}: {e}")
            return False

# Global document processor instance
document_processor = DocumentProcessor()


class GitHubDocumentBackup:
    """
    Handles backing up legal documents to GitHub repository
    """

    def __init__(self, repo_owner: str = "george-shepov", repo_name: str = "lcm-documents", token: str = None):
        self.repo_owner = repo_owner
        self.repo_name = repo_name
        self.token = token
        self.base_url = f"https://api.github.com/repos/{repo_owner}/{repo_name}"

    def upload_document(self, file_path: str, document_number: str, case_id: str) -> Dict[str, Any]:
        """Upload document to GitHub repository"""

        if not self.token:
            return {"success": False, "error": "GitHub token not configured"}

        try:
            # Read file content
            with open(file_path, 'rb') as f:
                file_content = f.read()

            # Encode content as base64
            encoded_content = base64.b64encode(file_content).decode('utf-8')

            # Create logical filename
            filename = Path(file_path).name
            logical_path = f"cases/{case_id}/documents/{document_number:0>3}_{filename}"

            # Check if file already exists
            existing_file = self._get_file_info(logical_path)

            # Prepare API request
            headers = {
                'Authorization': f'token {self.token}',
                'Accept': 'application/vnd.github.v3+json'
            }

            data = {
                'message': f'Add document #{document_number}: {filename}',
                'content': encoded_content,
                'branch': 'main'
            }

            # If file exists, include SHA for update
            if existing_file:
                data['sha'] = existing_file['sha']
                data['message'] = f'Update document #{document_number}: {filename}'

            # Upload to GitHub
            response = requests.put(
                f"{self.base_url}/contents/{logical_path}",
                headers=headers,
                json=data
            )

            if response.status_code in [200, 201]:
                result = response.json()
                return {
                    "success": True,
                    "github_url": result['content']['html_url'],
                    "download_url": result['content']['download_url'],
                    "sha": result['content']['sha'],
                    "path": logical_path
                }
            else:
                return {
                    "success": False,
                    "error": f"GitHub API error: {response.status_code} - {response.text}"
                }

        except Exception as e:
            return {"success": False, "error": f"Upload failed: {str(e)}"}

    def download_document(self, document_path: str) -> Optional[bytes]:
        """Download document from GitHub"""
        try:
            headers = {
                'Authorization': f'token {self.token}',
                'Accept': 'application/vnd.github.v3+json'
            }

            # Get file info first
            file_info = self._get_file_info(document_path)
            if not file_info:
                return None

            # Download raw content
            response = requests.get(file_info['download_url'])

            if response.status_code == 200:
                return response.content
            else:
                return None

        except Exception as e:
            logging.error(f"Download failed: {e}")
            return None


# Global GitHub backup instance
github_backup = GitHubDocumentBackup()
