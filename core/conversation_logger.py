#!/usr/bin/env python3
"""
Comprehensive Conversation Logger for Legal Team
Saves all conversations, AI analysis, and legal insights to MongoDB
Provides complete audit trail for legal proceedings
"""

import os
import json
import logging
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Any, Optional
from pathlib import Path
import hashlib

class LegalConversationLogger:
    """
    Comprehensive conversation logging system for legal team access
    Stores conversations, AI insights, case analysis, and tool usage
    """
    
    def __init__(self, case_number: str = "DR-25-403973"):
        self.case_number = case_number
        self.logger = logging.getLogger(__name__)
        
        # MongoDB connection
        self.db_client = None
        self.db = None
        self.conversations_collection = None
        self.legal_insights_collection = None
        self.tool_usage_collection = None
        
        self.setup_logging()
        self.connect_mongodb()
    
    def setup_logging(self):
        """Setup logging for conversation system"""
        log_dir = Path("conversation_logs")
        log_dir.mkdir(exist_ok=True)
        
        handler = logging.FileHandler(log_dir / "legal_conversations.log")
        formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
        handler.setFormatter(formatter)
        self.logger.addHandler(handler)
        self.logger.setLevel(logging.INFO)
    
    def connect_mongodb(self) -> bool:
        """Connect to MongoDB for conversation storage"""
        try:
            from pymongo import MongoClient
            
            # Connect to legal-cms-mongodb container
            mongodb_password = os.getenv('MONGODB_PASSWORD', 'YXmJFXbFgqyt7nMsn9Aercox0fPUprDKoLJ67cvTaE')
            mongo_url = os.getenv('MONGODB_URL', 
                f'mongodb://admin:{mongodb_password}@localhost:27018/legal_cms_documents?authSource=admin')
            
            self.db_client = MongoClient(mongo_url)
            self.db = self.db_client['legal_cms_documents']
            
            # Create collections for legal team access
            self.conversations_collection = self.db['legal_conversations']
            self.legal_insights_collection = self.db['legal_ai_insights']  
            self.tool_usage_collection = self.db['legal_tool_usage']
            
            # Create indexes for efficient querying
            self.conversations_collection.create_index([
                ("case_number", 1),
                ("session_id", 1), 
                ("timestamp", -1)
            ])
            
            self.legal_insights_collection.create_index([
                ("case_number", 1),
                ("insight_type", 1),
                ("timestamp", -1)
            ])
            
            self.tool_usage_collection.create_index([
                ("case_number", 1),
                ("tool_name", 1),
                ("timestamp", -1)
            ])
            
            self.logger.info("✅ Connected to MongoDB for legal conversation logging")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ MongoDB connection failed: {e}")
            return False
    
    def log_conversation_exchange(self, user_message: str, ai_response: str, 
                                context: Dict[str, Any] = None, session_id: str = None) -> str:
        """
        Log a complete conversation exchange for legal team review
        """
        if self.conversations_collection is None:
            self.logger.warning("MongoDB not available for conversation logging")
            return None
        
        exchange_id = hashlib.md5(f"{datetime.now().isoformat()}_{user_message[:100]}".encode()).hexdigest()[:12]
        
        conversation_record = {
            'exchange_id': exchange_id,
            'case_number': self.case_number,
            'session_id': session_id or 'unknown',
            'timestamp': datetime.now(timezone.utc),
            'user_message': {
                'content': user_message,
                'length': len(user_message),
                'keywords': self.extract_legal_keywords(user_message)
            },
            'ai_response': {
                'content': ai_response,
                'length': len(ai_response),
                'keywords': self.extract_legal_keywords(ai_response),
                'tools_used': context.get('tools_used', []) if context else []
            },
            'context': {
                'current_tab': context.get('current_tab') if context else None,
                'case_documents_count': context.get('documents_count') if context else 0,
                'active_features': context.get('active_features', []) if context else [],
                'system_status': context.get('system_status', {}) if context else {}
            },
            'legal_metadata': {
                'contains_legal_advice': self.detect_legal_advice(ai_response),
                'mentions_case_law': self.detect_case_law_references(ai_response),
                'document_references': self.extract_document_references(user_message, ai_response),
                'procedural_guidance': self.detect_procedural_guidance(ai_response),
                'urgency_level': self.assess_urgency_level(user_message)
            },
            'privacy_classification': self.classify_privacy_level(user_message, ai_response),
            'retention_policy': 'legal_team_access',
            'searchable_content': f"{user_message} {ai_response}".lower(),
            'word_count': len(user_message.split()) + len(ai_response.split())
        }
        
        try:
            result = self.conversations_collection.insert_one(conversation_record)
            self.logger.info(f"✅ Logged conversation exchange {exchange_id} for legal team")
            
            # Also extract and store any legal insights
            self.extract_and_store_legal_insights(exchange_id, user_message, ai_response, context)
            
            return str(result.inserted_id)
            
        except Exception as e:
            self.logger.error(f"❌ Failed to log conversation: {e}")
            return None
    
    def extract_legal_keywords(self, text: str) -> List[str]:
        """Extract legal keywords from text"""
        legal_keywords = [
            'motion', 'objection', 'filing', 'docket', 'court', 'judge', 'attorney', 'counsel',
            'discovery', 'deposition', 'subpoena', 'hearing', 'trial', 'appeal', 'ruling',
            'complaint', 'answer', 'counterclaim', 'evidence', 'testimony', 'witness',
            'affidavit', 'stipulation', 'settlement', 'judgment', 'order', 'injunction',
            'custody', 'visitation', 'support', 'alimony', 'divorce', 'separation',
            'property', 'asset', 'liability', 'debt', 'financial', 'income'
        ]
        
        text_lower = text.lower()
        found_keywords = [keyword for keyword in legal_keywords if keyword in text_lower]
        
        return found_keywords
    
    def detect_legal_advice(self, text: str) -> bool:
        """Detect if text contains legal advice"""
        advice_indicators = [
            'you should', 'i recommend', 'you must', 'you need to file',
            'the best strategy', 'you have grounds', 'strong case',
            'likely to succeed', 'advised to', 'suggest filing'
        ]
        
        text_lower = text.lower()
        return any(indicator in text_lower for indicator in advice_indicators)
    
    def detect_case_law_references(self, text: str) -> bool:
        """Detect case law or statute references"""
        law_indicators = [
            'v.', 'vs.', 'case law', 'precedent', 'statute', 'rule',
            'ohio revised code', 'orc', 'federal rule', 'supreme court'
        ]
        
        text_lower = text.lower()
        return any(indicator in text_lower for indicator in law_indicators)
    
    def extract_document_references(self, user_msg: str, ai_msg: str) -> List[str]:
        """Extract references to specific documents"""
        combined_text = f"{user_msg} {ai_msg}"
        
        # Look for document patterns
        import re
        document_patterns = [
            r'document[s]?\s+#?\d+',
            r'exhibit\s+[A-Z\d]+',
            r'filing\s+#?\d+',
            r'motion\s+#?\d+',
            r'docket\s+#?\d+',
            r'submission\s+#?\d+'
        ]
        
        references = []
        for pattern in document_patterns:
            matches = re.findall(pattern, combined_text, re.IGNORECASE)
            references.extend(matches)
        
        return list(set(references))
    
    def detect_procedural_guidance(self, text: str) -> bool:
        """Detect procedural guidance"""
        procedure_indicators = [
            'file with the court', 'serve the opposing party', 'deadline',
            'time limit', 'procedure', 'process', 'steps to',
            'how to file', 'required format', 'court rules'
        ]
        
        text_lower = text.lower()
        return any(indicator in text_lower for indicator in procedure_indicators)
    
    def assess_urgency_level(self, user_message: str) -> str:
        """Assess urgency level of user message"""
        urgent_keywords = ['urgent', 'asap', 'immediately', 'emergency', 'deadline', 'tomorrow']
        high_keywords = ['soon', 'quickly', 'important', 'critical', 'time sensitive']
        
        user_lower = user_message.lower()
        
        if any(keyword in user_lower for keyword in urgent_keywords):
            return 'urgent'
        elif any(keyword in user_lower for keyword in high_keywords):
            return 'high'
        else:
            return 'normal'
    
    def classify_privacy_level(self, user_msg: str, ai_msg: str) -> str:
        """Classify privacy level of conversation"""
        sensitive_patterns = [
            'social security', 'ssn', 'bank account', 'financial',
            'personal information', 'confidential', 'privileged',
            'attorney-client', 'settlement amount', 'strategy'
        ]
        
        combined_text = f"{user_msg} {ai_msg}".lower()
        
        if any(pattern in combined_text for pattern in sensitive_patterns):
            return 'confidential'
        elif 'legal' in combined_text or 'case' in combined_text:
            return 'legal_protected'
        else:
            return 'standard'
    
    def extract_and_store_legal_insights(self, exchange_id: str, user_message: str, 
                                       ai_response: str, context: Dict = None):
        """Extract and store legal insights for team analysis"""
        if self.legal_insights_collection is None:
            return
        
        insights = []
        
        # Case strategy insights
        if 'strategy' in ai_response.lower():
            insights.append({
                'type': 'case_strategy',
                'content': self.extract_strategy_content(ai_response),
                'confidence': 'medium'
            })
        
        # Document analysis insights  
        if 'document' in ai_response.lower() and 'analysis' in ai_response.lower():
            insights.append({
                'type': 'document_analysis',
                'content': self.extract_document_analysis(ai_response),
                'confidence': 'high'
            })
        
        # Legal procedure insights
        if self.detect_procedural_guidance(ai_response):
            insights.append({
                'type': 'procedural_guidance',
                'content': self.extract_procedural_content(ai_response),
                'confidence': 'high'
            })
        
        # Timeline and deadline insights
        if 'deadline' in ai_response.lower() or 'timeline' in ai_response.lower():
            insights.append({
                'type': 'timeline_management',
                'content': self.extract_timeline_content(ai_response),
                'confidence': 'high'
            })
        
        # Store insights
        for insight in insights:
            insight_record = {
                'exchange_id': exchange_id,
                'case_number': self.case_number,
                'timestamp': datetime.now(timezone.utc),
                'insight_type': insight['type'],
                'insight_content': insight['content'],
                'confidence_level': insight['confidence'],
                'source_user_message': user_message[:200],  # First 200 chars
                'source_ai_response': ai_response[:500],    # First 500 chars
                'context_tab': context.get('current_tab') if context else None,
                'legal_team_reviewed': False,
                'action_items': self.extract_action_items(ai_response),
                'priority': self.assess_insight_priority(insight['type'], insight['content'])
            }
            
            try:
                self.legal_insights_collection.insert_one(insight_record)
                self.logger.info(f"✅ Stored {insight['type']} insight for legal team")
            except Exception as e:
                self.logger.error(f"❌ Failed to store insight: {e}")
    
    def extract_strategy_content(self, text: str) -> str:
        """Extract strategy-related content"""
        # Simple extraction - could be enhanced with NLP
        sentences = text.split('.')
        strategy_sentences = [s.strip() for s in sentences if 'strategy' in s.lower()]
        return '. '.join(strategy_sentences[:3])  # First 3 strategy sentences
    
    def extract_document_analysis(self, text: str) -> str:
        """Extract document analysis content"""
        sentences = text.split('.')
        doc_sentences = [s.strip() for s in sentences if 'document' in s.lower()]
        return '. '.join(doc_sentences[:3])
    
    def extract_procedural_content(self, text: str) -> str:
        """Extract procedural guidance content"""
        sentences = text.split('.')
        proc_sentences = [s.strip() for s in sentences 
                         if any(word in s.lower() for word in ['file', 'procedure', 'step', 'process'])]
        return '. '.join(proc_sentences[:3])
    
    def extract_timeline_content(self, text: str) -> str:
        """Extract timeline and deadline content"""
        sentences = text.split('.')
        time_sentences = [s.strip() for s in sentences 
                         if any(word in s.lower() for word in ['deadline', 'timeline', 'date', 'time'])]
        return '. '.join(time_sentences[:3])
    
    def extract_action_items(self, text: str) -> List[str]:
        """Extract action items from AI response"""
        action_indicators = ['you should', 'need to', 'must', 'should file', 'recommend']
        
        sentences = text.split('.')
        actions = []
        
        for sentence in sentences:
            if any(indicator in sentence.lower() for indicator in action_indicators):
                actions.append(sentence.strip())
        
        return actions[:5]  # Top 5 action items
    
    def assess_insight_priority(self, insight_type: str, content: str) -> str:
        """Assess priority level of legal insight"""
        high_priority_types = ['timeline_management', 'procedural_guidance']
        urgent_keywords = ['deadline', 'urgent', 'immediately', 'critical']
        
        if insight_type in high_priority_types:
            return 'high'
        elif any(keyword in content.lower() for keyword in urgent_keywords):
            return 'urgent'
        else:
            return 'normal'
    
    def log_tool_usage(self, tool_name: str, tool_action: str, result_summary: str, 
                      success: bool = True, context: Dict = None):
        """Log tool usage for legal team audit trail"""
        if self.tool_usage_collection is None:
            return
        
        tool_record = {
            'case_number': self.case_number,
            'timestamp': datetime.now(timezone.utc),
            'tool_name': tool_name,
            'tool_action': tool_action,
            'result_summary': result_summary,
            'success': success,
            'context': context or {},
            'session_info': {
                'user_agent': context.get('user_agent') if context else None,
                'tab_active': context.get('current_tab') if context else None
            }
        }
        
        try:
            self.tool_usage_collection.insert_one(tool_record)
            self.logger.info(f"✅ Logged {tool_name} usage for legal audit trail")
        except Exception as e:
            self.logger.error(f"❌ Failed to log tool usage: {e}")
    
    def get_conversation_summary_for_legal_team(self, days: int = 30) -> Dict[str, Any]:
        """Generate conversation summary for legal team review"""
        if self.conversations_collection is None:
            return {'error': 'MongoDB not available'}
        
        try:
            # Date filter
            cutoff_date = datetime.now(timezone.utc) - timedelta(days=days)
            
            # Aggregate conversation data
            pipeline = [
                {'$match': {
                    'case_number': self.case_number,
                    'timestamp': {'$gte': cutoff_date}
                }},
                {'$group': {
                    '_id': '$case_number',
                    'total_exchanges': {'$sum': 1},
                    'total_words': {'$sum': '$word_count'},
                    'unique_sessions': {'$addToSet': '$session_id'},
                    'legal_advice_count': {'$sum': {'$cond': ['$legal_metadata.contains_legal_advice', 1, 0]}},
                    'urgent_matters': {'$sum': {'$cond': [{'$eq': ['$legal_metadata.urgency_level', 'urgent']}, 1, 0]}},
                    'document_references': {'$push': '$legal_metadata.document_references'}
                }}
            ]
            
            result = list(self.conversations_collection.aggregate(pipeline))
            
            if result:
                summary = result[0]
                summary['session_count'] = len(summary['unique_sessions'])
                summary['period_days'] = days
                summary['generated_at'] = datetime.now(timezone.utc)
                
                return summary
            else:
                return {'message': 'No conversations found for specified period'}
                
        except Exception as e:
            self.logger.error(f"❌ Failed to generate summary: {e}")
            return {'error': str(e)}
    
    def export_conversations_for_legal_review(self, output_format: str = 'json') -> str:
        """Export conversations for legal team review"""
        if self.conversations_collection is None:
            return None
        
        try:
            # Get all conversations for case
            conversations = list(self.conversations_collection.find(
                {'case_number': self.case_number},
                {'_id': 0}  # Exclude MongoDB _id field
            ).sort('timestamp', -1))
            
            # Export based on format
            export_dir = Path("legal_exports")
            export_dir.mkdir(exist_ok=True)
            
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            
            if output_format == 'json':
                filename = export_dir / f"legal_conversations_{self.case_number}_{timestamp}.json"
                with open(filename, 'w') as f:
                    json.dump(conversations, f, indent=2, default=str)
            
            elif output_format == 'csv':
                import csv
                filename = export_dir / f"legal_conversations_{self.case_number}_{timestamp}.csv"
                
                if conversations:
                    with open(filename, 'w', newline='') as f:
                        writer = csv.DictWriter(f, fieldnames=conversations[0].keys())
                        writer.writeheader()
                        writer.writerows(conversations)
            
            self.logger.info(f"✅ Exported {len(conversations)} conversations for legal team")
            return str(filename)
            
        except Exception as e:
            self.logger.error(f"❌ Export failed: {e}")
            return None

# Integration functions
def log_legal_conversation(user_message: str, ai_response: str, context: Dict = None, 
                         case_number: str = "DR-25-403973", session_id: str = None) -> str:
    """Log conversation for legal team access"""
    logger = LegalConversationLogger(case_number)
    return logger.log_conversation_exchange(user_message, ai_response, context, session_id)

def log_legal_tool_usage(tool_name: str, action: str, result: str, success: bool = True,
                        case_number: str = "DR-25-403973", context: Dict = None):
    """Log tool usage for legal audit trail"""
    logger = LegalConversationLogger(case_number)
    logger.log_tool_usage(tool_name, action, result, success, context)

def get_legal_team_summary(days: int = 30, case_number: str = "DR-25-403973") -> Dict:
    """Get conversation summary for legal team"""
    logger = LegalConversationLogger(case_number)
    return logger.get_conversation_summary_for_legal_team(days)

def export_for_legal_team(format: str = 'json', case_number: str = "DR-25-403973") -> str:
    """Export conversations for legal team review"""
    logger = LegalConversationLogger(case_number)
    return logger.export_conversations_for_legal_review(format)