#!/usr/bin/env python3
"""
Comprehensive Database Administration Center
Visual admin stats and controls for ALL databases:
- PostgreSQL (relational data)
- MongoDB (document storage) 
- Redis (cache/sessions)
- Qdrant (vector database)
- HashiCor<PERSON> Vault (secrets)
"""

import os
import json
import logging
import psutil
from datetime import datetime, timed<PERSON><PERSON>
from pathlib import Path
from typing import Dict, List, Any, Optional
import asyncio

class DatabaseAdministrator:
    """
    Comprehensive database administration and monitoring
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.admin_dir = Path("database_admin")
        self.admin_dir.mkdir(exist_ok=True)
        
        # Database connections
        self.connections = {}
        
        self.setup_logging()
    
    def setup_logging(self):
        """Setup admin logging"""
        handler = logging.FileHandler(self.admin_dir / "database_admin.log")
        formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
        handler.setFormatter(formatter)
        self.logger.addHandler(handler)
        self.logger.setLevel(logging.INFO)
    
    def get_postgresql_stats(self) -> Dict[str, Any]:
        """Get comprehensive PostgreSQL statistics"""
        try:
            import psycopg2
            from sqlalchemy import create_engine, text
            
            # Connect to PostgreSQL (legal-cms-postgresql container)
            pg_url = os.getenv('POSTGRES_URL', 'postgresql://legal_cms_user:cWO7LRL29U8tNfDgjG14RJSCA@localhost:5435/legal_cms_main?sslmode=prefer')
            engine = create_engine(pg_url)
            
            with engine.connect() as conn:
                # Database size and table stats
                db_size_query = """
                SELECT 
                    pg_size_pretty(pg_database_size(current_database())) as db_size,
                    pg_database_size(current_database()) as db_size_bytes
                """
                
                table_stats_query = """
                SELECT 
                    schemaname,
                    relname as tablename,
                    n_tup_ins as inserts,
                    n_tup_upd as updates,
                    n_tup_del as deletes,
                    n_live_tup as live_rows,
                    n_dead_tup as dead_rows,
                    pg_size_pretty(pg_total_relation_size(schemaname||'.'||relname)) as table_size
                FROM pg_stat_user_tables 
                ORDER BY n_live_tup DESC
                """
                
                connection_stats_query = """
                SELECT 
                    count(*) as total_connections,
                    count(case when state = 'active' then 1 end) as active_connections,
                    count(case when state = 'idle' then 1 end) as idle_connections
                FROM pg_stat_activity
                WHERE datname = current_database()
                """
                
                # Execute queries
                db_size_result = conn.execute(text(db_size_query)).fetchone()
                table_stats = conn.execute(text(table_stats_query)).fetchall()
                conn_stats = conn.execute(text(connection_stats_query)).fetchone()
                
                # Format results
                stats = {
                    'status': 'connected',
                    'database_size': db_size_result[0] if db_size_result else 'Unknown',
                    'database_size_bytes': db_size_result[1] if db_size_result else 0,
                    'total_connections': conn_stats[0] if conn_stats else 0,
                    'active_connections': conn_stats[1] if conn_stats else 0,
                    'idle_connections': conn_stats[2] if conn_stats else 0,
                    'tables': []
                }
                
                for row in table_stats:
                    stats['tables'].append({
                        'schema': row[0],
                        'name': row[1],
                        'inserts': row[2],
                        'updates': row[3],
                        'deletes': row[4],
                        'live_rows': row[5],
                        'dead_rows': row[6],
                        'size': row[7]
                    })
                
                # Cache usage stats
                cache_hit_query = """
                SELECT 
                    sum(heap_blks_hit) / (sum(heap_blks_hit) + sum(heap_blks_read)) * 100 as cache_hit_ratio
                FROM pg_statio_user_tables
                """
                
                cache_result = conn.execute(text(cache_hit_query)).fetchone()
                stats['cache_hit_ratio'] = round(cache_result[0], 2) if cache_result and cache_result[0] else 0
                
                return stats
                
        except Exception as e:
            self.logger.error(f"PostgreSQL stats error: {e}")
            return {'status': 'error', 'error': str(e)}
    
    def get_mongodb_stats(self) -> Dict[str, Any]:
        """Get comprehensive MongoDB statistics"""
        try:
            from pymongo import MongoClient
            
            # Connect to MongoDB (legal-cms-mongodb container)
            mongo_url = os.getenv('MONGODB_URL', '**********************************************************************************************')
            client = MongoClient(mongo_url)
            
            # Get database
            db_name = 'legal_cms_documents'
            db = client[db_name]
            
            # Database stats
            db_stats = db.command("dbStats")
            server_status = client.admin.command("serverStatus")
            
            # Collection stats with error handling
            collections = []
            for collection_name in db.list_collection_names():
                try:
                    collection = db[collection_name]
                    coll_stats = db.command("collStats", collection_name)
                    
                    collections.append({
                        'name': collection_name,
                        'documents': coll_stats.get('count', 0),
                        'size_bytes': coll_stats.get('size', 0),
                        'storage_size': coll_stats.get('storageSize', 0),
                        'indexes': coll_stats.get('nindexes', 0),
                        'avg_obj_size': coll_stats.get('avgObjSize', 0)
                    })
                except Exception as coll_error:
                    # Skip collections that can't be accessed or don't exist
                    self.logger.warning(f"Skipping collection {collection_name}: {coll_error}")
                    collections.append({
                        'name': collection_name,
                        'documents': 'N/A',
                        'size_bytes': 'N/A',
                        'storage_size': 'N/A',
                        'indexes': 'N/A',
                        'avg_obj_size': 'N/A',
                        'error': str(coll_error)
                    })
            
            stats = {
                'status': 'connected',
                'database_size_mb': round(db_stats.get('dataSize', 0) / (1024*1024), 2),
                'storage_size_mb': round(db_stats.get('storageSize', 0) / (1024*1024), 2),
                'index_size_mb': round(db_stats.get('indexSize', 0) / (1024*1024), 2),
                'collections_count': db_stats.get('collections', 0),
                'objects_count': db_stats.get('objects', 0),
                'collections': collections,
                'connections': server_status.get('connections', {}),
                'memory_usage_mb': round(server_status.get('mem', {}).get('resident', 0), 2),
                'uptime_seconds': server_status.get('uptime', 0)
            }
            
            client.close()
            return stats
            
        except Exception as e:
            self.logger.error(f"MongoDB stats error: {e}")
            return {'status': 'error', 'error': str(e)}
    
    def get_redis_advanced_stats(self) -> Dict[str, Any]:
        """Get advanced Redis statistics and cache analysis"""
        try:
            import redis
            
            # Connect to Redis
            redis_url = "redis://:QFVchUWhPsZDLtnM0NmrcWvdW@localhost:6382/0"
            client = redis.from_url(redis_url)
            
            # Get comprehensive info
            info = client.info()
            memory_info = client.info('memory')
            stats_info = client.info('stats')
            replication_info = client.info('replication')
            
            # Analyze keyspace
            keyspace_info = {}
            for db_key, db_info in info.items():
                if db_key.startswith('db'):
                    keyspace_info[db_key] = db_info
            
            # Sample keys for analysis
            keys_sample = client.keys('*')[:100]  # Sample first 100 keys
            key_types = {}
            key_sizes = {}
            
            for key in keys_sample[:10]:  # Analyze first 10 keys
                try:
                    key_type = client.type(key).decode()
                    key_types[key_type] = key_types.get(key_type, 0) + 1
                    
                    # Get key size (approximate)
                    if key_type == 'string':
                        key_sizes[key.decode()] = len(client.get(key) or b'')
                    elif key_type == 'hash':
                        key_sizes[key.decode()] = len(client.hgetall(key))
                    elif key_type == 'list':
                        key_sizes[key.decode()] = client.llen(key)
                    elif key_type == 'set':
                        key_sizes[key.decode()] = client.scard(key)
                except:
                    pass
            
            stats = {
                'status': 'connected',
                'version': info.get('redis_version', 'unknown'),
                'uptime_seconds': info.get('uptime_in_seconds', 0),
                'uptime_days': round(info.get('uptime_in_seconds', 0) / 86400, 2),
                
                # Memory stats
                'memory_used_mb': round(memory_info.get('used_memory', 0) / (1024*1024), 2),
                'memory_peak_mb': round(memory_info.get('used_memory_peak', 0) / (1024*1024), 2),
                'memory_rss_mb': round(memory_info.get('used_memory_rss', 0) / (1024*1024), 2),
                'memory_overhead_mb': round(memory_info.get('used_memory_overhead', 0) / (1024*1024), 2),
                
                # Connection stats
                'connected_clients': info.get('connected_clients', 0),
                'blocked_clients': info.get('blocked_clients', 0),
                'total_connections': stats_info.get('total_connections_received', 0),
                'rejected_connections': stats_info.get('rejected_connections', 0),
                
                # Performance stats
                'total_commands': stats_info.get('total_commands_processed', 0),
                'commands_per_sec': stats_info.get('instantaneous_ops_per_sec', 0),
                'keyspace_hits': stats_info.get('keyspace_hits', 0),
                'keyspace_misses': stats_info.get('keyspace_misses', 0),
                'hit_ratio': round((stats_info.get('keyspace_hits', 0) / 
                                 max(1, stats_info.get('keyspace_hits', 0) + stats_info.get('keyspace_misses', 0))) * 100, 2),
                
                # Key stats
                'total_keys': sum([db_info.get('keys', 0) for db_info in keyspace_info.values()]),
                'expired_keys': stats_info.get('expired_keys', 0),
                'evicted_keys': stats_info.get('evicted_keys', 0),
                
                # Key analysis
                'key_types': key_types,
                'sample_key_sizes': dict(list(key_sizes.items())[:10]),
                'keyspace_databases': keyspace_info,
                
                # Replication
                'role': replication_info.get('role', 'unknown'),
                'connected_slaves': replication_info.get('connected_slaves', 0)
            }
            
            return stats
            
        except Exception as e:
            self.logger.error(f"Redis advanced stats error: {e}")
            return {'status': 'error', 'error': str(e)}
    
    def get_qdrant_stats(self) -> Dict[str, Any]:
        """Get Qdrant vector database statistics"""
        try:
            import requests
            
            qdrant_url = "http://localhost:6334"
            
            # Get cluster info
            cluster_response = requests.get(f"{qdrant_url}/cluster", timeout=5)
            
            # Get collections info
            collections_response = requests.get(f"{qdrant_url}/collections", timeout=5)
            
            stats = {
                'status': 'connected',
                'cluster_info': cluster_response.json() if cluster_response.status_code == 200 else {},
                'collections': []
            }
            
            if collections_response.status_code == 200:
                collections_data = collections_response.json()
                
                for collection_name in collections_data.get('result', {}).get('collections', []):
                    # Get collection details
                    collection_response = requests.get(f"{qdrant_url}/collections/{collection_name['name']}", timeout=5)
                    
                    if collection_response.status_code == 200:
                        collection_info = collection_response.json().get('result', {})
                        
                        stats['collections'].append({
                            'name': collection_name['name'],
                            'vectors_count': collection_info.get('vectors_count', 0),
                            'indexed_vectors_count': collection_info.get('indexed_vectors_count', 0),
                            'points_count': collection_info.get('points_count', 0),
                            'segments_count': collection_info.get('segments_count', 0),
                            'config': collection_info.get('config', {})
                        })
            
            return stats
            
        except Exception as e:
            self.logger.error(f"Qdrant stats error: {e}")
            return {'status': 'error', 'error': str(e)}
    
    def get_vault_stats(self) -> Dict[str, Any]:
        """Get HashiCorp Vault statistics"""
        try:
            import requests
            from requests.exceptions import ConnectionError, RequestException
            
            vault_url = os.getenv('VAULT_ADDR', 'http://localhost:8200')
            vault_token = os.getenv('VAULT_TOKEN', '')
            
            headers = {'X-Vault-Token': vault_token} if vault_token else {}
            
            # Get health status with connection error handling
            try:
                health_response = requests.get(f"{vault_url}/v1/sys/health", timeout=3)
            except (ConnectionError, RequestException) as conn_error:
                self.logger.info(f"Vault not available: {conn_error}")
                return {
                    'status': 'unavailable', 
                    'error': f'Vault not running (optional service)',
                    'vault_url': vault_url,
                    'connection_error': str(conn_error)
                }
            
            stats = {
                'status': 'connected' if health_response.status_code in [200, 429, 472, 473] else 'error',
                'health': health_response.json() if health_response.status_code in [200, 429, 472, 473] else {},
            }
            
            if vault_token:
                # Get auth methods
                try:
                    auth_response = requests.get(f"{vault_url}/v1/sys/auth", headers=headers, timeout=5)
                    if auth_response.status_code == 200:
                        stats['auth_methods'] = auth_response.json().get('data', {})
                except:
                    pass
                
                # Get secret engines
                try:
                    mounts_response = requests.get(f"{vault_url}/v1/sys/mounts", headers=headers, timeout=5)
                    if mounts_response.status_code == 200:
                        stats['secret_engines'] = mounts_response.json().get('data', {})
                except:
                    pass
                
                # Get policies
                try:
                    policies_response = requests.get(f"{vault_url}/v1/sys/policies/acl", headers=headers, timeout=5)
                    if policies_response.status_code == 200:
                        stats['policies'] = policies_response.json().get('data', {}).get('policies', [])
                except:
                    pass
            
            return stats
            
        except Exception as e:
            self.logger.error(f"Vault stats error: {e}")
            return {'status': 'error', 'error': str(e)}
    
    def clear_redis_cache(self, pattern: str = "*") -> Dict[str, Any]:
        """Clear Redis cache with pattern matching"""
        try:
            import redis
            
            client = redis.from_url("redis://:QFVchUWhPsZDLtnM0NmrcWvdW@localhost:6382/0")
            
            if pattern == "*":
                # Clear all
                result = client.flushdb()
                cleared_count = "all"
            else:
                # Clear by pattern
                keys = client.keys(pattern)
                cleared_count = len(keys)
                if keys:
                    client.delete(*keys)
            
            return {
                'success': True,
                'cleared_keys': cleared_count,
                'pattern': pattern,
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"Redis cache clear error: {e}")
            return {'success': False, 'error': str(e)}
    
    def vacuum_postgresql(self) -> Dict[str, Any]:
        """Perform PostgreSQL maintenance operations"""
        try:
            from sqlalchemy import create_engine, text
            
            pg_url = os.getenv('POSTGRES_URL', 'postgresql://legal_cms_user:cWO7LRL29U8tNfDgjG14RJSCA@localhost:5435/legal_cms_main?sslmode=prefer')
            engine = create_engine(pg_url)
            
            results = []
            
            with engine.connect() as conn:
                # Get tables with dead rows
                dead_rows_query = """
                SELECT tablename, n_dead_tup 
                FROM pg_stat_user_tables 
                WHERE n_dead_tup > 0 
                ORDER BY n_dead_tup DESC
                """
                
                tables_with_dead_rows = conn.execute(text(dead_rows_query)).fetchall()
                
                for table_row in tables_with_dead_rows:
                    table_name = table_row[0]
                    dead_rows = table_row[1]
                    
                    try:
                        # Perform VACUUM ANALYZE on each table
                        conn.execute(text(f"VACUUM ANALYZE {table_name}"))
                        results.append({
                            'table': table_name,
                            'dead_rows_before': dead_rows,
                            'status': 'vacuumed'
                        })
                    except Exception as table_error:
                        results.append({
                            'table': table_name,
                            'status': 'error',
                            'error': str(table_error)
                        })
            
            return {
                'success': True,
                'tables_processed': len(results),
                'results': results,
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"PostgreSQL vacuum error: {e}")
            return {'success': False, 'error': str(e)}
    
    def optimize_mongodb_indexes(self) -> Dict[str, Any]:
        """Optimize MongoDB indexes"""
        try:
            from pymongo import MongoClient
            
            mongo_url = os.getenv('MONGODB_URL', '**********************************************************************************************')
            client = MongoClient(mongo_url)
            
            db = client['legal_cms_documents']
            results = []
            
            for collection_name in db.list_collection_names():
                collection = db[collection_name]
                
                # Get current indexes
                indexes = list(collection.list_indexes())
                
                # Reindex collection
                try:
                    db.command("reIndex", collection_name)
                    results.append({
                        'collection': collection_name,
                        'indexes_count': len(indexes),
                        'status': 'reindexed'
                    })
                except Exception as coll_error:
                    results.append({
                        'collection': collection_name,
                        'status': 'error',
                        'error': str(coll_error)
                    })
            
            client.close()
            
            return {
                'success': True,
                'collections_processed': len(results),
                'results': results,
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"MongoDB optimization error: {e}")
            return {'success': False, 'error': str(e)}
    
    def get_comprehensive_dashboard_data(self) -> Dict[str, Any]:
        """Get all database statistics for dashboard"""
        dashboard_data = {
            'timestamp': datetime.now().isoformat(),
            'databases': {
                'postgresql': self.get_postgresql_stats(),
                'mongodb': self.get_mongodb_stats(),
                'redis': self.get_redis_advanced_stats(),
                'qdrant': self.get_qdrant_stats(),
                'vault': self.get_vault_stats()
            }
        }
        
        # Calculate overall health
        connected_dbs = sum(1 for db_stats in dashboard_data['databases'].values() 
                          if db_stats.get('status') == 'connected')
        
        dashboard_data['overall_health'] = {
            'connected_databases': connected_dbs,
            'total_databases': len(dashboard_data['databases']),
            'health_percentage': round((connected_dbs / len(dashboard_data['databases'])) * 100, 1)
        }
        
        return dashboard_data

# Standalone functions for integration
def get_database_admin_data():
    """Get comprehensive database administration data"""
    admin = DatabaseAdministrator()
    return admin.get_comprehensive_dashboard_data()

def clear_cache_by_pattern(pattern: str = "*"):
    """Clear cache by pattern"""
    admin = DatabaseAdministrator()
    return admin.clear_redis_cache(pattern)

def perform_database_maintenance():
    """Perform maintenance on all databases"""
    admin = DatabaseAdministrator()
    results = {
        'postgresql_vacuum': admin.vacuum_postgresql(),
        'mongodb_reindex': admin.optimize_mongodb_indexes()
    }
    return results