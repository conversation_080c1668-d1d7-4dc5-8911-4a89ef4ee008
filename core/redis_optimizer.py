#!/usr/bin/env python3
"""
Dynamic Redis Optimizer
Automatically adjusts Redis configuration for optimal performance
Handles memory issues, connection limits, and performance tuning
"""

import os
import redis
import psutil
import logging
import subprocess
from typing import Dict, Any, Optional
from pathlib import Path
import json

class RedisOptimizer:
    """
    Dynamic Redis performance optimizer
    Automatically adjusts configuration based on system resources
    """
    
    def __init__(self, redis_url: str = None):
        if redis_url is None:
            # Use environment-based connection or default
            redis_password = os.getenv('REDIS_PASSWORD', '')
            redis_host = os.getenv('REDIS_HOST', 'localhost')
            redis_port = os.getenv('REDIS_PORT', '6382')
            
            if redis_password:
                self.redis_url = f"redis://:{redis_password}@{redis_host}:{redis_port}/0"
            else:
                self.redis_url = f"redis://{redis_host}:{redis_port}/0"
        else:
            self.redis_url = redis_url
        self.redis_client = None
        self.logger = logging.getLogger(__name__)
        
        # Configuration storage
        self.config_dir = Path("redis_optimization")
        self.config_dir.mkdir(exist_ok=True)
        
        self.setup_logging()
        self.connect_redis()
    
    def setup_logging(self):
        """Setup logging for Redis optimization"""
        handler = logging.FileHandler(self.config_dir / "redis_optimizer.log")
        formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
        handler.setFormatter(formatter)
        self.logger.addHandler(handler)
        self.logger.setLevel(logging.INFO)
    
    def connect_redis(self) -> bool:
        """Connect to Redis with error handling"""
        try:
            self.redis_client = redis.from_url(self.redis_url)
            self.redis_client.ping()
            self.logger.info("✅ Connected to Redis successfully")
            return True
        except Exception as e:
            self.logger.error(f"❌ Redis connection failed: {e}")
            return False
    
    def get_system_memory_info(self) -> Dict[str, float]:
        """Get current system memory information"""
        memory = psutil.virtual_memory()
        return {
            'total_gb': memory.total / (1024**3),
            'available_gb': memory.available / (1024**3),
            'used_percent': memory.percent,
            'free_gb': memory.free / (1024**3)
        }
    
    def get_redis_info(self) -> Dict[str, Any]:
        """Get comprehensive Redis information"""
        if not self.redis_client:
            return {}
        
        try:
            info = self.redis_client.info()
            config = self.redis_client.config_get("*")
            
            return {
                'memory_used_mb': info.get('used_memory', 0) / (1024**2),
                'memory_peak_mb': info.get('used_memory_peak', 0) / (1024**2),
                'connected_clients': info.get('connected_clients', 0),
                'total_connections_received': info.get('total_connections_received', 0),
                'total_commands_processed': info.get('total_commands_processed', 0),
                'keyspace_hits': info.get('keyspace_hits', 0),
                'keyspace_misses': info.get('keyspace_misses', 0),
                'evicted_keys': info.get('evicted_keys', 0),
                'expired_keys': info.get('expired_keys', 0),
                'config': config
            }
        except Exception as e:
            self.logger.error(f"Error getting Redis info: {e}")
            return {}
    
    def fix_memory_overcommit(self):
        """
        Fix Redis memory overcommit warning
        This is the main issue you were seeing
        """
        self.logger.info("🔧 Fixing Redis memory overcommit issue...")
        
        try:
            # Check current overcommit setting
            result = subprocess.run(['sysctl', 'vm.overcommit_memory'], 
                                  capture_output=True, text=True)
            current_value = result.stdout.strip()
            
            if 'vm.overcommit_memory = 1' not in current_value:
                self.logger.info("Setting vm.overcommit_memory = 1")
                
                # Try to set it temporarily
                subprocess.run(['sudo', 'sysctl', 'vm.overcommit_memory=1'], 
                             check=True)
                
                # Also add to sysctl.conf for permanent fix
                sysctl_conf = "/etc/sysctl.conf"
                try:
                    with open(sysctl_conf, 'r') as f:
                        content = f.read()
                    
                    if 'vm.overcommit_memory = 1' not in content:
                        with open(sysctl_conf, 'a') as f:
                            f.write("\\n# Redis optimization\\nvm.overcommit_memory = 1\\n")
                        self.logger.info("✅ Added vm.overcommit_memory=1 to /etc/sysctl.conf")
                except PermissionError:
                    self.logger.warning("⚠️ Cannot modify /etc/sysctl.conf (needs root)")
                
                self.logger.info("✅ Memory overcommit fixed")
            else:
                self.logger.info("✅ Memory overcommit already properly configured")
                
        except Exception as e:
            self.logger.error(f"❌ Failed to fix memory overcommit: {e}")
            self.logger.info("💡 Manual fix: Run 'sudo sysctl vm.overcommit_memory=1'")
    
    def optimize_redis_config(self) -> Dict[str, str]:
        """
        Dynamically optimize Redis configuration based on system resources
        """
        if not self.redis_client:
            return {}
        
        memory_info = self.get_system_memory_info()
        redis_info = self.get_redis_info()
        
        # Calculate optimal settings based on available memory
        available_memory_mb = memory_info['available_gb'] * 1024
        
        # Allocate 25% of available memory to Redis (conservative)
        optimal_maxmemory_mb = int(available_memory_mb * 0.25)
        
        optimizations = {}
        
        try:
            # Memory optimization
            current_maxmemory = redis_info.get('config', {}).get('maxmemory', '0')
            if current_maxmemory == '0' or int(current_maxmemory) < optimal_maxmemory_mb * 1024 * 1024:
                new_maxmemory = f"{optimal_maxmemory_mb}mb"
                self.redis_client.config_set('maxmemory', new_maxmemory)
                optimizations['maxmemory'] = new_maxmemory
                self.logger.info(f"🔧 Set maxmemory to {new_maxmemory}")
            
            # Memory policy - use allkeys-lru for automatic eviction
            current_policy = redis_info.get('config', {}).get('maxmemory-policy', '')
            if current_policy != 'allkeys-lru':
                self.redis_client.config_set('maxmemory-policy', 'allkeys-lru')
                optimizations['maxmemory-policy'] = 'allkeys-lru'
                self.logger.info("🔧 Set maxmemory-policy to allkeys-lru")
            
            # Connection optimization based on CPU cores
            cpu_count = psutil.cpu_count()
            optimal_max_clients = min(10000, cpu_count * 100)  # 100 connections per core, max 10k
            
            current_max_clients = redis_info.get('config', {}).get('maxclients', '10000')
            if int(current_max_clients) != optimal_max_clients:
                self.redis_client.config_set('maxclients', str(optimal_max_clients))
                optimizations['maxclients'] = str(optimal_max_clients)
                self.logger.info(f"🔧 Set maxclients to {optimal_max_clients}")
            
            # Enable lazy freeing for better performance
            lazy_configs = {
                'lazyfree-lazy-eviction': 'yes',
                'lazyfree-lazy-expire': 'yes',
                'lazyfree-lazy-server-del': 'yes'
            }
            
            for config_key, config_value in lazy_configs.items():
                current_value = redis_info.get('config', {}).get(config_key, 'no')
                if current_value != config_value:
                    self.redis_client.config_set(config_key, config_value)
                    optimizations[config_key] = config_value
                    self.logger.info(f"🔧 Set {config_key} to {config_value}")
            
            # Save configuration to Redis
            self.redis_client.config_rewrite()
            
            self.logger.info(f"✅ Applied {len(optimizations)} Redis optimizations")
            
        except Exception as e:
            self.logger.error(f"❌ Error optimizing Redis config: {e}")
        
        return optimizations
    
    def monitor_redis_performance(self) -> Dict[str, Any]:
        """
        Monitor Redis performance and detect issues
        """
        redis_info = self.get_redis_info()
        memory_info = self.get_system_memory_info()
        
        # Calculate hit ratio
        hits = redis_info.get('keyspace_hits', 0)
        misses = redis_info.get('keyspace_misses', 0)
        hit_ratio = (hits / (hits + misses)) * 100 if (hits + misses) > 0 else 0
        
        performance_report = {
            'timestamp': pd.Timestamp.now().isoformat(),
            'redis_memory_usage_mb': redis_info.get('memory_used_mb', 0),
            'redis_memory_peak_mb': redis_info.get('memory_peak_mb', 0),
            'system_memory_usage_percent': memory_info['used_percent'],
            'connected_clients': redis_info.get('connected_clients', 0),
            'hit_ratio_percent': round(hit_ratio, 2),
            'evicted_keys': redis_info.get('evicted_keys', 0),
            'expired_keys': redis_info.get('expired_keys', 0),
            'total_commands': redis_info.get('total_commands_processed', 0),
            'status': 'optimal'
        }
        
        # Detect performance issues
        issues = []
        
        if hit_ratio < 80:
            issues.append("Low cache hit ratio - consider increasing memory")
            performance_report['status'] = 'suboptimal'
        
        if redis_info.get('evicted_keys', 0) > 1000:
            issues.append("High key eviction - increase maxmemory")
            performance_report['status'] = 'needs_attention'
        
        if redis_info.get('connected_clients', 0) > 1000:
            issues.append("High client connections - monitor for connection leaks")
        
        if memory_info['used_percent'] > 90:
            issues.append("System memory critically low")
            performance_report['status'] = 'critical'
        
        performance_report['issues'] = issues
        
        # Log performance summary
        if issues:
            self.logger.warning(f"⚠️ Redis performance issues detected: {', '.join(issues)}")
        else:
            self.logger.info("✅ Redis performance is optimal")
        
        return performance_report
    
    def auto_optimize(self) -> Dict[str, Any]:
        """
        Run complete Redis optimization automatically
        This is the main function to call
        """
        self.logger.info("🚀 Starting Redis auto-optimization...")
        
        optimization_report = {
            'timestamp': pd.Timestamp.now().isoformat(),
            'memory_overcommit_fixed': False,
            'config_optimizations': {},
            'performance_report': {},
            'success': False
        }
        
        try:
            # Step 1: Fix memory overcommit (the main issue)
            self.fix_memory_overcommit()
            optimization_report['memory_overcommit_fixed'] = True
            
            # Step 2: Optimize Redis configuration
            config_changes = self.optimize_redis_config()
            optimization_report['config_optimizations'] = config_changes
            
            # Step 3: Monitor performance
            performance = self.monitor_redis_performance()
            optimization_report['performance_report'] = performance
            
            optimization_report['success'] = True
            
            # Save optimization report
            report_file = self.config_dir / f"optimization_report_{pd.Timestamp.now().strftime('%Y%m%d_%H%M%S')}.json"
            with open(report_file, 'w') as f:
                json.dump(optimization_report, f, indent=2)
            
            self.logger.info("🎉 Redis auto-optimization completed successfully!")
            
        except Exception as e:
            self.logger.error(f"❌ Auto-optimization failed: {e}")
            optimization_report['error'] = str(e)
        
        return optimization_report
    
    def get_optimization_summary(self) -> str:
        """
        Get human-readable optimization summary for the dashboard
        """
        try:
            performance = self.monitor_redis_performance()
            
            status_emoji = {
                'optimal': '✅',
                'suboptimal': '⚠️', 
                'needs_attention': '🔧',
                'critical': '🚨'
            }
            
            emoji = status_emoji.get(performance['status'], '❓')
            
            summary = f"{emoji} Redis Status: {performance['status'].upper()}\\n"
            summary += f"Memory: {performance['redis_memory_usage_mb']:.1f}MB\\n"
            summary += f"Hit Ratio: {performance['hit_ratio_percent']:.1f}%\\n"
            summary += f"Clients: {performance['connected_clients']}"
            
            if performance['issues']:
                summary += f"\\n⚠️ Issues: {len(performance['issues'])}"
            
            return summary
            
        except Exception as e:
            return f"❌ Redis monitoring error: {str(e)[:50]}..."

# Import pandas for timestamps
try:
    import pandas as pd
except ImportError:
    # Fallback to datetime if pandas not available
    from datetime import datetime
    class pd:
        @staticmethod
        class Timestamp:
            @staticmethod
            def now():
                return datetime.now()

# Integration functions for the main application
def optimize_redis_automatically() -> Dict[str, Any]:
    """Run Redis optimization automatically"""
    optimizer = RedisOptimizer()
    return optimizer.auto_optimize()

def get_redis_status_summary() -> str:
    """Get Redis status for system monitoring dashboard"""
    try:
        # Force correct Redis connection for web app
        optimizer = RedisOptimizer("redis://localhost:6382/0")
        return optimizer.get_optimization_summary()
    except Exception as e:
        # Return error status if Redis connection fails
        return f"⚠️ Redis Status: SUBOPTIMAL\\nMemory: 0.0MB\\nHit Ratio: 0.0%\\nClients: 0\\nIssues: 1\\nError: {str(e)}"

if __name__ == "__main__":
    # Run optimization
    optimizer = RedisOptimizer()
    report = optimizer.auto_optimize()
    print("Redis optimization completed!")
    print(f"Success: {report['success']}")
    if report.get('config_optimizations'):
        print(f"Applied {len(report['config_optimizations'])} optimizations")