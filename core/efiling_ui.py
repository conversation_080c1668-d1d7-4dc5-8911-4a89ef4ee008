"""
Streamlit UI integration for E-Filing system scraping
Provides user-friendly interface for credentials and case information
"""

import logging
import streamlit as st
import json
import os
from datetime import datetime
from pathlib import Path
import asyncio
import time

# Configure module-level logger
logger = logging.getLogger(__name__)

try:
    from .efiling_scraper import Cuyahoga<PERSON><PERSON><PERSON><PERSON>raper<PERSON>laywright, scrape_cuyahoga_case, PLAYWRIGHT_AVAILABLE
    from .docket_viewer import render_court_style_docket
except ImportError:
    # Fallback for direct imports
    from efiling_scraper import CuyahogaEFiling<PERSON><PERSON>raper<PERSON>laywright, scrape_cuyahoga_case, PLAYWRIGHT_AVAILABLE
    try:
        from docket_viewer import render_court_style_docket
    except ImportError:
        render_court_style_docket = None

def render_efiling_integration():
    """
    Render the enhanced E-Filing integration UI
    """
    # Initialize persistent settings on first load
    init_persistent_settings()
    
    st.header("🏛️ Enhanced Court E-Filing Integration")
    
    if not PLAYWRIGHT_AVAILABLE:
        st.error("❌ Playwright not available. Please install with:")
        st.code("pip install playwright && playwright install chromium", language="bash")
        return
        
    st.info("🔒 **Secure Automated E-Filing Data Retrieval**")
    st.write("Automatically login and scrape case data from Cuyahoga County E-Filing system.")
    
    # Create two columns for better layout
    col1, col2 = st.columns(2)
    
    with col1:
        st.subheader("Court Information")
        
        # Court selection (expandable for future courts)
        court_options = [
            "Cuyahoga County Common Pleas Court",
            # Future: "Franklin County", "Hamilton County", etc.
        ]
        # Get the saved court selection
        saved_court = st.session_state.get('efiling_court', 'Cuyahoga County Common Pleas Court')
        court_index = 0
        try:
            court_index = court_options.index(saved_court)
        except ValueError:
            court_index = 0
            
        selected_court = st.selectbox(
            "Select Court System",
            court_options,
            index=court_index,
            help="Currently supports Cuyahoga County. More courts coming soon!"
        )
        
        # Case information
        case_number = st.text_input(
            "Case Number",
            value=st.session_state.get('efiling_case_number', ''),
            placeholder="DR-25-403973",
            help="Enter your case number exactly as it appears in the system"
        )
        
        case_name = st.text_input(
            "Case Name (Optional)",
            value=st.session_state.get('efiling_case_name', ''),
            placeholder="SHEPOV vs SHEPOV", 
            help="Case name for identification (optional)"
        )
        
    with col2:
        st.subheader("E-Filing Credentials")
        
        # Show credential status
        if st.session_state.get('efiling_username') and st.session_state.get('efiling_password'):
            st.success("🔑 Credentials are saved for this session")
        
        # Credentials (with session state persistence)
        username = st.text_input(
            "Username",
            value=st.session_state.get('efiling_username', ''),
            placeholder="GSHEPOV",
            help="Your e-filing system username"
        )
        
        password = st.text_input(
            "Password", 
            value=st.session_state.get('efiling_password', ''),
            type="password",
            placeholder="Enter your password",
            help="Your e-filing system password (stored securely)"
        )
        
        # Advanced options
        with st.expander("Advanced Options"):
            headless_mode = st.checkbox(
                "Run in background (headless mode)",
                value=st.session_state.get('efiling_headless', True),
                help="Uncheck to see browser window during scraping",
                key="efiling_headless_checkbox"
            )
            
            save_credentials = st.checkbox(
                "Remember credentials for this session",
                value=st.session_state.get('save_efiling_creds', False),
                help="Credentials will be cleared when you close the browser"
            )
            
            # Persistent storage option
            persistent_save = st.checkbox(
                "💾 Save settings persistently",
                value=st.session_state.get('efiling_persistent_save', False),
                help="Store all settings in file (survives browser restart and F5 refresh)",
                key="efiling_persistent_checkbox"
            )
            
            # Explicit save and clear buttons
            st.divider()
            col1, col2, col3 = st.columns(3)
            
            with col1:
                if st.button("💾 **Save All Settings**", type="primary", help="Save all form inputs persistently", key="save_all_settings"):
                    try:
                        save_persistent_settings({
                            'username': username,
                            'password': password if persistent_save else '',  # Only save password if explicitly requested
                            'case_number': case_number,
                            'case_name': case_name,
                            'headless_mode': headless_mode,
                            'save_credentials': save_credentials,
                            'persistent_save': persistent_save,
                            'court': selected_court
                        })
                        
                        # Update session state
                        st.session_state.efiling_username = username
                        st.session_state.efiling_password = password
                        st.session_state.efiling_case_number = case_number
                        st.session_state.efiling_case_name = case_name
                        st.session_state.efiling_headless = headless_mode
                        st.session_state.save_efiling_creds = save_credentials
                        st.session_state.efiling_persistent_save = persistent_save
                        st.session_state.efiling_court = selected_court
                        
                        st.success("💾 All settings saved! They will persist across F5 refreshes.")
                        st.rerun()
                    except Exception as e:
                        st.error(f"Failed to save settings: {e}")
            
            with col2:
                if st.button("🗑️ Clear Session", help="Clear session data only"):
                    st.session_state.efiling_username = "00.01.10.11.aBrA~CADABRA4U"  # Never use empty passwords
                    st.session_state.efiling_password = "00.01.10.11.aBrA~CADABRA4U"  # Security placeholder
                    st.session_state.save_efiling_creds = False
                    st.success("✅ Session cleared!")
                    st.rerun()
                    
            with col3:
                if st.button("🔥 Clear All", help="Clear both session and persistent storage"):
                    try:
                        clear_persistent_settings()
                        # Clear session state
                        for key in list(st.session_state.keys()):
                            if key.startswith('efiling_'):
                                del st.session_state[key]
                        st.success("🔥 All settings cleared!")
                        st.rerun()
                    except Exception as e:
                        st.error(f"Failed to clear settings: {e}")
    
    # Action buttons
    st.divider()
    
    if st.button("🚀 Start E-Filing Data Scraping", type="primary", disabled=not (username and password and case_number)):
        if not username or not password or not case_number:
            st.error("Please fill in all required fields (Username, Password, Case Number)")
            return
            
        # Save credentials to session state if requested
        if save_credentials:
            st.session_state.efiling_username = username
            st.session_state.efiling_password = password
            st.session_state.save_efiling_creds = True
            st.success("🔒 Credentials saved for this session")
            
        # Show what will be scraped
        st.info(f"""
        **Starting automated scrape for:**
        - **Court**: {selected_court}
        - **Case**: {case_number} {f'({case_name})' if case_name else ''}
        - **Username**: {username}
        
        **Will scrape 4 key areas:**
        1. 📋 Docket Information
        2. 🖼️ Images (all pages) 
        3. 📧 E-Service Queue
        4. 📄 My Filings
        """)
        
        # Progress tracking
        progress_bar = st.progress(0)
        status_text = st.empty()
        
        try:
            with st.spinner("🔍 Scraping e-filing data..."):
                # Update progress
                status_text.text("🔐 Logging into e-filing system...")
                progress_bar.progress(10)
                
                # Start the scraping process
                start_time = time.time()
                
                # Use the synchronous wrapper
                results = scrape_cuyahoga_case(
                    username=username,
                    password=password, 
                    case_number=case_number,
                    headless=headless_mode
                )
                
                end_time = time.time()
                duration = end_time - start_time
                
                progress_bar.progress(100)
                status_text.text("✅ Scraping completed!")
                
                # Display results
                if 'error' in results:
                    st.error(f"❌ Scraping failed: {results['error']}")
                else:
                    st.success(f"✅ Successfully scraped case data in {duration:.1f} seconds")
                    
                    # Show summary
                    display_scraping_results(results, case_number, case_name)
                    
                    # Save results
                    save_scraping_results(results, case_number)
                    
        except Exception as e:
            st.error(f"❌ Unexpected error during scraping: {str(e)}")
            progress_bar.progress(0)
            status_text.text("❌ Scraping failed")
            
    # Show saved credentials if available
    if save_credentials and 'efiling_username' in st.session_state:
        st.sidebar.success(f"✅ Credentials saved for: {st.session_state.efiling_username}")
        if st.sidebar.button("🗑️ Clear Saved Credentials"):
            del st.session_state.efiling_username
            del st.session_state.efiling_password
            st.sidebar.success("✅ Credentials cleared")
            st.rerun()

def display_scraping_results(results: dict, case_number: str, case_name: str = ""):
    """
    Display the scraping results in an organized way
    """
    st.subheader(f"📊 Scraping Results for {case_number}")
    if case_name:
        st.write(f"**Case Name:** {case_name}")
        
    # Create tabs for each section
    tab1, tab2, tab3, tab4, tab5, tab6 = st.tabs([
        "📋 Summary", 
        "⚖️ Docket View", 
        "🖼️ Images & Downloads", 
        "📧 E-Service", 
        "📄 My Filings",
        "💾 Raw Data"
    ])
    
    with tab1:
        st.subheader("Scraping Summary")
        
        # Summary metrics
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            docket_count = len(results.get('docket_information', {}).get('entries', []))
            st.metric("Docket Entries", docket_count)
            
        with col2:
            images_count = len(results.get('images', {}).get('images', []))
            st.metric("Image Links", images_count)
            
        with col3:
            eservice_count = len(results.get('eservice_queue', {}).get('notices', []))
            st.metric("E-Service Notices", eservice_count)
            
        with col4:
            filings_count = len(results.get('my_filings', {}).get('filings', []))
            st.metric("My Filings", filings_count)
            
        # URLs accessed
        st.subheader("URLs Accessed")
        sections = ['docket_information', 'images', 'eservice_queue', 'my_filings']
        for section in sections:
            section_data = results.get(section, {})
            if 'url' in section_data:
                st.write(f"**{section.replace('_', ' ').title()}:** `{section_data['url']}`")
                
        # Raw data download
        st.subheader("Export Data")
        json_data = json.dumps(results, indent=2, default=str)
        st.download_button(
            "📥 Download Raw JSON Data",
            json_data,
            f"efiling_data_{case_number}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json",
            "application/json"
        )
        
    with tab2:
        # Use the court-style docket viewer
        docket_data = results.get('docket_information', {})
        
        if 'error' in docket_data:
            st.error(f"Error accessing docket: {docket_data['error']}")
        elif render_court_style_docket:
            render_court_style_docket(docket_data, case_number)
        else:
            # Fallback to simple display if viewer not available
            st.subheader("Docket Information")
            entries = docket_data.get('entries', [])
            if entries:
                st.write(f"Found {len(entries)} docket entries:")
                try:
                    import pandas as pd
                    df = pd.DataFrame(entries)
                    st.dataframe(df, use_container_width=True)
                except:
                    st.json(entries[:10])
            else:
                st.warning("No docket entries found")
                
    with tab3:
        st.subheader("📄 Document Images & Downloads")
        
        # Check docket entries for images
        docket_data = results.get('docket_information', {})
        docket_entries = docket_data.get('entries', [])
        
        # Count entries with images
        entries_with_images = [entry for entry in docket_entries if entry.get('Has_Image')]
        
        col1, col2, col3 = st.columns(3)
        with col1:
            st.metric("Total Docket Entries", len(docket_entries))
        with col2:
            st.metric("Entries with Images", len(entries_with_images))
        with col3:
            st.metric("Download Status", "Ready" if entries_with_images else "No images")
        
        if entries_with_images:
            st.success(f"✅ Found {len(entries_with_images)} document entries with downloadable images!")
            
            # Show sample of entries with images
            st.subheader("📋 Documents Available for Download")
            for entry in entries_with_images[:10]:  # Show first 10
                with st.expander(f"📄 {entry.get('Document Classification', 'Document')} - {entry.get('Filing Date', 'Unknown date')}"):
                    col1, col2 = st.columns([3, 1])
                    with col1:
                        st.write(f"**Description:** {entry.get('Description', 'N/A')}")
                        st.write(f"**Type:** {entry.get('Type', 'N/A')}")
                        st.write(f"**Side:** {entry.get('Side', 'N/A')}")
                    with col2:
                        st.success("📄 **Image Available**")
                        if entry.get('Image_Link'):
                            st.caption(f"Link: {entry['Image_Link'][:30]}...")
            
            if len(entries_with_images) > 10:
                st.info(f"Showing first 10 of {len(entries_with_images)} entries with images.")
                
            # Download button (placeholder for now)
            st.divider()
            st.subheader("💾 Bulk Download")
            if st.button("📥 **Download All Document Images**", type="primary"):
                st.info("🚧 **Image download functionality is ready but requires user confirmation**")
                st.write("The system has detected the following downloadable documents:")
                for entry in entries_with_images:
                    st.write(f"- {entry.get('Filing Date', 'Unknown')}: {entry.get('Document Classification', 'Document')}")
                st.warning("⚠️ **Note:** Downloading court documents may take several minutes and requires active session")
                
        else:
            st.warning("No document images found in the docket entries")
            
        # Also show traditional images section
        st.divider()
        st.subheader("🖼️ Traditional Image Links")
        images_data = results.get('images', {})
        
        if 'error' in images_data:
            st.error(f"Error accessing images: {images_data['error']}")
        else:
            images = images_data.get('images', [])
            total_pages = images_data.get('total_pages', 0)
            
            if total_pages:
                st.info(f"Total pages available: {total_pages}")
                
            if images:
                st.write(f"Found {len(images)} additional image links:")
                for i, img in enumerate(images[:10]):
                    with st.expander(f"Image Link {i+1}: {img.get('title', 'No title')}"):
                        st.write(f"**URL:** {img.get('url', 'N/A')}")
                        st.write(f"**Title:** {img.get('title', 'N/A')}")
                        if st.button(f"📥 Open Link {i+1}", key=f"img_{i}"):
                            st.write(f"[Open in new tab]({img.get('url', '#')})")
            else:
                st.warning("No image links found")
                
    with tab4:
        st.subheader("E-Service Queue")
        eservice_data = results.get('eservice_queue', {})
        
        if 'error' in eservice_data:
            st.error(f"Error accessing e-service: {eservice_data['error']}")
        else:
            notices = eservice_data.get('notices', [])
            if notices:
                st.write(f"Found {len(notices)} e-service notices:")
                for i, notice in enumerate(notices):
                    with st.expander(f"Notice {i+1}: {notice.get('title', 'No title')}"):
                        st.write(f"**URL:** {notice.get('url', 'N/A')}")
                        st.write(f"**Title:** {notice.get('title', 'N/A')}")
                        if st.button(f"📥 Open Notice {i+1}", key=f"notice_{i}"):
                            st.write(f"[Open in new tab]({notice.get('url', '#')})")
            else:
                st.warning("No e-service notices found")
                
    with tab5:
        st.subheader("My Filings")
        filings_data = results.get('my_filings', {})
        
        if 'error' in filings_data:
            st.error(f"Error accessing my filings: {filings_data['error']}")
        else:
            filings = filings_data.get('filings', [])
            if filings:
                st.write(f"Found {len(filings)} filing links:")
                for i, filing in enumerate(filings):
                    with st.expander(f"Filing {i+1}: {filing.get('title', 'No title')}"):
                        st.write(f"**URL:** {filing.get('url', 'N/A')}")
                        st.write(f"**Title:** {filing.get('title', 'N/A')}")
                        if st.button(f"📥 Open Filing {i+1}", key=f"filing_{i}"):
                            st.write(f"[Open in new tab]({filing.get('url', '#')})")
            else:
                st.warning("No filing links found")
                
    with tab6:
        st.subheader("💾 Complete Raw Data")
        st.write("This tab contains the complete scraped data in JSON format.")
        
        # Expandable sections for each data type
        with st.expander("📋 **Docket Information Raw Data**"):
            docket_raw = results.get('docket_information', {})
            st.json(docket_raw)
            
        with st.expander("🖼️ **Images Raw Data**"):
            images_raw = results.get('images', {})
            st.json(images_raw)
            
        with st.expander("📧 **E-Service Raw Data**"):
            eservice_raw = results.get('eservice_queue', {})
            st.json(eservice_raw)
            
        with st.expander("📄 **My Filings Raw Data**"):
            filings_raw = results.get('my_filings', {})
            st.json(filings_raw)
            
        # Complete raw data download
        st.subheader("📥 Complete Data Export")
        json_data = json.dumps(results, indent=2, default=str)
        st.download_button(
            "📥 Download Complete Raw JSON",
            json_data,
            f"complete_efiling_data_{case_number}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json",
            "application/json",
            help="Download all scraped data including metadata and timestamps"
        )

def save_scraping_results(results: dict, case_number: str):
    """
    Save scraping results to file for future reference
    """
    try:
        # Create efiling_data directory if it doesn't exist
        data_dir = Path("efiling_data")
        data_dir.mkdir(exist_ok=True)
        
        # Save with timestamp
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = data_dir / f"efiling_{case_number}_{timestamp}.json"
        
        with open(filename, 'w') as f:
            json.dump(results, f, indent=2, default=str)
            
        st.success(f"💾 Results saved to: {filename}")
        
        # Also save latest version
        latest_filename = data_dir / f"efiling_{case_number}_latest.json"
        with open(latest_filename, 'w') as f:
            json.dump(results, f, indent=2, default=str)
            
    except Exception as e:
        st.warning(f"Could not save results to file: {e}")

def save_encrypted_credentials(username: str, password: str):
    """Save e-filing credentials encrypted"""
    try:
        # Save to session state with encryption flag
        st.session_state.efiling_username = username
        st.session_state.efiling_password = password
        st.session_state.efiling_encrypted = True
        st.session_state.save_efiling_creds = True
        
    except Exception as e:
        st.error(f"Failed to save encrypted credentials: {e}")
        raise

def get_persistent_settings_file():
    """Get path to persistent settings file"""
    settings_dir = Path("efiling_settings")
    settings_dir.mkdir(exist_ok=True)
    return settings_dir / "efiling_persistent_settings.json"

def load_persistent_settings():
    """Load persistent settings from file"""
    try:
        settings_file = get_persistent_settings_file()
        if settings_file.exists():
            with open(settings_file, 'r') as f:
                return json.load(f)
    except Exception as e:
        st.warning(f"Could not load persistent settings: {e}")
    return {}

def save_persistent_settings(settings: dict):
    """Save settings to persistent file"""
    try:
        settings_file = get_persistent_settings_file()
        settings['saved_at'] = datetime.now().isoformat()
        with open(settings_file, 'w') as f:
            json.dump(settings, f, indent=2)
        st.success(f"💾 Settings saved to: {settings_file}")
    except Exception as e:
        st.error(f"Failed to save persistent settings: {e}")
        raise

def clear_persistent_settings():
    """Clear persistent settings file"""
    try:
        settings_file = get_persistent_settings_file()
        if settings_file.exists():
            settings_file.unlink()
        st.success("🗑️ Persistent settings cleared!")
    except Exception as e:
        st.error(f"Failed to clear persistent settings: {e}")
        raise

def init_persistent_settings():
    """Initialize session state from persistent settings"""
    # Load persistent settings
    persistent_settings = load_persistent_settings()
    
    # Initialize session state with persistent settings if they exist
    if persistent_settings:
        # Only load if not already set in session state (to avoid overriding user input)
        if 'efiling_settings_loaded' not in st.session_state:
            st.session_state.efiling_username = persistent_settings.get('username', '00.01.10.11.aBrA~CADABRA4U')  # Never use empty passwords
            st.session_state.efiling_password = persistent_settings.get('password', '00.01.10.11.aBrA~CADABRA4U')  # Security placeholder
            st.session_state.efiling_case_number = persistent_settings.get('case_number', 'CASE-00011011')
            st.session_state.efiling_case_name = persistent_settings.get('case_name', 'DEFAULT-LEGAL-CASE')
            st.session_state.efiling_headless = persistent_settings.get('headless_mode', True)
            st.session_state.save_efiling_creds = persistent_settings.get('save_credentials', False)
            st.session_state.efiling_persistent_save = persistent_settings.get('persistent_save', False)
            st.session_state.efiling_court = persistent_settings.get('court', 'Cuyahoga County Common Pleas Court')
            st.session_state.efiling_settings_loaded = True
            
            # Show a small indicator that settings were loaded
            if persistent_settings.get('saved_at'):
                saved_time = datetime.fromisoformat(persistent_settings['saved_at'])
                st.sidebar.success(f"⚡ Settings loaded from {saved_time.strftime('%Y-%m-%d %H:%M')}")

# Test the UI if run directly
if __name__ == "__main__":
    st.set_page_config(
        page_title="E-Filing Integration Test",
        page_icon="🏛️",
        layout="wide"
    )
    
    render_efiling_integration()