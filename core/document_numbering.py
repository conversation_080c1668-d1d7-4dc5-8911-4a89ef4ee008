"""
Document Numbering System for Legal Case Management
Provides logical, chronological numbering based on document type and filing date
"""

import logging
from datetime import datetime
from typing import Dict, List, Optional
import re

# Configure module-level logger
logger = logging.getLogger(__name__)

class DocumentNumberingSystem:
    """
    Handles logical document numbering for legal cases
    
    Rules:
    1. <PERSON><PERSON><PERSON><PERSON> for Divorce = Document #1 (always first)
    2. Subsequent documents numbered chronologically by filing date
    3. Multiple filings on same day get sub-numbers (1a, 1b, etc.)
    4. Document types have priority order for same-day filings
    """
    
    # Document type priority (lower number = higher priority for same day)
    DOCUMENT_PRIORITIES = {
        'complaint_for_divorce': 1,
        'summons': 2,
        'mandatory_disclosure_order': 3,
        'restraining_order': 4,
        'parenting_education_order': 5,
        'waiver_of_counsel': 6,
        'affidavit': 7,
        'motion': 8,
        'response': 9,
        'discovery': 10,
        'financial_disclosure': 11,
        'settlement_agreement': 12,
        'judgment': 13,
        'decree': 14,
        'other': 99
    }
    
    def __init__(self):
        self.documents = []
        
    def classify_document_type(self, filename: str, content: str = "") -> str:
        """Classify document type based on filename and content"""
        filename_lower = filename.lower()
        content_lower = content.lower()
        
        # Check for specific document types
        if 'complaint' in filename_lower and 'divorce' in filename_lower:
            return 'complaint_for_divorce'
        elif 'summons' in filename_lower:
            return 'summons'
        elif 'mandatory' in filename_lower and 'disclosure' in filename_lower:
            return 'mandatory_disclosure_order'
        elif 'restraining' in filename_lower or 'mutual restraining' in filename_lower:
            return 'restraining_order'
        elif 'parenting' in filename_lower and 'education' in filename_lower:
            return 'parenting_education_order'
        elif 'waiver' in filename_lower and ('counsel' in filename_lower or 'legal' in filename_lower):
            return 'waiver_of_counsel'
        elif 'affidavit' in filename_lower:
            return 'affidavit'
        elif 'motion' in filename_lower:
            return 'motion'
        elif 'response' in filename_lower or 'answer' in filename_lower:
            return 'response'
        elif 'discovery' in filename_lower:
            return 'discovery'
        elif 'financial' in filename_lower and 'disclosure' in filename_lower:
            return 'financial_disclosure'
        elif 'settlement' in filename_lower or 'agreement' in filename_lower:
            return 'settlement_agreement'
        elif 'judgment' in filename_lower:
            return 'judgment'
        elif 'decree' in filename_lower:
            return 'decree'
        else:
            return 'other'
    
    def extract_filing_date(self, filename: str, metadata: Dict = None) -> datetime:
        """Extract filing date from filename or metadata"""
        
        # Try to get from metadata first
        if metadata and 'filing_date' in metadata:
            if isinstance(metadata['filing_date'], datetime):
                return metadata['filing_date']
            elif isinstance(metadata['filing_date'], str):
                try:
                    return datetime.strptime(metadata['filing_date'], '%Y-%m-%d')
                except:
                    pass
        
        # Try to extract date from filename
        # Look for patterns like YYYY-MM-DD, MM-DD-YYYY, etc.
        date_patterns = [
            r'(\d{4})-(\d{2})-(\d{2})',  # YYYY-MM-DD
            r'(\d{2})-(\d{2})-(\d{4})',  # MM-DD-YYYY
            r'(\d{2})(\d{2})(\d{4})',    # MMDDYYYY
            r'(\d{4})(\d{2})(\d{2})',    # YYYYMMDD
        ]
        
        for pattern in date_patterns:
            match = re.search(pattern, filename)
            if match:
                try:
                    if len(match.group(1)) == 4:  # Year first
                        year, month, day = match.groups()
                    else:  # Month first
                        month, day, year = match.groups()
                    
                    return datetime(int(year), int(month), int(day))
                except:
                    continue
        
        # Default to current date if no date found
        return datetime.now()
    
    def add_document(self, filename: str, content: str = "", metadata: Dict = None) -> str:
        """Add document and return its logical number"""
        
        doc_type = self.classify_document_type(filename, content)
        filing_date = self.extract_filing_date(filename, metadata)
        priority = self.DOCUMENT_PRIORITIES.get(doc_type, 99)
        
        document = {
            'filename': filename,
            'doc_type': doc_type,
            'filing_date': filing_date,
            'priority': priority,
            'content': content,
            'metadata': metadata or {}
        }
        
        self.documents.append(document)
        
        # Re-sort and re-number all documents
        self._renumber_documents()
        
        # Find and return the number for this document
        for doc in self.documents:
            if doc['filename'] == filename:
                return doc['document_number']
        
        return "Unknown"
    
    def _renumber_documents(self):
        """Re-number all documents based on chronological order and priority"""
        
        # Sort by filing date, then by priority
        self.documents.sort(key=lambda x: (x['filing_date'], x['priority']))
        
        # Assign document numbers
        current_number = 1
        current_date = None
        same_day_counter = 0
        
        for doc in self.documents:
            if current_date != doc['filing_date'].date():
                # New day
                current_date = doc['filing_date'].date()
                same_day_counter = 0
                doc['document_number'] = str(current_number)
                current_number += 1
            else:
                # Same day - use sub-numbering
                same_day_counter += 1
                base_number = current_number - 1
                sub_letter = chr(ord('a') + same_day_counter - 1)
                doc['document_number'] = f"{base_number}{sub_letter}"
    
    def get_document_list(self) -> List[Dict]:
        """Get sorted list of all documents with their numbers"""
        return sorted(self.documents, key=lambda x: x['document_number'])
    
    def get_document_by_number(self, number: str) -> Optional[Dict]:
        """Get document by its logical number"""
        for doc in self.documents:
            if doc['document_number'] == number:
                return doc
        return None
    
    def get_next_expected_documents(self) -> List[str]:
        """Get list of documents typically expected next in divorce proceedings"""
        
        existing_types = {doc['doc_type'] for doc in self.documents}
        
        expected_sequence = [
            'complaint_for_divorce',
            'summons', 
            'mandatory_disclosure_order',
            'waiver_of_counsel',
            'financial_disclosure',
            'settlement_agreement',
            'judgment',
            'decree'
        ]
        
        # Find what's missing from the typical sequence
        missing = []
        for doc_type in expected_sequence:
            if doc_type not in existing_types:
                missing.append(doc_type.replace('_', ' ').title())
        
        return missing[:3]  # Return next 3 expected documents

# Global instance for the application
document_numbering = DocumentNumberingSystem()
