"""
Docket Reconciliation System
Matches downloaded documents to docket entries and creates a comprehensive legal record view
where every docket entry links to its corresponding PDF justification.
"""

import json
import os
import re
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Tuple
import logging
from dataclasses import dataclass, asdict
from difflib import SequenceMatcher

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class DocketEntry:
    """Represents a single docket entry"""
    date: str
    entry_number: str
    document_type: str
    description: str
    party: str  # P1 (Plaintiff), D1 (Defendant), D1A (Defendant Attorney), N/A (Court)
    attorney: str
    page_number: int
    row_number: int
    raw_html: str = ""
    matched_documents: List[str] = None
    has_pdf: bool = False
    
    def __post_init__(self):
        if self.matched_documents is None:
            self.matched_documents = []

@dataclass 
class DownloadedDocument:
    """Represents a downloaded document"""
    filename: str
    original_filename: str
    filepath: str
    size: int
    document_type: str
    efile_id: str
    source: str  # 'images', 'eservice', 'myfilings'
    filing_date: str = ""
    party: str = ""
    matched_docket_entries: List[str] = None
    similarity_scores: Dict[str, float] = None
    
    def __post_init__(self):
        if self.matched_docket_entries is None:
            self.matched_docket_entries = []
        if self.similarity_scores is None:
            self.similarity_scores = {}

class DocketReconciliationEngine:
    """
    Engine that reconciles docket entries with downloaded documents
    Creates a comprehensive legal record where every entry links to its PDF justification
    """
    
    def __init__(self, case_number: str = "DR-25-403973"):
        self.case_number = case_number
        self.docket_entries: List[DocketEntry] = []
        self.downloaded_documents: List[DownloadedDocument] = []
        self.reconciliation_results = {}
        
    def load_docket_data(self, docket_file_path: str):
        """Load docket data from scraped results"""
        try:
            with open(docket_file_path, 'r') as f:
                docket_data = json.load(f)
            
            # Process all records from all pages
            for page in docket_data.get('pages', []):
                for record in page.get('records', []):
                    # Extract docket entry information
                    data = record.get('data', {})
                    
                    entry = DocketEntry(
                        date=self.extract_date(data),
                        entry_number=self.extract_entry_number(data),
                        document_type=self.extract_document_type(data),
                        description=self.extract_description(data),
                        party=self.extract_party(data),
                        attorney=self.extract_attorney(data),
                        page_number=record.get('page_number', 0),
                        row_number=record.get('row_number', 0),
                        raw_html=record.get('raw_html', '')
                    )
                    
                    self.docket_entries.append(entry)
            
            logger.info(f"Loaded {len(self.docket_entries)} docket entries")
            
        except Exception as e:
            logger.error(f"Error loading docket data: {e}")
    
    def load_downloaded_documents(self, downloads_dir: str):
        """Load information about all downloaded documents"""
        downloads_path = Path(downloads_dir)
        
        # Look for results files from different scrapers
        result_files = {
            'images': list(downloads_path.glob('**/efiling_results_*.json')),
            'eservice': list(downloads_path.glob('**/eservice_results_*.json')), 
            'myfilings': list(downloads_path.glob('**/myfilings_results_*.json'))
        }
        
        for source, files in result_files.items():
            for file_path in files:
                self.load_documents_from_results(file_path, source)
        
        # Also scan download directories directly
        for subdir in downloads_path.iterdir():
            if subdir.is_dir():
                self.scan_download_directory(subdir)
        
        logger.info(f"Loaded {len(self.downloaded_documents)} downloaded documents")
    
    def load_documents_from_results(self, results_file: str, source: str):
        """Load document information from scraper results file"""
        try:
            with open(results_file, 'r') as f:
                results = json.load(f)
            
            # Process based on source type
            if source == 'myfilings':
                # Process My Filings results with EFile IDs
                for page in results.get('pages', []):
                    for filing in page.get('filings', []):
                        if filing.get('efile_id'):
                            for pdf_download in filing.get('pdf_downloads', []):
                                if pdf_download.get('success'):
                                    doc = DownloadedDocument(
                                        filename=pdf_download.get('safe_filename', ''),
                                        original_filename=pdf_download.get('original_filename', ''),
                                        filepath=pdf_download.get('filepath', ''),
                                        size=pdf_download.get('size', 0),
                                        document_type=pdf_download.get('document_type', ''),
                                        efile_id=filing.get('efile_id', ''),
                                        source=source
                                    )
                                    self.downloaded_documents.append(doc)
            
            elif source == 'images':
                # Process Images results 
                for page in results.get('pages', []):
                    for record in page.get('records', []):
                        for link in record.get('links', []):
                            if '.pdf' in link.get('url', '').lower():
                                doc = DownloadedDocument(
                                    filename=self.extract_filename_from_url(link.get('url', '')),
                                    original_filename=self.extract_filename_from_url(link.get('url', '')),
                                    filepath='',
                                    size=0,
                                    document_type=self.infer_document_type_from_context(record),
                                    efile_id='',
                                    source=source
                                )
                                self.downloaded_documents.append(doc)
            
            elif source == 'eservice':
                # Process E-Service results
                for page in results.get('pages', []):
                    for record in page.get('records', []):
                        for link in record.get('links', []):
                            if '.pdf' in link.get('url', '').lower():
                                doc = DownloadedDocument(
                                    filename=self.extract_filename_from_url(link.get('url', '')),
                                    original_filename=self.extract_filename_from_url(link.get('url', '')),
                                    filepath='',
                                    size=0,
                                    document_type=self.infer_document_type_from_context(record),
                                    efile_id='',
                                    source=source
                                )
                                self.downloaded_documents.append(doc)
                                
        except Exception as e:
            logger.error(f"Error loading documents from {results_file}: {e}")
    
    def scan_download_directory(self, directory: Path):
        """Scan a download directory for PDF files"""
        for pdf_file in directory.glob('**/*.pdf'):
            doc = DownloadedDocument(
                filename=pdf_file.name,
                original_filename=pdf_file.name,
                filepath=str(pdf_file),
                size=pdf_file.stat().st_size if pdf_file.exists() else 0,
                document_type=self.infer_document_type_from_filename(pdf_file.name),
                efile_id='',
                source='directory_scan'
            )
            self.downloaded_documents.append(doc)
    
    def reconcile_documents_to_docket(self):
        """Main reconciliation process - matches documents to docket entries"""
        logger.info("Starting docket reconciliation process...")
        
        # Phase 1: Direct matching by date and document type
        self.match_by_date_and_type()
        
        # Phase 2: Fuzzy matching by description
        self.match_by_description_similarity()
        
        # Phase 3: Match by party and attorney
        self.match_by_party_attorney()
        
        # Phase 4: Generate comprehensive results
        self.generate_reconciliation_report()
        
        logger.info("Docket reconciliation completed")
    
    def match_by_date_and_type(self):
        """Match documents to docket entries by filing date and document type"""
        logger.info("Phase 1: Matching by date and document type...")
        
        matches = 0
        for doc in self.downloaded_documents:
            doc_date = self.normalize_date(doc.filing_date)
            doc_type = self.normalize_document_type(doc.document_type)
            
            for entry in self.docket_entries:
                entry_date = self.normalize_date(entry.date)
                entry_type = self.normalize_document_type(entry.document_type)
                
                # Check for exact or close date match and document type similarity
                if (doc_date == entry_date or self.dates_are_close(doc_date, entry_date)) and \
                   self.document_types_match(doc_type, entry_type):
                    
                    # Add mutual references
                    doc.matched_docket_entries.append(f"{entry.date}_{entry.entry_number}")
                    entry.matched_documents.append(doc.filename)
                    entry.has_pdf = True
                    
                    # Calculate similarity score
                    similarity = self.calculate_similarity_score(doc, entry)
                    doc.similarity_scores[f"{entry.date}_{entry.entry_number}"] = similarity
                    
                    matches += 1
        
        logger.info(f"Phase 1 complete: {matches} matches by date and type")
    
    def match_by_description_similarity(self):
        """Match documents to docket entries by description similarity"""
        logger.info("Phase 2: Matching by description similarity...")
        
        matches = 0
        for doc in self.downloaded_documents:
            if doc.matched_docket_entries:  # Skip if already matched
                continue
                
            best_match = None
            best_score = 0.0
            
            # Compare document filename/type with docket descriptions
            doc_text = f"{doc.original_filename} {doc.document_type}".lower()
            
            for entry in self.docket_entries:
                if entry.matched_documents:  # Skip if already matched
                    continue
                
                entry_text = f"{entry.description} {entry.document_type}".lower()
                
                # Calculate similarity score
                similarity = SequenceMatcher(None, doc_text, entry_text).ratio()
                
                if similarity > best_score and similarity > 0.6:  # 60% threshold
                    best_score = similarity
                    best_match = entry
            
            if best_match:
                # Add mutual references
                doc.matched_docket_entries.append(f"{best_match.date}_{best_match.entry_number}")
                best_match.matched_documents.append(doc.filename)
                best_match.has_pdf = True
                
                doc.similarity_scores[f"{best_match.date}_{best_match.entry_number}"] = best_score
                matches += 1
        
        logger.info(f"Phase 2 complete: {matches} additional matches by description similarity")
    
    def match_by_party_attorney(self):
        """Match documents by party and attorney information"""
        logger.info("Phase 3: Matching by party and attorney...")
        
        matches = 0
        for doc in self.downloaded_documents:
            if doc.matched_docket_entries:  # Skip if already matched
                continue
            
            # Try to infer party from document metadata or filename
            doc_party = self.infer_party_from_document(doc)
            
            for entry in self.docket_entries:
                if entry.matched_documents:  # Skip if already matched
                    continue
                
                # Match by party
                if doc_party and entry.party and doc_party == entry.party:
                    # Add mutual references
                    doc.matched_docket_entries.append(f"{entry.date}_{entry.entry_number}")
                    entry.matched_documents.append(doc.filename)
                    entry.has_pdf = True
                    
                    # Lower similarity score since this is less certain
                    doc.similarity_scores[f"{entry.date}_{entry.entry_number}"] = 0.7
                    matches += 1
        
        logger.info(f"Phase 3 complete: {matches} additional matches by party/attorney")
    
    def generate_reconciliation_report(self):
        """Generate comprehensive reconciliation report"""
        logger.info("Generating reconciliation report...")
        
        # Calculate statistics
        total_docket_entries = len(self.docket_entries)
        entries_with_pdfs = len([e for e in self.docket_entries if e.has_pdf])
        entries_without_pdfs = total_docket_entries - entries_with_pdfs
        
        total_documents = len(self.downloaded_documents)
        matched_documents = len([d for d in self.downloaded_documents if d.matched_docket_entries])
        unmatched_documents = total_documents - matched_documents
        
        # Party breakdown
        party_stats = {}
        for entry in self.docket_entries:
            party = entry.party or 'Unknown'
            if party not in party_stats:
                party_stats[party] = {'total': 0, 'with_pdf': 0}
            party_stats[party]['total'] += 1
            if entry.has_pdf:
                party_stats[party]['with_pdf'] += 1
        
        # Detect inappropriate filings
        inappropriate_filings = self.detect_inappropriate_filings()
        
        # Detect missing records and potential tampering
        missing_records_analysis = self.detect_missing_records_and_tampering()
        
        self.reconciliation_results = {
            'timestamp': datetime.now().isoformat(),
            'case_number': self.case_number,
            'statistics': {
                'total_docket_entries': total_docket_entries,
                'entries_with_pdfs': entries_with_pdfs,
                'entries_without_pdfs': entries_without_pdfs,
                'pdf_coverage_percentage': (entries_with_pdfs / total_docket_entries * 100) if total_docket_entries > 0 else 0,
                'total_documents': total_documents,
                'matched_documents': matched_documents,
                'unmatched_documents': unmatched_documents,
                'matching_efficiency': (matched_documents / total_documents * 100) if total_documents > 0 else 0
            },
            'party_breakdown': party_stats,
            'inappropriate_filings': inappropriate_filings,
            'missing_records_analysis': missing_records_analysis,
            'docket_entries': [asdict(entry) for entry in self.docket_entries],
            'downloaded_documents': [asdict(doc) for doc in self.downloaded_documents],
            'missing_pdfs': [
                {
                    'date': entry.date,
                    'entry_number': entry.entry_number,
                    'description': entry.description,
                    'party': entry.party,
                    'document_type': entry.document_type
                }
                for entry in self.docket_entries if not entry.has_pdf
            ],
            'unmatched_documents': [
                {
                    'filename': doc.filename,
                    'document_type': doc.document_type,
                    'source': doc.source,
                    'efile_id': doc.efile_id
                }
                for doc in self.downloaded_documents if not doc.matched_docket_entries
            ]
        }
    
    def create_enhanced_docket_view(self) -> Dict:
        """Create enhanced docket view where every entry links to its PDF"""
        enhanced_docket = {
            'case_number': self.case_number,
            'generated_at': datetime.now().isoformat(),
            'total_entries': len(self.docket_entries),
            'entries_with_pdfs': len([e for e in self.docket_entries if e.has_pdf]),
            'entries': []
        }
        
        for entry in sorted(self.docket_entries, key=lambda x: (x.date, x.entry_number)):
            enhanced_entry = {
                'date': entry.date,
                'entry_number': entry.entry_number,
                'document_type': entry.document_type,
                'description': entry.description,
                'party': entry.party,
                'attorney': entry.attorney,
                'has_pdf': entry.has_pdf,
                'pdf_documents': []
            }
            
            # Add PDF document links
            for doc_name in entry.matched_documents:
                doc = next((d for d in self.downloaded_documents if d.filename == doc_name), None)
                if doc:
                    enhanced_entry['pdf_documents'].append({
                        'filename': doc.filename,
                        'original_filename': doc.original_filename,
                        'filepath': doc.filepath,
                        'size': doc.size,
                        'source': doc.source,
                        'similarity_score': doc.similarity_scores.get(f"{entry.date}_{entry.entry_number}", 0.0)
                    })
            
            enhanced_docket['entries'].append(enhanced_entry)
        
        return enhanced_docket
    
    def export_results(self, output_dir: str):
        """Export all reconciliation results"""
        output_path = Path(output_dir)
        output_path.mkdir(exist_ok=True)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Export reconciliation report
        reconciliation_file = output_path / f"docket_reconciliation_{self.case_number.replace(' ', '_')}_{timestamp}.json"
        with open(reconciliation_file, 'w') as f:
            json.dump(self.reconciliation_results, f, indent=2)
        
        # Export enhanced docket view
        enhanced_docket = self.create_enhanced_docket_view()
        enhanced_file = output_path / f"enhanced_docket_{self.case_number.replace(' ', '_')}_{timestamp}.json"
        with open(enhanced_file, 'w') as f:
            json.dump(enhanced_docket, f, indent=2)
        
        # Export HTML report
        html_file = output_path / f"docket_reconciliation_report_{self.case_number.replace(' ', '_')}_{timestamp}.html"
        self.create_html_report(html_file, enhanced_docket)
        
        logger.info(f"Results exported to {output_path}")
        return {
            'reconciliation_report': str(reconciliation_file),
            'enhanced_docket': str(enhanced_file),
            'html_report': str(html_file)
        }
    
    def create_html_report(self, output_file: str, enhanced_docket: Dict):
        """Create comprehensive HTML report"""
        stats = self.reconciliation_results['statistics']
        
        html_content = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>Docket Reconciliation Report - {self.case_number}</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; }}
                .header {{ background: #f0f8ff; padding: 20px; border-radius: 8px; margin-bottom: 20px; }}
                .stats {{ display: flex; justify-content: space-around; margin: 20px 0; }}
                .stat-box {{ background: #f9f9f9; padding: 15px; border-radius: 8px; text-align: center; }}
                .stat-number {{ font-size: 24px; font-weight: bold; color: #2c5aa0; }}
                .docket-table {{ width: 100%; border-collapse: collapse; margin-top: 20px; }}
                .docket-table th, .docket-table td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
                .docket-table th {{ background-color: #f2f2f2; }}
                .has-pdf {{ background-color: #e8f5e8; }}
                .no-pdf {{ background-color: #ffe8e8; }}
                .pdf-link {{ color: #2c5aa0; text-decoration: none; }}
                .pdf-link:hover {{ text-decoration: underline; }}
                .party-p1 {{ color: #2e7d32; font-weight: bold; }}
                .party-d1 {{ color: #d32f2f; font-weight: bold; }}
                .party-court {{ color: #1976d2; font-weight: bold; }}
                .missing-pdfs {{ margin-top: 30px; }}
                .missing-pdf-item {{ background: #fff3e0; padding: 10px; margin: 5px 0; border-left: 4px solid #ff9800; }}
            </style>
        </head>
        <body>
            <div class="header">
                <h1>📋 Docket Reconciliation Report</h1>
                <h2>Case: {self.case_number}</h2>
                <p>Generated: {enhanced_docket['generated_at']}</p>
            </div>
            
            <div class="stats">
                <div class="stat-box">
                    <div class="stat-number">{stats['total_docket_entries']}</div>
                    <div>Total Entries</div>
                </div>
                <div class="stat-box">
                    <div class="stat-number">{stats['entries_with_pdfs']}</div>
                    <div>With PDFs</div>
                </div>
                <div class="stat-box">
                    <div class="stat-number">{stats['entries_without_pdfs']}</div>
                    <div>Missing PDFs</div>
                </div>
                <div class="stat-box">
                    <div class="stat-number">{stats['pdf_coverage_percentage']:.1f}%</div>
                    <div>PDF Coverage</div>
                </div>
            </div>
            
            <h2>📄 Enhanced Docket View</h2>
            <p>Every docket entry with links to corresponding PDF justifications:</p>
            
            <table class="docket-table">
                <thead>
                    <tr>
                        <th>Date</th>
                        <th>Entry #</th>
                        <th>Type</th>
                        <th>Description</th>
                        <th>Party</th>
                        <th>Attorney</th>
                        <th>PDF Documents</th>
                    </tr>
                </thead>
                <tbody>
        """
        
        for entry in enhanced_docket['entries']:
            row_class = 'has-pdf' if entry['has_pdf'] else 'no-pdf'
            party_class = f"party-{entry['party'].lower()}" if entry['party'] else ''
            
            pdf_links = []
            for pdf in entry['pdf_documents']:
                filename = pdf['original_filename'] or pdf['filename']
                score = pdf.get('similarity_score', 0.0)
                pdf_links.append(f'<a href="{pdf["filepath"]}" class="pdf-link" title="Similarity: {score:.1f}">{filename}</a>')
            
            pdf_cell = '<br>'.join(pdf_links) if pdf_links else '❌ No PDF'
            
            html_content += f"""
                    <tr class="{row_class}">
                        <td>{entry['date']}</td>
                        <td>{entry['entry_number']}</td>
                        <td>{entry['document_type']}</td>
                        <td>{entry['description']}</td>
                        <td class="{party_class}">{entry['party']}</td>
                        <td>{entry['attorney']}</td>
                        <td>{pdf_cell}</td>
                    </tr>
            """
        
        html_content += """
                </tbody>
            </table>
        """
        
        # Add inappropriate filings section
        inappropriate_filings = self.reconciliation_results.get('inappropriate_filings', [])
        if inappropriate_filings:
            html_content += f"""
            <div class="inappropriate-filings" style="margin-top: 30px;">
                <h2>⚠️ Inappropriate Party Assignments</h2>
                <p style="color: #d32f2f;">Found {len(inappropriate_filings)} docket entries with potentially incorrect party designations:</p>
            """
            
            for filing in inappropriate_filings:
                severity_color = {'high': '#d32f2f', 'medium': '#f57c00', 'low': '#388e3c'}
                color = severity_color.get(filing.get('max_severity', 'medium'), '#666')
                
                html_content += f"""
                    <div style="background: #fff3e0; padding: 15px; margin: 10px 0; border-left: 4px solid {color}; border-radius: 4px;">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
                            <strong style="color: {color};">{filing['date']} - {filing['document_type']}</strong>
                            <span style="background: {color}; color: white; padding: 2px 8px; border-radius: 12px; font-size: 12px; font-weight: bold;">
                                {filing['max_severity'].upper()}
                            </span>
                        </div>
                        <div style="margin-bottom: 8px;">
                            <strong>Description:</strong> {filing['description']}<br>
                            <strong>Party:</strong> {filing['party']} | <strong>Attorney:</strong> {filing['attorney']}
                        </div>
                        <div style="background: #fff; padding: 10px; border-radius: 4px; border: 1px solid #e0e0e0;">
                            <strong>Issues Found ({filing['total_issues']}):</strong>
                            <ul style="margin: 5px 0; padding-left: 20px;">
                """
                
                for issue in filing['issues']:
                    html_content += f"<li style='margin: 5px 0;'><strong>{issue['type'].replace('_', ' ').title()}:</strong> {issue['issue']}</li>"
                
                html_content += f"""
                            </ul>
                            <small style="color: #666;">Entry {filing['entry_number']} | Page {filing['page_number']}, Row {filing['row_number']}</small>
                        </div>
                    </div>
                """
            
            html_content += "</div>"
        
        # Add missing records and tampering analysis section
        missing_analysis = self.reconciliation_results.get('missing_records_analysis', {})
        if missing_analysis.get('summary', {}).get('total_issues', 0) > 0:
            summary = missing_analysis['summary']
            html_content += f"""
            <div class="missing-records-analysis" style="margin-top: 30px;">
                <h2>🚨 Missing Records & Tampering Analysis</h2>
                <div style="background: #ffebee; padding: 15px; border-radius: 8px; border-left: 4px solid #d32f2f; margin-bottom: 20px;">
                    <strong style="color: #d32f2f;">CRITICAL ISSUES DETECTED:</strong>
                    <ul style="margin: 10px 0; padding-left: 20px;">
                        <li><strong>{summary['total_issues']}</strong> total integrity issues found</li>
                        <li><strong>{summary['high_priority_issues']}</strong> high priority concerns</li>
                        <li><strong>{summary['potential_tampering_indicators']}</strong> potential tampering indicators</li>
                    </ul>
                    <em>These issues may indicate record manipulation, missing documents, or procedural violations.</em>
                </div>
            """
            
            # Missing supporting documents
            if missing_analysis.get('missing_supporting_documents'):
                docs = missing_analysis['missing_supporting_documents']
                html_content += f"""
                <h3>📄 Missing Supporting Documents ({len(docs)} issues)</h3>
                <p style="color: #d32f2f;">Docket entries that should have supporting documents but don't:</p>
                """
                for doc in docs:
                    severity_color = '#d32f2f' if doc['severity'] == 'high' else '#f57c00'
                    html_content += f"""
                    <div style="background: #fff3e0; padding: 10px; margin: 5px 0; border-left: 4px solid {severity_color};">
                        <strong>{doc['date']} - {doc['document_type']}</strong> (Party: {doc['party']})<br>
                        <em>{doc['description']}</em><br>
                        <small style="color: {severity_color};">Reason: {doc['reason']}</small>
                    </div>
                    """
            
            # Entry sequence gaps
            if missing_analysis.get('entry_sequence_gaps'):
                gaps = missing_analysis['entry_sequence_gaps']
                html_content += f"""
                <h3>🔢 Docket Entry Sequence Gaps ({len(gaps)} gaps)</h3>
                <p style="color: #d32f2f;">Missing consecutive docket entries (potential record removal):</p>
                """
                for gap in gaps:
                    html_content += f"""
                    <div style="background: #ffebee; padding: 10px; margin: 5px 0; border-left: 4px solid #d32f2f;">
                        <strong>Missing entries {gap['gap_between']}</strong> (Gap: {gap['gap_size']} entries)<br>
                        <strong>Missing numbers:</strong> {', '.join(map(str, gap['missing_entry_numbers']))}<br>
                        <em>Between: "{gap['before_entry']['description']}" and "{gap['after_entry']['description']}"</em>
                    </div>
                    """
            
            # Suspicious modifications
            if missing_analysis.get('suspicious_modifications'):
                mods = missing_analysis['suspicious_modifications']
                html_content += f"""
                <h3>⚠️ Suspicious Modifications ({len(mods)} entries)</h3>
                <p style="color: #d32f2f;">Entries with potential tampering or corruption:</p>
                """
                for mod in mods:
                    html_content += f"""
                    <div style="background: #ffebee; padding: 15px; margin: 10px 0; border-left: 4px solid #d32f2f;">
                        <strong style="color: #d32f2f;">{mod['date']} - Entry {mod['entry_number']}</strong><br>
                        <strong>Description:</strong> "{mod['description']}"<br>
                        <strong>Issues detected:</strong>
                        <ul style="margin: 5px 0; padding-left: 20px;">
                    """
                    for issue in mod['issues']:
                        html_content += f"<li style='color: #d32f2f;'>{issue}</li>"
                    html_content += f"""
                        </ul>
                        <small>Page {mod['page_number']}, Row {mod['row_number']}</small>
                    </div>
                    """
            
            # Court entries without support
            if missing_analysis.get('court_entries_without_support'):
                court_entries = missing_analysis['court_entries_without_support']
                html_content += f"""
                <h3>⚖️ Court Actions Without Documentation ({len(court_entries)} entries)</h3>
                <p style="color: #d32f2f;">Court orders/judgments without supporting documents:</p>
                """
                for entry in court_entries:
                    html_content += f"""
                    <div style="background: #fff3e0; padding: 10px; margin: 5px 0; border-left: 4px solid #d32f2f;">
                        <strong>{entry['date']} - {entry['action_type']}</strong><br>
                        <em>{entry['description']}</em><br>
                        <small style="color: #d32f2f;">Court action without supporting documentation</small>
                    </div>
                    """
            
            # Cross-source discrepancies
            if missing_analysis.get('cross_source_discrepancies'):
                discrepancies = missing_analysis['cross_source_discrepancies']
                html_content += f"""
                <h3>🔄 Cross-Source Discrepancies ({len(discrepancies)} issues)</h3>
                <p style="color: #f57c00;">Documents that appear in one source but not others:</p>
                """
                for disc in discrepancies:
                    html_content += f"""
                    <div style="background: #fff8e1; padding: 8px; margin: 3px 0; border-left: 4px solid #f57c00;">
                        <strong>{disc['document_name']}</strong><br>
                        <small style="color: #f57c00;">Present in {disc['present_in']}, missing from {disc['missing_from']}</small>
                    </div>
                    """
            
            # Orphaned downloads
            if missing_analysis.get('orphaned_downloads'):
                orphans = missing_analysis['orphaned_downloads']
                html_content += f"""
                <h3>👻 Orphaned Downloads ({len(orphans)} files)</h3>
                <p style="color: #f57c00;">Downloaded documents with no corresponding docket entries:</p>
                """
                for orphan in orphans:
                    html_content += f"""
                    <div style="background: #fff8e1; padding: 8px; margin: 3px 0; border-left: 4px solid #f57c00;">
                        <strong>{orphan['original_filename'] or orphan['filename']}</strong> (Source: {orphan['source']})<br>
                        <small>Type: {orphan['document_type']} | Size: {orphan['size']} bytes</small>
                    </div>
                    """
            
            html_content += "</div>"
        
        # Add missing PDFs section
        missing_pdfs = self.reconciliation_results['missing_pdfs']
        if missing_pdfs:
            html_content += """
                </tbody>
            </table>
            
            <div class="missing-pdfs">
                <h2>❌ Missing PDF Justifications</h2>
                <p>These docket entries do not have corresponding PDF documents:</p>
            """
            
            for missing in missing_pdfs:
                html_content += f"""
                    <div class="missing-pdf-item">
                        <strong>{missing['date']} - {missing['document_type']}</strong><br>
                        {missing['description']}<br>
                        <small>Party: {missing['party']} | Entry: {missing['entry_number']}</small>
                    </div>
                """
            
            html_content += "</div>"
        
        html_content += """
            </tbody>
        </table>
        
        <div style="margin-top: 40px; padding: 20px; background: #f8f9fa; border-radius: 8px;">
            <h3>📊 Party Breakdown</h3>
        """
        
        for party, stats in self.reconciliation_results['party_breakdown'].items():
            coverage = (stats['with_pdf'] / stats['total'] * 100) if stats['total'] > 0 else 0
            html_content += f"""
                <p><strong>{party}:</strong> {stats['with_pdf']}/{stats['total']} entries have PDFs ({coverage:.1f}%)</p>
            """
        
        html_content += """
        </div>
        
        </body>
        </html>
        """
        
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(html_content)
    
    def detect_inappropriate_filings(self) -> List[Dict]:
        """
        Detect filings where attorney doesn't match expected party designation
        Examples: Attorney Basta represents D1 but filing shows P1
        """
        inappropriate = []
        
        # Define known attorney-party relationships
        attorney_party_map = {
            'BASTA': ['D1', 'D1A'],  # Defendant attorney
            'DEFENDANT': ['D1', 'D1A'],
            'DEFENSE': ['D1', 'D1A'],
        }
        
        # Define party expectations by type
        expected_party_patterns = {
            'P1': ['PLAINTIFF', 'PETITIONER'],
            'D1': ['DEFENDANT', 'RESPONDENT'], 
            'D1A': ['DEFENDANT ATTORNEY', 'DEFENSE ATTORNEY', 'COUNSEL FOR DEFENDANT'],
            'N/A': ['COURT', 'JUDGE', 'MAGISTRATE', 'CLERK']
        }
        
        for entry in self.docket_entries:
            issues = []
            
            # Check attorney-party mismatch
            if entry.attorney and entry.party:
                attorney_upper = entry.attorney.upper()
                
                # Check if attorney name suggests different party than assigned
                for attorney_keyword, expected_parties in attorney_party_map.items():
                    if attorney_keyword in attorney_upper:
                        if entry.party not in expected_parties:
                            issues.append({
                                'type': 'attorney_party_mismatch',
                                'issue': f"Attorney '{entry.attorney}' typically represents {'/'.join(expected_parties)} but filing shows {entry.party}",
                                'severity': 'high',
                                'expected_party': expected_parties,
                                'actual_party': entry.party
                            })
            
            # Check party designation consistency with description/document type
            if entry.party and entry.description:
                description_upper = entry.description.upper()
                document_type_upper = entry.document_type.upper() if entry.document_type else ''
                combined_text = f"{description_upper} {document_type_upper}"
                
                # Look for party indicators in description that don't match assigned party
                for party, indicators in expected_party_patterns.items():
                    for indicator in indicators:
                        if indicator in combined_text and entry.party != party:
                            # Only flag if it's a clear contradiction
                            if (party == 'P1' and entry.party in ['D1', 'D1A']) or \
                               (party in ['D1', 'D1A'] and entry.party == 'P1') or \
                               (party == 'N/A' and entry.party in ['P1', 'D1', 'D1A']):
                                issues.append({
                                    'type': 'party_description_mismatch',
                                    'issue': f"Description contains '{indicator}' suggesting {party} but filing shows {entry.party}",
                                    'severity': 'medium',
                                    'description_indicator': indicator,
                                    'expected_party': party,
                                    'actual_party': entry.party
                                })
            
            # Check for specific case: filings by attorneys marked as opposing party
            if entry.attorney and entry.party == 'P1':
                attorney_upper = entry.attorney.upper()
                # Common defendant attorney indicators
                defendant_attorney_keywords = ['BASTA', 'DEFENSE', 'DEFENDANT', 'RESPONDENT']
                
                for keyword in defendant_attorney_keywords:
                    if keyword in attorney_upper:
                        issues.append({
                            'type': 'opposing_party_filing',
                            'issue': f"Attorney '{entry.attorney}' appears to represent defendant but filing marked as P1 (Plaintiff)",
                            'severity': 'high',
                            'attorney_keyword': keyword,
                            'expected_party': 'D1A',
                            'actual_party': entry.party
                        })
            
            # Add entry to inappropriate filings if issues found
            if issues:
                inappropriate_entry = {
                    'date': entry.date,
                    'entry_number': entry.entry_number,
                    'document_type': entry.document_type,
                    'description': entry.description,
                    'party': entry.party,
                    'attorney': entry.attorney,
                    'issues': issues,
                    'total_issues': len(issues),
                    'max_severity': max(issue['severity'] for issue in issues),
                    'page_number': entry.page_number,
                    'row_number': entry.row_number
                }
                inappropriate.append(inappropriate_entry)
        
        logger.info(f"Found {len(inappropriate)} docket entries with inappropriate party assignments")
        
        # Sort by severity (high first) and then by date
        severity_order = {'high': 0, 'medium': 1, 'low': 2}
        inappropriate.sort(key=lambda x: (severity_order.get(x['max_severity'], 3), x['date']))
        
        return inappropriate

    def detect_missing_records_and_tampering(self) -> Dict:
        """
        Detect missing records across different data sources and potential tampering
        Analyzes discrepancies between docket entries, downloaded documents, and filing records
        """
        analysis = {
            'missing_supporting_documents': [],
            'orphaned_downloads': [],
            'entry_sequence_gaps': [],
            'suspicious_modifications': [],
            'cross_source_discrepancies': [],
            'court_entries_without_support': [],
            'summary': {
                'total_issues': 0,
                'high_priority_issues': 0,
                'potential_tampering_indicators': 0
            }
        }
        
        # 1. Check for docket entries without corresponding supporting documents
        self.check_missing_supporting_documents(analysis)
        
        # 2. Check for downloaded documents that don't match any docket entries
        self.check_orphaned_downloads(analysis)
        
        # 3. Check for gaps in entry sequence numbers
        self.check_entry_sequence_gaps(analysis)
        
        # 4. Check for suspicious modifications (gibberish text, unusual patterns)
        self.check_suspicious_modifications(analysis)
        
        # 5. Check court entries without any supporting evidence
        self.check_court_entries_without_support(analysis)
        
        # 6. Cross-reference different data sources for discrepancies
        self.check_cross_source_discrepancies(analysis)
        
        # Calculate summary statistics
        total_issues = (len(analysis['missing_supporting_documents']) + 
                       len(analysis['orphaned_downloads']) + 
                       len(analysis['entry_sequence_gaps']) +
                       len(analysis['suspicious_modifications']) +
                       len(analysis['cross_source_discrepancies']) +
                       len(analysis['court_entries_without_support']))
        
        analysis['summary']['total_issues'] = total_issues
        analysis['summary']['high_priority_issues'] = (
            len(analysis['suspicious_modifications']) + 
            len(analysis['entry_sequence_gaps'])
        )
        analysis['summary']['potential_tampering_indicators'] = len(analysis['suspicious_modifications'])
        
        logger.info(f"Missing records analysis: {total_issues} total issues, {analysis['summary']['high_priority_issues']} high priority")
        
        return analysis
    
    def check_missing_supporting_documents(self, analysis: Dict):
        """Check for docket entries that should have supporting documents but don't"""
        document_types_requiring_support = [
            'MOTION', 'AFFIDAVIT', 'ANSWER', 'COMPLAINT', 'NOTICE', 
            'ORDER', 'JUDGMENT', 'MEMORANDUM', 'BRIEF', 'EXHIBIT'
        ]
        
        for entry in self.docket_entries:
            if not entry.has_pdf:
                doc_type_upper = entry.document_type.upper() if entry.document_type else ''
                
                # Check if this type of document should have supporting files
                requires_support = any(req_type in doc_type_upper for req_type in document_types_requiring_support)
                
                # Also check if description suggests a document was filed
                description_upper = entry.description.upper() if entry.description else ''
                filing_indicators = ['FILED', 'SUBMITTED', 'ATTACHED', 'EXHIBIT', 'DOCUMENT']
                has_filing_language = any(indicator in description_upper for indicator in filing_indicators)
                
                if requires_support or has_filing_language:
                    severity = 'high' if entry.party in ['P1', 'D1', 'D1A'] else 'medium'
                    
                    analysis['missing_supporting_documents'].append({
                        'date': entry.date,
                        'entry_number': entry.entry_number,
                        'document_type': entry.document_type,
                        'description': entry.description,
                        'party': entry.party,
                        'attorney': entry.attorney,
                        'severity': severity,
                        'reason': 'Document type requires supporting files' if requires_support else 'Description suggests filed document',
                        'page_number': entry.page_number,
                        'row_number': entry.row_number
                    })
    
    def check_orphaned_downloads(self, analysis: Dict):
        """Check for downloaded documents that don't match any docket entries"""
        for doc in self.downloaded_documents:
            if not doc.matched_docket_entries:
                # This document was downloaded but doesn't match any docket entry
                analysis['orphaned_downloads'].append({
                    'filename': doc.filename,
                    'original_filename': doc.original_filename,
                    'document_type': doc.document_type,
                    'source': doc.source,
                    'efile_id': doc.efile_id,
                    'filepath': doc.filepath,
                    'size': doc.size,
                    'concern': 'Downloaded document has no corresponding docket entry',
                    'severity': 'medium'
                })
    
    def check_entry_sequence_gaps(self, analysis: Dict):
        """Check for gaps in docket entry sequence numbers"""
        if not self.docket_entries:
            return
        
        # Extract numeric entry numbers and sort
        numeric_entries = []
        for entry in self.docket_entries:
            try:
                entry_num = int(re.search(r'\d+', entry.entry_number).group()) if entry.entry_number else 0
                if entry_num > 0:
                    numeric_entries.append((entry_num, entry))
            except:
                continue
        
        if len(numeric_entries) < 2:
            return
        
        numeric_entries.sort(key=lambda x: x[0])
        
        # Look for gaps in sequence
        for i in range(1, len(numeric_entries)):
            current_num, current_entry = numeric_entries[i]
            prev_num, prev_entry = numeric_entries[i-1]
            
            gap = current_num - prev_num
            if gap > 1:  # Missing entry numbers
                missing_numbers = list(range(prev_num + 1, current_num))
                
                analysis['entry_sequence_gaps'].append({
                    'gap_between': f"{prev_num} and {current_num}",
                    'missing_entry_numbers': missing_numbers,
                    'gap_size': len(missing_numbers),
                    'before_entry': {
                        'number': prev_entry.entry_number,
                        'date': prev_entry.date,
                        'description': prev_entry.description
                    },
                    'after_entry': {
                        'number': current_entry.entry_number,
                        'date': current_entry.date,
                        'description': current_entry.description
                    },
                    'severity': 'high' if len(missing_numbers) > 5 else 'medium',
                    'concern': f"Missing {len(missing_numbers)} consecutive docket entries"
                })
    
    def check_suspicious_modifications(self, analysis: Dict):
        """Check for entries that might have been tampered with or corrupted"""
        for entry in self.docket_entries:
            issues = []
            
            # Check for gibberish text (random characters, excessive special chars)
            if entry.description:
                desc = entry.description
                
                # Count special characters vs letters
                special_chars = sum(1 for c in desc if not c.isalnum() and not c.isspace())
                letters = sum(1 for c in desc if c.isalpha())
                
                if letters > 0:
                    special_ratio = special_chars / letters
                    if special_ratio > 0.5:  # More than 50% special characters
                        issues.append("Excessive special characters in description")
                
                # Check for random character patterns
                if len(desc) > 10:
                    # Look for patterns like consecutive random chars
                    if re.search(r'[a-zA-Z]{1}[^a-zA-Z\s]{3,}[a-zA-Z]{1}', desc):
                        issues.append("Suspicious character patterns detected")
                
                # Check for truncated or incomplete words
                words = desc.split()
                incomplete_words = [w for w in words if len(w) > 2 and not w[-1].isalnum()]
                if len(incomplete_words) > len(words) * 0.3:  # More than 30% incomplete words
                    issues.append("High percentage of incomplete or truncated words")
                
                # Check for encoding issues
                if '?' in desc and desc.count('?') > len(desc) * 0.1:
                    issues.append("Possible encoding corruption (excessive question marks)")
            
            # Check for missing critical information
            critical_missing = []
            if not entry.document_type or entry.document_type.strip() == '':
                critical_missing.append("document_type")
            if not entry.description or entry.description.strip() == '':
                critical_missing.append("description")
            if not entry.party or entry.party.strip() == '':
                critical_missing.append("party")
            
            if len(critical_missing) >= 2:  # Missing 2+ critical fields
                issues.append(f"Missing critical information: {', '.join(critical_missing)}")
            
            if issues:
                analysis['suspicious_modifications'].append({
                    'date': entry.date,
                    'entry_number': entry.entry_number,
                    'document_type': entry.document_type,
                    'description': entry.description,
                    'party': entry.party,
                    'attorney': entry.attorney,
                    'issues': issues,
                    'total_issues': len(issues),
                    'severity': 'high',
                    'page_number': entry.page_number,
                    'row_number': entry.row_number,
                    'concern': 'Potential tampering or corruption detected'
                })
    
    def check_court_entries_without_support(self, analysis: Dict):
        """Check for court entries (orders, judgments) without supporting documentation"""
        court_entry_types = ['ORDER', 'JUDGMENT', 'RULING', 'DECISION', 'MINUTE']
        
        for entry in self.docket_entries:
            if entry.party == 'N/A' or (entry.attorney and 'COURT' in entry.attorney.upper()):
                doc_type_upper = entry.document_type.upper() if entry.document_type else ''
                
                is_court_action = any(court_type in doc_type_upper for court_type in court_entry_types)
                
                if is_court_action and not entry.has_pdf:
                    analysis['court_entries_without_support'].append({
                        'date': entry.date,
                        'entry_number': entry.entry_number,
                        'document_type': entry.document_type,
                        'description': entry.description,
                        'party': entry.party,
                        'attorney': entry.attorney,
                        'severity': 'high',
                        'concern': 'Court action without supporting documentation',
                        'action_type': doc_type_upper,
                        'page_number': entry.page_number,
                        'row_number': entry.row_number
                    })
    
    def check_cross_source_discrepancies(self, analysis: Dict):
        """Check for discrepancies between different data sources"""
        # Group downloaded documents by source
        by_source = {}
        for doc in self.downloaded_documents:
            source = doc.source
            if source not in by_source:
                by_source[source] = []
            by_source[source].append(doc)
        
        # Look for documents that appear in one source but not others
        if len(by_source) > 1:
            sources = list(by_source.keys())
            
            for i, source1 in enumerate(sources):
                for source2 in sources[i+1:]:
                    # Compare document lists between sources
                    source1_docs = {doc.original_filename or doc.filename for doc in by_source[source1]}
                    source2_docs = {doc.original_filename or doc.filename for doc in by_source[source2]}
                    
                    # Documents in source1 but not source2
                    missing_in_source2 = source1_docs - source2_docs
                    # Documents in source2 but not source1  
                    missing_in_source1 = source2_docs - source1_docs
                    
                    if missing_in_source2:
                        for doc_name in missing_in_source2:
                            analysis['cross_source_discrepancies'].append({
                                'document_name': doc_name,
                                'present_in': source1,
                                'missing_from': source2,
                                'severity': 'medium',
                                'concern': f'Document found in {source1} but missing from {source2}'
                            })
                    
                    if missing_in_source1:
                        for doc_name in missing_in_source1:
                            analysis['cross_source_discrepancies'].append({
                                'document_name': doc_name,
                                'present_in': source2,
                                'missing_from': source1,
                                'severity': 'medium',
                                'concern': f'Document found in {source2} but missing from {source1}'
                            })

    # Helper methods for matching and data processing
    def extract_date(self, data: Dict) -> str:
        """Extract date from docket entry data"""
        for key in ['Filing Date', 'Date Filed', 'Date', 'Filing_Date']:
            if key in data and data[key]:
                return data[key].strip()
        return ''
    
    def extract_entry_number(self, data: Dict) -> str:
        """Extract entry number from docket entry data"""
        for key in ['Entry', 'Entry Number', 'Docket', 'Entry_Number']:
            if key in data and data[key]:
                return data[key].strip()
        return ''
    
    def extract_document_type(self, data: Dict) -> str:
        """Extract document type from docket entry data"""
        for key in ['Document Type', 'Type', 'Doc Type', 'Document_Type']:
            if key in data and data[key]:
                return data[key].strip()
        return ''
    
    def extract_description(self, data: Dict) -> str:
        """Extract description from docket entry data"""
        for key in ['Description', 'Title', 'Document Description']:
            if key in data and data[key]:
                return data[key].strip()
        return ''
    
    def extract_party(self, data: Dict) -> str:
        """Extract party information and normalize to P1/D1/D1A/N/A"""
        for key in ['Party', 'Filed By', 'Filer', 'Attorney']:
            if key in data and data[key]:
                party_text = data[key].strip().upper()
                
                # Map various party indicators to standard format
                if 'PLAINTIFF' in party_text or 'P1' in party_text:
                    return 'P1'
                elif 'DEFENDANT' in party_text or 'D1' in party_text:
                    if 'ATTORNEY' in party_text or 'ATT' in party_text:
                        return 'D1A'
                    else:
                        return 'D1'
                elif 'COURT' in party_text or 'JUDGE' in party_text or 'MAGISTRATE' in party_text:
                    return 'N/A'
        
        return 'Unknown'
    
    def extract_attorney(self, data: Dict) -> str:
        """Extract attorney information from docket entry data"""
        for key in ['Attorney', 'Counsel', 'Filed By Attorney']:
            if key in data and data[key]:
                return data[key].strip()
        return ''
    
    def normalize_date(self, date_str: str) -> str:
        """Normalize date string for comparison"""
        if not date_str:
            return ''
        
        # Remove common separators and normalize format
        normalized = re.sub(r'[/-]', '', date_str.strip())
        return normalized
    
    def normalize_document_type(self, doc_type: str) -> str:
        """Normalize document type for comparison"""
        if not doc_type:
            return ''
        
        # Common document type mappings
        type_map = {
            'MOTION': ['MOTION', 'MO'],
            'AFFIDAVIT': ['AFFIDAVIT', 'AF'], 
            'ANSWER': ['ANSWER', 'AN'],
            'COMPLAINT': ['COMPLAINT', 'COMP'],
            'NOTICE': ['NOTICE', 'NT'],
            'ORDER': ['ORDER', 'OR'],
            'JUDGMENT': ['JUDGMENT', 'JE']
        }
        
        doc_type_upper = doc_type.upper()
        for standard_type, variations in type_map.items():
            if any(var in doc_type_upper for var in variations):
                return standard_type
        
        return doc_type_upper
    
    def dates_are_close(self, date1: str, date2: str) -> bool:
        """Check if two dates are within a reasonable range"""
        # Simple implementation - could be enhanced with actual date parsing
        return abs(len(date1) - len(date2)) <= 2
    
    def document_types_match(self, type1: str, type2: str) -> bool:
        """Check if document types are similar enough to match"""
        if not type1 or not type2:
            return False
        
        return type1 == type2 or \
               type1 in type2 or type2 in type1 or \
               SequenceMatcher(None, type1, type2).ratio() > 0.7
    
    def calculate_similarity_score(self, doc: DownloadedDocument, entry: DocketEntry) -> float:
        """Calculate overall similarity score between document and docket entry"""
        scores = []
        
        # Date similarity (if available)
        if doc.filing_date and entry.date:
            date_similarity = 1.0 if self.normalize_date(doc.filing_date) == self.normalize_date(entry.date) else 0.5
            scores.append(date_similarity)
        
        # Document type similarity
        if doc.document_type and entry.document_type:
            type_similarity = SequenceMatcher(None, 
                                            self.normalize_document_type(doc.document_type),
                                            self.normalize_document_type(entry.document_type)).ratio()
            scores.append(type_similarity)
        
        # Description similarity
        if doc.original_filename and entry.description:
            desc_similarity = SequenceMatcher(None, 
                                            doc.original_filename.lower(),
                                            entry.description.lower()).ratio()
            scores.append(desc_similarity)
        
        return sum(scores) / len(scores) if scores else 0.0
    
    def extract_filename_from_url(self, url: str) -> str:
        """Extract filename from URL"""
        return url.split('/')[-1] if url else ''
    
    def infer_document_type_from_context(self, record: Dict) -> str:
        """Infer document type from record context"""
        data = record.get('data', {})
        for value in data.values():
            if isinstance(value, str):
                value_upper = value.upper()
                if any(doc_type in value_upper for doc_type in ['MOTION', 'AFFIDAVIT', 'ANSWER', 'COMPLAINT']):
                    return value_upper
        return ''
    
    def infer_document_type_from_filename(self, filename: str) -> str:
        """Infer document type from filename"""
        filename_upper = filename.upper()
        doc_types = ['MOTION', 'AFFIDAVIT', 'ANSWER', 'COMPLAINT', 'NOTICE', 'ORDER', 'JUDGMENT']
        
        for doc_type in doc_types:
            if doc_type in filename_upper:
                return doc_type
        
        return 'DOCUMENT'
    
    def infer_party_from_document(self, doc: DownloadedDocument) -> str:
        """Infer party from document metadata or filename"""
        # Check filename for party indicators
        filename_upper = doc.original_filename.upper()
        
        if 'PLAINTIFF' in filename_upper or 'P1' in filename_upper:
            return 'P1'
        elif 'DEFENDANT' in filename_upper or 'D1' in filename_upper:
            return 'D1'
        elif 'COURT' in filename_upper:
            return 'N/A'
        
        return ''

# Convenience function to run full reconciliation
def reconcile_docket_with_downloads(docket_file: str, downloads_dir: str, case_number: str = "DR-25-403973", output_dir: str = "reconciliation_results") -> Dict:
    """
    Complete docket reconciliation workflow
    
    Args:
        docket_file: Path to docket scraping results JSON
        downloads_dir: Directory containing downloaded documents  
        case_number: Case number for the reconciliation
        output_dir: Directory to save results
        
    Returns:
        Dictionary with file paths to generated reports
    """
    engine = DocketReconciliationEngine(case_number)
    
    # Load data
    engine.load_docket_data(docket_file)
    engine.load_downloaded_documents(downloads_dir)
    
    # Perform reconciliation
    engine.reconcile_documents_to_docket()
    
    # Export results
    return engine.export_results(output_dir)

if __name__ == "__main__":
    # Example usage
    results = reconcile_docket_with_downloads(
        docket_file="efiling_results_latest.json",
        downloads_dir="all_downloads",
        case_number="DR-25-403973"
    )
    
    print("Reconciliation complete!")
    print(f"Reports generated: {results}")