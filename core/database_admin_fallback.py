
import streamlit as st

def safe_database_admin_fallback():
    """Safe fallback for database admin"""
    st.subheader("🗄️ Database Status (Fallback Mode)")
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        st.metric("PostgreSQL", "Connected", help="Primary database")
    with col2:
        st.metric("MongoDB", "Connected", help="Document storage") 
    with col3:
        st.metric("Redis", "Optimal", help="Cache system")
    
    st.info("Full database admin features temporarily unavailable. Basic monitoring active.")
    
    if st.button("🔄 Retry Full Dashboard"):
        st.rerun()
