#!/usr/bin/env python3
"""
Universal Table Integrity Monitor
Monitors ALL court tables for changes, tampering, and data manipulation
Works with: Docket entries, E-Service records, Case information, etc.
"""

import json
import hashlib
import logging
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional
from dataclasses import dataclass

@dataclass
class TableChange:
    """Represents a change detected in a court table"""
    change_type: str  # INSERT, UPDATE, DELETE, MOVE
    table_name: str
    record_id: str
    old_data: Optional[Dict] = None
    new_data: Optional[Dict] = None
    position_change: Optional[tuple] = None
    timestamp: str = ""
    severity: str = "MEDIUM"  # LOW, MEDIUM, HIGH, CRITICAL

class UniversalTableMonitor:
    """
    Universal monitoring system for ALL court tables
    Detects tampering, changes, and integrity violations
    """
    
    def __init__(self, case_number: str = "DR-25-403973"):
        self.case_number = case_number
        self.logger = logging.getLogger(__name__)
        
        # Storage
        self.monitor_dir = Path("table_monitoring")
        self.monitor_dir.mkdir(exist_ok=True)
        
        # Table baselines
        self.baselines_dir = self.monitor_dir / "baselines"
        self.baselines_dir.mkdir(exist_ok=True)
        
        # Change logs
        self.changes_dir = self.monitor_dir / "changes"
        self.changes_dir.mkdir(exist_ok=True)
        
        # Integrity alerts
        self.alerts_file = self.monitor_dir / f"{case_number}_integrity_alerts.json"
        
        self.setup_logging()
    
    def setup_logging(self):
        """Setup comprehensive logging"""
        handler = logging.FileHandler(self.monitor_dir / "table_monitor.log")
        formatter = logging.Formatter('%(asctime)s - %(levelname)s - [%(name)s] %(message)s')
        handler.setFormatter(formatter)
        self.logger.addHandler(handler)
        self.logger.setLevel(logging.INFO)
    
    def create_table_baseline(self, table_name: str, table_data: List[Dict], metadata: Dict = None):
        """
        Create baseline for any court table
        Supports: docket entries, e-service records, case info, etc.
        """
        baseline = {
            'table_name': table_name,
            'case_number': self.case_number,
            'baseline_date': datetime.now().isoformat(),
            'total_records': len(table_data),
            'records': table_data,
            'metadata': metadata or {},
            'record_hashes': {},
            'position_map': {},
            'integrity_signature': None
        }
        
        # Generate hashes and positions for each record
        for i, record in enumerate(table_data):
            record_id = self.generate_record_id(record)
            record_hash = self.calculate_record_hash(record)
            
            baseline['record_hashes'][record_id] = record_hash
            baseline['position_map'][record_id] = i + 1
        
        # Generate integrity signature for entire table
        baseline['integrity_signature'] = self.calculate_table_signature(table_data)
        
        # Save baseline
        baseline_file = self.baselines_dir / f"{table_name}_{self.case_number}_baseline.json"
        with open(baseline_file, 'w') as f:
            json.dump(baseline, f, indent=2)
        
        self.logger.info(f"✅ Created baseline for {table_name}: {len(table_data)} records")
        return baseline_file
    
    def monitor_table_changes(self, table_name: str, current_data: List[Dict]) -> List[TableChange]:
        """
        Monitor any table for changes
        Returns list of detected changes
        """
        baseline_file = self.baselines_dir / f"{table_name}_{self.case_number}_baseline.json"
        
        if not baseline_file.exists():
            self.logger.warning(f"No baseline found for {table_name}")
            return []
        
        # Load baseline
        with open(baseline_file, 'r') as f:
            baseline = json.load(f)
        
        changes = []
        timestamp = datetime.now().isoformat()
        
        # Create lookup maps
        baseline_records = {self.generate_record_id(rec): rec for rec in baseline['records']}
        current_records = {self.generate_record_id(rec): rec for rec in current_data}
        
        # Detect deletions (CRITICAL)
        for record_id, baseline_record in baseline_records.items():
            if record_id not in current_records:
                change = TableChange(
                    change_type="DELETE",
                    table_name=table_name,
                    record_id=record_id,
                    old_data=baseline_record,
                    timestamp=timestamp,
                    severity="CRITICAL"
                )
                changes.append(change)
                self.logger.critical(f"🚨 DELETION DETECTED in {table_name}: {record_id}")
        
        # Detect insertions
        for record_id, current_record in current_records.items():
            if record_id not in baseline_records:
                change = TableChange(
                    change_type="INSERT",
                    table_name=table_name,
                    record_id=record_id,
                    new_data=current_record,
                    timestamp=timestamp,
                    severity="MEDIUM"
                )
                changes.append(change)
                self.logger.info(f"➕ INSERT DETECTED in {table_name}: {record_id}")
        
        # Detect modifications
        for record_id in baseline_records.keys():
            if record_id in current_records:
                baseline_record = baseline_records[record_id]
                current_record = current_records[record_id]
                
                baseline_hash = self.calculate_record_hash(baseline_record)
                current_hash = self.calculate_record_hash(current_record)
                
                if baseline_hash != current_hash:
                    change = TableChange(
                        change_type="UPDATE",
                        table_name=table_name,
                        record_id=record_id,
                        old_data=baseline_record,
                        new_data=current_record,
                        timestamp=timestamp,
                        severity="HIGH"
                    )
                    changes.append(change)
                    self.logger.warning(f"✏️ UPDATE DETECTED in {table_name}: {record_id}")
        
        # Detect position changes
        current_positions = {self.generate_record_id(rec): i+1 for i, rec in enumerate(current_data)}
        
        for record_id, baseline_pos in baseline['position_map'].items():
            if record_id in current_positions:
                current_pos = current_positions[record_id]
                if baseline_pos != current_pos:
                    change = TableChange(
                        change_type="MOVE",
                        table_name=table_name,
                        record_id=record_id,
                        position_change=(baseline_pos, current_pos),
                        timestamp=timestamp,
                        severity="MEDIUM"
                    )
                    changes.append(change)
                    self.logger.info(f"📍 POSITION CHANGE in {table_name}: {record_id} moved from {baseline_pos} to {current_pos}")
        
        # Log changes
        if changes:
            self.log_table_changes(table_name, changes)
            
            # Handle critical issues
            critical_changes = [c for c in changes if c.severity == "CRITICAL"]
            if critical_changes:
                self.handle_critical_changes(table_name, critical_changes)
        
        return changes
    
    def generate_record_id(self, record: Dict) -> str:
        """Generate unique ID for a table record"""
        # Use multiple fields to create unique ID
        id_fields = []
        
        # Common identifying fields
        for field in ['url', 'Docket Number', 'date_received', 'Date Filed', 'Document Description']:
            if field in record and record[field]:
                id_fields.append(str(record[field]))
        
        # If no good ID fields, use hash of entire record
        if not id_fields:
            return hashlib.md5(json.dumps(record, sort_keys=True).encode()).hexdigest()[:12]
        
        return hashlib.md5('|'.join(id_fields).encode()).hexdigest()[:12]
    
    def calculate_record_hash(self, record: Dict) -> str:
        """Calculate hash for a single record"""
        # Create normalized string representation
        normalized = json.dumps(record, sort_keys=True, separators=(',', ':'))
        return hashlib.sha256(normalized.encode()).hexdigest()
    
    def calculate_table_signature(self, table_data: List[Dict]) -> str:
        """Calculate integrity signature for entire table"""
        all_hashes = [self.calculate_record_hash(record) for record in table_data]
        combined = '|'.join(sorted(all_hashes))
        return hashlib.sha256(combined.encode()).hexdigest()
    
    def log_table_changes(self, table_name: str, changes: List[TableChange]):
        """Log detected changes"""
        change_log = {
            'table_name': table_name,
            'case_number': self.case_number,
            'check_timestamp': datetime.now().isoformat(),
            'changes_detected': len(changes),
            'changes': []
        }
        
        for change in changes:
            change_log['changes'].append({
                'type': change.change_type,
                'record_id': change.record_id,
                'severity': change.severity,
                'position_change': change.position_change,
                'has_old_data': change.old_data is not None,
                'has_new_data': change.new_data is not None,
                'timestamp': change.timestamp
            })
        
        # Save to changes log
        changes_file = self.changes_dir / f"{table_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}_changes.json"
        with open(changes_file, 'w') as f:
            json.dump(change_log, f, indent=2)
        
        self.logger.info(f"📝 Logged {len(changes)} changes for {table_name}")
    
    def handle_critical_changes(self, table_name: str, critical_changes: List[TableChange]):
        """Handle critical integrity violations"""
        alert = {
            'alert_id': hashlib.md5(f"{table_name}_{datetime.now()}".encode()).hexdigest()[:8],
            'table_name': table_name,
            'case_number': self.case_number,
            'alert_timestamp': datetime.now().isoformat(),
            'severity': 'CRITICAL',
            'issue_count': len(critical_changes),
            'issues': [],
            'action_required': True,
            'status': 'OPEN'
        }
        
        for change in critical_changes:
            alert['issues'].append({
                'type': change.change_type,
                'record_id': change.record_id,
                'description': f"{change.change_type} detected in {table_name}",
                'timestamp': change.timestamp
            })
        
        # Save alert
        alerts = []
        if self.alerts_file.exists():
            with open(self.alerts_file, 'r') as f:
                alerts = json.load(f)
        
        alerts.append(alert)
        
        with open(self.alerts_file, 'w') as f:
            json.dump(alerts, f, indent=2)
        
        self.logger.critical(f"🚨 CRITICAL ALERT: {len(critical_changes)} critical issues in {table_name}")
    
    def get_integrity_report(self) -> Dict:
        """Generate comprehensive integrity report"""
        report = {
            'case_number': self.case_number,
            'report_timestamp': datetime.now().isoformat(),
            'monitored_tables': [],
            'total_alerts': 0,
            'critical_alerts': 0,
            'recent_changes': []
        }
        
        # Check all baselines
        for baseline_file in self.baselines_dir.glob(f"*_{self.case_number}_baseline.json"):
            with open(baseline_file, 'r') as f:
                baseline = json.load(f)
            
            report['monitored_tables'].append({
                'name': baseline['table_name'],
                'records': baseline['total_records'],
                'baseline_date': baseline['baseline_date']
            })
        
        # Check alerts
        if self.alerts_file.exists():
            with open(self.alerts_file, 'r') as f:
                alerts = json.load(f)
            
            report['total_alerts'] = len(alerts)
            report['critical_alerts'] = len([a for a in alerts if a['severity'] == 'CRITICAL'])
        
        # Get recent changes
        for changes_file in sorted(self.changes_dir.glob("*.json"))[-10:]:  # Last 10 change files
            with open(changes_file, 'r') as f:
                changes = json.load(f)
            
            report['recent_changes'].append({
                'table': changes['table_name'],
                'timestamp': changes['check_timestamp'],
                'changes_count': changes['changes_detected']
            })
        
        return report
    
    def create_monitoring_dashboard_html(self) -> str:
        """Create HTML dashboard for table monitoring"""
        report = self.get_integrity_report()
        
        html = f"""
<!DOCTYPE html>
<html>
<head>
    <title>Table Integrity Dashboard - {self.case_number}</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }}
        .header {{ background: linear-gradient(135deg, #e74c3c, #c0392b); color: white; padding: 20px; border-radius: 10px; }}
        .dashboard {{ display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin: 20px 0; }}
        .card {{ background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 5px rgba(0,0,0,0.1); }}
        .metric {{ font-size: 2em; font-weight: bold; margin: 10px 0; }}
        .critical {{ color: #e74c3c; }}
        .ok {{ color: #27ae60; }}
        .warning {{ color: #f39c12; }}
        table {{ width: 100%; border-collapse: collapse; margin-top: 10px; }}
        th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
        th {{ background: #34495e; color: white; }}
    </style>
</head>
<body>
    <div class="header">
        <h1>🛡️ Universal Table Integrity Monitor</h1>
        <h2>Case: {self.case_number}</h2>
        <p>Real-time monitoring of ALL court tables</p>
    </div>
    
    <div class="dashboard">
        <div class="card">
            <h3>📊 Monitoring Status</h3>
            <div class="metric ok">{len(report['monitored_tables'])}</div>
            <p>Tables Monitored</p>
        </div>
        
        <div class="card">
            <h3>🚨 Alerts</h3>
            <div class="metric {'critical' if report['critical_alerts'] > 0 else 'ok'}">{report['total_alerts']}</div>
            <p>Total Alerts ({report['critical_alerts']} Critical)</p>
        </div>
        
        <div class="card">
            <h3>🔄 Recent Activity</h3>
            <div class="metric">{len(report['recent_changes'])}</div>
            <p>Recent Change Events</p>
        </div>
    </div>
    
    <div class="card">
        <h3>📋 Monitored Tables</h3>
        <table>
            <tr><th>Table Name</th><th>Records</th><th>Baseline Date</th></tr>
"""
        
        for table in report['monitored_tables']:
            html += f"<tr><td>{table['name']}</td><td>{table['records']}</td><td>{table['baseline_date'][:10]}</td></tr>"
        
        html += """
        </table>
    </div>
    
    <div class="card">
        <h3>🔍 Recent Changes</h3>
        <table>
            <tr><th>Table</th><th>Timestamp</th><th>Changes</th></tr>
"""
        
        for change in report['recent_changes'][-10:]:
            html += f"<tr><td>{change['table']}</td><td>{change['timestamp'][:16]}</td><td>{change['changes_count']}</td></tr>"
        
        html += f"""
        </table>
    </div>
    
    <div class="card" style="background: #27ae60; color: white;">
        <h3>🎯 System Advantages</h3>
        <ul>
            <li>✅ Universal monitoring - ALL court tables</li>
            <li>✅ Tampering detection - Missing/changed entries</li>
            <li>✅ Position tracking - Sequence verification</li>
            <li>✅ Integrity signatures - Cryptographic verification</li>
            <li>✅ Real-time alerts - Immediate notification</li>
            <li>✅ Complete audit trail - All changes logged</li>
        </ul>
    </div>
</body>
</html>
        """
        
        dashboard_file = self.monitor_dir / f"{self.case_number}_monitoring_dashboard.html"
        with open(dashboard_file, 'w') as f:
            f.write(html)
        
        return str(dashboard_file)

# Example usage functions
def monitor_docket_table(docket_entries: List[Dict], case_number: str = "DR-25-403973"):
    """Monitor docket entries table"""
    monitor = UniversalTableMonitor(case_number)
    return monitor.monitor_table_changes("docket_entries", docket_entries)

def monitor_eservice_table(eservice_records: List[Dict], case_number: str = "DR-25-403973"):
    """Monitor E-Service records table"""
    monitor = UniversalTableMonitor(case_number)
    return monitor.monitor_table_changes("eservice_records", eservice_records)

def create_table_baseline(table_name: str, table_data: List[Dict], case_number: str = "DR-25-403973"):
    """Create baseline for any table"""
    monitor = UniversalTableMonitor(case_number)
    return monitor.create_table_baseline(table_name, table_data)