"""
Document Change Detection and Monitoring System
Critical for detecting stricken entries, content modifications, and forensic preservation
"""

import json
import hashlib
import os
from datetime import datetime
from typing import Dict, List, Optional, Tuple, Any
import logging
import psycopg2
from pathlib import Path
import threading
import time

try:
    import fitz  # PyMuPDF for PDF content extraction
    PDF_AVAILABLE = True
except ImportError:
    PDF_AVAILABLE = False
    
try:
    from core.database_managers import PostgreSQLManager
    from security.pgp_manager import create_pgp_manager
    DB_AVAILABLE = True
except ImportError:
    DB_AVAILABLE = False

class DocumentChangeMonitor:
    """
    Comprehensive document change detection and monitoring
    Tracks:
    - Docket entry metadata changes (titles, descriptions, dates)
    - Document content changes (PDF text, image hashes)
    - Stricken entries preservation (before/after states)
    - Complete audit trail with forensic integrity
    """
    
    def __init__(self, case_id: str, postgresql_config: Optional[Dict] = None):
        self.case_id = case_id
        self.logger = logging.getLogger(f"{__name__}.{case_id}")
        
        # Initialize database connections
        if DB_AVAILABLE and postgresql_config:
            self.postgresql = PostgreSQLManager(postgresql_config)
            self.setup_monitoring_tables()
        else:
            self.postgresql = None
            self.logger.warning("Database not available - using file-based monitoring")
        
        # Initialize PGP encryption
        try:
            self.key_manager, self.doc_encryption = create_pgp_manager()
            self.encryption_available = True
        except Exception as e:
            self.logger.warning(f"PGP encryption not available: {e}")
            self.encryption_available = False
        
        # Monitoring data storage
        self.snapshots_dir = Path(f"case_documents/snapshots/case_{case_id}")
        self.snapshots_dir.mkdir(parents=True, exist_ok=True)
        
        # Document tracking
        self.document_hashes = {}
        self.metadata_snapshots = {}
        self.change_log = []
        
        # Monitoring thread
        self.monitoring_active = False
        self.monitoring_thread = None
        
        self.logger.info(f"Document change monitor initialized for case {case_id}")
    
    def setup_monitoring_tables(self):
        """Setup PostgreSQL tables for document change monitoring"""
        if not self.postgresql:
            return
            
        try:
            with self.postgresql.get_connection() as conn:
                with conn.cursor() as cursor:
                    # Document snapshots table
                    cursor.execute("""
                    CREATE TABLE IF NOT EXISTS document_snapshots (
                        id SERIAL PRIMARY KEY,
                        case_id VARCHAR(50) NOT NULL,
                        document_id VARCHAR(100) NOT NULL,
                        snapshot_type VARCHAR(50) NOT NULL, -- 'metadata', 'content', 'full'
                        snapshot_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        content_hash VARCHAR(64) NOT NULL,
                        metadata_hash VARCHAR(64),
                        original_data JSONB,
                        encrypted_content TEXT,
                        file_path VARCHAR(500),
                        file_size BIGINT,
                        preservation_reason VARCHAR(200) -- 'initial', 'before_strike', 'routine_check'
                    )
                    """)
                    
                    # Document changes table  
                    cursor.execute("""
                    CREATE TABLE IF NOT EXISTS document_changes (
                        id SERIAL PRIMARY KEY,
                        case_id VARCHAR(50) NOT NULL,
                        document_id VARCHAR(100) NOT NULL,
                        change_type VARCHAR(50) NOT NULL, -- 'metadata_change', 'content_change', 'stricken', 'restored'
                        detection_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        before_hash VARCHAR(64),
                        after_hash VARCHAR(64),
                        change_summary JSONB,
                        impact_level VARCHAR(20) NOT NULL, -- 'LOW', 'MEDIUM', 'HIGH', 'CRITICAL'
                        change_details JSONB,
                        forensic_notes TEXT,
                        action_required BOOLEAN DEFAULT FALSE
                    )
                    """)
                    
                    # Stricken entries preservation
                    cursor.execute("""
                    CREATE TABLE IF NOT EXISTS stricken_entries_preservation (
                        id SERIAL PRIMARY KEY,
                        case_id VARCHAR(50) NOT NULL,
                        original_docket_number INT NOT NULL,
                        strike_detection_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        original_entry JSONB NOT NULL,
                        original_documents JSONB,
                        stricken_entry JSONB,
                        strike_reason VARCHAR(500),
                        judicial_officer VARCHAR(200),
                        preservation_status VARCHAR(50) DEFAULT 'PRESERVED',
                        evidence_integrity_verified BOOLEAN DEFAULT TRUE,
                        legal_implications TEXT
                    )
                    """)
                    
                    # Create indexes for performance
                    cursor.execute("CREATE INDEX IF NOT EXISTS idx_doc_snapshots_case_doc ON document_snapshots(case_id, document_id)")
                    cursor.execute("CREATE INDEX IF NOT EXISTS idx_doc_changes_case_doc ON document_changes(case_id, document_id)")
                    cursor.execute("CREATE INDEX IF NOT EXISTS idx_stricken_case ON stricken_entries_preservation(case_id)")
                    
                    conn.commit()
                    
            self.logger.info("Document monitoring tables initialized")
            
        except Exception as e:
            self.logger.error(f"Failed to setup monitoring tables: {e}")
    
    def create_document_snapshot(self, document_id: str, document_data: Dict, 
                                content_path: Optional[str] = None, 
                                reason: str = "routine_check") -> Dict:
        """
        Create comprehensive snapshot of document state
        Preserves both metadata and content for change detection
        
        Args:
            document_id: Unique identifier for document
            document_data: Docket entry metadata
            content_path: Path to actual document file (PDF, image, etc.)
            reason: Reason for snapshot (initial, before_strike, routine_check)
            
        Returns:
            Snapshot record with hashes and preservation status
        """
        try:
            snapshot_id = f"{self.case_id}_{document_id}_{int(time.time())}"
            
            # Create metadata hash
            metadata_json = json.dumps(document_data, sort_keys=True)
            metadata_hash = hashlib.sha256(metadata_json.encode()).hexdigest()
            
            # Create content hash if file available
            content_hash = None
            file_size = None
            content_text = None
            
            if content_path and os.path.exists(content_path):
                # Hash file content
                with open(content_path, 'rb') as f:
                    content_bytes = f.read()
                    content_hash = hashlib.sha256(content_bytes).hexdigest()
                    file_size = len(content_bytes)
                
                # Extract text from PDF for content monitoring
                if PDF_AVAILABLE and content_path.lower().endswith('.pdf'):
                    try:
                        doc = fitz.open(content_path)
                        content_text = ""
                        for page in doc:
                            content_text += page.get_text()
                        doc.close()
                    except Exception as e:
                        self.logger.warning(f"Failed to extract PDF text from {content_path}: {e}")
            
            snapshot_record = {
                'snapshot_id': snapshot_id,
                'case_id': self.case_id,
                'document_id': document_id,
                'snapshot_date': datetime.now().isoformat(),
                'metadata_hash': metadata_hash,
                'content_hash': content_hash,
                'metadata': document_data,
                'content_text': content_text,
                'file_path': content_path,
                'file_size': file_size,
                'preservation_reason': reason
            }
            
            # Store in database if available
            if self.postgresql:
                self.store_snapshot_in_database(snapshot_record)
            
            # Store locally encrypted
            self.store_snapshot_locally(snapshot_record)
            
            # Update tracking
            self.document_hashes[document_id] = {
                'metadata_hash': metadata_hash,
                'content_hash': content_hash,
                'last_snapshot': snapshot_id,
                'last_check': datetime.now().isoformat()
            }
            
            self.logger.info(f"📸 Document snapshot created: {snapshot_id} (reason: {reason})")
            return snapshot_record
            
        except Exception as e:
            self.logger.error(f"Failed to create document snapshot: {e}")
            return {'error': str(e)}
    
    def detect_document_changes(self, document_id: str, current_data: Dict, 
                               current_content_path: Optional[str] = None) -> Optional[Dict]:
        """
        Detect changes in document metadata or content
        Critical for identifying tampering, striking, or unauthorized modifications
        
        Args:
            document_id: Document identifier
            current_data: Current docket entry metadata
            current_content_path: Path to current document file
            
        Returns:
            Change detection results or None if no changes
        """
        try:
            if document_id not in self.document_hashes:
                # First time seeing this document - create initial snapshot
                self.create_document_snapshot(document_id, current_data, current_content_path, "initial")
                return None
            
            # Get previous state
            previous_state = self.document_hashes[document_id]
            
            # Check metadata changes
            current_metadata_json = json.dumps(current_data, sort_keys=True)
            current_metadata_hash = hashlib.sha256(current_metadata_json.encode()).hexdigest()
            
            metadata_changed = current_metadata_hash != previous_state['metadata_hash']
            
            # Check content changes
            content_changed = False
            current_content_hash = None
            
            if current_content_path and os.path.exists(current_content_path):
                with open(current_content_path, 'rb') as f:
                    current_content_hash = hashlib.sha256(f.read()).hexdigest()
                
                if previous_state['content_hash']:
                    content_changed = current_content_hash != previous_state['content_hash']
            
            if not metadata_changed and not content_changed:
                # No changes detected
                return None
            
            # CHANGE DETECTED - Analyze and preserve
            change_analysis = self.analyze_document_change(
                document_id, current_data, previous_state, 
                metadata_changed, content_changed
            )
            
            # Create preservation snapshot BEFORE recording the change
            if change_analysis.get('impact_level') in ['HIGH', 'CRITICAL']:
                self.create_document_snapshot(
                    document_id, current_data, current_content_path, 
                    f"change_detection_{change_analysis['change_type']}"
                )
            
            # Record change in database
            if self.postgresql:
                self.record_change_in_database(change_analysis)
            
            # Update current state
            self.document_hashes[document_id] = {
                'metadata_hash': current_metadata_hash,
                'content_hash': current_content_hash,
                'last_snapshot': change_analysis['change_id'],
                'last_check': datetime.now().isoformat()
            }
            
            self.logger.critical(f"🚨 DOCUMENT CHANGE DETECTED: {change_analysis['change_type']} in {document_id}")
            return change_analysis
            
        except Exception as e:
            self.logger.error(f"Failed to detect document changes: {e}")
            return {'error': str(e)}
    
    def analyze_document_change(self, document_id: str, current_data: Dict, 
                               previous_state: Dict, metadata_changed: bool, 
                               content_changed: bool) -> Dict:
        """
        Analyze detected changes and determine impact level and type
        Special handling for stricken entries and suspicious modifications
        """
        try:
            change_id = f"change_{self.case_id}_{document_id}_{int(time.time())}"
            
            change_analysis = {
                'change_id': change_id,
                'case_id': self.case_id,
                'document_id': document_id,
                'detection_date': datetime.now().isoformat(),
                'metadata_changed': metadata_changed,
                'content_changed': content_changed,
                'change_type': 'unknown',
                'impact_level': 'LOW',
                'change_details': {},
                'forensic_flags': []
            }
            
            # Analyze metadata changes
            if metadata_changed:
                change_details = {}
                
                # Check for stricken entry patterns
                current_desc = current_data.get('Description', '').lower()
                if 'stricken' in current_desc:
                    change_analysis['change_type'] = 'ENTRY_STRICKEN'
                    change_analysis['impact_level'] = 'CRITICAL'
                    change_analysis['forensic_flags'].append('EVIDENCE_MANIPULATION')
                    
                    # Preserve the original entry before it was stricken
                    self.preserve_stricken_entry(document_id, current_data, previous_state)
                    
                    self.logger.critical(f"🚨 STRICKEN ENTRY DETECTED: {document_id}")
                
                # Check for description changes
                if 'Description' in current_data:
                    change_analysis['change_details']['description_modified'] = True
                    change_analysis['impact_level'] = 'HIGH'
                
                # Check for date modifications
                if 'Filing Date' in current_data:
                    change_analysis['change_details']['filing_date_modified'] = True
                    change_analysis['impact_level'] = 'HIGH'
                    change_analysis['forensic_flags'].append('DATE_TAMPERING')
                
                # Check for party/side changes
                if 'Side' in current_data:
                    change_analysis['change_details']['party_side_modified'] = True
                    change_analysis['impact_level'] = 'MEDIUM'
            
            # Analyze content changes
            if content_changed:
                change_analysis['change_details']['document_content_modified'] = True
                change_analysis['impact_level'] = 'CRITICAL'
                change_analysis['forensic_flags'].append('DOCUMENT_TAMPERING')
                
                if change_analysis['change_type'] == 'unknown':
                    change_analysis['change_type'] = 'CONTENT_MODIFICATION'
                
                self.logger.critical(f"🚨 DOCUMENT CONTENT CHANGED: {document_id}")
            
            # Set default change type if still unknown
            if change_analysis['change_type'] == 'unknown':
                if metadata_changed:
                    change_analysis['change_type'] = 'METADATA_MODIFICATION'
                    change_analysis['impact_level'] = 'MEDIUM'
            
            return change_analysis
            
        except Exception as e:
            self.logger.error(f"Failed to analyze document change: {e}")
            return {'error': str(e), 'change_id': f"error_{int(time.time())}"}
    
    def preserve_stricken_entry(self, document_id: str, stricken_data: Dict, original_state: Dict):
        """
        Special preservation for stricken entries
        Maintains forensic record of original content before it was stricken
        """
        try:
            preservation_record = {
                'case_id': self.case_id,
                'document_id': document_id,
                'original_docket_number': stricken_data.get('Docket Number', 'unknown'),
                'strike_detection_date': datetime.now().isoformat(),
                'original_entry': original_state,
                'stricken_entry': stricken_data,
                'strike_reason': stricken_data.get('Description', ''),
                'preservation_status': 'PRESERVED',
                'evidence_integrity_verified': True,
                'legal_implications': (
                    "Original entry preserved before striking. This constitutes potential evidence "
                    "manipulation and requires immediate legal review. Original content remains "
                    "forensically preserved and admissible."
                )
            }
            
            # Store in database
            if self.postgresql:
                with self.postgresql.get_connection() as conn:
                    with conn.cursor() as cursor:
                        cursor.execute("""
                        INSERT INTO stricken_entries_preservation 
                        (case_id, original_docket_number, original_entry, stricken_entry, 
                         strike_reason, preservation_status, evidence_integrity_verified, legal_implications)
                        VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
                        """, (
                            preservation_record['case_id'],
                            preservation_record['original_docket_number'],
                            json.dumps(preservation_record['original_entry']),
                            json.dumps(preservation_record['stricken_entry']),
                            preservation_record['strike_reason'],
                            preservation_record['preservation_status'],
                            preservation_record['evidence_integrity_verified'],
                            preservation_record['legal_implications']
                        ))
                        conn.commit()
            
            # Create encrypted backup
            self.store_stricken_entry_backup(preservation_record)
            
            self.logger.critical(f"🛡️ STRICKEN ENTRY PRESERVED: Original content secured for {document_id}")
            
        except Exception as e:
            self.logger.error(f"Failed to preserve stricken entry: {e}")
    
    def store_snapshot_locally(self, snapshot_record: Dict):
        """Store snapshot locally with encryption if available"""
        try:
            snapshot_file = self.snapshots_dir / f"{snapshot_record['snapshot_id']}.json"
            
            if self.encryption_available:
                # Encrypt sensitive data
                encrypted_data = self.doc_encryption.encrypt_document(
                    json.dumps(snapshot_record).encode(),
                    self.case_id,
                    snapshot_record['snapshot_id']
                )
                with open(snapshot_file, 'w') as f:
                    json.dump({'encrypted': True, 'data': encrypted_data}, f)
            else:
                with open(snapshot_file, 'w') as f:
                    json.dump(snapshot_record, f, indent=2)
                    
        except Exception as e:
            self.logger.error(f"Failed to store snapshot locally: {e}")
    
    def store_stricken_entry_backup(self, preservation_record: Dict):
        """Create encrypted backup of stricken entry evidence"""
        try:
            backup_file = self.snapshots_dir / f"stricken_{preservation_record['document_id']}_{int(time.time())}.json"
            
            if self.encryption_available:
                encrypted_data = self.doc_encryption.encrypt_document(
                    json.dumps(preservation_record).encode(),
                    self.case_id,
                    f"stricken_{preservation_record['document_id']}"
                )
                with open(backup_file, 'w') as f:
                    json.dump({'encrypted': True, 'stricken_evidence': encrypted_data}, f)
            else:
                with open(backup_file, 'w') as f:
                    json.dump(preservation_record, f, indent=2)
                    
        except Exception as e:
            self.logger.error(f"Failed to store stricken entry backup: {e}")
    
    def store_snapshot_in_database(self, snapshot_record: Dict):
        """Store snapshot in PostgreSQL database"""
        if not self.postgresql:
            return
            
        try:
            with self.postgresql.get_connection() as conn:
                with conn.cursor() as cursor:
                    cursor.execute("""
                    INSERT INTO document_snapshots 
                    (case_id, document_id, snapshot_type, content_hash, metadata_hash, 
                     original_data, file_path, file_size, preservation_reason)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
                    """, (
                        snapshot_record['case_id'],
                        snapshot_record['document_id'],
                        'full',
                        snapshot_record['content_hash'],
                        snapshot_record['metadata_hash'],
                        json.dumps(snapshot_record['metadata']),
                        snapshot_record['file_path'],
                        snapshot_record['file_size'],
                        snapshot_record['preservation_reason']
                    ))
                    conn.commit()
                    
        except Exception as e:
            self.logger.error(f"Failed to store snapshot in database: {e}")
    
    def record_change_in_database(self, change_analysis: Dict):
        """Record detected change in database"""
        if not self.postgresql:
            return
            
        try:
            with self.postgresql.get_connection() as conn:
                with conn.cursor() as cursor:
                    cursor.execute("""
                    INSERT INTO document_changes 
                    (case_id, document_id, change_type, impact_level, change_summary, 
                     change_details, forensic_notes, action_required)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
                    """, (
                        change_analysis['case_id'],
                        change_analysis['document_id'],
                        change_analysis['change_type'],
                        change_analysis['impact_level'],
                        json.dumps({
                            'metadata_changed': change_analysis['metadata_changed'],
                            'content_changed': change_analysis['content_changed']
                        }),
                        json.dumps(change_analysis['change_details']),
                        f"Forensic flags: {', '.join(change_analysis['forensic_flags'])}",
                        change_analysis['impact_level'] in ['HIGH', 'CRITICAL']
                    ))
                    conn.commit()
                    
        except Exception as e:
            self.logger.error(f"Failed to record change in database: {e}")
    
    def start_continuous_monitoring(self, check_interval_minutes: int = 30):
        """Start continuous monitoring thread"""
        if self.monitoring_active:
            self.logger.warning("Monitoring already active")
            return
            
        self.monitoring_active = True
        self.monitoring_thread = threading.Thread(
            target=self._monitoring_loop,
            args=(check_interval_minutes,),
            daemon=True
        )
        self.monitoring_thread.start()
        
        self.logger.info(f"Started continuous monitoring (check interval: {check_interval_minutes} minutes)")
    
    def stop_monitoring(self):
        """Stop continuous monitoring"""
        self.monitoring_active = False
        if self.monitoring_thread:
            self.monitoring_thread.join(timeout=5)
        self.logger.info("Stopped continuous monitoring")
    
    def _monitoring_loop(self, check_interval_minutes: int):
        """Internal monitoring loop"""
        while self.monitoring_active:
            try:
                # This would integrate with the efiling scraper to check for changes
                # For now, just log that monitoring is active
                self.logger.debug(f"Document monitoring active for case {self.case_id}")
                time.sleep(check_interval_minutes * 60)
            except Exception as e:
                self.logger.error(f"Error in monitoring loop: {e}")
                time.sleep(60)  # Wait 1 minute before retrying

    def get_change_summary(self) -> Dict:
        """Get summary of all detected changes"""
        try:
            if self.postgresql:
                with self.postgresql.get_connection() as conn:
                    with conn.cursor() as cursor:
                        # Get change counts by type
                        cursor.execute("""
                        SELECT change_type, impact_level, COUNT(*) 
                        FROM document_changes 
                        WHERE case_id = %s 
                        GROUP BY change_type, impact_level
                        ORDER BY impact_level DESC, change_type
                        """, (self.case_id,))
                        
                        change_counts = cursor.fetchall()
                        
                        # Get stricken entries count
                        cursor.execute("""
                        SELECT COUNT(*) FROM stricken_entries_preservation 
                        WHERE case_id = %s
                        """, (self.case_id,))
                        
                        stricken_count = cursor.fetchone()[0]
                        
                        return {
                            'case_id': self.case_id,
                            'change_counts': change_counts,
                            'stricken_entries': stricken_count,
                            'total_changes': sum(count for _, _, count in change_counts),
                            'monitoring_active': self.monitoring_active
                        }
            else:
                return {
                    'case_id': self.case_id,
                    'monitoring_active': self.monitoring_active,
                    'note': 'Database not available - limited summary'
                }
                
        except Exception as e:
            self.logger.error(f"Failed to get change summary: {e}")
            return {'error': str(e)}