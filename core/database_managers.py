"""
Multi-Database Connection Managers for Legal Case Management System
Handles PostgreSQL, MongoDB, and Redis connections with security and encryption.
"""

import os
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Union
from contextlib import contextmanager
import ssl

# PostgreSQL imports
try:
    import psycopg2
    from psycopg2.extras import RealDictCursor
    from psycopg2.pool import ThreadedConnectionPool
    POSTGRESQL_AVAILABLE = True
except ImportError:
    POSTGRESQL_AVAILABLE = False
    logging.warning("psycopg2 not available. Install with: pip install psycopg2-binary")

# MongoDB imports
try:
    import pymongo
    from pymongo import MongoClient
    from pymongo.errors import ConnectionFailure, OperationFailure
    MONGODB_AVAILABLE = True
except ImportError:
    MONGODB_AVAILABLE = False
    logging.warning("pymongo not available. Install with: pip install pymongo")

# Redis imports
try:
    import redis
    from redis.sentinel import Sentinel
    REDIS_AVAILABLE = True
except ImportError:
    REDIS_AVAILABLE = False
    logging.warning("redis not available. Install with: pip install redis")

logger = logging.getLogger(__name__)

class PostgreSQLManager:
    """PostgreSQL connection manager with SSL and connection pooling"""
    
    def __init__(self, config: Dict):
        if not POSTGRESQL_AVAILABLE:
            raise RuntimeError("PostgreSQL support not available")
        
        self.config = config
        self.pool = None
        self._init_connection_pool()
    
    def _init_connection_pool(self):
        """Initialize PostgreSQL connection pool"""
        try:
            connection_params = {
                'host': self.config.get('host', 'localhost'),
                'port': self.config.get('port', 5432),
                'database': self.config.get('database', 'legal_cms'),
                'user': self.config.get('user', 'legal_cms_user'),
                'password': self.config.get('password', ''),
                'sslmode': self.config.get('sslmode', 'disable'),
                'sslcert': self.config.get('sslcert'),
                'sslkey': self.config.get('sslkey'),
                'sslrootcert': self.config.get('sslrootcert'),
                'connect_timeout': 10,
                'application_name': 'legal_cms'
            }
            
            # Remove None values
            connection_params = {k: v for k, v in connection_params.items() if v is not None}
            
            self.pool = ThreadedConnectionPool(
                minconn=2,
                maxconn=20,
                **connection_params
            )
            
            logger.info("PostgreSQL connection pool initialized")
            
        except Exception as e:
            logger.error(f"Failed to initialize PostgreSQL pool: {e}")
            raise
    
    @contextmanager
    def get_connection(self):
        """Get connection from pool with automatic cleanup"""
        conn = None
        try:
            conn = self.pool.getconn()
            yield conn
        except Exception as e:
            if conn:
                conn.rollback()
            logger.error(f"PostgreSQL operation failed: {e}")
            raise
        finally:
            if conn:
                self.pool.putconn(conn)
    
    def execute_query(self, query: str, params: tuple = None, fetch: bool = False) -> Optional[List[Dict]]:
        """Execute query with parameters"""
        with self.get_connection() as conn:
            with conn.cursor(cursor_factory=RealDictCursor) as cursor:
                cursor.execute(query, params)
                
                if fetch:
                    return [dict(row) for row in cursor.fetchall()]
                else:
                    conn.commit()
                    return cursor.rowcount
    
    def init_schema(self):
        """Initialize PostgreSQL schema"""
        schema_sql = """
        -- Users table
        CREATE TABLE IF NOT EXISTS users (
            id SERIAL PRIMARY KEY,
            username VARCHAR(50) UNIQUE NOT NULL,
            email VARCHAR(255) UNIQUE NOT NULL,
            password_hash TEXT NOT NULL,
            salt TEXT NOT NULL,
            role VARCHAR(20) DEFAULT 'user',
            is_active BOOLEAN DEFAULT true,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            last_login TIMESTAMP,
            failed_attempts INTEGER DEFAULT 0,
            locked_until TIMESTAMP,
            mfa_enabled BOOLEAN DEFAULT false,
            mfa_secret TEXT
        );
        
        -- Cases table
        CREATE TABLE IF NOT EXISTS cases (
            id SERIAL PRIMARY KEY,
            name VARCHAR(255) NOT NULL,
            case_number VARCHAR(100) UNIQUE,
            description TEXT,
            status VARCHAR(50) DEFAULT 'active',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            created_by INTEGER REFERENCES users(id),
            client_name VARCHAR(255),
            opposing_party VARCHAR(255),
            court_name VARCHAR(255),
            case_type VARCHAR(100),
            priority VARCHAR(20) DEFAULT 'medium',
            encrypted_metadata JSONB
        );
        
        -- Sessions table
        CREATE TABLE IF NOT EXISTS sessions (
            id SERIAL PRIMARY KEY,
            user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
            session_token VARCHAR(255) UNIQUE NOT NULL,
            expires_at TIMESTAMP NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            ip_address INET,
            user_agent TEXT,
            is_active BOOLEAN DEFAULT true
        );
        
        -- Audit logs table
        CREATE TABLE IF NOT EXISTS audit_logs (
            id SERIAL PRIMARY KEY,
            user_id INTEGER REFERENCES users(id),
            action VARCHAR(100) NOT NULL,
            resource_type VARCHAR(50),
            resource_id VARCHAR(100),
            details JSONB,
            ip_address INET,
            user_agent TEXT,
            timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            case_id INTEGER REFERENCES cases(id)
        );
        
        -- Permissions table
        CREATE TABLE IF NOT EXISTS case_permissions (
            id SERIAL PRIMARY KEY,
            user_id INTEGER REFERENCES users(id),
            case_id INTEGER REFERENCES cases(id),
            permission_level VARCHAR(20) DEFAULT 'read',
            granted_by INTEGER REFERENCES users(id),
            granted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            expires_at TIMESTAMP,
            UNIQUE(user_id, case_id)
        );
        
        -- System configuration table
        CREATE TABLE IF NOT EXISTS system_config (
            id SERIAL PRIMARY KEY,
            config_key VARCHAR(100) UNIQUE NOT NULL,
            config_value JSONB,
            encrypted BOOLEAN DEFAULT false,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
        
        -- Indexes for performance
        CREATE INDEX IF NOT EXISTS idx_sessions_token ON sessions(session_token);
        CREATE INDEX IF NOT EXISTS idx_sessions_user_id ON sessions(user_id);
        CREATE INDEX IF NOT EXISTS idx_audit_logs_user_id ON audit_logs(user_id);
        CREATE INDEX IF NOT EXISTS idx_audit_logs_timestamp ON audit_logs(timestamp);
        CREATE INDEX IF NOT EXISTS idx_cases_created_by ON cases(created_by);
        CREATE INDEX IF NOT EXISTS idx_case_permissions_user_case ON case_permissions(user_id, case_id);
        """
        
        try:
            with self.get_connection() as conn:
                with conn.cursor() as cursor:
                    cursor.execute(schema_sql)
                    conn.commit()
            logger.info("PostgreSQL schema initialized")
        except Exception as e:
            logger.error(f"Failed to initialize PostgreSQL schema: {e}")
            raise

class MongoDBManager:
    """MongoDB connection manager with replica set and encryption"""
    
    def __init__(self, config: Dict):
        if not MONGODB_AVAILABLE:
            raise RuntimeError("MongoDB support not available")
        
        self.config = config
        self.client = None
        self.db = None
        self._init_connection()
    
    def _init_connection(self):
        """Initialize MongoDB connection"""
        try:
            # Build connection string
            username = self.config.get('username')
            password = self.config.get('password')
            hosts = self.config.get('hosts', ['localhost:27017'])
            database = self.config.get('database', 'legal_cms')
            replica_set = self.config.get('replica_set')
            
            if isinstance(hosts, str):
                hosts = [hosts]
            
            host_string = ','.join(hosts)
            
            if username and password:
                auth_db = self.config.get('auth_db', 'admin')
                connection_string = f"mongodb://{username}:{password}@{host_string}/{database}?authSource={auth_db}"
            else:
                connection_string = f"mongodb://{host_string}/{database}"
            
            # Connection options
            options = {
                'serverSelectionTimeoutMS': 5000,
                'connectTimeoutMS': 10000,
                'socketTimeoutMS': 20000,
                'maxPoolSize': 50,
                'minPoolSize': 5,
                'maxIdleTimeMS': 30000,
                'waitQueueTimeoutMS': 5000,
                'w': 'majority',
                'readPreference': 'primaryPreferred'
            }
            
            if replica_set:
                options['replicaSet'] = replica_set
            
            if self.config.get('ssl', True):
                options['ssl'] = True
                options['ssl_cert_reqs'] = ssl.CERT_REQUIRED
                
                if self.config.get('ssl_ca_certs'):
                    options['ssl_ca_certs'] = self.config['ssl_ca_certs']
            
            self.client = MongoClient(connection_string, **options)
            self.db = self.client[database]
            
            # Test connection
            self.client.admin.command('ping')
            logger.info(f"Connected to MongoDB: {database}")
            
        except Exception as e:
            logger.error(f"Failed to connect to MongoDB: {e}")
            raise
    
    def get_collection(self, collection_name: str):
        """Get MongoDB collection"""
        return self.db[collection_name]
    
    def init_collections(self):
        """Initialize MongoDB collections with indexes"""
        try:
            # Documents collection
            documents = self.get_collection('documents')
            documents.create_index([('case_id', 1), ('filename', 1)])
            documents.create_index([('pgp_fingerprint', 1)])
            documents.create_index([('created_at', -1)])
            
            # Chat history collection
            chat_history = self.get_collection('chat_history')
            chat_history.create_index([('case_id', 1), ('created_at', -1)])
            chat_history.create_index([('session_id', 1)])
            
            # Document vectors collection
            document_vectors = self.get_collection('document_vectors')
            document_vectors.create_index([('document_id', 1)])
            document_vectors.create_index([('vector_collection', 1)])
            
            # Case analytics collection
            case_analytics = self.get_collection('case_analytics')
            case_analytics.create_index([('case_id', 1), ('generated_at', -1)])
            
            logger.info("MongoDB collections and indexes initialized")
            
        except Exception as e:
            logger.error(f"Failed to initialize MongoDB collections: {e}")
            raise

class RedisManager:
    """Redis connection manager with clustering and SSL support"""
    
    def __init__(self, config: Dict):
        if not REDIS_AVAILABLE:
            raise RuntimeError("Redis support not available")
        
        self.config = config
        self.client = None
        self._init_connection()
    
    def _init_connection(self):
        """Initialize Redis connection"""
        try:
            connection_params = {
                'host': self.config.get('host', 'localhost'),
                'port': self.config.get('port', 6379),
                'password': self.config.get('password'),
                'db': self.config.get('db', 0),
                'decode_responses': True,
                'socket_timeout': 5,
                'socket_connect_timeout': 5,
                'retry_on_timeout': True,
                'health_check_interval': 30
            }
            
            # SSL configuration
            if self.config.get('ssl', False):
                connection_params.update({
                    'ssl': True,
                    'ssl_cert_reqs': ssl.CERT_REQUIRED,
                    'ssl_ca_certs': self.config.get('ssl_ca_certs'),
                    'ssl_certfile': self.config.get('ssl_certfile'),
                    'ssl_keyfile': self.config.get('ssl_keyfile')
                })
            
            # Sentinel configuration for HA
            sentinels = self.config.get('sentinels')
            if sentinels:
                sentinel = Sentinel(sentinels)
                self.client = sentinel.master_for(
                    self.config.get('service_name', 'mymaster'),
                    **connection_params
                )
            else:
                self.client = redis.Redis(**connection_params)
            
            # Test connection
            self.client.ping()
            logger.info("Connected to Redis successfully")
            
        except Exception as e:
            logger.error(f"Failed to connect to Redis: {e}")
            raise
    
    def set_with_expiry(self, key: str, value: Any, expiry_seconds: int = 3600):
        """Set key with expiration"""
        if isinstance(value, (dict, list)):
            value = json.dumps(value)
        return self.client.setex(key, expiry_seconds, value)
    
    def get_json(self, key: str) -> Optional[Dict]:
        """Get JSON value from Redis"""
        value = self.client.get(key)
        if value:
            try:
                return json.loads(value)
            except json.JSONDecodeError:
                return value
        return None
    
    def delete_pattern(self, pattern: str) -> int:
        """Delete keys matching pattern"""
        keys = self.client.keys(pattern)
        if keys:
            return self.client.delete(*keys)
        return 0

class DatabaseManagerFactory:
    """Factory for creating database managers"""
    
    @staticmethod
    def create_managers(config: Dict) -> Dict:
        """Create all database managers from configuration"""
        managers = {}
        
        # PostgreSQL
        if config.get('postgresql') and POSTGRESQL_AVAILABLE:
            try:
                managers['postgresql'] = PostgreSQLManager(config['postgresql'])
                managers['postgresql'].init_schema()
            except Exception as e:
                logger.error(f"Failed to initialize PostgreSQL: {e}")
        
        # MongoDB
        if config.get('mongodb') and MONGODB_AVAILABLE:
            try:
                managers['mongodb'] = MongoDBManager(config['mongodb'])
                managers['mongodb'].init_collections()
            except Exception as e:
                logger.error(f"Failed to initialize MongoDB: {e}")
        
        # Redis
        if config.get('redis') and REDIS_AVAILABLE:
            try:
                managers['redis'] = RedisManager(config['redis'])
            except Exception as e:
                logger.error(f"Failed to initialize Redis: {e}")
        
        return managers

# Configuration loader
def load_database_config(config_path: str = "config/database.json") -> Dict:
    """Load database configuration from file with environment variable substitution"""
    try:
        with open(config_path, 'r') as f:
            config_text = f.read()
        
        # Substitute environment variables
        import re
        def replace_env_var(match):
            var_name = match.group(1)
            return os.environ.get(var_name, match.group(0))
        
        config_text = re.sub(r'\$\{([^}]+)\}', replace_env_var, config_text)
        
        return json.loads(config_text)
    except FileNotFoundError:
        logger.warning(f"Database config file not found: {config_path}")
        return {}
    except json.JSONDecodeError as e:
        logger.error(f"Invalid JSON in config file: {e}")
        return {}
