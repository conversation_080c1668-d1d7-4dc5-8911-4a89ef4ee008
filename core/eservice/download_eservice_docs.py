#!/usr/bin/env python3
"""
E-Service Document Downloader
Downloads all documents from efiling data
"""

import json
import os
import requests
import time
from pathlib import Path
from urllib.parse import urljoin, urlparse
import re
from datetime import datetime

class EServiceDocumentDownloader:
    def __init__(self):
        self.base_url = "https://efiling.cp.cuyahogacounty.gov/"
        self.download_dir = Path("eservice_documents")
        self.download_dir.mkdir(exist_ok=True)
        
        # Session for persistent connections
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        
        self.downloaded_count = 0
        self.failed_count = 0
        self.skipped_count = 0
    
    def sanitize_filename(self, filename: str) -> str:
        """Sanitize filename for safe storage"""
        # Remove invalid characters
        filename = re.sub(r'[<>:"/\\|?*]', '_', filename)
        # Limit length
        if len(filename) > 200:
            name, ext = os.path.splitext(filename)
            filename = name[:195] + ext
        return filename
    
    def download_document(self, doc_info: dict, entry_info: dict) -> bool:
        """Download a single document"""
        try:
            url = doc_info.get('url', '')
            if not url:
                return False
            
            # Make URL absolute
            if not url.startswith('http'):
                url = urljoin(self.base_url, url)
            
            # Generate filename
            docket_num = entry_info.get('Docket Number', 'unknown')
            filing_date = entry_info.get('Filing Date', 'unknown')
            doc_type = entry_info.get('Document Type', 'unknown')
            description = entry_info.get('Description', '')[:50]
            
            # Sanitize description
            description = re.sub(r'[^\w\s-]', '', description).strip()
            description = re.sub(r'\s+', '_', description)
            
            filename = f"docket_{docket_num}_{filing_date}_{doc_type}_{description}.pdf"
            filename = self.sanitize_filename(filename)
            filepath = self.download_dir / filename
            
            # Skip if already exists
            if filepath.exists():
                print(f"⏭️  Skipping (exists): {filename}")
                self.skipped_count += 1
                return True
            
            print(f"📥 Downloading: {filename}")
            print(f"   URL: {url}")
            
            response = self.session.get(url, timeout=30)
            response.raise_for_status()
            
            # Check if response is actually a PDF or HTML
            content_type = response.headers.get('content-type', '').lower()
            
            if 'pdf' in content_type or response.content.startswith(b'%PDF'):
                # It's a PDF, save it
                with open(filepath, 'wb') as f:
                    f.write(response.content)
                print(f"✅ Downloaded: {filename} ({len(response.content):,} bytes)")
                self.downloaded_count += 1
                return True
            elif 'html' in content_type:
                # It's HTML, might be an error page or viewer
                print(f"⚠️  HTML response for {filename}, saving as .html for inspection")
                html_filepath = filepath.with_suffix('.html')
                with open(html_filepath, 'wb') as f:
                    f.write(response.content)
                return False
            else:
                print(f"⚠️  Unexpected content type: {content_type}")
                return False
                
        except Exception as e:
            print(f"❌ Failed to download {filename}: {e}")
            self.failed_count += 1
            return False
    
    def download_image_document(self, image_query: str, entry_info: dict) -> bool:
        """Download document image using image query"""
        try:
            # Construct image URL
            image_url = f"{self.base_url}DisplayImageList.aspx?q={image_query}"
            
            docket_num = entry_info.get('Docket Number', 'unknown')
            filing_date = entry_info.get('Filing Date', 'unknown')
            doc_type = entry_info.get('Document Type', 'unknown')
            
            filename = f"docket_{docket_num}_{filing_date}_{doc_type}_image.pdf"
            filename = self.sanitize_filename(filename)
            filepath = self.download_dir / filename
            
            # Skip if already exists
            if filepath.exists():
                print(f"⏭️  Skipping (exists): {filename}")
                self.skipped_count += 1
                return True
            
            print(f"📸 Downloading image: {filename}")
            print(f"   URL: {image_url}")
            
            response = self.session.get(image_url, timeout=30)
            response.raise_for_status()
            
            # For image lists, we might need to parse the HTML to get actual image URLs
            if 'html' in response.headers.get('content-type', '').lower():
                # Save HTML for later inspection
                html_filepath = self.download_dir / f"{filename}_viewer.html"
                with open(html_filepath, 'wb') as f:
                    f.write(response.content)
                print(f"📄 Saved image viewer HTML: {html_filepath.name}")
                return True
            else:
                with open(filepath, 'wb') as f:
                    f.write(response.content)
                print(f"✅ Downloaded image: {filename} ({len(response.content):,} bytes)")
                self.downloaded_count += 1
                return True
                
        except Exception as e:
            print(f"❌ Failed to download image {filename}: {e}")
            self.failed_count += 1
            return False
    
    def download_all_documents(self, efiling_data: dict):
        """Download all documents from efiling data"""
        print("🚀 Starting E-Service Document Download")
        print("=" * 60)
        print(f"📋 Case: {efiling_data.get('case_number', 'Unknown')}")
        print(f"📁 Download directory: {self.download_dir.absolute()}")
        print()
        
        docket_info = efiling_data.get('docket_information', {})
        entries = docket_info.get('entries', [])
        
        print(f"📋 Processing {len(entries)} docket entries...")
        print()
        
        for entry in entries:
            docket_num = entry.get('Docket Number', 'unknown')
            filing_date = entry.get('Filing Date', 'unknown')
            doc_type = entry.get('Document Type', 'unknown')
            description = entry.get('Description', '')[:100]
            
            print(f"📋 Entry {docket_num}: {filing_date} - {doc_type}")
            print(f"   {description}")
            
            # Download regular documents
            documents = entry.get('documents', [])
            if documents:
                print(f"   📎 {len(documents)} document(s) found")
                for doc in documents:
                    self.download_document(doc, entry)
            
            # Download images if available
            if entry.get('Has_Image') and entry.get('Image_Query'):
                print(f"   🖼️  Image available")
                self.download_image_document(entry['Image_Query'], entry)
            
            print()
            
            # Small delay to be respectful
            time.sleep(0.5)
        
        # Final summary
        print("=" * 60)
        print("📊 Download Summary:")
        print(f"✅ Successfully downloaded: {self.downloaded_count}")
        print(f"⏭️  Skipped (already exists): {self.skipped_count}")
        print(f"❌ Failed downloads: {self.failed_count}")
        print(f"📁 Files saved to: {self.download_dir.absolute()}")
        print()
        
        # List downloaded files
        pdf_files = list(self.download_dir.glob("*.pdf"))
        html_files = list(self.download_dir.glob("*.html"))
        
        if pdf_files:
            print(f"📄 Downloaded PDF files: {len(pdf_files)}")
        if html_files:
            print(f"📄 Downloaded HTML files: {len(html_files)}")

def main():
    """Main download function"""
    
    # Load latest efiling data
    data_file = Path("efiling_data/efiling_DR-25-403973_latest.json")
    
    if not data_file.exists():
        print("❌ Efiling data file not found")
        return
    
    print(f"📂 Loading efiling data from: {data_file}")
    
    with open(data_file, 'r') as f:
        efiling_data = json.load(f)
    
    # Start download
    downloader = EServiceDocumentDownloader()
    downloader.download_all_documents(efiling_data)

if __name__ == "__main__":
    main()