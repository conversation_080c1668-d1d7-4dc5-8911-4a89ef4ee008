#!/usr/bin/env python3
"""
Simple direct downloader for E-Service documents
Uses playwright to download documents directly from URLs
"""
import os
import json
import asyncio
from pathlib import Path
from playwright.async_api import async_playwright

async def download_eservice_documents_simple():
    """Download E-Service documents using simple approach"""
    
    # Load document metadata
    metadata_file = Path("case_documents/DR_25_403973/eservice_docs/document_metadata.json")
    output_dir = Path("case_documents/DR_25_403973/eservice_docs")
    
    if not metadata_file.exists():
        print("❌ No metadata file found. Run extract_eservice_docs.py first")
        return
    
    with open(metadata_file, 'r') as f:
        documents = json.load(f)
    
    print(f"📄 Found {len(documents)} documents to download")
    
    # E-filing base URL
    base_url = "https://efiling.cp.cuyahogacounty.gov/"
    
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=True)
        context = await browser.new_context()
        page = await context.new_page()
        
        # Login first
        print("🔐 Logging into e-filing system...")
        await page.goto("https://efiling.cp.cuyahogacounty.gov/")
        
        # Wait for login form and fill credentials
        await page.wait_for_selector('#SheetContentPlaceHolder_txtUserName')
        await page.fill('#SheetContentPlaceHolder_txtUserName', 'GSHEPOV')
        await page.fill('#SheetContentPlaceHolder_txtPassword', 'VhodaNet4u')
        await page.click('#SheetContentPlaceHolder_btnLogin')
        
        # Wait for successful login
        await page.wait_for_url("**/CaseSelectionMenu.aspx", timeout=10000)
        print("✅ Login successful")
        
        successful_downloads = 0
        failed_downloads = 0
        
        for i, doc in enumerate(documents):
            try:
                print(f"📥 Downloading {i+1}/{len(documents)}: {doc['name']}")
                
                # Navigate to document URL
                full_url = base_url + doc['url']
                await page.goto(full_url, timeout=30000)
                
                # Wait for page to load
                await page.wait_for_load_state('networkidle', timeout=10000)
                
                # Look for PDF download links or image links
                pdf_links = await page.query_selector_all('a[href$=".pdf"]')
                
                if pdf_links:
                    # Download PDF files
                    for j, link in enumerate(pdf_links):
                        href = await link.get_attribute('href')
                        if href:
                            # Determine filename
                            filename = f"{doc['name']}_{j+1:02d}.pdf" if len(pdf_links) > 1 else f"{doc['name']}.pdf"
                            file_path = output_dir / filename
                            
                            # Download the file
                            async with page.expect_download() as download_info:
                                await link.click()
                            download = await download_info.value
                            await download.save_as(file_path)
                            print(f"   ✅ Saved: {filename}")
                else:
                    # Look for image links or try to download page content
                    # Sometimes documents are displayed as images
                    img_elements = await page.query_selector_all('img[src*="ViewImage"]')
                    
                    if img_elements:
                        # Download images
                        for j, img in enumerate(img_elements):
                            src = await img.get_attribute('src')
                            if src and 'ViewImage' in src:
                                filename = f"{doc['name']}_{j+1:02d}.png"
                                file_path = output_dir / filename
                                
                                # Navigate to image URL and screenshot
                                img_url = base_url + src if not src.startswith('http') else src
                                response = await page.goto(img_url)
                                
                                if response and response.status == 200:
                                    await page.screenshot(path=file_path, full_page=True)
                                    print(f"   ✅ Saved image: {filename}")
                    else:
                        # Take screenshot of entire page as fallback
                        filename = f"{doc['name']}_page.png"
                        file_path = output_dir / filename
                        await page.screenshot(path=file_path, full_page=True)
                        print(f"   📸 Screenshot saved: {filename}")
                
                successful_downloads += 1
                
                # Small delay between downloads
                await asyncio.sleep(1)
                
            except Exception as e:
                print(f"   ❌ Failed to download {doc['name']}: {e}")
                failed_downloads += 1
                continue
        
        await browser.close()
    
    print(f"\n🎉 Download complete!")
    print(f"   ✅ Successful: {successful_downloads}")
    print(f"   ❌ Failed: {failed_downloads}")

if __name__ == "__main__":
    asyncio.run(download_eservice_documents_simple())