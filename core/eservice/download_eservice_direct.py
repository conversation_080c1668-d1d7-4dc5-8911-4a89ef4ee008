#!/usr/bin/env python3
"""
Direct downloader for E-Service documents using existing scraper
"""
import os
import json
import asyncio
from pathlib import Path
from core.efiling_scraper import CuyahogaEFilingScraperPlaywright

async def download_eservice_documents_direct():
    """Download E-Service documents using existing scraper methods"""
    
    # Load document metadata
    metadata_file = Path("case_documents/DR_25_403973/eservice_docs/document_metadata.json")
    output_dir = Path("case_documents/DR_25_403973/eservice_docs")
    
    if not metadata_file.exists():
        print("❌ No metadata file found. Run extract_eservice_docs.py first")
        return
    
    with open(metadata_file, 'r') as f:
        documents = json.load(f)
    
    print(f"📄 Found {len(documents)} documents to download")
    
    # Get credentials
    username = "GSHEPOV"
    password = "VhodaNet4u"
    
    scraper = CuyahogaEFilingScraperPlaywright(username, password)
    
    try:
        successful_downloads = 0
        failed_downloads = 0
        
        # Download documents one by one using direct method
        for i, doc in enumerate(documents):
            try:
                print(f"📥 Downloading {i+1}/{len(documents)}: {doc['name']}")
                
                # Use the download_single_document method
                success = await scraper.download_single_document(
                    doc['url'],
                    str(output_dir),
                    doc['name']
                )
                
                if success:
                    successful_downloads += 1
                    print(f"   ✅ Downloaded successfully")
                else:
                    failed_downloads += 1
                    print(f"   ❌ Download failed")
                
            except Exception as e:
                print(f"   ❌ Error downloading {doc['name']}: {e}")
                failed_downloads += 1
                continue
        
        print(f"\n🎉 Download complete!")
        print(f"   ✅ Successful: {successful_downloads}")
        print(f"   ❌ Failed: {failed_downloads}")
        
    finally:
        await scraper.close()

if __name__ == "__main__":
    asyncio.run(download_eservice_documents_direct())