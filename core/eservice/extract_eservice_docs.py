#!/usr/bin/env python3
"""
Extract and download E-Service documents from HTML files
Creates better naming system than court's messy format
"""
import os
import re
import json
from pathlib import Path
from bs4 import BeautifulSoup
from datetime import datetime
import asyncio
from core.efiling_scraper import <PERSON>uyahogaEFilingScraperPlaywright

async def extract_document_links_from_html(html_file: str) -> list:
    """Extract document information from E-Service HTML files"""
    documents = []
    
    with open(html_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    soup = BeautifulSoup(content, 'html.parser')
    
    # Find all rows in the table
    rows = soup.find_all('tr')
    
    for row in rows:
        # Skip header row
        if 'gridview_header' in row.get('class', []):
            continue
            
        cells = row.find_all('td')
        if len(cells) < 8:
            continue
            
        try:
            # Extract document link
            link_cell = cells[2]  # Image column
            link_elem = link_cell.find('a')
            if not link_elem:
                continue
                
            doc_url = link_elem.get('href', '')
            if not doc_url.startswith('DisplayImageList.aspx'):
                continue
                
            # Extract metadata
            date_received = cells[3].get_text(strip=True)
            recipient = cells[4].get_text(strip=True)
            case_number = cells[5].get_text(strip=True)
            case_caption = cells[6].get_text(strip=True)
            filing_type = cells[7].get_text(strip=True)
            sender = cells[8].get_text(strip=True) if len(cells) > 8 else "UNKNOWN"
            
            # Create clean document name
            # Format: #ID DATE TYPE SENDER
            doc_id = len(documents) + 1
            clean_date = date_received.replace('/', '-')
            clean_type = filing_type.replace(' ', '_').upper()
            clean_sender = sender.replace(' ', '_').replace('/', '_')
            
            doc_name = f"#{doc_id:03d}_{clean_date}_{clean_type}_{clean_sender}"
            
            document = {
                'id': doc_id,
                'name': doc_name,
                'url': doc_url,
                'date_received': date_received,
                'recipient': recipient,
                'case_number': case_number,
                'case_caption': case_caption,
                'filing_type': filing_type,
                'sender': sender,
                'source_file': os.path.basename(html_file)
            }
            
            documents.append(document)
            
        except Exception as e:
            print(f"Error processing row: {e}")
            continue
    
    return documents

async def download_eservice_documents():
    """Main function to extract and download all E-Service documents"""
    
    # Input directory with HTML files
    input_dir = Path("case_documents/partial-manual-input")
    output_dir = Path("case_documents/DR_25_403973/eservice_docs")
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # Get credentials
    username = os.getenv('EFILING_USERNAME')
    password = os.getenv('EFILING_PASSWORD')
    
    if not username or not password:
        print("❌ E-filing credentials not found in environment")
        print("Please set EFILING_USERNAME and EFILING_PASSWORD")
        return
    
    # Extract documents from all HTML files
    all_documents = []
    html_files = list(input_dir.glob("E-Service-Page*.html"))
    html_files.sort()
    
    print(f"📄 Found {len(html_files)} HTML files to process")
    
    for html_file in html_files:
        print(f"🔍 Processing {html_file.name}")
        docs = await extract_document_links_from_html(html_file)
        all_documents.extend(docs)
        print(f"   Found {len(docs)} documents")
    
    print(f"📊 Total documents found: {len(all_documents)}")
    
    # Save document metadata
    metadata_file = output_dir / "document_metadata.json"
    with open(metadata_file, 'w') as f:
        json.dump(all_documents, f, indent=2)
    
    print(f"💾 Saved metadata to {metadata_file}")
    
    # Download documents
    if all_documents:
        print(f"🔄 Starting download of {len(all_documents)} documents...")
        
        scraper = CuyahogaEFilingScraperPlaywright(username, password)
        
        try:
            # Convert documents to format expected by scraper
            download_docs = []
            for doc in all_documents:
                # The scraper expects specific format for document entries
                download_docs.append({
                    'Date Filed': doc['date_received'],
                    'Docket Number': f"{doc['id']:03d}",
                    'Document Description': doc['filing_type'],
                    'document_links': [{
                        'href': doc['url'],
                        'text': doc['name']
                    }]
                })
            
            # Download using existing scraper method
            results = await scraper.download_document_images(
                download_docs, 
                str(output_dir)
            )
            
            print(f"✅ Download completed!")
            print(f"   Successful: {results.get('successful_downloads', 0)}")
            print(f"   Failed: {results.get('failed_downloads', 0)}")
            
            if results.get('failed_downloads', 0) > 0:
                print("❌ Failed downloads:")
                for failure in results.get('failures', []):
                    print(f"   - {failure}")
            
        finally:
            await scraper.close()
    
    print("🎉 E-Service document extraction and download complete!")

if __name__ == "__main__":
    asyncio.run(download_eservice_documents())