#!/usr/bin/env python3
"""
Database Administration Dashboard UI
Visual admin interface for all databases with controls
"""

import streamlit as st
import plotly.graph_objects as go
import plotly.express as px
from datetime import datetime
import pandas as pd
import json

def render_database_admin_dashboard():
    """Render the comprehensive database admin dashboard"""
    
    st.header("🗄️ Database Administration Center")
    st.markdown("**Comprehensive control panel for all database systems**")
    
    # Get database stats
    try:
        from core.database_admin import get_database_admin_data
        dashboard_data = get_database_admin_data()
    except Exception as e:
        st.error(f"❌ Error loading database admin data: {e}")
        return
    
    # Overall health summary
    health = dashboard_data.get('overall_health', {})
    connected = health.get('connected_databases', 0)
    total = health.get('total_databases', 5)
    health_pct = health.get('health_percentage', 0)
    
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric("🏥 Database Health", f"{health_pct}%", f"{connected}/{total} Connected")
    
    with col2:
        total_size = 0
        pg_stats = dashboard_data['databases'].get('postgresql', {})
        mongo_stats = dashboard_data['databases'].get('mongodb', {})
        
        if pg_stats.get('database_size_bytes'):
            total_size += pg_stats['database_size_bytes']
        if mongo_stats.get('database_size_mb'):
            total_size += mongo_stats['database_size_mb'] * 1024 * 1024
            
        st.metric("💾 Total Data Size", f"{total_size/(1024**3):.2f} GB")
    
    with col3:
        redis_stats = dashboard_data['databases'].get('redis', {})
        hit_ratio = redis_stats.get('hit_ratio', 0)
        st.metric("🎯 Cache Hit Ratio", f"{hit_ratio}%")
    
    with col4:
        total_connections = 0
        total_connections += pg_stats.get('total_connections', 0)
        total_connections += redis_stats.get('connected_clients', 0)
        st.metric("🔗 Active Connections", total_connections)
    
    st.divider()
    
    # Database tabs
    pg_tab, mongo_tab, redis_tab, qdrant_tab, vault_tab = st.tabs([
        "🐘 PostgreSQL", "🍃 MongoDB", "🔴 Redis", "🔍 Qdrant", "🔒 Vault"
    ])
    
    with pg_tab:
        render_postgresql_admin(dashboard_data['databases']['postgresql'])
    
    with mongo_tab:
        render_mongodb_admin(dashboard_data['databases']['mongodb'])
    
    with redis_tab:
        render_redis_admin(dashboard_data['databases']['redis'])
    
    with qdrant_tab:
        render_qdrant_admin(dashboard_data['databases']['qdrant'])
    
    with vault_tab:
        render_vault_admin(dashboard_data['databases']['vault'])

def render_postgresql_admin(pg_stats):
    """Render PostgreSQL administration interface"""
    
    if pg_stats.get('status') != 'connected':
        st.error(f"❌ PostgreSQL: {pg_stats.get('error', 'Not connected')}")
        return
    
    st.success("✅ PostgreSQL Connected")
    
    # Stats overview
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric("Database Size", pg_stats.get('database_size', 'Unknown'))
    
    with col2:
        st.metric("Cache Hit Ratio", f"{pg_stats.get('cache_hit_ratio', 0):.1f}%")
    
    with col3:
        st.metric("Active Connections", pg_stats.get('active_connections', 0))
    
    with col4:
        st.metric("Total Connections", pg_stats.get('total_connections', 0))
    
    # Table statistics
    st.subheader("📊 Table Statistics")
    
    tables = pg_stats.get('tables', [])
    if tables:
        # Create DataFrame for table stats
        df = pd.DataFrame(tables)
        
        # Display table with live rows chart
        col1, col2 = st.columns([2, 1])
        
        with col1:
            st.dataframe(df[['name', 'live_rows', 'dead_rows', 'size', 'inserts', 'updates', 'deletes']], 
                        use_container_width=True)
        
        with col2:
            if len(df) > 0:
                # Pie chart of table sizes
                fig = px.pie(df.head(5), values='live_rows', names='name', 
                           title="Top Tables by Row Count")
                st.plotly_chart(fig, use_container_width=True)
    
    # Maintenance controls
    st.subheader("🔧 Maintenance Operations")
    
    col1, col2 = st.columns(2)
    
    with col1:
        if st.button("🧹 VACUUM All Tables", key="pg_vacuum"):
            with st.spinner("Running VACUUM ANALYZE on all tables..."):
                try:
                    from core.database_admin import perform_database_maintenance
                    result = perform_database_maintenance()
                    
                    pg_result = result.get('postgresql_vacuum', {})
                    if pg_result.get('success'):
                        st.success(f"✅ Vacuumed {pg_result.get('tables_processed', 0)} tables")
                        st.json(pg_result.get('results', []))
                    else:
                        st.error(f"❌ Vacuum failed: {pg_result.get('error', 'Unknown error')}")
                except Exception as e:
                    st.error(f"❌ Maintenance error: {e}")
    
    with col2:
        st.info("💡 **VACUUM Benefits:**\n- Reclaims dead row space\n- Updates table statistics\n- Improves query performance")

def render_mongodb_admin(mongo_stats):
    """Render MongoDB administration interface"""
    
    if mongo_stats.get('status') != 'connected':
        st.error(f"❌ MongoDB: {mongo_stats.get('error', 'Not connected')}")
        return
    
    st.success("✅ MongoDB Connected")
    
    # Stats overview
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric("Data Size", f"{mongo_stats.get('database_size_mb', 0):.1f} MB")
    
    with col2:
        st.metric("Storage Size", f"{mongo_stats.get('storage_size_mb', 0):.1f} MB")
    
    with col3:
        st.metric("Collections", mongo_stats.get('collections_count', 0))
    
    with col4:
        st.metric("Documents", f"{mongo_stats.get('objects_count', 0):,}")
    
    # Collection statistics
    st.subheader("📂 Collection Statistics")
    
    collections = mongo_stats.get('collections', [])
    if collections:
        df = pd.DataFrame(collections)
        
        col1, col2 = st.columns([2, 1])
        
        with col1:
            st.dataframe(df[['name', 'documents', 'size_bytes', 'indexes', 'avg_obj_size']], 
                        use_container_width=True)
        
        with col2:
            if len(df) > 0:
                # Bar chart of document counts
                fig = px.bar(df, x='name', y='documents', 
                           title="Documents per Collection")
                fig.update_xaxes(tickangle=45)
                st.plotly_chart(fig, use_container_width=True)
    
    # Maintenance controls
    st.subheader("🔧 Index Optimization")
    
    col1, col2 = st.columns(2)
    
    with col1:
        if st.button("🔄 Reindex All Collections", key="mongo_reindex"):
            with st.spinner("Optimizing MongoDB indexes..."):
                try:
                    from core.database_admin import perform_database_maintenance
                    result = perform_database_maintenance()
                    
                    mongo_result = result.get('mongodb_reindex', {})
                    if mongo_result.get('success'):
                        st.success(f"✅ Reindexed {mongo_result.get('collections_processed', 0)} collections")
                        st.json(mongo_result.get('results', []))
                    else:
                        st.error(f"❌ Reindex failed: {mongo_result.get('error', 'Unknown error')}")
                except Exception as e:
                    st.error(f"❌ Index optimization error: {e}")
    
    with col2:
        st.info("💡 **Reindex Benefits:**\n- Optimizes query performance\n- Rebuilds corrupted indexes\n- Reclaims index space")

def render_redis_admin(redis_stats):
    """Render Redis administration interface with cache management"""
    
    if redis_stats.get('status') != 'connected':
        st.error(f"❌ Redis: {redis_stats.get('error', 'Not connected')}")
        return
    
    st.success("✅ Redis Connected")
    
    # Performance metrics
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric("Memory Used", f"{redis_stats.get('memory_used_mb', 0):.1f} MB")
    
    with col2:
        st.metric("Hit Ratio", f"{redis_stats.get('hit_ratio', 0):.1f}%")
    
    with col3:
        st.metric("Total Keys", f"{redis_stats.get('total_keys', 0):,}")
    
    with col4:
        st.metric("Connected Clients", redis_stats.get('connected_clients', 0))
    
    # Performance charts
    st.subheader("📈 Performance Analytics")
    
    col1, col2 = st.columns(2)
    
    with col1:
        # Memory usage gauge
        memory_used = redis_stats.get('memory_used_mb', 0)
        memory_peak = redis_stats.get('memory_peak_mb', 0)
        
        fig = go.Figure(go.Indicator(
            mode = "gauge+number+delta",
            value = memory_used,
            domain = {'x': [0, 1], 'y': [0, 1]},
            title = {'text': "Memory Usage (MB)"},
            delta = {'reference': memory_peak},
            gauge = {
                'axis': {'range': [None, max(memory_peak * 1.2, 100)]},
                'bar': {'color': "darkblue"},
                'steps': [
                    {'range': [0, memory_peak * 0.7], 'color': "lightgray"},
                    {'range': [memory_peak * 0.7, memory_peak], 'color': "gray"}],
                'threshold': {
                    'line': {'color': "red", 'width': 4},
                    'thickness': 0.75,
                    'value': memory_peak * 0.9}}))
        
        st.plotly_chart(fig, use_container_width=True)
    
    with col2:
        # Hit ratio gauge
        hit_ratio = redis_stats.get('hit_ratio', 0)
        
        fig = go.Figure(go.Indicator(
            mode = "gauge+number",
            value = hit_ratio,
            domain = {'x': [0, 1], 'y': [0, 1]},
            title = {'text': "Cache Hit Ratio (%)"},
            gauge = {
                'axis': {'range': [None, 100]},
                'bar': {'color': "darkgreen"},
                'steps': [
                    {'range': [0, 60], 'color': "red"},
                    {'range': [60, 80], 'color': "yellow"},
                    {'range': [80, 100], 'color': "green"}],
                'threshold': {
                    'line': {'color': "red", 'width': 4},
                    'thickness': 0.75,
                    'value': 90}}))
        
        st.plotly_chart(fig, use_container_width=True)
    
    # Key analysis
    st.subheader("🔑 Key Analysis")
    
    key_types = redis_stats.get('key_types', {})
    if key_types:
        col1, col2 = st.columns(2)
        
        with col1:
            # Key types pie chart
            fig = px.pie(values=list(key_types.values()), names=list(key_types.keys()), 
                        title="Key Types Distribution")
            st.plotly_chart(fig, use_container_width=True)
        
        with col2:
            # Sample key sizes
            sample_sizes = redis_stats.get('sample_key_sizes', {})
            if sample_sizes:
                st.write("**Sample Key Sizes:**")
                for key, size in list(sample_sizes.items())[:5]:
                    st.write(f"• `{key}`: {size}")
    
    # Cache management
    st.subheader("🧹 Cache Management")
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        if st.button("🗑️ Clear All Cache", key="redis_clear_all"):
            if st.confirm("⚠️ This will clear ALL cached data. Continue?"):
                with st.spinner("Clearing all cache..."):
                    try:
                        from core.database_admin import clear_cache_by_pattern
                        result = clear_cache_by_pattern("*")
                        
                        if result.get('success'):
                            st.success("✅ All cache cleared")
                            st.rerun()
                        else:
                            st.error(f"❌ Clear failed: {result.get('error')}")
                    except Exception as e:
                        st.error(f"❌ Clear cache error: {e}")
    
    with col2:
        # Pattern-based clearing
        pattern = st.text_input("Pattern (e.g., session:*)", placeholder="session:*", key="redis_pattern")
        if st.button("🎯 Clear by Pattern", key="redis_clear_pattern"):
            if pattern:
                with st.spinner(f"Clearing pattern: {pattern}"):
                    try:
                        from core.database_admin import clear_cache_by_pattern
                        result = clear_cache_by_pattern(pattern)
                        
                        if result.get('success'):
                            cleared = result.get('cleared_keys', 0)
                            st.success(f"✅ Cleared {cleared} keys matching '{pattern}'")
                            st.rerun()
                        else:
                            st.error(f"❌ Clear failed: {result.get('error')}")
                    except Exception as e:
                        st.error(f"❌ Clear cache error: {e}")
            else:
                st.warning("Please enter a pattern")
    
    with col3:
        st.info("💡 **Common Patterns:**\n- `session:*` - User sessions\n- `cache:*` - Application cache\n- `temp:*` - Temporary data")

def render_qdrant_admin(qdrant_stats):
    """Render Qdrant vector database administration"""
    
    if qdrant_stats.get('status') != 'connected':
        st.error(f"❌ Qdrant: {qdrant_stats.get('error', 'Not connected')}")
        return
    
    st.success("✅ Qdrant Connected")
    
    collections = qdrant_stats.get('collections', [])
    
    if collections:
        # Collections overview
        col1, col2, col3, col4 = st.columns(4)
        
        total_vectors = sum(c.get('vectors_count', 0) for c in collections)
        total_points = sum(c.get('points_count', 0) for c in collections)
        
        with col1:
            st.metric("Collections", len(collections))
        
        with col2:
            st.metric("Total Vectors", f"{total_vectors:,}")
        
        with col3:
            st.metric("Total Points", f"{total_points:,}")
        
        with col4:
            indexed_vectors = sum(c.get('indexed_vectors_count', 0) for c in collections)
            st.metric("Indexed Vectors", f"{indexed_vectors:,}")
        
        # Collections details
        st.subheader("🗂️ Vector Collections")
        
        for collection in collections:
            with st.expander(f"📂 {collection['name']}"):
                col1, col2, col3 = st.columns(3)
                
                with col1:
                    st.metric("Vectors", f"{collection.get('vectors_count', 0):,}")
                
                with col2:
                    st.metric("Points", f"{collection.get('points_count', 0):,}")
                
                with col3:
                    st.metric("Segments", collection.get('segments_count', 0))
                
                config = collection.get('config', {})
                if config:
                    st.json(config)
    else:
        st.info("📝 No vector collections found")

def render_vault_admin(vault_stats):
    """Render HashiCorp Vault administration"""
    
    if vault_stats.get('status') == 'unavailable':
        st.info("ℹ️ HashiCorp Vault (Optional Service)")
        st.write("**Status:** Not currently running")
        st.write("**Purpose:** Secure secret management and key storage")
        
        vault_url = vault_stats.get('vault_url', 'http://localhost:8200')
        st.write(f"**Expected URL:** {vault_url}")
        
        st.divider()
        
        col1, col2 = st.columns(2)
        
        with col1:
            st.write("**Start Vault:**")
            if st.button("🔒 Deploy Vault", help="Deploy HashiCorp Vault container"):
                try:
                    import subprocess
                    result = subprocess.run(['python', 'setup_vault.py'], capture_output=True, text=True, timeout=60)
                    if result.returncode == 0:
                        st.success("✅ Vault deployment initiated")
                        st.info("Refresh page to see Vault status")
                    else:
                        st.error(f"❌ Vault deployment failed: {result.stderr}")
                except Exception as e:
                    st.error(f"❌ Deployment error: {e}")
        
        with col2:
            st.write("**Alternative Options:**")
            st.info("The system works without Vault")
            st.caption("• Basic secret storage available")
            st.caption("• Full functionality maintained")
            st.caption("• Vault optional for enhanced security")
        
        return
    elif vault_stats.get('status') != 'connected':
        st.warning(f"⚠️ Vault Connection Issue: {vault_stats.get('error', 'Unknown error')}")
        
        # Show connection details for troubleshooting
        if 'connection_error' in vault_stats:
            with st.expander("🔧 Connection Details"):
                st.code(vault_stats['connection_error'])
        
        return
    
    st.success("✅ HashiCorp Vault Connected")
    
    health = vault_stats.get('health', {})
    
    # Health status
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        sealed = health.get('sealed', True)
        st.metric("Status", "🔓 Unsealed" if not sealed else "🔒 Sealed")
    
    with col2:
        version = health.get('version', 'Unknown')
        st.metric("Version", version)
    
    with col3:
        cluster_name = health.get('cluster_name', 'Unknown')
        st.metric("Cluster", cluster_name)
    
    with col4:
        standby = health.get('standby', False)
        st.metric("Role", "Standby" if standby else "Active")
    
    # Auth methods
    auth_methods = vault_stats.get('auth_methods', {})
    if auth_methods:
        st.subheader("🔐 Authentication Methods")
        
        auth_df = []
        for path, method_info in auth_methods.items():
            auth_df.append({
                'Path': path,
                'Type': method_info.get('type', 'unknown'),
                'Description': method_info.get('description', 'N/A')
            })
        
        if auth_df:
            st.dataframe(pd.DataFrame(auth_df), use_container_width=True)
    
    # Secret engines
    secret_engines = vault_stats.get('secret_engines', {})
    if secret_engines:
        st.subheader("🗝️ Secret Engines")
        
        engines_df = []
        for path, engine_info in secret_engines.items():
            engines_df.append({
                'Path': path,
                'Type': engine_info.get('type', 'unknown'),
                'Description': engine_info.get('description', 'N/A')
            })
        
        if engines_df:
            st.dataframe(pd.DataFrame(engines_df), use_container_width=True)
    
    # Policies
    policies = vault_stats.get('policies', [])
    if policies:
        st.subheader("📋 Access Policies")
        
        for policy in policies:
            st.write(f"• {policy}")
    else:
        st.info("ℹ️ No policy information available (may require additional permissions)")

# Integration function
def show_database_admin_dashboard():
    """Main function to show the database admin dashboard"""
    render_database_admin_dashboard()