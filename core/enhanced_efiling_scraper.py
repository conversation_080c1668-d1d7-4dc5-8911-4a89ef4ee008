"""
Enhanced E-Filing Images Scraper with Full Pagination Support
Handles multi-page image scraping for Cuyahoga County E-Filing System
"""

import asyncio
import os
import time
import logging
import re
import aiohttp
import aiofiles
from datetime import datetime
from typing import List, Dict, Optional, Tuple
from urllib.parse import urljoin, urlparse, parse_qs
from pathlib import Path

try:
    from playwright.async_api import async_playwright, Browser, Page
    PLAYWRIGHT_AVAILABLE = True
except ImportError:
    PLAYWRIGHT_AVAILABLE = False
    logging.warning("Playwright not available. Install with: pip install playwright")

class EnhancedEFilingImagesScraper:
    """
    Enhanced scraper specifically for e-filing images with full pagination support
    Handles the specific case of 63 images across 5 pages (4x15 + 3)
    """
    
    def __init__(self, username: str, password: str, headless: bool = False, download_dir: str = None):
        self.username = username
        self.password = password
        self.headless = headless
        self.base_url = "https://efiling.cp.cuyahogacounty.gov"
        self.login_url = f"{self.base_url}/Login.aspx"
        self.images_base_url = f"{self.base_url}/Images.aspx"
        
        # Setup download directory
        if download_dir:
            self.download_dir = Path(download_dir)
        else:
            self.download_dir = Path("efiling_downloads") / datetime.now().strftime("%Y%m%d_%H%M%S")
        
        self.download_dir.mkdir(parents=True, exist_ok=True)
        
        self.browser = None
        self.page = None
        self.logged_in = False
        self.session_cookies = None
        
        # Setup logging
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
    
    async def start_browser(self):
        """Start Playwright browser"""
        if not PLAYWRIGHT_AVAILABLE:
            raise RuntimeError("Playwright not available")
        
        self.playwright = await async_playwright().start()
        self.browser = await self.playwright.chromium.launch(
            headless=self.headless,
            args=['--no-sandbox', '--disable-dev-shm-usage']
        )
        self.page = await self.browser.new_page()
        
        # Set reasonable timeouts
        self.page.set_default_timeout(30000)  # 30 seconds
        
    async def login(self) -> bool:
        """Login to the e-filing system"""
        try:
            await self.page.goto(self.login_url, wait_until='networkidle')
            
            # Fill in login credentials
            await self.page.fill('input[name="ctl00$ContentPlaceHolder1$txtUserName"]', self.username)
            await self.page.fill('input[name="ctl00$ContentPlaceHolder1$txtPassword"]', self.password)
            
            # Click login button
            await self.page.click('input[name="ctl00$ContentPlaceHolder1$btnLogin"]')
            
            # Wait for redirect to home page
            await self.page.wait_for_url('**/Home.aspx', timeout=15000)
            
            self.logged_in = True
            
            # Get session cookies for downloads
            self.session_cookies = await self.page.context.cookies()
            self.logger.info("Successfully logged in and captured session cookies")
            return True
            
        except Exception as e:
            self.logger.error(f"Login failed: {e}")
            return False
    
    async def scrape_all_images_pages(self, case_query: str) -> Dict:
        """
        Scrape ALL pages of images for a case with full pagination support
        
        Args:
            case_query: The ?q= parameter from the case URL
            
        Returns:
            Complete dictionary with all images from all pages
        """
        if not self.logged_in:
            await self.login()
        
        images_url = f"{self.images_base_url}?q={case_query}"
        
        try:
            # Navigate to images page
            await self.page.goto(images_url, wait_until='networkidle')
            await asyncio.sleep(3)  # Allow page to fully load
            
            # Initialize results structure
            results = {
                'case_query': case_query,
                'scraped_at': datetime.now().isoformat(),
                'base_url': images_url,
                'total_pages': 0,
                'total_images': 0,
                'total_records': 0,
                'pages': [],
                'all_images': [],
                'all_records': [],
                'table_headers': []
            }
            
            # Detect total pages
            total_pages = await self.detect_total_pages()
            results['total_pages'] = total_pages
            
            self.logger.info(f"Found {total_pages} pages of images")
            
            # Scrape each page
            for page_num in range(1, total_pages + 1):
                self.logger.info(f"Scraping page {page_num} of {total_pages}")
                
                page_data = await self.scrape_single_images_page(case_query, page_num)
                results['pages'].append(page_data)
                results['all_images'].extend(page_data['images'])
                results['all_records'].extend(page_data.get('records', []))
                
                # Small delay between pages
                await asyncio.sleep(2)
            
            results['total_images'] = len(results['all_images'])
            results['total_records'] = len(results['all_records'])
            
            # Extract table headers from first page if available
            if results['pages'] and results['pages'][0].get('table_headers'):
                results['table_headers'] = results['pages'][0]['table_headers']
            
            self.logger.info(f"Scraped {results['total_images']} total images and {results['total_records']} table records from {total_pages} pages")
            
            # Download all images
            if results['all_images']:
                self.logger.info("Starting download of all images...")
                download_results = await self.download_all_images(results['all_images'], case_query)
                results['downloads'] = download_results
                results['download_directory'] = str(self.download_dir)
            
            return results
            
        except Exception as e:
            self.logger.error(f"Failed to scrape images: {e}")
            return {
                'error': str(e),
                'case_query': case_query,
                'scraped_at': datetime.now().isoformat()
            }
    
    async def detect_total_pages(self) -> int:
        """Detect the total number of pages from pagination controls"""
        try:
            # Look for pagination info - multiple possible selectors
            pagination_selectors = [
                'span:has-text("Page")',
                'div:has-text("Page")', 
                'span[id*="page"]',
                'div[id*="page"]',
                'span[id*="Label"]',
                'td:has-text("Page")'
            ]
            
            for selector in pagination_selectors:
                try:
                    elements = await self.page.query_selector_all(selector)
                    for element in elements:
                        text = await element.text_content()
                        if text and 'page' in text.lower():
                            # Look for "Page X of Y" pattern
                            page_match = re.search(r'page\s+(\d+)\s+of\s+(\d+)', text.lower())
                            if page_match:
                                total_pages = int(page_match.group(2))
                                self.logger.info(f"Found pagination info: {text} -> {total_pages} pages")
                                return total_pages
                except:
                    continue
            
            # Alternative: Look for "Next" buttons and count
            next_buttons = await self.page.query_selector_all('a:has-text("Next"), input[value*="Next"]')
            if next_buttons:
                # If there's a Next button, try to count pages by looking at page numbers
                page_numbers = await self.page.query_selector_all('a:regex(\\d+)')
                if page_numbers:
                    max_page = 0
                    for page_link in page_numbers:
                        text = await page_link.text_content()
                        if text and text.isdigit():
                            max_page = max(max_page, int(text))
                    if max_page > 0:
                        return max_page
            
            # Default assumption: if no pagination detected, assume 1 page
            self.logger.warning("Could not detect pagination, assuming 1 page")
            return 1
            
        except Exception as e:
            self.logger.error(f"Error detecting pages: {e}")
            return 1
    
    async def scrape_single_images_page(self, case_query: str, page_num: int) -> Dict:
        """Scrape a single page of images"""
        
        # Navigate to specific page
        if page_num > 1:
            page_url = f"{self.images_base_url}?q={case_query}&page={page_num}"
            await self.page.goto(page_url, wait_until='networkidle')
            await asyncio.sleep(2)
        
        page_data = {
            'page_number': page_num,
            'url': self.page.url,
            'scraped_at': datetime.now().isoformat(),
            'images': [],
            'records': [],
            'table_headers': []
        }
        
        # First, extract complete table data (like E-Service scraper)
        await self.extract_table_data(page_data)
        
        # Then, look for all possible image elements
        image_selectors = [
            'a[href*="DisplayImage"]',
            'a[href*="Image.aspx"]',
            'a[title*="View"]',
            'img[src*="images.png"]',
            'img[src*="ImageSheet"]',
            'a[onclick*="Image"]',
            'td[onclick*="Image"]'
        ]
        
        found_images = set()  # Use set to avoid duplicates
        
        for selector in image_selectors:
            try:
                elements = await self.page.query_selector_all(selector)
                self.logger.info(f"Found {len(elements)} elements with selector: {selector}")
                
                for element in elements:
                    image_info = await self.extract_image_info(element)
                    if image_info and image_info.get('link') and image_info['link'] not in found_images:
                        page_data['images'].append(image_info)
                        found_images.add(image_info['link'])
                        
            except Exception as e:
                self.logger.warning(f"Error with selector {selector}: {e}")
                continue
        
        # Also try to find images in table rows
        try:
            rows = await self.page.query_selector_all('tr')
            for row in rows:
                row_text = await row.text_content()
                if row_text and any(keyword in row_text.lower() for keyword in ['image', 'pdf', 'doc']):
                    # This row might contain an image link
                    links = await row.query_selector_all('a')
                    for link in links:
                        image_info = await self.extract_image_info(link)
                        if image_info and image_info['link'] not in found_images:
                            page_data['images'].append(image_info)
                            found_images.add(image_info['link'])
        except:
            pass
        
        self.logger.info(f"Page {page_num}: Found {len(page_data['images'])} unique images")
        return page_data
    
    async def extract_image_info(self, element) -> Optional[Dict]:
        """Extract image information from an element"""
        try:
            image_info = {}
            
            # Get href (for links)
            href = await element.get_attribute('href')
            if href:
                image_info['link'] = href
                image_info['full_url'] = urljoin(self.base_url, href)
            
            # Get src (for images)
            src = await element.get_attribute('src')
            if src:
                image_info['src'] = src
                image_info['image_url'] = urljoin(self.base_url, src)
            
            # Get title and alt text
            image_info['title'] = await element.get_attribute('title') or ''
            image_info['alt'] = await element.get_attribute('alt') or ''
            
            # Get onclick events (some images use onclick)
            onclick = await element.get_attribute('onclick')
            if onclick:
                image_info['onclick'] = onclick
            
            # Get text content
            text = await element.text_content()
            if text:
                image_info['text'] = text.strip()
            
            # Extract any document/case information from the context
            parent = await element.query_selector('xpath=..')
            if parent:
                parent_text = await parent.text_content()
                if parent_text:
                    image_info['context'] = parent_text.strip()
            
            # Only return if we found a useful link or src
            if href or src:
                return image_info
            
            return None
            
        except Exception as e:
            self.logger.warning(f"Error extracting image info: {e}")
            return None
    
    async def extract_table_data(self, page_data: Dict):
        """Extract complete table data from the images page (similar to E-Service scraper)"""
        try:
            # Look for the main table containing image records
            table_selectors = [
                'table[id*="GridView"]',
                'table[id*="DataGrid"]', 
                'table[class*="grid"]',
                'table:has(tr:has(td))'  # Any table with data rows
            ]
            
            main_table = None
            for selector in table_selectors:
                try:
                    tables = await self.page.query_selector_all(selector)
                    for table in tables:
                        # Check if table has multiple rows (indicating data)
                        rows = await table.query_selector_all('tr')
                        if len(rows) > 1:  # More than just header row
                            main_table = table
                            break
                    if main_table:
                        break
                except:
                    continue
            
            if not main_table:
                self.logger.info(f"No data table found on page {page_data['page_number']} - trying alternative approach")
                # Alternative: look for any structured data in rows
                await self.extract_alternative_table_data(page_data)
                return
            
            # Extract table headers
            header_row = await main_table.query_selector('tr:first-child')
            headers = []
            if header_row:
                header_cells = await header_row.query_selector_all('th, td')
                for cell in header_cells:
                    text = await cell.text_content()
                    headers.append(text.strip() if text else '')
                page_data['table_headers'] = headers
            
            # Extract data rows
            data_rows = await main_table.query_selector_all('tr:not(:first-child)')
            
            for row_index, row in enumerate(data_rows):
                try:
                    record = {
                        'row_number': row_index + 1,
                        'page_number': page_data['page_number'],
                        'raw_html': await row.inner_html(),
                        'data': {},
                        'links': [],
                        'images': []
                    }
                    
                    # Extract cell data
                    cells = await row.query_selector_all('td')
                    for cell_index, cell in enumerate(cells):
                        cell_text = await cell.text_content()
                        cell_text = cell_text.strip() if cell_text else ''
                        
                        # Use header name if available, otherwise use column index
                        column_name = headers[cell_index] if cell_index < len(headers) else f'Column_{cell_index + 1}'
                        record['data'][column_name] = cell_text
                        
                        # Look for links in this cell
                        links = await cell.query_selector_all('a')
                        for link in links:
                            href = await link.get_attribute('href')
                            link_text = await link.text_content()
                            if href:
                                link_info = {
                                    'url': href,
                                    'full_url': urljoin(self.base_url, href),
                                    'text': link_text.strip() if link_text else '',
                                    'column': column_name
                                }
                                record['links'].append(link_info)
                        
                        # Look for images in this cell
                        images = await cell.query_selector_all('img')
                        for img in images:
                            src = await img.get_attribute('src')
                            alt = await img.get_attribute('alt')
                            if src:
                                img_info = {
                                    'src': src,
                                    'full_url': urljoin(self.base_url, src),
                                    'alt': alt or '',
                                    'column': column_name
                                }
                                record['images'].append(img_info)
                    
                    page_data['records'].append(record)
                    
                except Exception as e:
                    self.logger.warning(f"Error processing table row {row_index + 1} on page {page_data['page_number']}: {e}")
                    continue
            
            self.logger.info(f"Page {page_data['page_number']}: Extracted {len(page_data['records'])} table records")
            
        except Exception as e:
            self.logger.error(f"Error extracting table data from page {page_data['page_number']}: {e}")
    
    async def extract_alternative_table_data(self, page_data: Dict):
        """Alternative method to extract structured data when no formal table is found"""
        try:
            # Look for any structured rows that might contain image information
            row_selectors = [
                'tr',  # Table rows
                'div[class*="row"]',  # Div-based rows
                'li',  # List items
                'div:has(a[href*="Image"])'  # Divs containing image links
            ]
            
            record_count = 0
            for selector in row_selectors:
                try:
                    elements = await self.page.query_selector_all(selector)
                    
                    for element_index, element in enumerate(elements):
                        # Check if this element contains useful information
                        text_content = await element.text_content()
                        if not text_content or len(text_content.strip()) < 10:
                            continue
                        
                        # Look for links and images in this element
                        links = await element.query_selector_all('a')
                        images = await element.query_selector_all('img')
                        
                        if links or images:  # Only create record if it has links or images
                            record = {
                                'row_number': record_count + 1,
                                'page_number': page_data['page_number'],
                                'raw_html': await element.inner_html(),
                                'data': {
                                    'text_content': text_content.strip(),
                                    'element_type': selector
                                },
                                'links': [],
                                'images': []
                            }
                            
                            # Extract links
                            for link in links:
                                href = await link.get_attribute('href')
                                link_text = await link.text_content()
                                if href:
                                    link_info = {
                                        'url': href,
                                        'full_url': urljoin(self.base_url, href),
                                        'text': link_text.strip() if link_text else '',
                                        'column': 'extracted_link'
                                    }
                                    record['links'].append(link_info)
                            
                            # Extract images
                            for img in images:
                                src = await img.get_attribute('src')
                                alt = await img.get_attribute('alt')
                                if src:
                                    img_info = {
                                        'src': src,
                                        'full_url': urljoin(self.base_url, src),
                                        'alt': alt or '',
                                        'column': 'extracted_image'
                                    }
                                    record['images'].append(img_info)
                            
                            page_data['records'].append(record)
                            record_count += 1
                            
                            if record_count >= 20:  # Limit to avoid too many records
                                break
                    
                    if record_count > 0:
                        break  # Found structured data, no need to try other selectors
                        
                except Exception as e:
                    continue
            
            if record_count > 0:
                self.logger.info(f"Page {page_data['page_number']}: Extracted {record_count} alternative structured records")
            else:
                self.logger.warning(f"Page {page_data['page_number']}: No structured data found")
                
        except Exception as e:
            self.logger.error(f"Error extracting alternative table data: {e}")
    
    async def navigate_to_next_page(self) -> bool:
        """Navigate to the next page if available"""
        try:
            # Look for Next button or link
            next_selectors = [
                'a:has-text("Next")',
                'input[value*="Next"]',
                'button:has-text("Next")',
                'a[title*="Next"]'
            ]
            
            for selector in next_selectors:
                try:
                    next_button = await self.page.query_selector(selector)
                    if next_button:
                        # Check if it's enabled/clickable
                        is_disabled = await next_button.get_attribute('disabled')
                        if not is_disabled:
                            await next_button.click()
                            await self.page.wait_for_load_state('networkidle')
                            await asyncio.sleep(2)
                            return True
                except:
                    continue
            
            return False
            
        except Exception as e:
            self.logger.error(f"Error navigating to next page: {e}")
            return False
    
    async def download_all_images(self, all_images: List[Dict], case_query: str) -> Dict:
        """Download all found images to local directory"""
        download_results = {
            'total_attempted': len(all_images),
            'successful_downloads': 0,
            'failed_downloads': 0,
            'downloaded_files': [],
            'errors': []
        }
        
        # Create session for downloads with cookies
        timeout = aiohttp.ClientTimeout(total=60)  # 60 second timeout per download
        
        async with aiohttp.ClientSession(timeout=timeout) as session:
            # Add cookies to session
            if self.session_cookies:
                for cookie in self.session_cookies:
                    session.cookie_jar.update_cookies({cookie['name']: cookie['value']}, 
                                                     response_url=cookie.get('domain', self.base_url))
            
            # Download images with concurrency control
            semaphore = asyncio.Semaphore(3)  # Max 3 concurrent downloads
            
            download_tasks = []
            for i, image_info in enumerate(all_images):
                task = self.download_single_image(session, image_info, i + 1, case_query, semaphore)
                download_tasks.append(task)
            
            # Execute all downloads
            download_results_list = await asyncio.gather(*download_tasks, return_exceptions=True)
            
            # Process results
            for result in download_results_list:
                if isinstance(result, Exception):
                    download_results['failed_downloads'] += 1
                    download_results['errors'].append(str(result))
                elif result and result.get('success'):
                    download_results['successful_downloads'] += 1
                    download_results['downloaded_files'].append(result['filename'])
                else:
                    download_results['failed_downloads'] += 1
                    if result and result.get('error'):
                        download_results['errors'].append(result['error'])
        
        self.logger.info(f"Download complete: {download_results['successful_downloads']}/{download_results['total_attempted']} successful")
        return download_results
    
    async def download_single_image(self, session: aiohttp.ClientSession, image_info: Dict, 
                                   image_number: int, case_query: str, semaphore: asyncio.Semaphore) -> Dict:
        """Download a single image file"""
        async with semaphore:
            try:
                # Get the download URL
                download_url = image_info.get('full_url') or image_info.get('link')
                if not download_url:
                    return {'success': False, 'error': 'No download URL found'}
                
                # Make sure URL is absolute
                if not download_url.startswith('http'):
                    download_url = urljoin(self.base_url, download_url)
                
                self.logger.info(f"Downloading image {image_number}: {download_url}")
                
                # Download the file
                async with session.get(download_url) as response:
                    if response.status == 200:
                        # Determine file extension from content type or URL
                        content_type = response.headers.get('content-type', '')
                        file_extension = self.get_file_extension(content_type, download_url)
                        
                        # Generate filename
                        filename = f"{case_query}_image_{image_number:03d}{file_extension}"
                        filepath = self.download_dir / filename
                        
                        # Save file
                        async with aiofiles.open(filepath, 'wb') as f:
                            async for chunk in response.content.iter_chunked(8192):
                                await f.write(chunk)
                        
                        file_size = filepath.stat().st_size
                        self.logger.info(f"Downloaded: {filename} ({file_size} bytes)")
                        
                        return {
                            'success': True,
                            'filename': filename,
                            'filepath': str(filepath),
                            'size': file_size,
                            'url': download_url
                        }
                    else:
                        error_msg = f"HTTP {response.status} for {download_url}"
                        self.logger.error(error_msg)
                        return {'success': False, 'error': error_msg}
                        
            except Exception as e:
                error_msg = f"Failed to download image {image_number}: {str(e)}"
                self.logger.error(error_msg)
                return {'success': False, 'error': error_msg}
    
    def get_file_extension(self, content_type: str, url: str) -> str:
        """Determine file extension from content type or URL"""
        # Check content type first
        if 'pdf' in content_type.lower():
            return '.pdf'
        elif 'image/jpeg' in content_type.lower() or 'image/jpg' in content_type.lower():
            return '.jpg'
        elif 'image/png' in content_type.lower():
            return '.png'
        elif 'image/gif' in content_type.lower():
            return '.gif'
        elif 'image/tiff' in content_type.lower():
            return '.tiff'
        elif 'text/html' in content_type.lower():
            return '.html'
        
        # Check URL extension
        if url:
            url_lower = url.lower()
            if '.pdf' in url_lower:
                return '.pdf'
            elif '.jpg' in url_lower or '.jpeg' in url_lower:
                return '.jpg'
            elif '.png' in url_lower:
                return '.png'
            elif '.gif' in url_lower:
                return '.gif'
            elif '.tiff' in url_lower or '.tif' in url_lower:
                return '.tiff'
            elif '.html' in url_lower or '.htm' in url_lower:
                return '.html'
        
        # Default to .pdf for court documents
        return '.pdf'
    
    async def close(self):
        """Clean up browser resources"""
        if self.page:
            await self.page.close()
        if self.browser:
            await self.browser.close()
        if hasattr(self, 'playwright'):
            await self.playwright.stop()

# Convenience function for one-shot scraping
async def scrape_all_case_images(username: str, password: str, case_query: str, headless: bool = False, download_dir: str = None) -> Dict:
    """
    Convenience function to scrape and download all images for a case
    
    Args:
        username: E-filing login username
        password: E-filing login password  
        case_query: The case query parameter (from URL ?q=...)
        headless: Whether to run browser in headless mode
        download_dir: Directory to save downloaded files (optional)
        
    Returns:
        Complete results dictionary with all images and download info
    """
    scraper = EnhancedEFilingImagesScraper(username, password, headless, download_dir)
    
    try:
        await scraper.start_browser()
        results = await scraper.scrape_all_images_pages(case_query)
        return results
    finally:
        await scraper.close()

# Example usage
if __name__ == "__main__":
    async def main():
        # Extract case query from URL
        url = "https://efiling.cp.cuyahogacounty.gov/Images.aspx?q=I-fJldkwrgJ3jp2LoiI3Mg2"
        case_query = url.split('?q=')[1] if '?q=' in url else ""
        
        if case_query:
            results = await scrape_all_case_images(
                username="your_username",
                password="your_password", 
                case_query=case_query,
                headless=False  # Set to True for production
            )
            
            print(f"Total images found: {results.get('total_images', 0)}")
            print(f"Total pages: {results.get('total_pages', 0)}")
            
            # Save results
            import json
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"efiling_images_{case_query}_{timestamp}.json"
            
            with open(filename, 'w') as f:
                json.dump(results, f, indent=2)
            
            print(f"Results saved to: {filename}")
        else:
            print("Could not extract case query from URL")
    
    asyncio.run(main())