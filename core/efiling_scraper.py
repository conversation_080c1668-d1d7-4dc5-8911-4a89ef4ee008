"""
Enhanced Cuyahoga County E-Filing System Integration
Uses Playwright for more reliable web scraping with better timeout handling
"""

import asyncio
import os
import time
import logging
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Optional, Tuple
import re
import base64
from urllib.parse import urljoin, urlparse, parse_qs

try:
    from playwright.async_api import async_playwright, <PERSON><PERSON><PERSON>, Page
    PLAYWRIGHT_AVAILABLE = True
except ImportError:
    PLAYWRIGHT_AVAILABLE = False
    logging.warning("Playwright not available. Install with: pip install playwright")

class CuyahogaEFilingScraperPlaywright:
    """
    Enhanced Cuyahoga County E-Filing System scraper using Playwright
    
    Handles:
    - Automatic login with credentials
    - Case navigation and document scraping
    - Four key areas: Docket Info, Images, E-Service, My Filings
    - Robust timeout and error handling
    """
    
    def __init__(self, username: str, password: str, headless: bool = True):
        self.username = username
        self.password = password
        self.headless = headless
        self.base_url = "https://efiling.cp.cuyahogacounty.gov"
        self.docket_base_url = "https://cpdocket.cp.cuyahogacounty.us"
        self.login_url = f"{self.base_url}/Login.aspx"
        self.home_url = f"{self.base_url}/Home.aspx"
        
        # URLs for different sections
        self.urls = {
            'login': f"{self.base_url}/Login.aspx",
            'home': f"{self.base_url}/Home.aspx",
            'docket': f"{self.base_url}/CV_CaseInformation_Docket.aspx",
            'docket_alt': f"{self.base_url}/CaseInformation.aspx",
            'docket_alt2': f"{self.base_url}/CV_CaseInformation.aspx", 
            'images': f"{self.base_url}/Images.aspx", 
            'eservice': f"{self.base_url}/EService_Queue.aspx",
            'myfilings': f"{self.base_url}/EFiling.aspx"
        }
        
        self.browser = None
        self.page = None
        self.logged_in = False
        self.case_query_param = None  # Will store the ?q= parameter for the case
        
        # Setup logging
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
        
    async def __aenter__(self):
        """Async context manager entry"""
        await self.start_browser()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        await self.close()
        
    async def start_browser(self):
        """Initialize Playwright browser"""
        if not PLAYWRIGHT_AVAILABLE:
            raise Exception("Playwright not available. Install with: pip install playwright && playwright install chromium")
            
        self.playwright = await async_playwright().start()
        
        # Launch browser with options for better compatibility and dark theme
        self.browser = await self.playwright.chromium.launch(
            headless=self.headless,
            args=[
                '--disable-dev-shm-usage',
                '--disable-gpu',
                '--no-sandbox',
                '--disable-web-security',
                '--disable-features=VizDisplayCompositor',
                '--disable-blink-features=AutomationControlled',
                '--force-dark-mode',
                '--enable-features=WebUIDarkMode',
                '--force-color-profile=srgb'
            ]
        )
        
        # Create new page with extended timeout
        self.page = await self.browser.new_page()
        self.page.set_default_timeout(30000)  # 30 seconds
        
        # Set realistic user agent
        await self.page.set_extra_http_headers({
            'User-Agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        })
        
        # Inject dark theme CSS to ensure consistent dark appearance
        await self.page.add_init_script("""
            const style = document.createElement('style');
            style.textContent = `
                * {
                    background-color: #1e1e1e !important;
                    color: #ffffff !important;
                    border-color: #444 !important;
                }
                body, html {
                    background-color: #1e1e1e !important;
                    color: #ffffff !important;
                }
                table, td, th {
                    background-color: #2d2d2d !important;
                    color: #ffffff !important;
                    border-color: #444 !important;
                }
                input, select, textarea {
                    background-color: #3d3d3d !important;
                    color: #ffffff !important;
                    border: 1px solid #555 !important;
                }
                a {
                    color: #4da6ff !important;
                }
                .gridview, .gridview_header {
                    background-color: #2d2d2d !important;
                    color: #ffffff !important;
                }
            `;
            document.head.appendChild(style);
        """)
        
        self.logger.info("Browser started successfully with dark theme")
        
    async def close(self):
        """Close browser and cleanup"""
        try:
            if self.page:
                await self.page.close()
            if self.browser:
                await self.browser.close()
            if hasattr(self, 'playwright'):
                await self.playwright.stop()
            self.logger.info("Browser closed successfully")
        except Exception as e:
            self.logger.error(f"Error closing browser: {e}")
            
    async def login(self) -> bool:
        """
        Login to the e-filing system with provided credentials
        """
        try:
            self.logger.info("Attempting to login...")
            
            # Navigate to login page
            await self.page.goto(self.login_url, wait_until='networkidle')
            await asyncio.sleep(2)  # Let page fully load
            
            # Check if we're already logged in
            if self.home_url in self.page.url:
                self.logged_in = True
                self.logger.info("Already logged in")
                return True
                
            # Find and fill username field
            username_selector = 'input[name*="Username"], input[id*="Username"], input[type="text"]'
            await self.page.wait_for_selector(username_selector, timeout=10000)
            await self.page.fill(username_selector, self.username)
            
            # Find and fill password field  
            password_selector = 'input[name*="Password"], input[id*="Password"], input[type="password"]'
            await self.page.fill(password_selector, self.password)
            
            # Find and click login button
            login_selectors = [
                'input[type="submit"]',
                'button[type="submit"]', 
                'input[value*="Login"], input[value*="Sign In"]',
                'button:has-text("Login"), button:has-text("Sign In")'
            ]
            
            login_clicked = False
            for selector in login_selectors:
                try:
                    await self.page.click(selector, timeout=5000)
                    login_clicked = True
                    break
                except:
                    continue
                    
            if not login_clicked:
                raise Exception("Could not find login button")
                
            # Wait for navigation after login
            try:
                await self.page.wait_for_load_state('networkidle', timeout=15000)
            except:
                await asyncio.sleep(3)  # Fallback wait
                
            # Check if login was successful
            current_url = self.page.url
            if self.home_url in current_url or 'Home.aspx' in current_url:
                self.logged_in = True
                self.logger.info("Login successful")
                return True
            else:
                # Check for error messages
                error_selectors = [
                    'span[id*="error"], div[id*="error"]',
                    '.error, .alert-danger',
                    'span:has-text("Invalid"), span:has-text("incorrect")'
                ]
                
                for selector in error_selectors:
                    try:
                        error_element = await self.page.query_selector(selector)
                        if error_element:
                            error_text = await error_element.text_content()
                            raise Exception(f"Login failed: {error_text}")
                    except:
                        continue
                        
                raise Exception(f"Login failed - redirected to: {current_url}")
                
        except Exception as e:
            self.logger.error(f"Login failed: {e}")
            return False
            
    async def find_case(self, case_number: str) -> Optional[str]:
        """
        Find the case link and extract query parameter
        
        Args:
            case_number: Case number to search for (e.g., 'DR-25-403973')
            
        Returns:
            Query parameter string for the case URL (e.g., 'I-fJldkwrgJ3jp2LoiI3Mg2')
        """
        try:
            if not self.logged_in:
                await self.login()
                
            # Navigate to home page
            await self.page.goto(self.home_url, wait_until='networkidle')
            await asyncio.sleep(2)
            
            # Look for case link containing the case number
            case_link_selector = f'a:has-text("{case_number}")'
            
            try:
                await self.page.wait_for_selector(case_link_selector, timeout=10000)
                case_link = await self.page.query_selector(case_link_selector)
                
                if case_link:
                    href = await case_link.get_attribute('href')
                    self.logger.info(f"Found case link: {href}")
                    
                    # Extract query parameter from URL
                    if '?q=' in href:
                        query_param = href.split('?q=')[1].split('&')[0]
                        self.case_query_param = query_param
                        self.logger.info(f"Extracted query parameter: {query_param}")
                        return query_param
                    else:
                        raise Exception("No query parameter found in case URL")
                else:
                    raise Exception(f"Case link not found for {case_number}")
                    
            except Exception as e:
                # Fallback: search in all links on the page
                all_links = await self.page.query_selector_all('a')
                for link in all_links:
                    try:
                        text = await link.text_content()
                        href = await link.get_attribute('href')
                        if case_number in text and href and '?q=' in href:
                            query_param = href.split('?q=')[1].split('&')[0] 
                            self.case_query_param = query_param
                            self.logger.info(f"Found case via fallback search: {query_param}")
                            return query_param
                    except:
                        continue
                        
                raise Exception(f"Case {case_number} not found on home page")
                
        except Exception as e:
            self.logger.error(f"Error finding case: {e}")
            return None

    async def scrape_public_docket(self, case_number: str) -> Dict:
        """
        Scrape public docket without authentication
        Public docket shows case entries but no document access
        
        Args:
            case_number: Case number (e.g., 'DR-25-403973')
            
        Returns:
            Dictionary containing public docket information
        """
        try:
            # Temporarily store login state
            was_logged_in = self.logged_in
            self.logged_in = False
            
            self.logger.info(f"🌐 Scraping PUBLIC docket for {case_number} (no authentication)")
            
            # Navigate to public case search or docket URL
            # Try direct case access first
            public_urls = [
                f"{self.base_url}/CV_CaseInformation_Docket.aspx?CaseNumber={case_number}",
                f"{self.base_url}/CaseInformation.aspx?CaseNumber={case_number}",
                f"{self.base_url}/PublicDocket.aspx?Case={case_number}"
            ]
            
            public_docket_data = None
            
            for url in public_urls:
                try:
                    self.logger.info(f"🔍 Trying public URL: {url}")
                    await self.page.goto(url, wait_until='networkidle')
                    await asyncio.sleep(2)
                    
                    # Try to extract docket data
                    temp_data = await self.try_extract_docket_data(url)
                    
                    if temp_data and temp_data.get('entries'):
                        public_docket_data = temp_data
                        public_docket_data['access_level'] = 'public'
                        public_docket_data['document_availability'] = 'public view - documents not accessible'
                        self.logger.info(f"✅ Public docket found: {len(temp_data['entries'])} entries")
                        break
                    else:
                        self.logger.warning(f"❌ No public docket entries at: {url}")
                        
                except Exception as e:
                    self.logger.warning(f"⚠️ Public URL failed {url}: {e}")
                    continue
            
            # Restore login state
            self.logged_in = was_logged_in
            
            if not public_docket_data:
                return {
                    'error': 'Public docket not accessible',
                    'case_number': case_number,
                    'access_level': 'public',
                    'entries': []
                }
                
            return public_docket_data
            
        except Exception as e:
            self.logger.error(f"Error scraping public docket: {e}")
            return {'error': str(e), 'case_number': case_number, 'access_level': 'public'}
            
    async def scrape_docket_information(self, case_query: str) -> Dict:
        """
        Scrape docket information for the case
        Handles both public docket (no login) and authenticated docket (login required)
        
        Args:
            case_query: Query parameter for the case
            
        Returns:
            Dictionary containing docket information with access level indicator
        """
        try:
            # Try multiple docket URLs to find the correct endpoint
            docket_urls_to_try = [
                f"{self.urls['docket']}?q={case_query}",
                f"{self.urls['docket_alt']}?q={case_query}",
                f"{self.urls['docket_alt2']}?q={case_query}",
                f"{self.urls['docket']}",  # Without case query
                f"{self.urls['docket_alt']}",
                f"{self.urls['docket_alt2']}"
            ]
            
            self.logger.info(f"🔍 Attempting to find docket data across {len(docket_urls_to_try)} possible URLs")
            
            successful_url = None
            docket_data = None
            
            for docket_url in docket_urls_to_try:
                self.logger.info(f"🌐 Trying docket URL: {docket_url}")
                
                try:
                    await self.page.goto(docket_url, wait_until='networkidle')
                    await asyncio.sleep(3)  # Longer wait for potential dynamic content
                    
                    # Check page title and URL to confirm we're on the right page
                    page_title = await self.page.title()
                    current_url = self.page.url
                    self.logger.info(f"📄 Page title: '{page_title}'")
                    self.logger.info(f"🔗 Current URL: {current_url}")
                    
                    # Try to extract data from this URL
                    temp_data = await self.try_extract_docket_data(docket_url)
                    
                    if temp_data and temp_data.get('entries'):
                        self.logger.info(f"✅ Found {len(temp_data['entries'])} docket entries at: {docket_url}")
                        successful_url = docket_url
                        docket_data = temp_data
                        break
                    else:
                        self.logger.warning(f"❌ No docket entries found at: {docket_url}")
                        
                except Exception as e:
                    self.logger.warning(f"⚠️ Failed to load {docket_url}: {e}")
                    continue
            
            if docket_data and docket_data.get('entries'):
                self.logger.info(f"🎉 Successfully found docket data with {len(docket_data['entries'])} entries")
                return docket_data
            else:
                self.logger.error("❌ Could not find docket entries at any URL")
                # Fallback to original logic
                docket_url = docket_urls_to_try[0]
                docket_data = {
                    'url': docket_url,
                    'scraped_at': datetime.now().isoformat(),
                    'entries': []
                }
                
            # Determine access level based on available documents
            if docket_data and docket_data.get('entries'):
                authenticated_entries = sum(1 for entry in docket_data['entries'] if entry.get('Has_Image', False))
                total_entries = len(docket_data['entries'])
                
                if self.logged_in and authenticated_entries > 0:
                    docket_data['access_level'] = 'authenticated'
                    docket_data['document_availability'] = f"{authenticated_entries}/{total_entries} entries have documents"
                    self.logger.info(f"🔐 Authenticated docket access: {authenticated_entries} entries with documents available")
                elif not self.logged_in:
                    docket_data['access_level'] = 'public'
                    docket_data['document_availability'] = 'public docket - limited document access'
                    self.logger.warning(f"👁️ Public docket access: {total_entries} entries visible, documents may not be accessible")
                else:
                    docket_data['access_level'] = 'authenticated_no_docs'
                    docket_data['document_availability'] = 'authenticated but no documents found'
                    self.logger.warning(f"⚠️ Authenticated but no document access: {total_entries} entries")
            
            self.logger.info(f"Scraped {len(docket_data.get('entries', []))} docket entries")
            return docket_data
            
        except Exception as e:
            self.logger.error(f"Error scraping docket information: {e}")
            return {'error': str(e), 'url': f"{self.urls['docket']}?q={case_query}"}
    
    async def is_docket_table(self, table, rows) -> bool:
        """
        Validate whether a table contains actual docket data vs navigation/header content
        """
        try:
            if not rows or len(rows) < 2:  # Need at least header + 1 data row
                return False
            
            # Check if table has reasonable number of columns for docket data (3-10 typical)
            if rows:
                first_row_cells = await rows[0].query_selector_all('td, th')
                if len(first_row_cells) < 3 or len(first_row_cells) > 12:
                    return False
            
            # Check header row content for docket-related terms
            header_text = ""
            if rows:
                first_row = rows[0]
                header_text = await first_row.text_content()
                header_text = header_text.lower()
            
            # Navigation/header table indicators (reject these)
            navigation_indicators = [
                'logout', 'login', 'welcome', 'clerk of courts',
                'home', 'search', 'help', 'contact', 'menu'
            ]
            
            if any(indicator in header_text for indicator in navigation_indicators):
                return False
            
            # Docket table indicators (accept these)
            docket_indicators = [
                'date', 'filed', 'filing', 'document', 'entry', 'docket', 
                'type', 'description', 'party', 'attorney', 'sequence',
                'seq', 'doc', 'desc'
            ]
            
            # Require at least 2 docket indicators in header
            docket_indicator_count = sum(1 for indicator in docket_indicators if indicator in header_text)
            if docket_indicator_count < 2:
                return False
            
            # Check second row for actual data content
            if len(rows) > 1:
                second_row = rows[1]
                second_row_text = await second_row.text_content()
                second_row_text = second_row_text.lower()
                
                # Look for date patterns (common in docket entries)
                import re
                date_patterns = [
                    r'\d{1,2}[-/]\d{1,2}[-/]\d{2,4}',  # MM/DD/YYYY or MM-DD-YYYY
                    r'\d{4}[-/]\d{1,2}[-/]\d{1,2}',   # YYYY/MM/DD or YYYY-MM-DD
                    r'[a-z]{3}\s+\d{1,2},?\s+\d{4}'   # Jan 1, 2025 or Jan 1 2025
                ]
                
                has_date = any(re.search(pattern, second_row_text) for pattern in date_patterns)
                
                # Check for common docket entry content
                docket_content_indicators = [
                    'motion', 'order', 'complaint', 'answer', 'notice',
                    'judgment', 'decree', 'affidavit', 'objection'
                ]
                
                has_docket_content = any(indicator in second_row_text for indicator in docket_content_indicators)
                
                # Table is valid if it has either date patterns or docket content
                if not (has_date or has_docket_content):
                    return False
            
            self.logger.info(f"✅ Found valid docket table with {len(rows)} rows")
            return True
            
        except Exception as e:
            self.logger.warning(f"Error validating docket table: {e}")
            return False
    
    async def try_extract_docket_data(self, docket_url: str) -> Dict:
        """Extract docket data from current page"""
        try:
            docket_data = {
                'url': docket_url,
                'scraped_at': datetime.now().isoformat(),
                'entries': []
            }
            
            # Debug: Log all tables found on the page
            all_tables = await self.page.query_selector_all('table')
            self.logger.info(f"🔍 Found {len(all_tables)} total tables on page")
            
            # Look for docket entries in table format
            # More specific selectors for actual docket tables, ordered by specificity
            table_selectors = [
                # Most specific - known docket table IDs/classes
                'table[id*="gvDocket"]',
                'table[id*="DocketGrid"]', 
                'table[id*="docketTable"]',
                'table[class*="docket-table"]',
                'table[id*="docket"], table[class*="docket"]',
                'table[id*="Docket"], table[class*="Docket"]', 
                # GridView controls commonly used for docket data
                'table.gridview[id*="gv"]',
                'table[id*="GridView"]',
                'table[id*="gv"][class*="gridview"]',
                # Data tables with relevant content
                'table[id*="case"], table[id*="Case"]',
                'table[class*="case"], table[class*="Case"]',
                # Generic table fallbacks - will be filtered by content
                'table[class*="gridview"]',
                'table[id*="gv"]',
                'table'
            ]
            
            for i, selector in enumerate(table_selectors):
                try:
                    self.logger.debug(f"🔍 Trying selector {i+1}/{len(table_selectors)}: {selector}")
                    table = await self.page.query_selector(selector)
                    
                    if table:
                        rows = await table.query_selector_all('tr')
                        
                        # Get table attributes for debugging
                        table_id = await table.get_attribute('id') or 'no-id'
                        table_class = await table.get_attribute('class') or 'no-class'
                        self.logger.debug(f"📊 Found table: id='{table_id}', class='{table_class}', rows={len(rows)}")
                        
                        # Skip tables that are clearly not docket data
                        if not await self.is_docket_table(table, rows):
                            self.logger.debug(f"❌ Skipping table with selector {selector} - not a docket table")
                            continue
                        
                        # Extract table headers from first row
                        headers = []
                        if rows:
                            header_cells = await rows[0].query_selector_all('td, th')
                            for cell in header_cells:
                                header_text = await cell.text_content()
                                if header_text:
                                    # Clean and standardize header names
                                    clean_header = header_text.strip().replace('\n', ' ').replace('\t', ' ')
                                    # Remove extra spaces
                                    clean_header = ' '.join(clean_header.split())
                                    # Convert common abbreviations to full names
                                    header_mappings = {
                                        'Seq': 'Sequence Number',
                                        'Doc': 'Document Type',
                                        'Desc': 'Description', 
                                        'Filed': 'Filing Date',
                                        'File': 'Filing Date',
                                        'Date': 'Filing Date',
                                        'Party': 'Filing Party',
                                        'Atty': 'Attorney',
                                        'Attorney Name': 'Attorney Name',
                                        'Docket Text': 'Docket Description',
                                        'Entry': 'Entry Number',
                                        'Type': 'Document Type'
                                    }
                                    
                                    # Apply mappings or use original if no mapping
                                    final_header = header_mappings.get(clean_header, clean_header)
                                    headers.append(final_header)
                                else:
                                    headers.append(f'Column {len(headers) + 1}')
                        
                        docket_data['headers'] = headers
                        self.logger.info(f"📋 Table headers: {headers}")
                        
                        # Process data rows using actual headers
                        for row_index, row in enumerate(rows[1:], 1):  # Skip header row, start numbering at 1
                            cells = await row.query_selector_all('td, th')
                            if len(cells) >= 3:
                                entry = {}
                                entry['Docket Number'] = row_index  # Chronological order: first filed = #1
                                
                                # Use actual header names as keys and extract document links
                                documents = []
                                for i, cell in enumerate(cells):
                                    text = await cell.text_content()
                                    header_name = headers[i] if i < len(headers) else f'Column {i + 1}'
                                    entry[header_name] = text.strip() if text else ''
                                    
                                    # Check for document links in ANY cell (not just image column)
                                    # Based on actual e-filing page structure analysis
                                    document_links = await cell.query_selector_all('a[href*="DisplayImageList"]')
                                    for doc_link in document_links:
                                        href = await doc_link.get_attribute('href')
                                        title = await doc_link.get_attribute('title') or ''
                                        if href:
                                            doc_info = {
                                                'url': href,
                                                'title': title,
                                                'type': 'DisplayImageList',
                                                'found_in_column': header_name
                                            }
                                            documents.append(doc_info)
                                            self.logger.info(f"📎 Found document link in {header_name}: {title}")
                                    
                                    # Also check for old image link pattern for compatibility
                                    if 'image' in header_name.lower():
                                        image_link = await cell.query_selector('a[href*="DisplayImageList"]')
                                        if image_link:
                                            href = await image_link.get_attribute('href')
                                            if href:
                                                entry['Image_Link'] = href
                                                entry['Has_Image'] = True
                                                # Extract image query parameter
                                                if '?q=' in href:
                                                    image_query = href.split('?q=')[1]
                                                    entry['Image_Query'] = image_query
                                        else:
                                            entry['Has_Image'] = False
                                    
                                # Try to identify document types and special entries
                                # Create a copy of items to avoid "dictionary changed size during iteration"
                                for header_name, cell_text in list(entry.items()):
                                    # Skip docket number field and safely handle non-string values
                                    if header_name == 'Docket Number' or not cell_text:
                                        continue
                                    
                                    # Convert to string and lowercase for pattern matching
                                    try:
                                        cell_text_str = str(cell_text)
                                        cell_text_lower = cell_text_str.lower()
                                    except:
                                        continue  # Skip if conversion fails
                                    
                                    if 'complaint' in cell_text_lower and 'divorce' in cell_text_lower:
                                        entry['Document Classification'] = 'Initial Filing - Complaint for Divorce'
                                        entry['Priority'] = 1  # Highest priority - filed first
                                    elif any(motion_word in cell_text_lower for motion_word in ['motion', 'petition', 'request']):
                                        entry['Document Classification'] = f'Motion/Petition - {cell_text_str}'
                                    elif any(order_word in cell_text_lower for order_word in ['order', 'judgment', 'decree']):
                                        entry['Document Classification'] = f'Court Order/Judgment - {cell_text_str}'
                                    elif 'stricken' in cell_text_lower:
                                        entry['Document Classification'] = 'STRICKEN RECORD - EVIDENCE REMOVED'
                                        entry['Stricken_Status'] = True
                                        entry['Priority'] = 99  # High priority - evidence issue
                                        # Extract original docket reference if available
                                        if 'docket' in cell_text_lower:
                                            import re
                                            docket_match = re.search(r'docket\s+(\d+)', cell_text_str, re.IGNORECASE)
                                            if docket_match:
                                                entry['Original_Docket_ID'] = docket_match.group(1)
                                        # Mark as critical for review
                                        entry['Requires_Investigation'] = True
                                        entry['Issue_Type'] = 'Evidence Manipulation'
                                    
                                # Add documents to entry if found
                                if documents:
                                    entry['documents'] = documents
                                    entry['document_count'] = len(documents)
                                    self.logger.info(f"📎 Added {len(documents)} document links to docket entry {row_index}")
                                else:
                                    entry['documents'] = []
                                    entry['document_count'] = 0
                                
                                # Check if entry has meaningful content (any non-empty string value)
                                has_content = any(
                                    v and str(v).strip() 
                                    for k, v in entry.items() 
                                    if k != 'Docket Number' and v is not None
                                )
                                if has_content:
                                    docket_data['entries'].append(entry)
                                    
                        self.logger.info(f"✅ Extracted {len(docket_data['entries'])} docket entries from table")
                        
                        # If we found entries, break from trying other selectors
                        if docket_data['entries']:
                            break
                            
                except Exception as e:
                    self.logger.warning(f"Error processing table with selector {selector}: {e}")
                    continue
                    
            return docket_data
            
        except Exception as e:
            self.logger.error(f"Error in try_extract_docket_data: {e}")
            return {
                'url': docket_url,
                'scraped_at': datetime.now().isoformat(),
                'entries': [],
                'error': str(e)
            }
            
    async def scrape_images(self, case_query: str) -> Dict:
        """
        Scrape all image pages and download links
        
        Args:
            case_query: Query parameter for the case
            
        Returns:
            Dictionary containing image information and download links
        """
        try:
            images_url = f"{self.urls['images']}?q={case_query}"
            await self.page.goto(images_url, wait_until='networkidle')
            await asyncio.sleep(2)
            
            images_data = {
                'url': images_url,
                'scraped_at': datetime.now().isoformat(),
                'total_pages': 0,
                'images': []
            }
            
            # Look for pagination info to get total pages
            page_info_selectors = [
                'span:has-text("Page"), div:has-text("Page")',
                'span[id*="page"], div[id*="page"]'
            ]
            
            for selector in page_info_selectors:
                try:
                    page_info = await self.page.query_selector(selector)
                    if page_info:
                        text = await page_info.text_content()
                        # Extract page numbers (e.g., "Page 1 of 5")
                        page_match = re.search(r'(\d+)\s+of\s+(\d+)', text)
                        if page_match:
                            images_data['total_pages'] = int(page_match.group(2))
                            break
                except:
                    continue
                    
            # Look for image links
            image_link_selectors = [
                'a[href*="Image"], a[title*="Image"]',
                'img[src*="images.png"], img[src*="ImageSheet.png"]',
                'a[href*="DisplayImage"]'
            ]
            
            for selector in image_link_selectors:
                try:
                    image_links = await self.page.query_selector_all(selector)
                    for link in image_links:
                        href = await link.get_attribute('href')
                        title = await link.get_attribute('title') or ''
                        
                        if href:
                            full_url = urljoin(self.base_url, href)
                            images_data['images'].append({
                                'url': full_url,
                                'title': title,
                                'relative_url': href
                            })
                except:
                    continue
                    
            self.logger.info(f"Found {len(images_data['images'])} image links, {images_data['total_pages']} pages")
            return images_data
            
        except Exception as e:
            self.logger.error(f"Error scraping images: {e}")
            return {'error': str(e), 'url': f"{self.urls['images']}?q={case_query}"}
            
    async def scrape_eservice_queue(self) -> Dict:
        """
        Scrape E-Service queue with "Unread Notices Only" unchecked
        
        Returns:
            Dictionary containing e-service notices and image links
        """
        try:
            await self.page.goto(self.urls['eservice'], wait_until='networkidle')
            await asyncio.sleep(2)
            
            eservice_data = {
                'url': self.urls['eservice'],
                'scraped_at': datetime.now().isoformat(),
                'notices': []
            }
            
            # Uncheck "Unread Notices Only" if present
            unread_checkbox_selectors = [
                'input[type="checkbox"][id*="Unread"]',
                'input[type="checkbox"]:has-text("Unread")',
                'input[type="checkbox"][value*="unread"]'
            ]
            
            for selector in unread_checkbox_selectors:
                try:
                    checkbox = await self.page.query_selector(selector)
                    if checkbox:
                        is_checked = await checkbox.is_checked()
                        if is_checked:
                            await checkbox.uncheck()
                            self.logger.info("Unchecked 'Unread Notices Only'")
                        break
                except:
                    continue
                    
            # Click search button
            search_selectors = [
                'input[type="submit"][value*="Search"]',
                'button:has-text("Search")',
                'input[id*="Search"]'
            ]
            
            for selector in search_selectors:
                try:
                    await self.page.click(selector)
                    await self.page.wait_for_load_state('networkidle', timeout=10000)
                    break
                except:
                    continue
                    
            await asyncio.sleep(2)
            
            # Look for image links in the results
            image_link_selectors = [
                'a[href*="DisplayImageList"]',
                'img[src*="ImageSheet.png"]',
                'a[title*="View Image"]'
            ]
            
            for selector in image_link_selectors:
                try:
                    links = await self.page.query_selector_all(selector)
                    for link in links:
                        href = await link.get_attribute('href')
                        title = await link.get_attribute('title') or ''
                        
                        if href and 'DisplayImageList' in href:
                            full_url = urljoin(self.docket_base_url, href)
                            eservice_data['notices'].append({
                                'url': full_url,
                                'title': title,
                                'relative_url': href,
                                'type': 'e_service_notice'
                            })
                except:
                    continue
                    
            self.logger.info(f"Found {len(eservice_data['notices'])} e-service notices")
            return eservice_data
            
        except Exception as e:
            self.logger.error(f"Error scraping e-service queue: {e}")
            return {'error': str(e), 'url': self.urls['eservice']}
            
    async def scrape_my_filings(self) -> Dict:
        """
        Scrape My Filings section for filing images
        
        Returns:
            Dictionary containing filing information and image links
        """
        try:
            await self.page.goto(self.urls['myfilings'], wait_until='networkidle')
            await asyncio.sleep(2)
            
            filings_data = {
                'url': self.urls['myfilings'],
                'scraped_at': datetime.now().isoformat(),
                'filings': []
            }
            
            # Look for filing image links
            filing_link_selectors = [
                'a[href*="Images.aspx"]',
                'img[src*="images.png"]',
                'a[title*="View Image"]'
            ]
            
            for selector in filing_link_selectors:
                try:
                    links = await self.page.query_selector_all(selector)
                    for link in links:
                        href = await link.get_attribute('href') 
                        title = await link.get_attribute('title') or ''
                        
                        if href:
                            full_url = urljoin(self.base_url, href)
                            filings_data['filings'].append({
                                'url': full_url,
                                'title': title,
                                'relative_url': href,
                                'type': 'my_filing'
                            })
                except:
                    continue
                    
            self.logger.info(f"Found {len(filings_data['filings'])} filing links")
            return filings_data
            
        except Exception as e:
            self.logger.error(f"Error scraping my filings: {e}")
            return {'error': str(e), 'url': self.urls['myfilings']}
            
    async def extract_efile_document_links(self, docket_entries: List[Dict]) -> Dict:
        """
        Extract actual document download links by following Efile ID links
        User requirement: "follow link in the column Efile ID (https://efiling.cp.cuyahogacounty.gov/EFiling_Overview.aspx?q=CmDsxw0Krl--27-s5O5TAQ2)"
        
        Args:
            docket_entries: List of docket entries from scrape_docket_information
            
        Returns:
            Dictionary with Efile document links and metadata
        """
        try:
            efile_results = {
                'total_entries': len(docket_entries),
                'entries_with_efile_ids': 0,
                'documents_found': 0,
                'document_links': [],
                'errors': []
            }
            
            for entry in docket_entries:
                # Look for Efile ID link in the entry
                efile_id = None
                efile_link = None
                
                # Check various possible fields for Efile ID
                for field_name, field_value in entry.items():
                    if 'efile' in field_name.lower() or 'e-file' in field_name.lower():
                        if field_value and 'EFiling_Overview.aspx?q=' in str(field_value):
                            efile_link = str(field_value)
                            # Extract the q parameter
                            if '?q=' in efile_link:
                                efile_id = efile_link.split('?q=')[1].split('&')[0]
                            break
                
                # If no direct Efile link found, check for Image_Link and Image_Query fields
                # These contain DisplayImageList.aspx links that we can convert to EFiling_Overview
                if not efile_link:
                    # Check Image_Query field first (most direct)
                    if entry.get('Image_Query') and entry.get('Has_Image'):
                        efile_id = str(entry['Image_Query']).strip()
                        # Convert to EFiling_Overview URL as requested by user
                        efile_link = f"{self.base_url}/EFiling_Overview.aspx?q={efile_id}"
                        self.logger.info(f"🔄 Converting Image_Query to EFiling_Overview URL: {efile_id}")
                    
                    # Fallback: check Image_Link field
                    elif entry.get('Image_Link') and entry.get('Has_Image'):
                        image_link = str(entry['Image_Link'])
                        if 'DisplayImageList.aspx?q=' in image_link:
                            # Extract query parameter from DisplayImageList URL
                            if '?q=' in image_link:
                                efile_id = image_link.split('?q=')[1].split('&')[0]
                                # Convert to EFiling_Overview URL as requested by user
                                efile_link = f"{self.base_url}/EFiling_Overview.aspx?q={efile_id}"
                                self.logger.info(f"🔄 Converting Image_Link to EFiling_Overview URL: {efile_id}")
                
                # If still no link found, check for embedded document IDs in description
                if not efile_link:
                    description = entry.get('Description', '')
                    if description:
                        # Look for embedded document ID patterns
                        import re
                        doc_id_patterns = [
                            r'\((\d{8,})\)',  # (57726906) pattern - most common
                            r'#(\d+)',        # #123 pattern
                            r'ID[:\s]+(\w+)', # ID: ABC123 pattern
                            r'REF[:\s]+(\w+)' # REF: ABC123 pattern
                        ]
                        
                        for pattern in doc_id_patterns:
                            matches = re.findall(pattern, description)
                            if matches:
                                # Use the first match as potential document ID
                                potential_doc_id = matches[0]
                                # Try to construct an EFiling_Overview URL
                                efile_link = f"{self.base_url}/EFiling_Overview.aspx?q={potential_doc_id}"
                                efile_id = potential_doc_id
                                self.logger.info(f"🔍 Found embedded document ID in description: {potential_doc_id}")
                                break
                
                # If still no link found, check if there's an Efile ID that we need to construct the link for
                if not efile_link:
                    for field_name, field_value in entry.items():
                        if 'efile' in field_name.lower() and field_value:
                            # Construct the EFiling_Overview URL
                            efile_id = str(field_value).strip()
                            efile_link = f"{self.base_url}/EFiling_Overview.aspx?q={efile_id}"
                            break
                
                if efile_link and efile_id:
                    efile_results['entries_with_efile_ids'] += 1
                    
                    try:
                        self.logger.info(f"🔍 Following Efile ID link: {efile_link}")
                        
                        # Navigate to the EFiling_Overview page with improved loading strategy
                        try:
                            # Try networkidle first (most reliable but may timeout)
                            await self.page.goto(efile_link, wait_until='networkidle', timeout=20000)
                        except Exception as networkidle_error:
                            self.logger.debug(f"networkidle timeout, trying domcontentloaded: {networkidle_error}")
                            try:
                                # Fallback to domcontentloaded (faster but may miss some content)
                                await self.page.goto(efile_link, wait_until='domcontentloaded', timeout=15000)
                            except Exception as domload_error:
                                self.logger.debug(f"domcontentloaded timeout, trying load: {domload_error}")
                                # Final fallback to basic load event
                                await self.page.goto(efile_link, wait_until='load', timeout=10000)
                        
                        # Wait for page to stabilize and JavaScript to execute
                        await asyncio.sleep(5)  # Increased wait time for JavaScript execution
                        
                        # Look for DOCUMENT INFORMATION section
                        document_info = await self.extract_document_information_from_page(entry, efile_id)
                        
                        if document_info:
                            efile_results['documents_found'] += len(document_info.get('documents', []))
                            efile_results['document_links'].extend(document_info.get('documents', []))
                            self.logger.info(f"✅ Found {len(document_info.get('documents', []))} documents for Efile ID {efile_id}")
                        else:
                            self.logger.warning(f"⚠️ No documents found in DOCUMENT INFORMATION for Efile ID {efile_id}")
                            
                    except Exception as e:
                        error_msg = f"Error processing Efile ID {efile_id}: {e}"
                        self.logger.error(error_msg)
                        efile_results['errors'].append(error_msg)
            
            self.logger.info(f"Efile document extraction complete: {efile_results['documents_found']} documents found from {efile_results['entries_with_efile_ids']} entries")
            return efile_results
            
        except Exception as e:
            self.logger.error(f"Error extracting efile document links: {e}")
            return {'error': str(e)}
    
    async def extract_all_document_links(self, case_data: Dict) -> Dict:
        """
        Extract document links from ALL sources: docket, images, eservice_queue, and my_filings
        
        Args:
            case_data: Complete case data from scrape_case_data()
            
        Returns:
            Dictionary with comprehensive document extraction results
        """
        try:
            all_results = {
                'total_sources': 4,
                'sources_processed': 0,
                'total_documents_found': 0,
                'document_links': [],
                'source_breakdown': {
                    'docket_entries': {'count': 0, 'documents': []},
                    'images_tab': {'count': 0, 'documents': []},
                    'eservice_queue': {'count': 0, 'documents': []}, 
                    'my_filings': {'count': 0, 'documents': []}
                },
                'errors': []
            }
            
            # 1. Process docket entries (OPTIMIZED - documents already extracted during docket scraping)
            if 'docket_information' in case_data and 'entries' in case_data['docket_information']:
                # Count documents from optimized docket extraction
                docket_docs = []
                doc_count = 0
                for entry in case_data['docket_information']['entries']:
                    if entry.get('documents'):
                        docket_docs.extend(entry['documents'])
                        doc_count += len(entry['documents'])
                
                all_results['source_breakdown']['docket_entries']['count'] = doc_count
                all_results['source_breakdown']['docket_entries']['documents'] = docket_docs
                all_results['total_documents_found'] += doc_count
                all_results['document_links'].extend(docket_docs)
                all_results['sources_processed'] += 1
                self.logger.info(f"📎 Using {doc_count} documents from optimized docket extraction")
            
            # 2. Process E-Service Queue notices
            if 'eservice_queue' in case_data and 'notices' in case_data['eservice_queue']:
                notices = case_data['eservice_queue']['notices']
                eservice_docs = []
                
                for notice in notices:
                    if notice.get('url') and 'DisplayImageList.aspx?q=' in notice['url']:
                        # Extract the query parameter
                        url = notice['url']
                        if '?q=' in url:
                            doc_id = url.split('?q=')[1].split('&')[0]
                            # Convert to EFiling_Overview URL
                            efile_link = f"{self.base_url}/EFiling_Overview.aspx?q={doc_id}"
                            
                            doc_info = {
                                'source': 'eservice_queue',
                                'document_id': doc_id,
                                'original_url': url,
                                'efile_overview_url': efile_link,
                                'type': notice.get('type', 'e_service_notice'),
                                'title': notice.get('title', 'E-Service Notice')
                            }
                            eservice_docs.append(doc_info)
                
                all_results['source_breakdown']['eservice_queue']['count'] = len(eservice_docs)
                all_results['source_breakdown']['eservice_queue']['documents'] = eservice_docs
                all_results['total_documents_found'] += len(eservice_docs)
                all_results['document_links'].extend(eservice_docs)
                all_results['sources_processed'] += 1
                self.logger.info(f"📧 E-Service notices: {len(eservice_docs)} documents")
            
            # 3. Process Images tab
            if 'images' in case_data and 'images' in case_data['images']:
                images = case_data['images']['images']
                image_docs = []
                
                for image in images:
                    if image.get('url') and 'DisplayImageList.aspx?q=' in image['url']:
                        # Extract the query parameter
                        url = image['url']
                        if '?q=' in url:
                            doc_id = url.split('?q=')[1].split('&')[0]
                            # Convert to EFiling_Overview URL
                            efile_link = f"{self.base_url}/EFiling_Overview.aspx?q={doc_id}"
                            
                            doc_info = {
                                'source': 'images_tab',
                                'document_id': doc_id,
                                'original_url': url,
                                'efile_overview_url': efile_link,
                                'type': 'image_document',
                                'title': image.get('title', 'Image Document')
                            }
                            image_docs.append(doc_info)
                
                all_results['source_breakdown']['images_tab']['count'] = len(image_docs)
                all_results['source_breakdown']['images_tab']['documents'] = image_docs
                all_results['total_documents_found'] += len(image_docs)
                all_results['document_links'].extend(image_docs)
                all_results['sources_processed'] += 1
                self.logger.info(f"🖼️ Images tab: {len(image_docs)} documents")
            
            # 4. Process My Filings
            if 'my_filings' in case_data and 'filings' in case_data['my_filings']:
                filings = case_data['my_filings']['filings']
                filing_docs = []
                
                for filing in filings:
                    if filing.get('url') and 'DisplayImageList.aspx?q=' in filing['url']:
                        # Extract the query parameter
                        url = filing['url']
                        if '?q=' in url:
                            doc_id = url.split('?q=')[1].split('&')[0]
                            # Convert to EFiling_Overview URL
                            efile_link = f"{self.base_url}/EFiling_Overview.aspx?q={doc_id}"
                            
                            doc_info = {
                                'source': 'my_filings',
                                'document_id': doc_id,
                                'original_url': url,
                                'efile_overview_url': efile_link,
                                'type': filing.get('type', 'my_filing'),
                                'title': filing.get('title', 'My Filing')
                            }
                            filing_docs.append(doc_info)
                
                all_results['source_breakdown']['my_filings']['count'] = len(filing_docs)
                all_results['source_breakdown']['my_filings']['documents'] = filing_docs
                all_results['total_documents_found'] += len(filing_docs)
                all_results['document_links'].extend(filing_docs)
                all_results['sources_processed'] += 1
                self.logger.info(f"📁 My filings: {len(filing_docs)} documents")
            
            # Remove duplicates based on document_id
            unique_docs = {}
            for doc in all_results['document_links']:
                doc_id = doc.get('document_id') or doc.get('efile_id')
                if doc_id and doc_id not in unique_docs:
                    unique_docs[doc_id] = doc
            
            all_results['document_links'] = list(unique_docs.values())
            all_results['total_documents_found'] = len(unique_docs)
            
            self.logger.info(f"🎯 COMPREHENSIVE EXTRACTION COMPLETE: {all_results['total_documents_found']} unique documents from {all_results['sources_processed']} sources")
            
            return all_results
            
        except Exception as e:
            self.logger.error(f"Error in comprehensive document extraction: {e}")
            return {
                'total_sources': 4,
                'sources_processed': 0,
                'total_documents_found': 0,
                'document_links': [],
                'source_breakdown': {},
                'errors': [str(e)]
            }
    
    async def extract_document_information_from_page(self, docket_entry: Dict, efile_id: str) -> Optional[Dict]:
        """
        Extract document information from EFiling_Overview.aspx page
        Handles JavaScript-based document links as mentioned by user
        
        Args:
            docket_entry: Original docket entry for context
            efile_id: Efile ID for this document
            
        Returns:
            Dictionary with document metadata and download links
        """
        try:
            document_info = {
                'efile_id': efile_id,
                'docket_entry': docket_entry.get('Docket Number', 'unknown'),
                'filing_date': docket_entry.get('Filing Date', 'unknown'),
                'document_type': docket_entry.get('Document Type', 'unknown'),
                'documents': []
            }
            
            # Look for DOCUMENT INFORMATION section
            doc_info_selectors = [
                'div:has-text("DOCUMENT INFORMATION")',
                'table:has-text("DOCUMENT INFORMATION")',
                'span:has-text("DOCUMENT INFORMATION")',
                'h3:has-text("DOCUMENT INFORMATION")',
                'div[id*="document"], div[class*="document"]'
            ]
            
            document_section_found = False
            
            for selector in doc_info_selectors:
                try:
                    doc_section = await self.page.query_selector(selector)
                    if doc_section:
                        document_section_found = True
                        self.logger.info(f"📋 Found DOCUMENT INFORMATION section with selector: {selector}")
                        
                        # Look for document links within or near this section
                        # Check parent and sibling elements for document links
                        parent = await doc_section.query_selector('xpath=..')
                        if parent:
                            doc_links = await parent.query_selector_all('a[href*="document"], a[href*="pdf"], a[href*="image"], a[onclick*="window.open"]')
                        else:
                            doc_links = await doc_section.query_selector_all('a[href*="document"], a[href*="pdf"], a[href*="image"], a[onclick*="window.open"]')
                        
                        # Also check for JavaScript-based links that open in new windows
                        js_links = await self.page.query_selector_all('a[onclick*="window.open"], input[onclick*="window.open"], button[onclick*="window.open"]')
                        doc_links.extend(js_links)
                        
                        for link in doc_links:
                            await self.process_document_link(link, document_info, efile_id)
                        
                        break
                        
                except Exception as e:
                    self.logger.debug(f"Selector {selector} failed: {e}")
                    continue
            
            if not document_section_found:
                # Fallback: look for any document download links on the entire page
                self.logger.warning("⚠️ DOCUMENT INFORMATION section not found, searching entire page for document links")
                
                # First, try to trigger JavaScript by hovering over potential download buttons
                await self.trigger_javascript_downloads()
                
                fallback_selectors = [
                    # E-filing specific patterns based on actual page analysis
                    'a[href*="DisplayImageList.aspx"]',  # E-filing document view pages
                    'a[title*="View Image"]',  # Links with "View Image" title
                    'img[src*="ImageSheet.png"]',  # The actual download button images
                    'img[alt*="View Image Button"]',  # Image alt text
                    # Original fallback patterns
                    'a[href*="pdf"]',
                    'a[href*="document"]',
                    'a[href*="download"]',
                    'a[onclick*="window.open"]',
                    'input[onclick*="window.open"][value*="View"], input[onclick*="window.open"][value*="Download"]',
                    'button:has-text("Download")', 'button:has-text("View")',
                    'input[value*="Download"], input[value*="View"]',
                    'a:has-text("Download"), a:has-text("View")'
                ]
                
                for selector in fallback_selectors:
                    try:
                        elements = await self.page.query_selector_all(selector)
                        for element in elements:
                            # Handle img elements inside links - get the parent link
                            tag_name = await element.evaluate('el => el.tagName.toLowerCase()')
                            if tag_name == 'img':
                                # Try to get parent link element
                                parent_link = await element.evaluate('el => el.closest("a")')
                                if parent_link:
                                    # Process the parent link element
                                    parent_element = await self.page.evaluate_handle('el => el', parent_link)
                                    await self.process_document_link(parent_element, document_info, efile_id)
                                else:
                                    self.logger.debug(f"📎 Found img element but no parent link: {selector}")
                            else:
                                # Process the element directly (should be a link)
                                await self.process_document_link(element, document_info, efile_id)
                    except Exception as e:
                        self.logger.debug(f"Fallback selector {selector} failed: {e}")
                        continue
            
            return document_info if document_info['documents'] else None
            
        except Exception as e:
            self.logger.error(f"Error extracting document information from page: {e}")
            return None
    
    async def trigger_javascript_downloads(self):
        """
        Trigger JavaScript that generates download links on hover/interaction
        Based on user's observation that links populate when hovering over download buttons
        """
        try:
            self.logger.info("🎯 Attempting to trigger JavaScript download link generation...")
            
            # Look for potential download buttons/elements that might trigger JavaScript
            potential_triggers = [
                # E-filing specific selectors based on actual page structure analysis
                'img[src*="ImageSheet.png"]',  # The actual download button images
                'a[href*="DisplayImageList.aspx"]',  # Document view links
                'a[title*="View Image"]',  # Links with "View Image" title
                'img[alt*="View Image Button"]',  # Image buttons with view text
                # Original generic selectors as fallback
                'button:has-text("Download")',
                'input[value*="Download"]',
                'a:has-text("Download")',
                'button:has-text("View")',
                'input[value*="View"]',
                'a:has-text("View")',
                'span:has-text("Download")',
                'div:has-text("Download")',
                # Look for common button/link patterns
                'input[type="button"]',
                'input[type="submit"]',
                'button',
                'a[href="#"]',
                # Look for elements with onclick handlers
                '[onclick*="window.open"]',
                '[onclick*="document"]',
                '[onclick*="pdf"]'
            ]
            
            triggered_elements = 0
            
            for selector in potential_triggers:
                try:
                    elements = await self.page.query_selector_all(selector)
                    for element in elements:
                        try:
                            # Get element text to see if it's relevant
                            text = await element.text_content() or ''
                            value = await element.get_attribute('value') or ''
                            onclick = await element.get_attribute('onclick') or ''
                            
                            # Only interact with elements that seem document-related
                            if any(keyword in (text + value + onclick).lower() for keyword in 
                                   ['download', 'view', 'document', 'pdf', 'file', 'open']):
                                
                                self.logger.info(f"🔍 Hovering over potential trigger: {text[:50] or value[:50] or 'element'}")
                                
                                # Hover to trigger any JavaScript
                                await element.hover(timeout=3000)
                                await asyncio.sleep(1)  # Give JavaScript time to execute
                                
                                # Also try clicking if it seems safe (has onclick handler)
                                if onclick and ('window.open' in onclick or 'document' in onclick.lower()):
                                    self.logger.info(f"🖱️ Clicking element with onclick: {onclick[:100]}")
                                    try:
                                        await element.click(timeout=3000)
                                        await asyncio.sleep(2)  # Wait for any popups or redirects
                                    except Exception as click_error:
                                        self.logger.debug(f"Click failed: {click_error}")
                                
                                triggered_elements += 1
                                
                        except Exception as element_error:
                            self.logger.debug(f"Error interacting with element: {element_error}")
                            continue
                            
                except Exception as selector_error:
                    self.logger.debug(f"Selector {selector} failed: {selector_error}")
                    continue
            
            self.logger.info(f"🎯 Triggered {triggered_elements} potential JavaScript elements")
            
            # Wait a bit more for any delayed JavaScript execution
            await asyncio.sleep(3)
            
            # Try to execute any page JavaScript that might generate links
            try:
                # Execute common patterns that might trigger link generation
                js_commands = [
                    "if (typeof updateLinks === 'function') updateLinks();",
                    "if (typeof loadDocuments === 'function') loadDocuments();",
                    "if (typeof initializeDownloads === 'function') initializeDownloads();",
                    # Trigger common jQuery/DOM events
                    "$('body').trigger('mouseover');",
                    "$(document).trigger('ready');",
                    "window.dispatchEvent(new Event('load'));",
                    "window.dispatchEvent(new Event('DOMContentLoaded'));"
                ]
                
                for cmd in js_commands:
                    try:
                        await self.page.evaluate(cmd)
                        await asyncio.sleep(0.5)
                    except Exception as js_error:
                        self.logger.debug(f"JS command failed: {cmd} - {js_error}")
                        
            except Exception as js_exec_error:
                self.logger.debug(f"JavaScript execution failed: {js_exec_error}")
            
            self.logger.info("🎯 JavaScript trigger attempt completed")
            
            # Optional: Save page HTML after JavaScript execution for debugging
            if triggered_elements > 0:
                try:
                    html_content = await self.page.content()
                    debug_dir = Path("debug_html")
                    debug_dir.mkdir(exist_ok=True)
                    debug_file = debug_dir / f"after_js_trigger_{int(time.time())}.html"
                    with open(debug_file, 'w', encoding='utf-8') as f:
                        f.write(html_content)
                    self.logger.info(f"📄 Saved post-JavaScript HTML to {debug_file}")
                except Exception as save_error:
                    self.logger.debug(f"Could not save debug HTML: {save_error}")
            
        except Exception as e:
            self.logger.error(f"Error triggering JavaScript downloads: {e}")
    
    async def process_document_link(self, link_element, document_info: Dict, efile_id: str):
        """
        Process a single document link element and extract download information
        
        Args:
            link_element: Playwright element for the link
            document_info: Document info dictionary to add to
            efile_id: Current Efile ID for context
        """
        try:
            # Get link attributes
            href = await link_element.get_attribute('href')
            onclick = await link_element.get_attribute('onclick')
            title = await link_element.get_attribute('title') or ''
            text = await link_element.text_content() or ''
            
            # Handle JavaScript-based links
            if onclick and 'window.open' in onclick:
                # Extract URL from onclick JavaScript
                import re
                url_match = re.search(r"window\.open\s*\(\s*['\"]([^'\"]+)['\"]", onclick)
                if url_match:
                    href = url_match.group(1)
                    self.logger.info(f"📎 Extracted JavaScript URL: {href}")
            
            if href:
                # Make URL absolute with proper base domain
                if href.startswith('/'):
                    # For docket-related URLs, use the docket base URL
                    if 'DisplayImageList.aspx' in href or 'cpdocket' in href:
                        full_url = f"{self.docket_base_url}{href}"
                    else:
                        full_url = f"{self.base_url}{href}"
                elif not href.startswith('http'):
                    # For relative URLs, determine the correct base
                    if 'DisplayImageList.aspx' in href or any(x in href for x in ['PublicDocket', 'CaseInformation']):
                        full_url = f"{self.docket_base_url}/{href}"
                    else:
                        full_url = f"{self.base_url}/{href}"
                else:
                    full_url = href
                
                # Determine document type from URL or link text
                doc_type = 'document'
                if '.pdf' in href.lower() or 'pdf' in text.lower():
                    doc_type = 'pdf'
                elif any(img_ext in href.lower() for img_ext in ['.jpg', '.jpeg', '.png', '.gif', '.tiff']):
                    doc_type = 'image'
                
                document_info['documents'].append({
                    'url': full_url,
                    'title': title,
                    'link_text': text.strip(),
                    'document_type': doc_type,
                    'efile_id': efile_id,
                    'extraction_method': 'javascript' if onclick else 'direct_href',
                    'original_href': href,
                    'onclick_script': onclick
                })
                
                self.logger.info(f"📄 Added document link: {text[:30]}... -> {full_url}")
                
        except Exception as e:
            self.logger.error(f"Error processing document link: {e}")

    async def compare_public_vs_authenticated_docket(self, case_number: str, authenticated_docket: Dict) -> Dict:
        """
        Compare public vs authenticated docket to identify hidden/sealed entries
        Critical for document completeness verification
        
        Args:
            case_number: Case number for public docket lookup
            authenticated_docket: Already scraped authenticated docket data
            
        Returns:
            Dictionary with comparison results and hidden entries analysis
        """
        try:
            self.logger.info(f"🔍 Comparing public vs authenticated docket access for {case_number}")
            
            # Scrape public docket
            public_docket = await self.scrape_public_docket(case_number)
            
            comparison_results = {
                'case_number': case_number,
                'comparison_date': datetime.now().isoformat(),
                'public_entries_count': len(public_docket.get('entries', [])),
                'authenticated_entries_count': len(authenticated_docket.get('entries', [])),
                'hidden_entries': [],
                'document_access_difference': {},
                'analysis': {}
            }
            
            # Compare entry counts
            public_entries = public_docket.get('entries', [])
            auth_entries = authenticated_docket.get('entries', [])
            
            if len(auth_entries) > len(public_entries):
                comparison_results['hidden_entries_count'] = len(auth_entries) - len(public_entries)
                self.logger.warning(f"🚨 {comparison_results['hidden_entries_count']} entries hidden from public view")
                
                # Try to identify hidden entries (simplified comparison)
                # In real implementation, would need more sophisticated matching
                comparison_results['analysis']['hidden_entry_analysis'] = (
                    f"Authenticated access reveals {comparison_results['hidden_entries_count']} additional entries "
                    f"not visible in public docket. This may indicate sealed records, confidential documents, "
                    f"or restricted access materials."
                )
            else:
                comparison_results['analysis']['visibility'] = "All authenticated entries appear to be publicly visible"
            
            # Document access comparison
            public_docs_available = sum(1 for entry in public_entries if entry.get('Has_Image', False))
            auth_docs_available = sum(1 for entry in auth_entries if entry.get('Has_Image', False))
            
            comparison_results['document_access_difference'] = {
                'public_documents': public_docs_available,
                'authenticated_documents': auth_docs_available,
                'additional_document_access': auth_docs_available - public_docs_available
            }
            
            if auth_docs_available > public_docs_available:
                self.logger.critical(
                    f"🔐 CRITICAL: Authenticated access provides {auth_docs_available - public_docs_available} "
                    f"additional document downloads not available to public"
                )
                comparison_results['analysis']['document_access'] = (
                    "Authenticated access provides significantly more document access than public view. "
                    "This represents the complete case record vs public-limited access."
                )
            
            return comparison_results
            
        except Exception as e:
            self.logger.error(f"Error comparing docket access levels: {e}")
            return {
                'error': str(e),
                'case_number': case_number,
                'comparison_date': datetime.now().isoformat()
            }

    async def download_efile_documents(self, document_links: List[Dict], download_dir: str = "case_documents/efile") -> Dict:
        """
        Download documents from extracted Efile document links
        Supports both direct downloads and JavaScript-initiated downloads
        
        Args:
            document_links: List of document link dictionaries from extract_efile_document_links
            download_dir: Directory to save downloaded documents
            
        Returns:
            Dictionary with download results
        """
        try:
            import os
            from pathlib import Path
            
            # Create download directory
            Path(download_dir).mkdir(parents=True, exist_ok=True)
            
            download_results = {
                'total_documents': len(document_links),
                'successful_downloads': 0,
                'failed_downloads': 0,
                'downloaded_files': [],
                'errors': []
            }
            
            for i, doc_info in enumerate(document_links):
                try:
                    url = doc_info.get('url', '')
                    
                    # Extract meaningful information for filename
                    docket_num = doc_info.get('docket_entry', f"{i+1:03d}")
                    filing_date = doc_info.get('filing_date', '').replace('/', '_')
                    description = doc_info.get('description', doc_info.get('title', '')).replace('/', '_').replace('\\', '_')
                    doc_type = doc_info.get('type', 'document')
                    
                    # Clean up description for filename
                    safe_description = ''.join(c for c in description if c.isalnum() or c in '-_. ')[:80].strip()
                    if not safe_description:
                        safe_description = f"DOCUMENT_{i+1}"
                    
                    # Create meaningful filename for legal use
                    # Format: DOCKET_###_DATE_DESCRIPTION.pdf
                    if filing_date and filing_date != 'unknown':
                        filename_base = f"DOCKET_{docket_num:03d}_{filing_date}_{safe_description}"
                    else:
                        filename_base = f"DOCKET_{docket_num:03d}_{safe_description}"
                    
                    # Determine file extension - assume PDF for legal documents
                    extension = '.pdf'
                    if doc_type == 'image' or any(ext in url.lower() for ext in ['.jpg', '.jpeg', '.png', '.gif', '.tiff']):
                        if '.png' in url.lower():
                            extension = '.png'
                        elif '.gif' in url.lower():
                            extension = '.gif'
                        elif '.tiff' in url.lower() or '.tif' in url.lower():
                            extension = '.tiff'
                        else:
                            extension = '.jpg'
                    
                    filename = f"{filename_base}{extension}"
                    file_path = os.path.join(download_dir, filename)
                    
                    self.logger.info(f"📥 Downloading document {i+1}/{len(document_links)}: {filename}")
                    
                    # Handle e-filing system specific downloads (DisplayImageList.aspx pages)
                    if 'DisplayImageList.aspx' in url:
                        self.logger.info(f"🔍 Processing e-filing document page: {url}")
                        
                        # Navigate to the DisplayImageList page
                        await self.page.goto(url, wait_until='networkidle', timeout=30000)
                        await asyncio.sleep(3)  # Allow page to fully load
                        
                        # Look for the actual PDF download link on this page
                        pdf_links = await self.page.query_selector_all('a[href*=".pdf"], a[href*="PDF"], a[onclick*="pdf"], a[onclick*="PDF"]')
                        
                        if not pdf_links:
                            # Try broader selectors for download links
                            pdf_links = await self.page.query_selector_all('a[href*="download"], a[onclick*="download"], input[onclick*="download"]')
                        
                        if pdf_links:
                            # Try the first PDF link found
                            pdf_link = pdf_links[0]
                            href = await pdf_link.get_attribute('href')
                            onclick = await pdf_link.get_attribute('onclick') or ''
                            
                            self.logger.info(f"📄 Found PDF link: {href or onclick[:100]}")
                            
                            if href and not href.startswith('javascript:'):
                                # Direct PDF link
                                if not href.startswith('http'):
                                    href = f"{self.base_url}/{href.lstrip('/')}"
                                
                                async with self.page.expect_download(timeout=15000) as download_info:
                                    await self.page.goto(href)
                            else:
                                # JavaScript-based download
                                async with self.page.expect_download(timeout=15000) as download_info:
                                    await pdf_link.click()
                        else:
                            # No direct PDF link found, try downloading the page content if it's a PDF
                            content_type = await self.page.evaluate('() => document.contentType || ""')
                            if 'pdf' in content_type.lower():
                                # The page itself is a PDF
                                async with self.page.expect_download(timeout=15000) as download_info:
                                    # Force download by navigating again
                                    await self.page.goto(url)
                            else:
                                raise Exception(f"No PDF download link found on DisplayImageList page: {url}")
                    
                    elif doc_info.get('extraction_method') == 'javascript' and doc_info.get('onclick_script'):
                        # For JavaScript links, we need to execute the script or navigate directly
                        try:
                            # Try direct navigation first
                            await self.page.goto(url, wait_until='networkidle')
                            await asyncio.sleep(2)
                            
                            # Check if this triggers a download
                            async with self.page.expect_download(timeout=10000) as download_info:
                                # The download should start automatically if the URL is correct
                                pass
                                
                        except Exception:
                            # If direct navigation doesn't work, try clicking the original element
                            self.logger.warning(f"Direct download failed for {filename}, attempting JavaScript execution")
                            # This would require navigating back to the original page and finding the element
                            # For now, skip these complex cases
                            raise Exception("JavaScript-based download not supported yet")
                    else:
                        # Direct download
                        async with self.page.expect_download(timeout=15000) as download_info:
                            await self.page.goto(url)
                        
                    # Save the download
                    download = await download_info.value
                    await download.save_as(file_path)
                    
                    download_results['successful_downloads'] += 1
                    download_results['downloaded_files'].append({
                        'filename': filename,
                        'path': file_path,
                        'efile_id': efile_id,
                        'document_type': doc_type,
                        'url': url,
                        'file_size': os.path.getsize(file_path) if os.path.exists(file_path) else 0
                    })
                    
                    self.logger.info(f"✅ Successfully downloaded: {filename}")
                    
                except Exception as e:
                    error_msg = f"Failed to download document {i+1}: {e}"
                    self.logger.error(error_msg)
                    download_results['errors'].append(error_msg)
                    download_results['failed_downloads'] += 1
                    continue
            
            self.logger.info(f"Document download completed: {download_results['successful_downloads']} successful, {download_results['failed_downloads']} failed")
            return download_results
            
        except Exception as e:
            self.logger.error(f"Error downloading efile documents: {e}")
            return {'error': str(e)}
    
    async def download_single_document(self, doc_url: str, download_dir: str, doc_name: str) -> bool:
        """
        Download a single document from the e-filing system
        
        Args:
            doc_url: Direct URL to the document
            download_dir: Directory to save the document  
            doc_name: Name for the downloaded file
            
        Returns:
            True if successful, False otherwise
        """
        try:
            import os
            from pathlib import Path
            
            # Create download directory
            Path(download_dir).mkdir(parents=True, exist_ok=True)
            
            # Navigate to the document URL
            await self.page.goto(doc_url, wait_until="networkidle")
            await asyncio.sleep(2)
            
            # Try to download the document
            try:
                # Wait for any authentication redirects
                await self.page.wait_for_load_state("networkidle", timeout=10000)
                
                # Check if we need to login again
                if "login" in self.page.url.lower() or "sign" in self.page.url.lower():
                    self.logger.warning("Authentication required - logging in again")
                    await self.login()
                    await self.page.goto(doc_url, wait_until="networkidle")
                
                # Set up download
                download_path = os.path.join(download_dir, f"{doc_name}.pdf")
                
                # Start download and wait for it to complete
                async with self.page.expect_download() as download_info:
                    # Click download button or link
                    download_selectors = [
                        "a[href*='pdf']",
                        "a[href*='download']",
                        "button:has-text('Download')",
                        "a:has-text('Download')",
                        "input[type='submit'][value*='Download']"
                    ]
                    
                    clicked = False
                    for selector in download_selectors:
                        try:
                            if await self.page.locator(selector).count() > 0:
                                await self.page.locator(selector).first.click()
                                clicked = True
                                break
                        except:
                            continue
                    
                    # If no download button found, try to get PDF directly
                    if not clicked:
                        # Check if page content is already a PDF
                        content_type = await self.page.evaluate("() => document.contentType")
                        if "pdf" in content_type.lower():
                            # Page is already a PDF, save it directly
                            pdf_content = await self.page.content()
                            with open(download_path, 'wb') as f:
                                # This won't work for actual PDF content, need different approach
                                pass
                        else:
                            self.logger.warning(f"No download mechanism found for {doc_url}")
                            return False
                
                download = await download_info.value
                await download.save_as(download_path)
                
                self.logger.info(f"✅ Downloaded: {doc_name} to {download_path}")
                return True
                
            except Exception as download_error:
                self.logger.error(f"Download failed for {doc_name}: {download_error}")
                
                # Try alternative: get PDF content directly if it's a direct PDF URL
                try:
                    if doc_url.endswith('.pdf') or 'pdf' in doc_url.lower():
                        # Direct PDF download using requests
                        response = await self.page.request.get(doc_url)
                        if response.ok:
                            content = await response.body()
                            download_path = os.path.join(download_dir, f"{doc_name}.pdf")
                            
                            with open(download_path, 'wb') as f:
                                f.write(content)
                            
                            self.logger.info(f"✅ Direct download successful: {doc_name}")
                            return True
                except Exception as direct_error:
                    self.logger.error(f"Direct download also failed: {direct_error}")
                
                return False
                
        except Exception as e:
            self.logger.error(f"Error downloading single document: {e}")
            return False

    async def download_document_images(self, docket_entries: List[Dict], download_dir: str = "case_documents/images") -> Dict:
        """
        Download all images from docket entries that have image links
        
        Args:
            docket_entries: List of docket entries with image links
            download_dir: Directory to save downloaded images
            
        Returns:
            Dictionary with download results
        """
        try:
            import os
            from pathlib import Path
            
            # Create download directory
            Path(download_dir).mkdir(parents=True, exist_ok=True)
            
            download_results = {
                'total_entries': len(docket_entries),
                'entries_with_images': 0,
                'images_downloaded': 0,
                'failed_downloads': 0,
                'downloaded_files': [],
                'errors': []
            }
            
            for entry in docket_entries:
                if entry.get('Has_Image') and entry.get('Image_Link'):
                    download_results['entries_with_images'] += 1
                    
                    try:
                        # Navigate to the image display page
                        image_url = f"{self.base_url}/{entry['Image_Link']}" if not entry['Image_Link'].startswith('http') else entry['Image_Link']
                        await self.page.goto(image_url, wait_until='networkidle')
                        await asyncio.sleep(2)
                        
                        # Look for actual image or PDF download links
                        download_links = []
                        
                        # Check for direct image links
                        img_elements = await self.page.query_selector_all('img[src*=".pdf"], img[src*=".jpg"], img[src*=".png"], img[src*=".gif"]')
                        for img in img_elements:
                            src = await img.get_attribute('src')
                            if src and not src.endswith('.png'):  # Skip UI icons
                                download_links.append(src)
                        
                        # Check for PDF links
                        pdf_links = await self.page.query_selector_all('a[href*=".pdf"]')
                        for link in pdf_links:
                            href = await link.get_attribute('href')
                            if href:
                                download_links.append(href)
                                
                        # Check for generic document download links
                        doc_links = await self.page.query_selector_all('a[title*="Download"], a[title*="View"], a[href*="document"]')
                        for link in doc_links:
                            href = await link.get_attribute('href')
                            if href:
                                download_links.append(href)
                        
                        # Download each found link
                        for i, link in enumerate(download_links[:3]):  # Limit to first 3 per entry
                            try:
                                # Create filename from docket info
                                filing_date = entry.get('Filing Date', 'unknown')
                                doc_type = entry.get('Type', 'document')
                                docket_num = entry.get('Docket Number', i)
                                
                                # Clean filename
                                safe_date = filing_date.replace('/', '-') if filing_date else 'unknown'
                                safe_type = ''.join(c for c in doc_type if c.isalnum() or c in '-_').strip()
                                
                                filename = f"{docket_num:03d}_{safe_date}_{safe_type}_{i+1}"
                                
                                # Determine file extension
                                if '.pdf' in link.lower():
                                    filename += '.pdf'
                                elif any(ext in link.lower() for ext in ['.jpg', '.jpeg']):
                                    filename += '.jpg'
                                elif '.png' in link.lower():
                                    filename += '.png'
                                else:
                                    filename += '.pdf'  # Default to PDF
                                
                                file_path = os.path.join(download_dir, filename)
                                
                                # Download using playwright
                                if link.startswith('http'):
                                    full_url = link
                                else:
                                    full_url = f"{self.base_url}/{link.lstrip('/')}"
                                
                                # Start download
                                async with self.page.expect_download() as download_info:
                                    await self.page.goto(full_url)
                                download = await download_info.value
                                
                                # Save file
                                await download.save_as(file_path)
                                
                                download_results['images_downloaded'] += 1
                                download_results['downloaded_files'].append({
                                    'filename': filename,
                                    'path': file_path,
                                    'docket_number': entry.get('Docket Number'),
                                    'filing_date': entry.get('Filing Date'),
                                    'document_type': entry.get('Type'),
                                    'url': full_url
                                })
                                
                                self.logger.info(f"Downloaded: {filename}")
                                
                            except Exception as download_error:
                                error_msg = f"Failed to download {link}: {download_error}"
                                self.logger.error(error_msg)
                                download_results['errors'].append(error_msg)
                                download_results['failed_downloads'] += 1
                        
                    except Exception as entry_error:
                        error_msg = f"Failed to process entry {entry.get('Docket Number')}: {entry_error}"
                        self.logger.error(error_msg)
                        download_results['errors'].append(error_msg)
                        download_results['failed_downloads'] += 1
            
            self.logger.info(f"Image download completed: {download_results['images_downloaded']} downloaded, {download_results['failed_downloads']} failed")
            return download_results
            
        except Exception as e:
            self.logger.error(f"Error downloading document images: {e}")
            return {'error': str(e)}
            
    def attach_forensic_extension(self, forensic_extension):
        """Attach forensic extension for tampering detection"""
        self.forensic_extension = forensic_extension
        self.logger.info("🔒 Forensic extension attached - tampering detection enabled")
    
    def attach_change_monitor(self, change_monitor):
        """Attach document change monitor for comprehensive change detection"""
        self.change_monitor = change_monitor
        self.logger.info("📊 Document change monitor attached - change detection enabled")
        
    async def perform_forensic_analysis(self, docket_data: Dict, case_number: str):
        """
        Perform forensic analysis on scraped docket data
        Preserves snapshots and detects tampering
        """
        if not hasattr(self, 'forensic_extension') or not self.forensic_extension:
            return
            
        try:
            self.logger.info("🔍 Starting forensic analysis...")
            
            entries = docket_data.get('entries', [])
            if not entries:
                return
                
            # Get case ID (simplified - in real implementation, look up from database)
            case_id = 1  # TODO: Get actual case ID from database
            
            forensic_results = {
                'snapshots_created': 0,
                'tampering_detected': 0,
                'critical_incidents': 0,
                'evidence_preserved': True
            }
            
            # Process each document entry
            for entry in entries:
                try:
                    # Check if this is a first-time scan (preserve snapshot)
                    document_id = f"{case_id}_{entry.get('Docket Number', 'unknown')}"
                    existing_snapshot = self.forensic_extension.get_original_snapshot(document_id)
                    
                    if not existing_snapshot:
                        # Create initial forensic snapshot
                        snapshot = self.forensic_extension.preserve_document_snapshot(entry, case_id)
                        forensic_results['snapshots_created'] += 1
                        self.logger.info(f"📸 Snapshot created for document {document_id}")
                    else:
                        # Check for tampering
                        tampering = self.forensic_extension.detect_document_tampering(entry, case_id)
                        if tampering:
                            forensic_results['tampering_detected'] += 1
                            
                            if tampering.severity == 'CRITICAL':
                                forensic_results['critical_incidents'] += 1
                                self.logger.critical(f"🚨 CRITICAL TAMPERING: {tampering.tamper_type} in {document_id}")
                                
                                # Special handling for judicial misconduct
                                if tampering.judicial_actor:
                                    await self.document_judicial_misconduct(tampering, case_id)
                            else:
                                self.logger.warning(f"⚠️ Document tampering detected: {tampering.tamper_type}")
                                
                except Exception as entry_error:
                    self.logger.error(f"❌ Error analyzing document entry: {entry_error}")
                    continue
                    
            # Log forensic summary
            if forensic_results['tampering_detected'] > 0:
                self.logger.critical(
                    f"🚨 FORENSIC ALERT: {forensic_results['tampering_detected']} tampering incidents detected "
                    f"({forensic_results['critical_incidents']} critical)"
                )
            else:
                self.logger.info(f"✅ Forensic analysis complete: {forensic_results['snapshots_created']} snapshots preserved")
                
            # Store results in docket data for UI display
            docket_data['forensic_analysis'] = forensic_results
            
        except Exception as e:
            self.logger.error(f"❌ Forensic analysis failed: {e}")
            docket_data['forensic_analysis'] = {
                'error': str(e),
                'evidence_preserved': False
            }
            
    async def document_judicial_misconduct(self, tampering_evidence, case_id: int):
        """Document judicial misconduct incident"""
        try:
            misconduct_data = {
                'case_id': case_id,
                'incident_type': f'JUDICIAL_DOCUMENT_TAMPERING_{tampering_evidence.tamper_type}',
                'description': f'Judicial officer {tampering_evidence.judicial_actor} engaged in unauthorized document alteration',
                'evidence_documents': [tampering_evidence.document_id],
                'judicial_officer': tampering_evidence.judicial_actor,
                'incident_date': tampering_evidence.detection_time,
                'severity': tampering_evidence.severity,
                'legal_violations': tampering_evidence.misconduct_indicators,
                'status': 'REQUIRES_IMMEDIATE_ACTION'
            }
            
            # Store in database
            sql = """
            INSERT INTO judicial_misconduct 
            (case_id, incident_type, description, evidence_documents, judicial_officer, 
             incident_date, severity, status, legal_violations)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
            """
            
            with self.forensic_extension.postgresql.get_connection() as conn:
                with conn.cursor() as cursor:
                    cursor.execute(sql, (
                        misconduct_data['case_id'],
                        misconduct_data['incident_type'],
                        misconduct_data['description'],
                        misconduct_data['evidence_documents'],
                        misconduct_data['judicial_officer'],
                        misconduct_data['incident_date'],
                        misconduct_data['severity'],
                        misconduct_data['status'],
                        misconduct_data['legal_violations']
                    ))
                    conn.commit()
                    
            self.logger.critical(f"📋 JUDICIAL MISCONDUCT DOCUMENTED: {tampering_evidence.judicial_actor}")
            
        except Exception as e:
            self.logger.error(f"❌ Failed to document judicial misconduct: {e}")
            
    async def scrape_all_case_data(self, case_number: str) -> Dict:
        """
        Main method to scrape all data for a case
        
        Args:
            case_number: Case number (e.g., 'DR-25-403973')
            
        Returns:
            Complete case data dictionary
        """
        try:
            # Login first
            if not await self.login():
                return {'error': 'Login failed'}
                
            # Find the case and get query parameter
            case_query = await self.find_case(case_number)
            if not case_query:
                return {'error': f'Case {case_number} not found'}
                
            # Scrape all four areas
            results = {
                'case_number': case_number,
                'case_query': case_query,
                'scraped_at': datetime.now().isoformat(),
                'docket_information': {},
                'images': {},
                'eservice_queue': {},
                'my_filings': {}
            }
            
            # 1. Docket Information
            self.logger.info("Scraping docket information...")
            results['docket_information'] = await self.scrape_docket_information(case_query)
            
            # 1.5 Forensic preservation and tampering detection
            if hasattr(self, 'forensic_extension') and self.forensic_extension:
                await self.perform_forensic_analysis(results['docket_information'], case_number)
            
            # 1.6 Compare public vs authenticated docket access (NEW - completeness verification)
            self.logger.info("Comparing public vs authenticated docket access...")
            docket_comparison = await self.compare_public_vs_authenticated_docket(case_number, results['docket_information'])
            results['docket_access_analysis'] = docket_comparison
            
            auth_entries = docket_comparison.get('authenticated_entries_count', 0)
            public_entries = docket_comparison.get('public_entries_count', 0) 
            if auth_entries > public_entries:
                self.logger.critical(f"🚨 COMPLETE DOCKET ACCESS: {auth_entries} total entries ({auth_entries - public_entries} hidden from public)")
            else:
                self.logger.info(f"📋 Docket access verified: {auth_entries} entries (all publicly visible)")
            
            # 1.7 Extract actual document links from Efile IDs (OPTIMIZED - now handled directly in docket extraction)
            # The optimized docket extraction now gets document links directly from the main table
            # This eliminates the need for individual Efile ID page processing
            self.logger.info("Document links already extracted during optimized docket processing")
            
            # Count documents from optimized extraction
            total_docs = 0
            if results['docket_information'].get('entries'):
                total_docs = sum(entry.get('document_count', 0) for entry in results['docket_information']['entries'])
                results['efile_documents'] = {
                    'documents_found': total_docs,
                    'source': 'optimized_docket_extraction',
                    'method': 'direct_table_extraction'
                }
                self.logger.info(f"✅ Found {total_docs} document links via optimized extraction (no individual page processing needed)")
                
                # 1.8 Download actual PDF documents (CRITICAL - user requirement: "I NEED THE ACTUAL PDFS")
                self.logger.info("🔽 Downloading actual PDF documents for each docket entry...")
                
                # Create download directory for this case
                case_download_dir = f"case_documents/{case_number.replace('-', '_')}/efiling_pdfs"
                
                # Collect all document links from docket entries
                all_document_links = []
                for entry in results['docket_information']['entries']:
                    if entry.get('documents'):
                        for doc in entry['documents']:
                            # Add metadata for better organization
                            doc_with_metadata = doc.copy()
                            doc_with_metadata['docket_entry'] = entry.get('Docket Number', 'unknown')
                            doc_with_metadata['filing_date'] = entry.get('Filing Date', 'unknown')
                            doc_with_metadata['description'] = entry.get('Description', 'unknown')
                            all_document_links.append(doc_with_metadata)
                
                if all_document_links:
                    self.logger.info(f"📄 Starting download of {len(all_document_links)} PDF documents...")
                    download_results = await self.download_efile_documents(all_document_links, case_download_dir)
                    results['pdf_downloads'] = download_results
                    
                    if download_results.get('successful_downloads', 0) > 0:
                        self.logger.info(f"✅ Successfully downloaded {download_results['successful_downloads']} PDF documents to {case_download_dir}")
                    else:
                        self.logger.warning(f"⚠️ No PDFs downloaded successfully. Check download results: {download_results}")
                else:
                    self.logger.warning("⚠️ No document links found for PDF download")
                    results['pdf_downloads'] = {'error': 'No document links available for download'}
                    
            else:
                results['efile_documents'] = {'error': 'No docket entries available for document extraction'}
                results['pdf_downloads'] = {'error': 'No docket entries available for PDF download'}
            
            # 2. Images
            self.logger.info("Scraping images...")
            results['images'] = await self.scrape_images(case_query)
            
            # 3. E-Service Queue
            self.logger.info("Scraping e-service queue...")
            results['eservice_queue'] = await self.scrape_eservice_queue()
            
            # 4. My Filings
            self.logger.info("Scraping my filings...")
            results['my_filings'] = await self.scrape_my_filings()
            
            return results
            
        except Exception as e:
            self.logger.error(f"Error scraping case data: {e}")
            return {'error': str(e), 'case_number': case_number}


# Synchronous wrapper function for easy integration
def scrape_cuyahoga_case(username: str, password: str, case_number: str, headless: bool = True) -> Dict:
    """
    Synchronous wrapper to scrape Cuyahoga County case data
    
    Args:
        username: E-filing system username
        password: E-filing system password
        case_number: Case number to scrape (e.g., 'DR-25-403973')
        headless: Run browser in headless mode
        
    Returns:
        Complete case data dictionary
    """
    async def _scrape():
        async with CuyahogaEFilingScraperPlaywright(username, password, headless) as scraper:
            return await scraper.scrape_all_case_data(case_number)
    
    return asyncio.run(_scrape())


if __name__ == "__main__":
    # Test the scraper
    import json
    
    # Example usage
    username = "GSHEPOV"
    password = "VhodaNet4u"
    case_number = "DR-25-403973"
    
    print("Starting Cuyahoga County E-Filing scraper...")
    results = scrape_cuyahoga_case(username, password, case_number, headless=True)
    
    print(f"Scraping completed. Results:")
    print(json.dumps(results, indent=2, default=str))