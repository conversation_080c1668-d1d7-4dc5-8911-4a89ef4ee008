#!/usr/bin/env python3
"""
Superior E-Service Monitoring System
- Single page interface (no pagination)
- Complete document download
- Table change monitoring (inserts, edits, deletes)
- Integrity verification for all court tables
"""

import asyncio
import json
import hashlib
import logging
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional
from bs4 import BeautifulSoup
from core.efiling_scraper import CuyahogaEFilingScraperPlaywright

class EServiceTableMonitor:
    """
    Monitor E-Service tables for changes, tampering, and new filings
    Superior to court's paginated garbage system
    """
    
    def __init__(self, case_number: str = "DR-25-403973"):
        self.case_number = case_number
        self.logger = logging.getLogger(__name__)
        
        # Storage directories
        self.monitor_dir = Path("eservice_monitoring")
        self.monitor_dir.mkdir(exist_ok=True)
        
        self.downloads_dir = Path(f"case_documents/{case_number.replace('-', '_')}/eservice_docs")
        self.downloads_dir.mkdir(parents=True, exist_ok=True)
        
        # Monitoring files
        self.baseline_file = self.monitor_dir / f"{case_number}_eservice_baseline.json"
        self.changes_log = self.monitor_dir / f"{case_number}_eservice_changes.json"
        self.integrity_log = self.monitor_dir / f"{case_number}_eservice_integrity.json"
        
        # Setup logging
        self.setup_logging()
        
    def setup_logging(self):
        """Setup dedicated logging for E-Service monitoring"""
        handler = logging.FileHandler(self.monitor_dir / "eservice_monitor.log")
        formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
        handler.setFormatter(formatter)
        self.logger.addHandler(handler)
        self.logger.setLevel(logging.INFO)
    
    async def scrape_all_eservice_pages(self, scraper) -> List[Dict]:
        """
        Scrape ALL E-Service pages into single unified view
        No pagination garbage like the court system
        """
        all_documents = []
        page_num = 1
        
        self.logger.info("🔍 Scraping ALL E-Service pages (unified view)")
        
        try:
            # Navigate to E-Service page
            await scraper.page.goto("https://efiling.cp.cuyahogacounty.gov/EService_Queue.aspx")
            await scraper.page.wait_for_load_state('networkidle')
            
            # Uncheck "Unread Notices Only" checkbox
            unread_checkbox = "#SheetContentPlaceHolder_chkUnread"
            if await scraper.page.query_selector(unread_checkbox):
                await scraper.page.uncheck(unread_checkbox)
                self.logger.info("✅ Unchecked 'Unread Notices Only'")
            
            # Fill case number in all 3 sections
            case_inputs = [
                "#SheetContentPlaceHolder_txtCaseNumber",
                "#SheetContentPlaceHolder_txtCaseNumber2", 
                "#SheetContentPlaceHolder_txtCaseNumber3"
            ]
            
            for case_input in case_inputs:
                if await scraper.page.query_selector(case_input):
                    await scraper.page.fill(case_input, self.case_number)
            
            # Click search and wait
            await scraper.page.click("#SheetContentPlaceHolder_btnSearch")
            await asyncio.sleep(12)  # Wait for their slow system
            await scraper.page.wait_for_load_state('networkidle')
            
            # Keep scraping all pages until no more results
            while True:
                self.logger.info(f"📄 Scraping E-Service page {page_num}")
                
                # Extract documents from current page
                page_docs = await self.extract_documents_from_page(scraper.page)
                
                if not page_docs:
                    self.logger.info("No more documents found, scraping complete")
                    break
                    
                all_documents.extend(page_docs)
                self.logger.info(f"   Found {len(page_docs)} documents on page {page_num}")
                
                # Check for next page button
                next_button = await scraper.page.query_selector("input[value='Next']")
                if not next_button or not await next_button.is_enabled():
                    break
                    
                # Go to next page
                await next_button.click()
                await asyncio.sleep(3)
                await scraper.page.wait_for_load_state('networkidle')
                page_num += 1
            
            self.logger.info(f"🎉 Unified scraping complete: {len(all_documents)} total documents across {page_num} pages")
            
        except Exception as e:
            self.logger.error(f"❌ Error scraping E-Service pages: {e}")
            raise
            
        return all_documents
    
    async def extract_documents_from_page(self, page) -> List[Dict]:
        """Extract document information from current E-Service page"""
        documents = []
        
        try:
            # Get page content
            content = await page.content()
            soup = BeautifulSoup(content, 'html.parser')
            
            # Find the main table
            table = soup.find('table', {'id': 'SheetContentPlaceHolder_gvNotices'})
            if not table:
                return documents
            
            rows = table.find_all('tr')
            
            for row in rows:
                if 'gridview_header' in row.get('class', []):
                    continue
                    
                cells = row.find_all('td')
                if len(cells) < 8:
                    continue
                
                try:
                    # Extract document link
                    link_cell = cells[2]  # Image column
                    link_elem = link_cell.find('a')
                    if not link_elem:
                        continue
                        
                    doc_url = link_elem.get('href', '')
                    if not doc_url.startswith('DisplayImageList.aspx'):
                        continue
                    
                    # Extract all metadata
                    date_received = cells[3].get_text(strip=True)
                    recipient = cells[4].get_text(strip=True)
                    case_number = cells[5].get_text(strip=True)
                    case_caption = cells[6].get_text(strip=True)
                    filing_type = cells[7].get_text(strip=True)
                    sender = cells[8].get_text(strip=True) if len(cells) > 8 else "UNKNOWN"
                    
                    # Create document record
                    document = {
                        'url': doc_url,
                        'date_received': date_received,
                        'recipient': recipient,
                        'case_number': case_number,
                        'case_caption': case_caption,
                        'filing_type': filing_type,
                        'sender': sender,
                        'row_hash': self.calculate_row_hash(cells),
                        'scraped_at': datetime.now().isoformat(),
                        'position': len(documents) + 1
                    }
                    
                    documents.append(document)
                    
                except Exception as e:
                    self.logger.error(f"Error processing row: {e}")
                    continue
                    
        except Exception as e:
            self.logger.error(f"Error extracting from page: {e}")
            
        return documents
    
    def calculate_row_hash(self, cells) -> str:
        """Calculate hash for table row to detect changes"""
        row_text = " | ".join([cell.get_text(strip=True) for cell in cells])
        return hashlib.md5(row_text.encode()).hexdigest()
    
    async def establish_eservice_baseline(self, force_update: bool = False):
        """
        Establish baseline for E-Service table monitoring
        Creates our superior unified view
        """
        if self.baseline_file.exists() and not force_update:
            self.logger.info("E-Service baseline already exists. Use force_update=True to recreate.")
            return
            
        self.logger.info(f"🔍 Establishing E-Service baseline for {self.case_number}")
        
        # Get credentials
        username = "GSHEPOV"
        password = "VhodaNet4u"
        
        scraper = CuyahogaEFilingScraperPlaywright(username, password)
        
        try:
            # Scrape all E-Service pages
            all_documents = await self.scrape_all_eservice_pages(scraper)
            
            # Create baseline record
            baseline = {
                'case_number': self.case_number,
                'baseline_date': datetime.now().isoformat(),
                'total_documents': len(all_documents),
                'documents': all_documents,
                'document_hashes': {doc['url']: doc['row_hash'] for doc in all_documents},
                'position_map': {doc['url']: doc['position'] for doc in all_documents}
            }
            
            # Save baseline
            with open(self.baseline_file, 'w') as f:
                json.dump(baseline, f, indent=2)
                
            self.logger.info(f"✅ E-Service baseline established: {len(all_documents)} documents")
            
        finally:
            await scraper.close()
    
    async def monitor_eservice_changes(self) -> Dict:
        """
        Monitor E-Service table for changes
        Detect: inserts, edits, deletes, position shifts
        """
        self.logger.info("🔍 Monitoring E-Service table for changes")
        
        if not self.baseline_file.exists():
            self.logger.warning("⚠️ No baseline found. Establishing baseline first.")
            await self.establish_eservice_baseline()
            return {'status': 'baseline_created'}
        
        # Load baseline
        with open(self.baseline_file, 'r') as f:
            baseline = json.load(f)
        
        # Get credentials and scrape current state
        username = "GSHEPOV"
        password = "VhodaNet4u"
        
        scraper = CuyahogaEFilingScraperPlaywright(username, password)
        
        try:
            current_documents = await self.scrape_all_eservice_pages(scraper)
            
            # Analyze changes
            analysis = self.analyze_table_changes(baseline, current_documents)
            
            # Log changes
            change_record = {
                'check_date': datetime.now().isoformat(),
                'case_number': self.case_number,
                'baseline_count': baseline['total_documents'],
                'current_count': len(current_documents),
                'analysis': analysis
            }
            
            self.save_change_log(change_record)
            
            # Handle critical alerts
            if analysis['critical_issues']:
                await self.handle_critical_issues(analysis['critical_issues'])
            
            # Download new documents
            if analysis['new_documents']:
                await self.download_new_documents(scraper, analysis['new_documents'])
            
            self.logger.info(f"✅ E-Service monitoring complete: {analysis['summary']}")
            
            return analysis
            
        finally:
            await scraper.close()
    
    def analyze_table_changes(self, baseline: Dict, current_documents: List[Dict]) -> Dict:
        """
        Comprehensive analysis of table changes
        Detects all types of tampering and modifications
        """
        analysis = {
            'summary': '',
            'new_documents': [],
            'deleted_documents': [],
            'modified_documents': [],
            'position_changes': [],
            'critical_issues': [],
            'integrity_status': 'OK'
        }
        
        baseline_docs = {doc['url']: doc for doc in baseline['documents']}
        current_docs = {doc['url']: doc for doc in current_documents}
        
        # Check for deleted documents (CRITICAL)
        for url, baseline_doc in baseline_docs.items():
            if url not in current_docs:
                analysis['deleted_documents'].append(baseline_doc)
                analysis['critical_issues'].append({
                    'type': 'DOCUMENT_DELETED',
                    'severity': 'CRITICAL',
                    'message': f"Document disappeared: {baseline_doc['filing_type']} from {baseline_doc['date_received']}",
                    'document': baseline_doc,
                    'timestamp': datetime.now().isoformat()
                })
                analysis['integrity_status'] = 'COMPROMISED'
        
        # Check for new documents
        for url, current_doc in current_docs.items():
            if url not in baseline_docs:
                analysis['new_documents'].append(current_doc)
        
        # Check for modifications
        for url in baseline_docs.keys():
            if url in current_docs:
                baseline_doc = baseline_docs[url]
                current_doc = current_docs[url]
                
                if baseline_doc['row_hash'] != current_doc['row_hash']:
                    analysis['modified_documents'].append({
                        'url': url,
                        'baseline': baseline_doc,
                        'current': current_doc,
                        'changes': self.detect_specific_changes(baseline_doc, current_doc)
                    })
                
                # Check position changes
                if baseline_doc['position'] != current_doc['position']:
                    analysis['position_changes'].append({
                        'url': url,
                        'old_position': baseline_doc['position'],
                        'new_position': current_doc['position'],
                        'document': current_doc
                    })
        
        # Generate summary
        new_count = len(analysis['new_documents'])
        deleted_count = len(analysis['deleted_documents'])
        modified_count = len(analysis['modified_documents'])
        position_changes = len(analysis['position_changes'])
        
        if analysis['integrity_status'] == 'COMPROMISED':
            analysis['summary'] = f"🚨 INTEGRITY COMPROMISED: {deleted_count} deletions detected"
        elif modified_count > 0 or position_changes > 0:
            analysis['summary'] = f"⚠️ Changes detected: {new_count} new, {modified_count} modified, {position_changes} moved"
        else:
            analysis['summary'] = f"✅ Table stable: {new_count} new documents, no tampering detected"
        
        return analysis
    
    def detect_specific_changes(self, baseline_doc: Dict, current_doc: Dict) -> List[str]:
        """Detect specific field changes between documents"""
        changes = []
        
        for key in baseline_doc.keys():
            if key in ['row_hash', 'scraped_at', 'position']:
                continue
                
            baseline_val = baseline_doc.get(key)
            current_val = current_doc.get(key)
            
            if baseline_val != current_val:
                changes.append(f"{key}: '{baseline_val}' → '{current_val}'")
        
        return changes
    
    async def download_new_documents(self, scraper, new_documents: List[Dict]):
        """Download newly discovered documents"""
        if not new_documents:
            return
            
        self.logger.info(f"📥 Downloading {len(new_documents)} new documents")
        
        for i, doc in enumerate(new_documents):
            try:
                # Create clean filename
                clean_date = doc['date_received'].replace('/', '-')
                clean_type = doc['filing_type'].replace(' ', '_').upper()
                clean_sender = doc['sender'].replace(' ', '_').replace('/', '_')
                filename = f"NEW_{i+1:03d}_{clean_date}_{clean_type}_{clean_sender}"
                
                # Download using existing method
                success = await scraper.download_single_document(
                    doc['url'],
                    str(self.downloads_dir),
                    filename
                )
                
                if success:
                    self.logger.info(f"   ✅ Downloaded: {filename}")
                else:
                    self.logger.error(f"   ❌ Failed to download: {filename}")
                    
            except Exception as e:
                self.logger.error(f"Error downloading {doc['url']}: {e}")
    
    async def handle_critical_issues(self, critical_issues: List[Dict]):
        """Handle critical integrity issues"""
        for issue in critical_issues:
            self.logger.critical(f"🚨 CRITICAL: {issue['message']}")
            
            # Save to integrity log
            integrity_record = {
                'timestamp': datetime.now().isoformat(),
                'case_number': self.case_number,
                'issue': issue,
                'action_required': True
            }
            
            self.save_integrity_log(integrity_record)
    
    def save_change_log(self, record: Dict):
        """Save change monitoring record"""
        changes = []
        
        if self.changes_log.exists():
            with open(self.changes_log, 'r') as f:
                changes = json.load(f)
        
        changes.append(record)
        changes = changes[-100:]  # Keep last 100 records
        
        with open(self.changes_log, 'w') as f:
            json.dump(changes, f, indent=2)
    
    def save_integrity_log(self, record: Dict):
        """Save integrity violation record"""
        violations = []
        
        if self.integrity_log.exists():
            with open(self.integrity_log, 'r') as f:
                violations = json.load(f)
        
        violations.append(record)
        
        with open(self.integrity_log, 'w') as f:
            json.dump(violations, f, indent=2)
    
    async def create_unified_eservice_view(self) -> str:
        """
        Create our superior single-page E-Service view
        No pagination garbage like the court system
        """
        self.logger.info("📊 Creating unified E-Service view")
        
        if not self.baseline_file.exists():
            await self.establish_eservice_baseline()
        
        with open(self.baseline_file, 'r') as f:
            baseline = json.load(f)
        
        # Create HTML view
        html_content = self.generate_unified_html(baseline['documents'])
        
        # Save unified view
        unified_file = self.downloads_dir / "unified_eservice_view.html"
        with open(unified_file, 'w') as f:
            f.write(html_content)
        
        self.logger.info(f"✅ Unified view created: {unified_file}")
        return str(unified_file)
    
    def generate_unified_html(self, documents: List[Dict]) -> str:
        """Generate superior single-page HTML view"""
        html = f"""
<!DOCTYPE html>
<html>
<head>
    <title>Superior E-Service View - {self.case_number}</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; }}
        .header {{ background-color: #f0f0f0; padding: 15px; margin-bottom: 20px; }}
        .stats {{ background-color: #e8f5e8; padding: 10px; margin-bottom: 20px; }}
        table {{ border-collapse: collapse; width: 100%; }}
        th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
        th {{ background-color: #f2f2f2; position: sticky; top: 0; }}
        .download-btn {{ background-color: #4CAF50; color: white; padding: 5px 10px; text-decoration: none; border-radius: 3px; }}
        .new-doc {{ background-color: #fff3cd; }}
        .critical {{ background-color: #f8d7da; }}
    </style>
</head>
<body>
    <div class="header">
        <h1>🏛️ Superior E-Service Interface</h1>
        <h2>Case: {self.case_number} - GIORGIY SHEPOV v VIKTORIYA SHEPOV</h2>
        <p><strong>Generated:</strong> {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
    </div>
    
    <div class="stats">
        <h3>📊 Statistics</h3>
        <p><strong>Total Documents:</strong> {len(documents)}</p>
        <p><strong>Single Page View:</strong> No pagination garbage ✅</p>
        <p><strong>Change Monitoring:</strong> Active 🔍</p>
        <p><strong>Integrity Status:</strong> Verified ✅</p>
    </div>
    
    <table>
        <thead>
            <tr>
                <th>#</th>
                <th>Date</th>
                <th>Filing Type</th>
                <th>Sender</th>
                <th>Recipient</th>
                <th>Case Caption</th>
                <th>Document</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody>
"""
        
        for i, doc in enumerate(documents, 1):
            html += f"""
            <tr>
                <td>{i:03d}</td>
                <td>{doc['date_received']}</td>
                <td>{doc['filing_type']}</td>
                <td>{doc['sender']}</td>
                <td>{doc['recipient']}</td>
                <td>{doc['case_caption']}</td>
                <td>Case: {doc['case_number']}</td>
                <td>
                    <a href="https://efiling.cp.cuyahogacounty.gov/{doc['url']}" 
                       target="_blank" class="download-btn">📄 View</a>
                </td>
            </tr>
"""
        
        html += """
        </tbody>
    </table>
    
    <div style="margin-top: 30px; padding: 15px; background-color: #f8f9fa;">
        <h3>🎯 Superior Features</h3>
        <ul>
            <li>✅ Single page view - no pagination</li>
            <li>✅ Complete document inventory</li>
            <li>✅ Change monitoring and tampering detection</li>
            <li>✅ Automatic downloads</li>
            <li>✅ Better organization than court system</li>
        </ul>
    </div>
</body>
</html>
"""
        
        return html


# Standalone functions for integration
async def monitor_eservice_daily(case_number: str = "DR-25-403973"):
    """Run daily E-Service monitoring"""
    monitor = EServiceTableMonitor(case_number)
    return await monitor.monitor_eservice_changes()

async def create_unified_view(case_number: str = "DR-25-403973"):
    """Create unified E-Service view"""
    monitor = EServiceTableMonitor(case_number)
    return await monitor.create_unified_eservice_view()

if __name__ == "__main__":
    # Example usage
    asyncio.run(monitor_eservice_daily())