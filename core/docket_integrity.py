#!/usr/bin/env python3
"""
Docket Integrity and Positional Awareness System
Each document knows its exact position and neighbors to detect missing records
"""

import json
from datetime import datetime
from typing import Dict, List, Optional, Tuple
from pathlib import Path
import hashlib

class DocketPositionalAwareness:
    """
    Smart docket system where each record knows its neighbors
    Detects gaps, missing entries, and maintains chain of custody
    """
    
    def __init__(self, case_number: str = "DR-25-403973"):
        self.case_number = case_number
        self.neighbor_range = 5  # Each record knows 5 ahead and 5 behind
        self.integrity_file = Path(f"docket_monitoring/{case_number}_positional_map.json")
        self.gaps_file = Path(f"docket_monitoring/{case_number}_detected_gaps.json")
        
    def create_positional_map(self, docket_entries: List[Dict]) -> Dict:
        """
        Create comprehensive positional map where each record knows its neighbors
        """
        positional_map = {
            'case_number': self.case_number,
            'created_at': datetime.now().isoformat(),
            'total_entries': len(docket_entries),
            'neighbor_range': self.neighbor_range,
            'entries': {},
            'chronological_chain': [],
            'integrity_hashes': {}
        }
        
        # Sort entries by filing date (chronological order) instead of docket number
        # Convert filing dates for proper sorting
        def parse_filing_date(entry):
            filing_date = entry.get('Filing Date', '')
            try:
                if filing_date:
                    return datetime.strptime(filing_date, '%m/%d/%Y')
                else:
                    return datetime.min
            except:
                return datetime.min
                
        sorted_entries = sorted(docket_entries, key=parse_filing_date)
        
        for i, entry in enumerate(sorted_entries):
            # Use submission sequence ID instead of docket number
            submission_id = i + 1  # Start from 1 (April 4 complaint)
            
            # Create comprehensive position record
            position_record = {
                'submission_id': submission_id,  # Sequential ID starting from April 4
                'docket_number': entry.get('Docket Number', 'MISSING'),  # Track original docket #
                'position_index': i,
                'filing_date': entry.get('Filing Date', ''),
                'description': entry.get('Description', ''),
                'document_type': entry.get('Document Type', ''),
                'side': entry.get('Side', ''),
                'has_documents': entry.get('document_count', 0) > 0,
                'document_count': entry.get('document_count', 0),
                
                # Tampering detection fields
                'filer_legitimacy': self._analyze_filer_legitimacy(entry),
                'tampering_indicators': self._detect_tampering_indicators(entry),
                
                # Neighbor awareness (key feature for gap detection)
                'neighbors': {
                    'previous': self._get_neighbor_info(sorted_entries, i, -self.neighbor_range, 0),
                    'next': self._get_neighbor_info(sorted_entries, i, 1, self.neighbor_range + 1)
                },
                
                # Positional integrity data
                'expected_position': i,
                'chain_hash': self._generate_chain_hash(entry, i),
                'neighbor_hash': '',  # Will be calculated after all neighbors set
            }
            
            positional_map['entries'][str(submission_id)] = position_record
            positional_map['chronological_chain'].append({
                'submission_id': submission_id,
                'docket_number': entry.get('Docket Number'),
                'position': i,
                'hash': position_record['chain_hash']
            })
        
        # Calculate neighbor hashes for integrity checking
        for docket_num, record in positional_map['entries'].items():
            neighbor_data = json.dumps(record['neighbors'], sort_keys=True)
            record['neighbor_hash'] = hashlib.md5(neighbor_data.encode()).hexdigest()
        
        # Generate overall integrity hash
        chain_data = json.dumps(positional_map['chronological_chain'], sort_keys=True)
        positional_map['integrity_hashes']['full_chain'] = hashlib.md5(chain_data.encode()).hexdigest()
        
        return positional_map
    
    def _get_neighbor_info(self, entries: List[Dict], current_index: int, start_offset: int, end_offset: int) -> List[Dict]:
        """Get information about neighboring entries"""
        neighbors = []
        
        for offset in range(start_offset, end_offset):
            neighbor_index = current_index + offset
            if 0 <= neighbor_index < len(entries) and neighbor_index != current_index:
                neighbor = entries[neighbor_index]
                neighbors.append({
                    'docket_number': neighbor.get('Docket Number'),
                    'position_offset': offset,
                    'filing_date': neighbor.get('Filing Date', ''),
                    'description': neighbor.get('Description', '')[:100],  # Truncated for storage
                    'document_type': neighbor.get('Document Type', ''),
                    'has_documents': neighbor.get('document_count', 0) > 0
                })
        
        return neighbors
    
    def _generate_chain_hash(self, entry: Dict, position: int) -> str:
        """Generate hash for chain of custody verification"""
        chain_data = {
            'docket_number': entry.get('Docket Number'),
            'filing_date': entry.get('Filing Date'),
            'description': entry.get('Description'),
            'position': position
        }
        return hashlib.md5(json.dumps(chain_data, sort_keys=True).encode()).hexdigest()
    
    def _analyze_filer_legitimacy(self, entry: Dict) -> Dict:
        """Analyze if filer information is legitimate"""
        side = entry.get('Side', '').upper()
        description = entry.get('Description', '').lower()
        
        # Known legitimate SHEPOV filings
        shepov_indicators = ['shepov', 'petitioner', 'pro se']
        opposing_indicators = ['respondent', 'attorney for respondent']
        
        legitimacy = {
            'appears_shepov_filing': any(indicator in side.lower() for indicator in shepov_indicators),
            'hijack_suspected': False,
            'original_filer': side
        }
        
        # Check for potential hijacking
        if any(indicator in description for indicator in ['certificate', 'completion', 'parenting']):
            if not legitimacy['appears_shepov_filing']:
                legitimacy['hijack_suspected'] = True
                legitimacy['hijack_reason'] = 'Parenting certificate should be SHEPOV filing'
                
        return legitimacy
    
    def _detect_tampering_indicators(self, entry: Dict) -> List[str]:
        """Detect tampering indicators in entry"""
        indicators = []
        
        filing_date = entry.get('Filing Date', '')
        description = entry.get('Description', '').lower()
        side = entry.get('Side', '')
        
        # Check for known missing entries
        if 'may 16' in filing_date.lower() or '5/16' in filing_date:
            if 'parenting' in description or 'completion' in description:
                indicators.append('KNOWN_MISSING_ENTRY_MAY16_PARENTING_CERT')
                
        # Check for suspicious timing
        if filing_date == '04/04/2025' and 'complaint' in description:
            if 'paper' not in description.lower():
                indicators.append('PAPER_FILING_SHOWS_AS_ELECTRONIC')
                
        # Check for filer misattribution
        if 'shepov' not in side.lower() and any(word in description for word in ['certificate', 'completion']):
            indicators.append('SHEPOV_FILING_MISATTRIBUTED')
            
        return indicators
    
    def detect_gaps_and_anomalies(self, current_entries: List[Dict]) -> Dict:
        """
        Detect missing records by analyzing neighbor relationships
        """
        if not self.integrity_file.exists():
            return {'error': 'No baseline positional map found. Create one first.'}
        
        with open(self.integrity_file, 'r') as f:
            baseline_map = json.load(f)
        
        current_map = self.create_positional_map(current_entries)
        
        analysis = {
            'case_number': self.case_number,
            'analysis_date': datetime.now().isoformat(),
            'baseline_entries': baseline_map['total_entries'],
            'current_entries': current_map['total_entries'],
            'detected_gaps': [],
            'missing_records': [],
            'position_shifts': [],
            'new_insertions': [],
            'integrity_status': 'INTACT',
            'chain_breaks': [],
            
            # TAMPERING DETECTION
            'known_missing_entries': self._check_known_missing_entries(current_entries),
            'hijacked_entries': [],
            'tampering_severity': 'NONE',
            'automatic_motion_needed': False
        }
        
        # Analyze each baseline record for gaps
        baseline_entries = baseline_map['entries']
        current_entries_map = current_map['entries']
        
        for docket_num, baseline_record in baseline_entries.items():
            if docket_num not in current_entries_map:
                # Record is missing - analyze the gap
                gap_info = self._analyze_missing_record_gap(baseline_record, baseline_map, current_map)
                analysis['missing_records'].append(gap_info)
                analysis['integrity_status'] = 'COMPROMISED'
            else:
                # Record exists - check if neighbors are intact
                current_record = current_entries_map[docket_num]
                neighbor_analysis = self._analyze_neighbor_integrity(
                    baseline_record, current_record, baseline_map, current_map
                )
                
                if neighbor_analysis['has_gaps']:
                    analysis['detected_gaps'].extend(neighbor_analysis['gaps'])
                    analysis['integrity_status'] = 'SUSPICIOUS'
                
                if neighbor_analysis['position_changed']:
                    analysis['position_shifts'].append(neighbor_analysis['shift_info'])
        
        # Detect new insertions
        for docket_num in current_entries_map:
            if docket_num not in baseline_entries:
                analysis['new_insertions'].append({
                    'docket_number': docket_num,
                    'position': current_entries_map[docket_num]['position_index'],
                    'description': current_entries_map[docket_num]['description']
                })
        
        # Check tampering severity and motion generation
        if analysis['known_missing_entries'] or analysis['hijacked_entries']:
            analysis['tampering_severity'] = 'CRITICAL'
            analysis['automatic_motion_needed'] = True
            analysis['integrity_status'] = 'COMPROMISED'
        elif analysis['missing_records']:
            analysis['tampering_severity'] = 'HIGH'
            analysis['integrity_status'] = 'SUSPICIOUS'
            
        return analysis
    
    def _check_known_missing_entries(self, current_entries: List[Dict]) -> List[Dict]:
        """Check for known missing entries (like May 16 parenting certificate)"""
        missing_entries = []
        
        # Known SHEPOV submissions that should be present
        known_submissions = [
            {
                'submission_id': 2,
                'expected_date': '5/16/2025',
                'document_type': 'Certificate of Completion - Parenting Program',
                'description_keywords': ['parenting', 'completion', 'certificate'],
                'filer': 'SHEPOV',
                'evidence': 'Photos of docket, submission confirmation',
                'status': 'CRITICAL_MISSING'
            }
        ]
        
        for known_sub in known_submissions:
            found = False
            
            # Search current docket for this submission
            for entry in current_entries:
                filing_date = entry.get('Filing Date', '')
                description = entry.get('Description', '').lower()
                
                # Check if this matches the known submission
                date_match = known_sub['expected_date'] in filing_date
                keyword_match = any(keyword in description for keyword in known_sub['description_keywords'])
                
                if date_match and keyword_match:
                    found = True
                    break
                    
            if not found:
                missing_entries.append({
                    **known_sub,
                    'disappeared_date': datetime.now().isoformat(),
                    'alert_level': 'CRITICAL',
                    'motion_required': True
                })
                
        return missing_entries
    
    def _analyze_missing_record_gap(self, missing_record: Dict, baseline_map: Dict, current_map: Dict) -> Dict:
        """
        Analyze where a missing record should be based on its neighbors
        """
        gap_info = {
            'missing_docket_number': missing_record['docket_number'],
            'original_position': missing_record['position_index'],
            'filing_date': missing_record['filing_date'],
            'description': missing_record['description'],
            'gap_boundaries': {
                'before': None,
                'after': None
            },
            'expected_neighbors_still_present': [],
            'gap_size_estimate': 1
        }
        
        # Find the expected neighbors that should still be there
        previous_neighbors = missing_record['neighbors']['previous']
        next_neighbors = missing_record['neighbors']['next']
        
        current_entries_map = current_map['entries']
        
        # Check which previous neighbors are still present
        for neighbor in reversed(previous_neighbors):  # Start from closest
            neighbor_docket = str(neighbor['docket_number'])
            if neighbor_docket in current_entries_map:
                gap_info['gap_boundaries']['before'] = {
                    'docket_number': neighbor['docket_number'],
                    'current_position': current_entries_map[neighbor_docket]['position_index']
                }
                break
        
        # Check which next neighbors are still present
        for neighbor in next_neighbors:  # Start from closest
            neighbor_docket = str(neighbor['docket_number'])
            if neighbor_docket in current_entries_map:
                gap_info['gap_boundaries']['after'] = {
                    'docket_number': neighbor['docket_number'],
                    'current_position': current_entries_map[neighbor_docket]['position_index']
                }
                break
        
        # Calculate gap size
        if gap_info['gap_boundaries']['before'] and gap_info['gap_boundaries']['after']:
            before_pos = gap_info['gap_boundaries']['before']['current_position']
            after_pos = gap_info['gap_boundaries']['after']['current_position']
            gap_info['gap_size_estimate'] = max(1, after_pos - before_pos - 1)
        
        return gap_info
    
    def _analyze_neighbor_integrity(self, baseline_record: Dict, current_record: Dict, baseline_map: Dict, current_map: Dict) -> Dict:
        """
        Analyze if a record's neighbors are still intact
        """
        analysis = {
            'has_gaps': False,
            'gaps': [],
            'position_changed': False,
            'shift_info': None
        }
        
        # Check if position changed
        if baseline_record['position_index'] != current_record['position_index']:
            analysis['position_changed'] = True
            analysis['shift_info'] = {
                'docket_number': baseline_record['docket_number'],
                'original_position': baseline_record['position_index'],
                'current_position': current_record['position_index'],
                'shift_amount': current_record['position_index'] - baseline_record['position_index']
            }
        
        # Check neighbor integrity
        baseline_neighbors = baseline_record['neighbors']
        current_entries_map = current_map['entries']
        
        # Check previous neighbors
        for neighbor in baseline_neighbors['previous']:
            neighbor_docket = str(neighbor['docket_number'])
            if neighbor_docket not in current_entries_map:
                analysis['has_gaps'] = True
                analysis['gaps'].append({
                    'type': 'missing_previous_neighbor',
                    'missing_docket': neighbor['docket_number'],
                    'relative_to': baseline_record['docket_number'],
                    'offset': neighbor['position_offset']
                })
        
        # Check next neighbors
        for neighbor in baseline_neighbors['next']:
            neighbor_docket = str(neighbor['docket_number'])
            if neighbor_docket not in current_entries_map:
                analysis['has_gaps'] = True
                analysis['gaps'].append({
                    'type': 'missing_next_neighbor',
                    'missing_docket': neighbor['docket_number'],
                    'relative_to': baseline_record['docket_number'],
                    'offset': neighbor['position_offset']
                })
        
        return analysis
    
    def save_positional_map(self, positional_map: Dict):
        """Save positional map for future gap detection"""
        self.integrity_file.parent.mkdir(exist_ok=True)
        
        with open(self.integrity_file, 'w') as f:
            json.dump(positional_map, f, indent=2)
    
    def generate_integrity_report(self, analysis: Dict) -> str:
        """Generate human-readable integrity report"""
        report = f"""
🛡️ DOCKET INTEGRITY ANALYSIS REPORT
Case: {analysis['case_number']}
Date: {analysis['analysis_date']}

📊 OVERVIEW:
- Baseline Entries: {analysis['baseline_entries']}
- Current Entries: {analysis['current_entries']}
- Status: {analysis['integrity_status']}

"""
        
        if analysis['missing_records']:
            report += f"""
🚨 MISSING RECORDS ({len(analysis['missing_records'])}):
"""
            for missing in analysis['missing_records']:
                gap = missing['gap_boundaries']
                before = gap['before']['docket_number'] if gap['before'] else 'START'
                after = gap['after']['docket_number'] if gap['after'] else 'END'
                
                report += f"""
  ❌ Docket #{missing['missing_docket_number']}
     Filed: {missing['filing_date']}
     Description: {missing['description'][:100]}...
     Gap Location: Between #{before} and #{after}
     Estimated Gap Size: {missing['gap_size_estimate']} record(s)
"""
        
        if analysis['detected_gaps']:
            report += f"""
⚠️ NEIGHBOR GAPS DETECTED ({len(analysis['detected_gaps'])}):
"""
            for gap in analysis['detected_gaps']:
                report += f"""
  🔍 {gap['type']}: Docket #{gap['missing_docket']} 
     (Relative to #{gap['relative_to']}, offset: {gap['offset']})
"""
        
        if analysis['position_shifts']:
            report += f"""
🔄 POSITION CHANGES ({len(analysis['position_shifts'])}):
"""
            for shift in analysis['position_shifts']:
                report += f"""
  📍 Docket #{shift['docket_number']}
     Moved from position {shift['original_position']} to {shift['current_position']}
     Shift: {shift['shift_amount']} positions
"""
        
        if analysis['new_insertions']:
            report += f"""
📄 NEW ENTRIES ({len(analysis['new_insertions'])}):
"""
            for new_entry in analysis['new_insertions']:
                report += f"""
  ➕ Docket #{new_entry['docket_number']} at position {new_entry['position']}
     Description: {new_entry['description'][:100]}...
"""
        
        # Add automatic motion if tampering detected
        if analysis.get('automatic_motion_needed', False):
            report += f"""

🚨 AUTOMATIC MOTION GENERATOR ACTIVATED

Motion Type: EMERGENCY MOTION TO COMPEL ACCURATE DOCKET
Reason: Critical tampering detected - missing entries with evidence
Priority: IMMEDIATE FILING REQUIRED

Generated Motion Text:
{self._generate_emergency_motion(analysis)}

📁 Evidence Files Ready:
- Baseline submission sequence
- Tampering detection report  
- Photo evidence (if available)
- Confirmation numbers/receipts
"""
        
        return report
    
    def _generate_emergency_motion(self, analysis: Dict) -> str:
        """Generate emergency motion text for docket tampering"""
        missing_entries = analysis.get('known_missing_entries', [])
        
        motion_text = f"""
EMERGENCY MOTION TO COMPEL ACCURATE DOCKET AND INVESTIGATE TAMPERING

TO THE HONORABLE COURT:

Petitioner SHEPOV respectfully moves this Court for emergency relief due to systematic tampering with the official docket in Case No. {self.case_number}.

FACTUAL BACKGROUND:

1. Automated integrity monitoring has detected CRITICAL tampering with court records.

2. MISSING ENTRIES: {len(missing_entries)} verified submissions have DISAPPEARED from the docket.

SPECIFIC TAMPERING EVIDENCE:
"""
        
        for missing in missing_entries:
            motion_text += f"""
• SUBMISSION ID {missing['submission_id']}: {missing['document_type']}
  - Filed: {missing['expected_date']}  
  - Evidence: {missing['evidence']}
  - Status: DISAPPEARED from current docket
  - Alert Level: {missing['alert_level']}
"""
        
        motion_text += f"""

TECHNICAL ANALYSIS:

This Court should note that Petitioner employs automated monitoring systems that maintain cryptographic hashes and positional tracking of all docket entries. The disappearance of entries is mathematically provable through chain-of-custody verification.

PRAYER FOR RELIEF:

WHEREFORE, Petitioner respectfully requests this Court:

1. IMMEDIATE emergency hearing on this motion
2. FORENSIC audit of electronic docket system  
3. RESTORATION of all missing entries
4. INVESTIGATION into unauthorized docket access
5. SANCTIONS for tampering with court records
6. PRESERVATION order for all system logs
7. APPOINTMENT of neutral technical expert
8. Such other relief as justice requires

Respectfully submitted,
SHEPOV, Pro Se Petitioner

CERTIFICATE OF AUTOMATED DETECTION:
This motion was generated automatically upon detection of docket tampering on {analysis['analysis_date']}.
Technical evidence available upon request.
"""
        
        return motion_text


class DocumentPositionalViewer:
    """
    Document viewer that shows each document's position and neighbors
    """
    
    def __init__(self, case_number: str = "DR-25-403973"):
        self.case_number = case_number
        self.position_system = DocketPositionalAwareness(case_number)
    
    def get_document_with_context(self, submission_id: int) -> Dict:
        """
        Get document with full positional context and neighbor awareness
        """
        if not self.position_system.integrity_file.exists():
            return {'error': 'Positional map not found. Create baseline first.'}
        
        with open(self.position_system.integrity_file, 'r') as f:
            positional_map = json.load(f)
        
        document_record = positional_map['entries'].get(str(submission_id))
        
        if not document_record:
            return {'error': f'Submission #{submission_id} not found in positional map'}
        
        # Enhanced context
        context = {
            'document': document_record,
            'position_info': {
                'chronological_position': document_record['position_index'],
                'total_documents': positional_map['total_entries'],
                'position_percentage': (document_record['position_index'] / positional_map['total_entries']) * 100
            },
            'neighbor_context': {
                'previous_5': document_record['neighbors']['previous'],
                'next_5': document_record['neighbors']['next'],
                'neighbor_range': positional_map['neighbor_range']
            },
            'integrity_info': {
                'chain_hash': document_record['chain_hash'],
                'neighbor_hash': document_record['neighbor_hash'],
                'expected_position': document_record['expected_position']
            }
        }
        
        return context
    
    def verify_document_chain(self, submission_id: int) -> Dict:
        """
        Verify the integrity of a document's position in the chain
        """
        context = self.get_document_with_context(submission_id)
        
        if 'error' in context:
            return context
        
        verification = {
            'submission_id': submission_id,
            'chain_status': 'INTACT',
            'position_verified': True,
            'neighbors_verified': True,
            'missing_neighbors': [],
            'verification_timestamp': datetime.now().isoformat()
        }
        
        # This would need current docket data to verify against
        # For now, we return the structure
        return verification


# Integration function for the main application
def create_positional_baseline(docket_entries: List[Dict], case_number: str = "DR-25-403973") -> Dict:
    """
    Create positional baseline for a case's docket entries
    """
    position_system = DocketPositionalAwareness(case_number)
    positional_map = position_system.create_positional_map(docket_entries)
    position_system.save_positional_map(positional_map)
    
    return {
        'success': True,
        'total_entries': len(docket_entries),
        'neighbor_range': position_system.neighbor_range,
        'baseline_file': str(position_system.integrity_file)
    }