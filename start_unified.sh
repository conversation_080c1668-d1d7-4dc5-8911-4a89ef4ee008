#!/bin/bash
# Start the unified Legal Case Management System

echo "🚀 Starting Legal Case Management System - Unified Application"

# Set environment variables
export POSTGRES_PASSWORD=AxWuQaxIGX8g
export MONGODB_PASSWORD=AxWuQaxIGX8g
export REDIS_PASSWORD=
export REDIS_HOST=localhost
export REDIS_PORT=6382
export USE_SECURE_ARCHITECTURE=true

# Load from .env file if it exists
if [ -f .env ]; then
    echo "📄 Loading environment from .env file"
    source .env
fi

# Check if Docker containers are running
echo "🐳 Checking Docker containers..."
if docker ps | grep -q "legal-cms"; then
    echo "✅ Docker containers are running"
    docker ps --filter "name=legal-cms" --format "table {{.Names}}\t{{.Status}}"
else
    echo "❌ Docker containers not found. Starting them..."
    ./deployment/deploy_secure_architecture.sh deploy
fi

# Test database connections
echo "🔗 Testing database connections..."

# PostgreSQL
if PGPASSWORD="$POSTGRES_PASSWORD" docker exec legal-cms-postgresql psql -U legal_cms_user -d legal_cms_main -c "SELECT 1;" > /dev/null 2>&1; then
    echo "✅ PostgreSQL connection successful"
else
    echo "❌ PostgreSQL connection failed"
fi

# MongoDB
if docker exec legal-cms-mongodb mongosh --eval "db.adminCommand('ping')" --quiet > /dev/null 2>&1; then
    echo "✅ MongoDB connection successful"
else
    echo "❌ MongoDB connection failed"
fi

# Redis
if docker exec legal-cms-redis redis-cli ping > /dev/null 2>&1; then
    echo "✅ Redis connection successful"
else
    echo "❌ Redis connection failed"
fi

echo ""
echo "🌐 Starting Streamlit application on port 8503..."
echo "📱 Access the application at: http://localhost:8503"
echo ""

# Start the unified application
streamlit run legal_cms_unified.py --server.port 8503 --server.address 0.0.0.0